key,name,type,severity,status,sysTags
java:S3751,"""@RequestMapping"" methods should be ""public""",CODE_SMELL,MAJOR,READY,owasp-a6;spring
java:S5301,"""ActiveMQConnectionFactory"" should not be vulnerable to malicious code deserialization",VULNERABILITY,MINOR,READY,cwe;owasp-a8
java:S2254,"""HttpServletRequest.getRequestedSessionId()"" should not be used",VULNERABILITY,CRITICAL,READY,cwe;owasp-a2;sans-top25-porous
java:S2115,A secure password should be used when connecting to a database,VULNERABILITY,BLOCKER,READY,cwe;owasp-a2;owasp-a3
java:S4434,Allowing deserialization of LDAP objects is security-sensitive,SECURITY_HOTSPOT,MAJOR,READY,cwe;owasp-a8
findbugs:JUA_DONT_ASSERT_INSTANCEOF_IN_TESTS,Bad practice -  Asserting value of instanceof in tests is not recommended.,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:CO_ABSTRACT_SELF,Bad practice - Abstract class defines covariant compareTo() method,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:EQ_ABSTRACT_SELF,Bad practice - Abstract class defines covariant equals() method,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:DMI_ENTRY_SETS_MAY_REUSE_ENTRY_OBJECTS,Bad practice - Adding elements of an entry set may fail due to reuse of Entry objects,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:SW_SWING_METHODS_INVOKED_IN_SWING_THREAD,Bad practice - Certain swing methods need to be invoked in Swing thread,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:BIT_SIGNED_CHECK,Bad practice - Check for sign of bitwise operation,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:CN_IMPLEMENTS_CLONE_BUT_NOT_CLONEABLE,Bad practice - Class defines clone() but doesn't implement Cloneable,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:EQ_COMPARETO_USE_OBJECT_EQUALS,Bad practice - Class defines compareTo(...) and uses Object.equals(),CODE_SMELL,MAJOR,READY,bad-practice
findbugs:HE_EQUALS_USE_HASHCODE,Bad practice - Class defines equals() and uses Object.hashCode(),CODE_SMELL,MAJOR,READY,bad-practice
findbugs:HE_EQUALS_NO_HASHCODE,Bad practice - Class defines equals() but not hashCode(),CODE_SMELL,MAJOR,READY,bad-practice
findbugs:HE_HASHCODE_USE_OBJECT_EQUALS,Bad practice - Class defines hashCode() and uses Object.equals(),CODE_SMELL,MAJOR,READY,bad-practice
findbugs:HE_HASHCODE_NO_EQUALS,Bad practice - Class defines hashCode() but not equals(),CODE_SMELL,MAJOR,READY,bad-practice
findbugs:CN_IDIOM,Bad practice - Class implements Cloneable but does not define or use clone method,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:HE_INHERITS_EQUALS_USE_HASHCODE,Bad practice - Class inherits equals() and uses Object.hashCode(),CODE_SMELL,MAJOR,READY,bad-practice
findbugs:SE_NO_SUITABLE_CONSTRUCTOR_FOR_EXTERNALIZATION,Bad practice - Class is Externalizable but doesn't define a void constructor,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:NM_CLASS_NOT_EXCEPTION,"Bad practice - Class is not derived from an Exception, even though it is named as such",CODE_SMELL,MAJOR,READY,bad-practice
findbugs:SE_NO_SUITABLE_CONSTRUCTOR,Bad practice - Class is Serializable but its superclass doesn't define a void constructor,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:SE_NO_SERIALVERSIONID,"Bad practice - Class is Serializable, but doesn't define serialVersionUID",CODE_SMELL,MAJOR,READY,bad-practice
findbugs:NM_CLASS_NAMING_CONVENTION,Bad practice - Class names should start with an upper case letter,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:NM_SAME_SIMPLE_NAME_AS_INTERFACE,Bad practice - Class names shouldn't shadow simple name of implemented interface,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:NM_SAME_SIMPLE_NAME_AS_SUPERCLASS,Bad practice - Class names shouldn't shadow simple name of superclass,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:CN_IDIOM_NO_SUPER_CALL,Bad practice - clone method does not call super.clone(),CODE_SMELL,MAJOR,READY,bad-practice
findbugs:NP_CLONE_COULD_RETURN_NULL,Bad practice - Clone method may return null,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:SE_COMPARATOR_SHOULD_BE_SERIALIZABLE,Bad practice - Comparator doesn't implement Serializable,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:CO_COMPARETO_INCORRECT_FLOATING,Bad practice - compareTo()/compare() incorrectly handles float or double value,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:CO_COMPARETO_RESULTS_MIN_VALUE,Bad practice - compareTo()/compare() returns Integer.MIN_VALUE,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:ES_COMPARING_STRINGS_WITH_EQ,Bad practice - Comparison of String objects using == or !=,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:ES_COMPARING_PARAMETER_STRING_WITH_EQ,Bad practice - Comparison of String parameter using == or !=,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:NM_CONFUSING,Bad practice - Confusing method names,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:CO_SELF_NO_OBJECT,Bad practice - Covariant compareTo() method defined,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:EQ_SELF_NO_OBJECT,Bad practice - Covariant equals() method defined,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:AM_CREATES_EMPTY_JAR_FILE_ENTRY,Bad practice - Creates an empty jar file entry,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:AM_CREATES_EMPTY_ZIP_FILE_ENTRY,Bad practice - Creates an empty zip file entry,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:PZ_DONT_REUSE_ENTRY_OBJECTS_IN_ITERATORS,Bad practice - Don't reuse entry objects in iterators,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:DMI_USING_REMOVEALL_TO_CLEAR_COLLECTION,Bad practice - Don't use removeAll to clear a collection,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:IMSE_DONT_CATCH_IMSE,Bad practice - Dubious catching of IllegalMonitorStateException,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:FI_EMPTY,Bad practice - Empty finalizer should be deleted,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:ME_MUTABLE_ENUM_FIELD,Bad practice - Enum field is public and mutable,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:EQ_CHECK_FOR_OPERAND_NOT_COMPATIBLE_WITH_THIS,Bad practice - Equals checks for incompatible operand,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:EQ_GETCLASS_AND_CLASS_CONSTANT,Bad practice - equals method fails for subtypes,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:BC_EQUALS_METHOD_SHOULD_WORK_FOR_ALL_OBJECTS,Bad practice - Equals method should not assume anything about the type of its argument,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:NP_EQUALS_SHOULD_HANDLE_NULL_ARGUMENT,Bad practice - equals() method does not check for null argument,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:FI_EXPLICIT_INVOCATION,Bad practice - Explicit invocation of finalizer,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:NM_FIELD_NAMING_CONVENTION,Bad practice - Field names should start with a lower case letter,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:JCIP_FIELD_ISNT_FINAL_IN_IMMUTABLE_CLASS,Bad practice - Fields of immutable classes should be final,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:FI_MISSING_SUPER_CALL,Bad practice - Finalizer does not call superclass finalizer,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:FI_USELESS,Bad practice - Finalizer does nothing but call superclass finalizer,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:FI_NULLIFY_SUPER,Bad practice - Finalizer nullifies superclass finalizer,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:FI_FINALIZER_NULLS_FIELDS,Bad practice - Finalizer nulls fields,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:FI_FINALIZER_ONLY_NULLS_FIELDS,Bad practice - Finalizer only nulls fields,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:VA_FORMAT_STRING_USES_NEWLINE,Bad practice - Format string should use %n rather than \n,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:IT_NO_SUCH_ELEMENT,Bad practice - Iterator next() method cannot throw NoSuchElementException,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:NM_WRONG_PACKAGE_INTENTIONAL,Bad practice - Method doesn't override method in superclass due to wrong package for parameter,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:RV_RETURN_VALUE_IGNORED_BAD_PRACTICE,Bad practice - Method ignores exceptional return value,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:RR_NOT_CHECKED,Bad practice - Method ignores results of InputStream.read(),CODE_SMELL,MAJOR,READY,bad-practice
findbugs:SR_NOT_CHECKED,Bad practice - Method ignores results of InputStream.skip(),CODE_SMELL,MAJOR,READY,bad-practice
findbugs:THROWS_METHOD_THROWS_RUNTIMEEXCEPTION,Bad practice - Method intentionally throws RuntimeException.,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:DM_RUN_FINALIZERS_ON_EXIT,Bad practice - Method invokes dangerous method runFinalizersOnExit,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:DM_EXIT,Bad practice - Method invokes System.exit(...),CODE_SMELL,MAJOR,READY,bad-practice
findbugs:THROWS_METHOD_THROWS_CLAUSE_BASIC_EXCEPTION,Bad practice - Method lists Exception in its throws clause.,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:THROWS_METHOD_THROWS_CLAUSE_THROWABLE,Bad practice - Method lists Throwable in its throws clause.,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:ODR_OPEN_DATABASE_RESOURCE,Bad practice - Method may fail to close database resource,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:ODR_OPEN_DATABASE_RESOURCE_EXCEPTION_PATH,Bad practice - Method may fail to close database resource on exception,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:OS_OPEN_STREAM,Bad practice - Method may fail to close stream,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:OS_OPEN_STREAM_EXCEPTION_PATH,Bad practice - Method may fail to close stream on exception,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:DE_MIGHT_DROP,Bad practice - Method might drop exception,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:DE_MIGHT_IGNORE,Bad practice - Method might ignore exception,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:NM_METHOD_NAMING_CONVENTION,Bad practice - Method names should start with a lower case letter,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:NP_BOOLEAN_RETURN_NULL,Bad practice - Method with Boolean return type returns explicit null,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:ISC_INSTANTIATE_STATIC_CLASS,Bad practice - Needless instantiation of class that only supplies static methods,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:RV_NEGATING_RESULT_OF_COMPARETO,Bad practice - Negating the result of compareTo()/compare(),CODE_SMELL,MAJOR,READY,bad-practice
findbugs:SE_BAD_FIELD_INNER_CLASS,Bad practice - Non-serializable class has a serializable inner class,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:SE_BAD_FIELD_STORE,Bad practice - Non-serializable value stored into instance field of a serializable class,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:SE_BAD_FIELD,Bad practice - Non-transient non-serializable instance field in serializable class,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:ME_ENUM_FIELD_SETTER,Bad practice - Public enum method unconditionally sets its field,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:DMI_RANDOM_USED_ONLY_ONCE,Bad practice - Random object created and used only once,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:CNT_ROUGH_CONSTANT_VALUE,Bad practice - Rough value of known constant found,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:SE_INNER_CLASS,Bad practice - Serializable inner class,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:SE_NONFINAL_SERIALVERSIONID,Bad practice - serialVersionUID isn't final,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:SE_NONLONG_SERIALVERSIONID,Bad practice - serialVersionUID isn't long,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:SE_NONSTATIC_SERIALVERSIONID,Bad practice - serialVersionUID isn't static,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:SI_INSTANCE_BEFORE_FINALS_ASSIGNED,Bad practice - Static initializer creates instance before all static final fields assigned,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:J2EE_STORE_OF_NON_SERIALIZABLE_OBJECT_INTO_SESSION,Bad practice - Store of non serializable object into HttpSession,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:IC_SUPERCLASS_USES_SUBCLASS_DURING_INITIALIZATION,Bad practice - Superclass uses subclass during initialization,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:RC_REF_COMPARISON_BAD_PRACTICE_BOOLEAN,Bad practice - Suspicious reference comparison of Boolean values,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:RC_REF_COMPARISON_BAD_PRACTICE,Bad practice - Suspicious reference comparison to constant,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:SE_READ_RESOLVE_MUST_RETURN_OBJECT,Bad practice - The readResolve method must be declared with a return type of Object.,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:NP_TOSTRING_COULD_RETURN_NULL,Bad practice - toString method may return null,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:SE_TRANSIENT_FIELD_NOT_RESTORED,Bad practice - Transient field that isn't set by deserialization.,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:GC_UNCHECKED_TYPE_IN_GENERIC_CALL,Bad practice - Unchecked type in generic call,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:UI_INHERITANCE_UNSAFE_GETRESOURCE,Bad practice - Usage of GetResource may be unsafe if class is extended,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:NM_FUTURE_KEYWORD_USED_AS_IDENTIFIER,Bad practice - Use of identifier that is a keyword in later versions of Java,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:NM_FUTURE_KEYWORD_USED_AS_MEMBER_IDENTIFIER,Bad practice - Use of identifier that is a keyword in later versions of Java,CODE_SMELL,MAJOR,READY,bad-practice
findbugs:NM_VERY_CONFUSING_INTENTIONAL,Bad practice - Very confusing method names (but perhaps intentional),CODE_SMELL,MAJOR,READY,bad-practice
java:S2647,Basic authentication should not be used,VULNERABILITY,CRITICAL,READY,cwe;owasp-a3;sans-top25-porous
java:S5547,Cipher algorithms should be robust,VULNERABILITY,CRITICAL,READY,cert;cwe;owasp-a3;owasp-a6;privacy;sans-top25-porous
java:S3329,Cipher Block Chaining IV's should be unpredictable,VULNERABILITY,CRITICAL,READY,cwe;owasp-a6
java:S2658,Classes should not be loaded dynamically,VULNERABILITY,CRITICAL,READY,
findbugs:RE_POSSIBLE_UNINTENDED_PATTERN,"Correctness - ""."" or ""|"" used for regular expression",BUG,MAJOR,READY,correctness
findbugs:ICAST_BAD_SHIFT_AMOUNT,Correctness - 32 bit int shifted by an amount not in the range -31..31,BUG,MAJOR,READY,correctness
findbugs:IL_CONTAINER_ADDED_TO_ITSELF,Correctness - A collection is added to itself,BUG,MAJOR,READY,correctness
findbugs:NP_NULL_INSTANCEOF,Correctness - A known null value is checked to see if it is an instance of a type,BUG,MAJOR,READY,correctness
findbugs:IP_PARAMETER_IS_DEAD_BUT_OVERWRITTEN,Correctness - A parameter is dead upon entry to a method but overwritten,BUG,MAJOR,READY,correctness
findbugs:IL_INFINITE_LOOP,Correctness - An apparent infinite loop,BUG,MAJOR,READY,correctness
findbugs:IL_INFINITE_RECURSIVE_LOOP,Correctness - An apparent infinite recursive loop,BUG,MAJOR,READY,correctness
findbugs:NM_METHOD_CONSTRUCTOR_CONFUSION,Correctness - Apparent method/constructor confusion,BUG,MAJOR,READY,correctness
findbugs:RANGE_ARRAY_INDEX,Correctness - Array index is out of bounds,BUG,CRITICAL,READY,correctness
findbugs:RANGE_ARRAY_LENGTH,Correctness - Array length is out of bounds,BUG,CRITICAL,READY,correctness
findbugs:RANGE_ARRAY_OFFSET,Correctness - Array offset is out of bounds,BUG,CRITICAL,READY,correctness
findbugs:BAC_BAD_APPLET_CONSTRUCTOR,Correctness - Bad Applet Constructor relies on uninitialized AppletStub,BUG,MAJOR,READY,correctness
findbugs:RV_ABSOLUTE_VALUE_OF_HASHCODE,Correctness - Bad attempt to compute absolute value of signed 32-bit hashcode,BUG,MAJOR,READY,correctness
findbugs:RV_ABSOLUTE_VALUE_OF_RANDOM_INT,Correctness - Bad attempt to compute absolute value of signed random integer,BUG,MAJOR,READY,correctness
findbugs:INT_BAD_COMPARISON_WITH_INT_VALUE,Correctness - Bad comparison of int value with long constant,BUG,MAJOR,READY,correctness
findbugs:INT_BAD_COMPARISON_WITH_NONNEGATIVE_VALUE,Correctness - Bad comparison of nonnegative value with negative constant or zero,BUG,MAJOR,READY,correctness
findbugs:INT_BAD_COMPARISON_WITH_SIGNED_BYTE,Correctness - Bad comparison of signed byte,BUG,MAJOR,READY,correctness
findbugs:DMI_BAD_MONTH,Correctness - Bad constant value for month,BUG,MAJOR,READY,correctness
findbugs:DMI_BIGDECIMAL_CONSTRUCTED_FROM_DOUBLE,Correctness - BigDecimal constructed from double that isn't represented precisely,BUG,MAJOR,READY,correctness
findbugs:BIT_ADD_OF_SIGNED_BYTE,Correctness - Bitwise add of signed byte value,BUG,MAJOR,READY,correctness
findbugs:BIT_IOR_OF_SIGNED_BYTE,Correctness - Bitwise OR of signed byte value,BUG,MAJOR,READY,correctness
findbugs:EC_UNRELATED_INTERFACES,Correctness - Call to equals() comparing different interface types,BUG,MAJOR,READY,correctness
findbugs:EC_UNRELATED_TYPES,Correctness - Call to equals() comparing different types,BUG,MAJOR,READY,correctness
findbugs:EC_UNRELATED_CLASS_AND_INTERFACE,Correctness - Call to equals() comparing unrelated class and interface,BUG,MAJOR,READY,correctness
findbugs:EC_NULL_ARG,Correctness - Call to equals(null),BUG,MAJOR,READY,correctness
findbugs:DMI_ANNOTATION_IS_NOT_VISIBLE_TO_REFLECTION,Correctness - Cannot use reflection to check for presence of annotation without runtime retention,BUG,MAJOR,READY,correctness
findbugs:BIT_SIGNED_CHECK_HIGH_BIT,Correctness - Check for sign of bitwise operation involving negative number,BUG,MAJOR,READY,correctness
findbugs:BIT_AND_ZZ,Correctness - Check to see if ((...) & 0) == 0,BUG,MAJOR,READY,correctness
findbugs:NM_BAD_EQUAL,Correctness - Class defines equal(Object); should it be equals(Object)?,BUG,MAJOR,READY,correctness
findbugs:MF_CLASS_MASKS_FIELD,Correctness - Class defines field that masks a superclass field,BUG,MAJOR,READY,correctness
findbugs:NM_LCASE_HASHCODE,Correctness - Class defines hashcode(); should it be hashCode()?,BUG,MAJOR,READY,correctness
findbugs:NM_LCASE_TOSTRING,Correctness - Class defines tostring(); should it be toString()?,BUG,MAJOR,READY,correctness
findbugs:VR_UNRESOLVABLE_REFERENCE,Correctness - Class makes reference to unresolvable class or method,BUG,MAJOR,READY,correctness
findbugs:BOA_BADLY_OVERRIDDEN_ADAPTER,Correctness - Class overrides a method implemented in super class Adapter wrongly,BUG,MAJOR,READY,correctness
findbugs:NP_CLOSING_NULL,Correctness - close() invoked on a value that is always null,BUG,MAJOR,READY,correctness
findbugs:RV_CHECK_COMPARETO_FOR_SPECIFIC_RETURN_VALUE,Correctness - Code checks for specific values returned by compareTo,BUG,MAJOR,READY,correctness
findbugs:DMI_COLLECTIONS_SHOULD_NOT_CONTAIN_THEMSELVES,Correctness - Collections should not contain themselves,BUG,MAJOR,READY,correctness
findbugs:TQ_COMPARING_VALUES_WITH_INCOMPATIBLE_TYPE_QUALIFIERS,Correctness - Comparing values with incompatible type qualifiers,BUG,MAJOR,READY,correctness
findbugs:EQ_DONT_DEFINE_EQUALS_FOR_ENUM,Correctness - Covariant equals() method defined for enum,BUG,MAJOR,READY,correctness
findbugs:EQ_SELF_USE_OBJECT,"Correctness - Covariant equals() method defined, Object.equals(Object) inherited",BUG,MAJOR,READY,correctness
findbugs:DMI_SCHEDULED_THREAD_POOL_EXECUTOR_WITH_ZERO_CORE_THREADS,Correctness - Creation of ScheduledThreadPoolExecutor with zero core threads,BUG,MAJOR,READY,correctness
findbugs:DMI_DOH,Correctness - D'oh! A nonsensical method invocation,BUG,MAJOR,READY,correctness
findbugs:EOS_BAD_END_OF_STREAM_CHECK,Correctness - Data read is converted before comparison to -1,BUG,MAJOR,READY,correctness
findbugs:SF_DEAD_STORE_DUE_TO_SWITCH_FALLTHROUGH,Correctness - Dead store due to switch statement fall through,BUG,MAJOR,READY,correctness
findbugs:SF_DEAD_STORE_DUE_TO_SWITCH_FALLTHROUGH_TO_THROW,Correctness - Dead store due to switch statement fall through to throw,BUG,MAJOR,READY,correctness
findbugs:DLS_DEAD_STORE_OF_CLASS_LITERAL,Correctness - Dead store of class literal,BUG,MAJOR,READY,correctness
findbugs:SIC_THREADLOCAL_DEADLY_EMBRACE,Correctness - Deadly embrace of non-static inner class and thread local,BUG,MAJOR,READY,correctness
findbugs:FL_FLOATS_AS_LOOP_COUNTERS,Correctness - Do not use floating-point variables as loop counters,BUG,MAJOR,READY,correctness
findbugs:IO_APPENDING_TO_OBJECT_OUTPUT_STREAM,Correctness - Doomed attempt to append to an object output stream,BUG,MAJOR,READY,correctness
findbugs:FE_TEST_IF_EQUAL_TO_NOT_A_NUMBER,Correctness - Doomed test for equality to NaN,BUG,MAJOR,READY,correctness
findbugs:DMI_LONG_BITS_TO_DOUBLE_INVOKED_ON_INT,Correctness - Double.longBitsToDouble invoked on an int,BUG,MAJOR,READY,correctness
findbugs:EQ_ALWAYS_FALSE,Correctness - equals method always returns false,BUG,MAJOR,READY,correctness
findbugs:EQ_ALWAYS_TRUE,Correctness - equals method always returns true,BUG,MAJOR,READY,correctness
findbugs:EQ_COMPARING_CLASS_NAMES,Correctness - equals method compares class names rather than class objects,BUG,MAJOR,READY,correctness
findbugs:EQ_OVERRIDING_EQUALS_NOT_SYMMETRIC,Correctness - equals method overrides equals in superclass and may not be symmetric,BUG,MAJOR,READY,correctness
findbugs:EQ_OTHER_NO_OBJECT,Correctness - equals() method defined that doesn't override equals(Object),BUG,MAJOR,READY,correctness
findbugs:EQ_OTHER_USE_OBJECT,Correctness - equals() method defined that doesn't override Object.equals(Object),BUG,MAJOR,READY,correctness
findbugs:EC_ARRAY_AND_NONARRAY,Correctness - equals() used to compare array and nonarray,BUG,MAJOR,READY,correctness
findbugs:EC_INCOMPATIBLE_ARRAY_COMPARE,Correctness - equals(...) used to compare incompatible arrays,BUG,MAJOR,READY,correctness
findbugs:RV_EXCEPTION_NOT_THROWN,Correctness - Exception created and dropped rather than thrown,BUG,MAJOR,READY,correctness
findbugs:UWF_NULL_FIELD,Correctness - Field only ever set to null,BUG,MAJOR,READY,correctness
findbugs:RE_CANT_USE_FILE_SEPARATOR_AS_REGULAR_EXPRESSION,Correctness - File.separator used for regular expression,BUG,MAJOR,READY,correctness
findbugs:DMI_FUTILE_ATTEMPT_TO_CHANGE_MAXPOOL_SIZE_OF_SCHEDULED_THREAD_POOL_EXECUTOR,Correctness - Futile attempt to change max pool size of ScheduledThreadPoolExecutor,BUG,MAJOR,READY,correctness
findbugs:DMI_CALLING_NEXT_FROM_HASNEXT,Correctness - hasNext method invokes next,BUG,MAJOR,READY,correctness
findbugs:BC_IMPOSSIBLE_CAST,Correctness - Impossible cast,BUG,CRITICAL,READY,correctness
findbugs:BC_IMPOSSIBLE_CAST_PRIMITIVE_ARRAY,Correctness - Impossible cast involving primitive array,BUG,CRITICAL,READY,correctness
findbugs:BC_IMPOSSIBLE_DOWNCAST,Correctness - Impossible downcast,BUG,CRITICAL,READY,correctness
findbugs:BC_IMPOSSIBLE_DOWNCAST_OF_TOARRAY,Correctness - Impossible downcast of toArray() result,BUG,CRITICAL,READY,correctness
findbugs:BIT_IOR,Correctness - Incompatible bit masks,BUG,MAJOR,READY,correctness
findbugs:BIT_AND,Correctness - Incompatible bit masks,BUG,MAJOR,READY,correctness
findbugs:DM_INVALID_MIN_MAX,Correctness - Incorrect combination of Math.max and Math.min,BUG,MAJOR,READY,correctness
findbugs:BC_IMPOSSIBLE_INSTANCEOF,Correctness - instanceof will always return false,BUG,CRITICAL,READY,correctness
findbugs:ICAST_INT_CAST_TO_FLOAT_PASSED_TO_ROUND,Correctness - int value cast to float and then passed to Math.round,BUG,MAJOR,READY,correctness
findbugs:ICAST_INT_2_LONG_AS_INSTANT,Correctness - int value converted to long and used as absolute time,BUG,MAJOR,READY,correctness
findbugs:IM_MULTIPLYING_RESULT_OF_IREM,Correctness - Integer multiply of result of integer remainder,BUG,MAJOR,READY,correctness
findbugs:ICAST_INT_CAST_TO_DOUBLE_PASSED_TO_CEIL,Correctness - Integral value cast to double and then passed to Math.ceil,BUG,MAJOR,READY,correctness
findbugs:RE_BAD_SYNTAX_FOR_REGULAR_EXPRESSION,Correctness - Invalid syntax for regular expression,BUG,MAJOR,READY,correctness
findbugs:EC_BAD_ARRAY_COMPARE,"Correctness - Invocation of equals() on an array, which is equivalent to ==",BUG,MAJOR,READY,correctness
findbugs:DMI_INVOKING_HASHCODE_ON_ARRAY,Correctness - Invocation of hashCode on an array,BUG,MAJOR,READY,correctness
findbugs:DMI_INVOKING_TOSTRING_ON_ARRAY,Correctness - Invocation of toString on an array,BUG,MAJOR,READY,correctness
findbugs:DMI_INVOKING_TOSTRING_ON_ANONYMOUS_ARRAY,Correctness - Invocation of toString on an unnamed array,BUG,MAJOR,READY,correctness
findbugs:IJU_ASSERT_METHOD_INVOKED_FROM_RUN_METHOD,Correctness - JUnit assertion in run method will not be noticed by JUnit,BUG,MAJOR,READY,correctness
findbugs:QBA_QUESTIONABLE_BOOLEAN_ASSIGNMENT,Correctness - Method assigns boolean literal in boolean expression,BUG,MAJOR,READY,correctness
findbugs:SQL_BAD_PREPARED_STATEMENT_ACCESS,Correctness - Method attempts to access a prepared statement parameter with index 0,BUG,MAJOR,READY,correctness
findbugs:SQL_BAD_RESULTSET_ACCESS,Correctness - Method attempts to access a result set field with index 0,BUG,MAJOR,READY,correctness
findbugs:NP_NULL_PARAM_DEREF,Correctness - Method call passes null for non-null parameter,BUG,MAJOR,READY,correctness
findbugs:NP_NULL_PARAM_DEREF_ALL_TARGETS_DANGEROUS,Correctness - Method call passes null for non-null parameter,BUG,MAJOR,READY,correctness
findbugs:NP_NONNULL_PARAM_VIOLATION,Correctness - Method call passes null to a non-null parameter,BUG,MAJOR,READY,correctness
findbugs:MF_METHOD_MASKS_FIELD,Correctness - Method defines a variable that obscures a field,BUG,MAJOR,READY,correctness
findbugs:NP_ARGUMENT_MIGHT_BE_NULL,Correctness - Method does not check for null argument,BUG,MAJOR,READY,correctness
findbugs:NM_WRONG_PACKAGE,Correctness - Method doesn't override method in superclass due to wrong package for parameter,BUG,MAJOR,READY,correctness
fb-contrib:MDM_STRING_BYTES_ENCODING,Correctness - Method encodes String bytes without specifying the character encoding,BUG,MAJOR,READY,correctness
findbugs:RV_RETURN_VALUE_IGNORED,Correctness - Method ignores return value,BUG,MAJOR,READY,correctness
findbugs:NP_NONNULL_RETURN_VIOLATION,"Correctness - Method may return null, but is declared @Nonnull",BUG,MAJOR,READY,correctness
findbugs:SE_METHOD_MUST_BE_PRIVATE,Correctness - Method must be private in order for serialization to work,BUG,MAJOR,READY,correctness
findbugs:FL_MATH_USING_FLOAT_PRECISION,Correctness - Method performs math using floating point precision,BUG,MAJOR,READY,correctness
findbugs:NP_OPTIONAL_RETURN_NULL,Correctness - Method with Optional return type returns explicit null,BUG,MAJOR,READY,correctness
findbugs:FB_MISSING_EXPECTED_WARNING,Correctness - Missing expected or desired warning from SpotBugs,BUG,MAJOR,READY,correctness
findbugs:GC_UNRELATED_TYPES,Correctness - No relationship between generic parameter and method argument,BUG,MAJOR,READY,correctness
findbugs:NP_NONNULL_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR,Correctness - Non-null field is not initialized,BUG,MAJOR,READY,correctness
findbugs:NP_NULL_PARAM_DEREF_NONVIRTUAL,Correctness - Non-virtual method call passes null for non-null parameter,BUG,MAJOR,READY,correctness
findbugs:SA_FIELD_SELF_COMPUTATION,"Correctness - Nonsensical self computation involving a field (e.g., x & x)",BUG,MAJOR,READY,correctness
findbugs:SA_LOCAL_SELF_COMPUTATION,"Correctness - Nonsensical self computation involving a variable (e.g., x & x)",BUG,MAJOR,READY,correctness
findbugs:NP_ALWAYS_NULL,Correctness - Null pointer dereference,BUG,MAJOR,READY,correctness
findbugs:NP_ALWAYS_NULL_EXCEPTION,Correctness - Null pointer dereference in method on exception path,BUG,MAJOR,READY,correctness
findbugs:NP_GUARANTEED_DEREF,Correctness - Null value is guaranteed to be dereferenced,BUG,MAJOR,READY,correctness
findbugs:RCN_REDUNDANT_NULLCHECK_WOULD_HAVE_BEEN_A_NPE,Correctness - Nullcheck of value previously dereferenced,BUG,MAJOR,READY,correctness
findbugs:DLS_OVERWRITTEN_INCREMENT,Correctness - Overwritten increment,BUG,MAJOR,READY,correctness
findbugs:BSHIFT_WRONG_ADD_PRIORITY,Correctness - Possible bad parsing of shift operation,BUG,MAJOR,READY,correctness
findbugs:NP_NULL_ON_SOME_PATH,Correctness - Possible null pointer dereference,BUG,MAJOR,READY,correctness
findbugs:NP_NULL_ON_SOME_PATH_EXCEPTION,Correctness - Possible null pointer dereference in method on exception path,BUG,MAJOR,READY,correctness
findbugs:CAA_COVARIANT_ARRAY_ELEMENT_STORE,Correctness - Possibly incompatible element is stored in covariant array,BUG,MAJOR,READY,correctness
findbugs:VA_PRIMITIVE_ARRAY_PASSED_TO_OBJECT_VARARG,Correctness - Primitive array passed to function expecting a variable number of object arguments,BUG,MAJOR,READY,correctness
findbugs:RV_01_TO_INT,Correctness - Random value from 0 to 1 is coerced to the integer 0,BUG,MAJOR,READY,correctness
findbugs:NP_UNWRITTEN_FIELD,Correctness - Read of unwritten field,BUG,MAJOR,READY,correctness
findbugs:RpC_REPEATED_CONDITIONAL_TEST,Correctness - Repeated conditional tests,BUG,MAJOR,READY,correctness
findbugs:DMI_ARGUMENTS_WRONG_ORDER,Correctness - Reversed method arguments,BUG,MAJOR,READY,correctness
findbugs:SA_FIELD_SELF_ASSIGNMENT,Correctness - Self assignment of field,BUG,MAJOR,READY,correctness
findbugs:SA_LOCAL_SELF_ASSIGNMENT_INSTEAD_OF_FIELD,Correctness - Self assignment of local rather than assignment to field,BUG,MAJOR,READY,correctness
findbugs:SA_FIELD_SELF_COMPARISON,Correctness - Self comparison of field with itself,BUG,MAJOR,READY,correctness
findbugs:SA_LOCAL_SELF_COMPARISON,Correctness - Self comparison of value with itself,BUG,MAJOR,READY,correctness
findbugs:HE_SIGNATURE_DECLARES_HASHING_OF_UNHASHABLE_CLASS,Correctness - Signature declares use of unhashable class in hashed construct,BUG,MAJOR,READY,correctness
findbugs:STI_INTERRUPTED_ON_UNKNOWNTHREAD,Correctness - Static Thread.interrupted() method invoked on thread instance,BUG,MAJOR,READY,correctness
findbugs:NP_STORE_INTO_NONNULL_FIELD,Correctness - Store of null value into field annotated @Nonnull,BUG,MAJOR,READY,correctness
findbugs:RANGE_STRING_INDEX,Correctness - String index is out of bounds,BUG,CRITICAL,READY,correctness
findbugs:OVERRIDING_METHODS_MUST_INVOKE_SUPER,"Correctness - Super method is annotated with @OverridingMethodsMustInvokeSuper, but the overriding method isn't calling the super method.",BUG,MAJOR,READY,correctness
findbugs:RC_REF_COMPARISON,Correctness - Suspicious reference comparison,BUG,MAJOR,READY,correctness
findbugs:IJU_BAD_SUITE_METHOD,Correctness - TestCase declares a bad suite method,BUG,MAJOR,READY,correctness
findbugs:IJU_SETUP_NO_SUPER,Correctness - TestCase defines setUp that doesn't call super.setUp(),BUG,MAJOR,READY,correctness
findbugs:IJU_TEARDOWN_NO_SUPER,Correctness - TestCase defines tearDown that doesn't call super.tearDown(),BUG,MAJOR,READY,correctness
findbugs:IJU_NO_TESTS,Correctness - TestCase has no tests,BUG,MAJOR,READY,correctness
findbugs:IJU_SUITE_NOT_STATIC,Correctness - TestCase implements a non-static suite method,BUG,MAJOR,READY,correctness
findbugs:SE_READ_RESOLVE_IS_STATIC,Correctness - The readResolve method must not be declared as a static method.,BUG,MAJOR,READY,correctness
findbugs:UMAC_UNCALLABLE_METHOD_OF_ANONYMOUS_CLASS,Correctness - Uncallable method defined in anonymous class,BUG,MAJOR,READY,correctness
findbugs:FB_UNEXPECTED_WARNING,Correctness - Unexpected/undesired warning from SpotBugs,BUG,MAJOR,READY,correctness
findbugs:UR_UNINIT_READ,Correctness - Uninitialized read of field in constructor,BUG,MAJOR,READY,correctness
findbugs:UR_UNINIT_READ_CALLED_FROM_SUPER_CONSTRUCTOR,Correctness - Uninitialized read of field method called from constructor of superclass,BUG,MAJOR,READY,correctness
findbugs:SIO_SUPERFLUOUS_INSTANCEOF,Correctness - Unnecessary type check done using instanceof operator,BUG,MAJOR,READY,correctness
findbugs:STI_INTERRUPTED_ON_CURRENTTHREAD,"Correctness - Unneeded use of currentThread() call, to call interrupted()",BUG,MAJOR,READY,correctness
findbugs:UWF_UNWRITTEN_FIELD,Correctness - Unwritten field,BUG,MAJOR,READY,correctness
findbugs:HE_USE_OF_UNHASHABLE_CLASS,Correctness - Use of class without a hashCode() method in a hashed data structure,BUG,MAJOR,READY,correctness
findbugs:DLS_DEAD_LOCAL_INCREMENT_IN_RETURN,Correctness - Useless increment in return statement,BUG,MAJOR,READY,correctness
findbugs:DMI_VACUOUS_CALL_TO_EASYMOCK_METHOD,Correctness - Useless/vacuous call to EasyMock method,BUG,MAJOR,READY,correctness
findbugs:EC_UNRELATED_TYPES_USING_POINTER_EQUALITY,Correctness - Using pointer equality to compare different types,BUG,MAJOR,READY,correctness
findbugs:DMI_VACUOUS_SELF_COLLECTION_CALL,Correctness - Vacuous call to collections,BUG,MAJOR,READY,correctness
findbugs:TQ_ALWAYS_VALUE_USED_WHERE_NEVER_REQUIRED,Correctness - Value annotated as carrying a type qualifier used where a value that must not carry that qualifier is required,BUG,MAJOR,READY,correctness
findbugs:TQ_NEVER_VALUE_USED_WHERE_ALWAYS_REQUIRED,Correctness - Value annotated as never carrying a type qualifier used where value carrying that qualifier is required,BUG,MAJOR,READY,correctness
findbugs:NP_GUARANTEED_DEREF_ON_EXCEPTION_PATH,Correctness - Value is null and guaranteed to be dereferenced on exception path,BUG,MAJOR,READY,correctness
findbugs:TQ_MAYBE_SOURCE_VALUE_REACHES_NEVER_SINK,Correctness - Value that might carry a type qualifier is always used in a way prohibits it from having that type qualifier,BUG,MAJOR,READY,correctness
findbugs:TQ_MAYBE_SOURCE_VALUE_REACHES_ALWAYS_SINK,Correctness - Value that might not carry a type qualifier is always used in a way requires that type qualifier,BUG,MAJOR,READY,correctness
findbugs:TQ_UNKNOWN_VALUE_USED_WHERE_ALWAYS_STRICTLY_REQUIRED,Correctness - Value without a type qualifier used where a value is required to have that qualifier,BUG,MAJOR,READY,correctness
findbugs:NM_VERY_CONFUSING,Correctness - Very confusing method names,BUG,MAJOR,READY,correctness
java:S4426,Cryptographic keys should be robust,VULNERABILITY,CRITICAL,READY,cwe;owasp-a3;owasp-a6;privacy;rules
java:S3355,Defined filters should be used,VULNERABILITY,CRITICAL,READY,owasp-a6
java:S5542,Encryption algorithms should be used with secure mode and padding scheme,VULNERABILITY,CRITICAL,READY,cert;cwe;owasp-a3;owasp-a6;privacy;sans-top25-porous
java:S1989,Exceptions should not be thrown from servlet methods,VULNERABILITY,MINOR,READY,cert;cwe;error-handling;owasp-a3
findbugs:DM_CONVERT_CASE,I18n - Consider using Locale parameterized version of invoked method,CODE_SMELL,INFO,READY,i18n
findbugs:DM_DEFAULT_ENCODING,I18n - Reliance on default encoding,CODE_SMELL,INFO,READY,i18n
java:S4433,LDAP connections should be authenticated,VULNERABILITY,CRITICAL,READY,cwe;owasp-a2
findbugs:MC_OVERRIDABLE_METHOD_CALL_IN_CONSTRUCTOR,Malicious code - An overridable method is called from a constructor,CODE_SMELL,INFO,READY,malicious-code
findbugs:MC_OVERRIDABLE_METHOD_CALL_IN_CLONE,Malicious code - An overridable method is called from the clone() method.,CODE_SMELL,INFO,READY,malicious-code
findbugs:DP_CREATE_CLASSLOADER_INSIDE_DO_PRIVILEGED,Malicious code - Classloaders should only be created inside doPrivileged block,CODE_SMELL,INFO,READY,malicious-code
findbugs:PERM_SUPER_NOT_CALLED_IN_GETPERMISSIONS,Malicious code - Custom class loader does not call its superclass's getPermissions(),CODE_SMELL,INFO,READY,malicious-code
findbugs:MS_MUTABLE_ARRAY,Malicious code - Field is a mutable array,CODE_SMELL,INFO,READY,malicious-code
findbugs:MS_MUTABLE_COLLECTION,Malicious code - Field is a mutable collection,CODE_SMELL,INFO,READY,malicious-code
findbugs:MS_MUTABLE_COLLECTION_PKGPROTECT,Malicious code - Field is a mutable collection which should be package protected,CODE_SMELL,INFO,READY,malicious-code
findbugs:MS_MUTABLE_HASHTABLE,Malicious code - Field is a mutable Hashtable,CODE_SMELL,INFO,READY,malicious-code
findbugs:MS_CANNOT_BE_FINAL,Malicious code - Field isn't final and cannot be protected from malicious code,CODE_SMELL,INFO,READY,malicious-code
findbugs:MS_SHOULD_BE_FINAL,Malicious code - Field isn't final but should be,CODE_SMELL,INFO,READY,malicious-code
findbugs:MS_SHOULD_BE_REFACTORED_TO_BE_FINAL,Malicious code - Field isn't final but should be refactored to be so,CODE_SMELL,INFO,READY,malicious-code
findbugs:MS_FINAL_PKGPROTECT,Malicious code - Field should be both final and package protected,CODE_SMELL,INFO,READY,malicious-code
findbugs:MS_OOI_PKGPROTECT,Malicious code - Field should be moved out of an interface and made package protected,CODE_SMELL,INFO,READY,malicious-code
findbugs:MS_PKGPROTECT,Malicious code - Field should be package protected,CODE_SMELL,INFO,READY,malicious-code
findbugs:FI_PUBLIC_SHOULD_BE_PROTECTED,"Malicious code - Finalizer should be protected, not public",CODE_SMELL,INFO,READY,malicious-code
findbugs:EI_EXPOSE_BUF2,Malicious code - May expose internal representation by creating a buffer which incorporates reference to array,CODE_SMELL,INFO,READY,malicious-code
findbugs:EI_EXPOSE_REP2,Malicious code - May expose internal representation by incorporating reference to mutable object,CODE_SMELL,INFO,READY,malicious-code
findbugs:EI_EXPOSE_BUF,Malicious code - May expose internal representation by returning a buffer sharing non-public data,CODE_SMELL,INFO,READY,malicious-code
findbugs:MS_EXPOSE_BUF,Malicious code - May expose internal representation by returning a buffer sharing non-public data,CODE_SMELL,INFO,READY,malicious-code
findbugs:EI_EXPOSE_REP,Malicious code - May expose internal representation by returning reference to mutable object,CODE_SMELL,INFO,READY,malicious-code
findbugs:EI_EXPOSE_STATIC_BUF2,Malicious code - May expose internal static state by creating a buffer which stores an external array into a static field,CODE_SMELL,INFO,READY,malicious-code
findbugs:EI_EXPOSE_STATIC_REP2,Malicious code - May expose internal static state by storing a mutable object into a static field,CODE_SMELL,INFO,READY,malicious-code
findbugs:DP_DO_INSIDE_DO_PRIVILEGED,Malicious code - Method invoked that should be only be invoked inside a doPrivileged block,CODE_SMELL,INFO,READY,malicious-code
findbugs:USC_POTENTIAL_SECURITY_CHECK_BASED_ON_UNTRUSTED_SOURCE,Malicious code - Potential security check based on untrusted source.,CODE_SMELL,INFO,READY,malicious-code
findbugs:REFLC_REFLECTION_MAY_INCREASE_ACCESSIBILITY_OF_CLASS,Malicious code - Public method uses reflection to create a class it gets in its parameter which could increase the accessibility of any class,CODE_SMELL,INFO,READY,malicious-code
findbugs:REFLF_REFLECTION_MAY_INCREASE_ACCESSIBILITY_OF_FIELD,Malicious code - Public method uses reflection to modify a field it gets in its parameter which could increase the accessibility of any class,CODE_SMELL,INFO,READY,malicious-code
findbugs:MS_EXPOSE_REP,Malicious code - Public static method may expose internal representation by returning array,CODE_SMELL,INFO,READY,malicious-code
findbugs:DM_USELESS_THREAD,Multi-threading - A thread was created using the default empty run method,BUG,MAJOR,READY,multi-threading
findbugs:VO_VOLATILE_REFERENCE_TO_ARRAY,Multi-threading - A volatile reference to an array doesn't treat the array elements as volatile,BUG,MAJOR,READY,multi-threading
findbugs:VO_VOLATILE_INCREMENT,Multi-threading - An increment to a volatile field isn't atomic,BUG,MAJOR,READY,multi-threading
findbugs:STCAL_INVOKE_ON_STATIC_CALENDAR_INSTANCE,Multi-threading - Call to static Calendar,BUG,MAJOR,READY,multi-threading
findbugs:STCAL_INVOKE_ON_STATIC_DATE_FORMAT_INSTANCE,Multi-threading - Call to static DateFormat,BUG,MAJOR,READY,multi-threading
findbugs:RS_READOBJECT_SYNC,Multi-threading - Class's readObject() method is synchronized,BUG,MAJOR,READY,multi-threading
findbugs:WS_WRITEOBJECT_SYNC,Multi-threading - Class's writeObject() method is synchronized but nothing else is,BUG,MAJOR,READY,multi-threading
findbugs:WA_AWAIT_NOT_IN_LOOP,Multi-threading - Condition.await() not in loop,BUG,MAJOR,READY,multi-threading
findbugs:SC_START_IN_CTOR,Multi-threading - Constructor invokes Thread.start(),BUG,MAJOR,READY,multi-threading
findbugs:ESync_EMPTY_SYNC,Multi-threading - Empty synchronized block,BUG,MAJOR,READY,multi-threading
findbugs:IS_FIELD_NOT_GUARDED,Multi-threading - Field not guarded against concurrent access,BUG,MAJOR,READY,multi-threading
findbugs:IS_INCONSISTENT_SYNC,Multi-threading - Inconsistent synchronization,BUG,MAJOR,READY,multi-threading
findbugs:IS2_INCONSISTENT_SYNC,Multi-threading - Inconsistent synchronization,BUG,MAJOR,READY,multi-threading
findbugs:LI_LAZY_INIT_UPDATE_STATIC,Multi-threading - Incorrect lazy initialization and update of static field,BUG,MAJOR,READY,multi-threading
findbugs:LI_LAZY_INIT_STATIC,Multi-threading - Incorrect lazy initialization of static field,BUG,MAJOR,READY,multi-threading
findbugs:SSD_DO_NOT_USE_INSTANCE_LOCK_ON_SHARED_STATIC_DATA,Multi-threading - Instance level lock was used on a shared static data,BUG,MAJOR,READY,multi-threading
findbugs:RU_INVOKE_RUN,Multi-threading - Invokes run on a thread (did you mean to start it instead?),BUG,MAJOR,READY,multi-threading
findbugs:SWL_SLEEP_WITH_LOCK_HELD,Multi-threading - Method calls Thread.sleep() with a lock held,BUG,MAJOR,READY,multi-threading
findbugs:UL_UNRELEASED_LOCK_EXCEPTION_PATH,Multi-threading - Method does not release lock on all exception paths,BUG,MAJOR,READY,multi-threading
findbugs:UL_UNRELEASED_LOCK,Multi-threading - Method does not release lock on all paths,BUG,MAJOR,READY,multi-threading
findbugs:SP_SPIN_ON_FIELD,Multi-threading - Method spins on field,BUG,MAJOR,READY,multi-threading
findbugs:ML_SYNC_ON_UPDATED_FIELD,Multi-threading - Method synchronizes on an updated field,BUG,MAJOR,READY,multi-threading
findbugs:MWN_MISMATCHED_NOTIFY,Multi-threading - Mismatched notify(),BUG,MAJOR,READY,multi-threading
findbugs:MWN_MISMATCHED_WAIT,Multi-threading - Mismatched wait(),BUG,MAJOR,READY,multi-threading
findbugs:DM_MONITOR_WAIT_ON_CONDITION,Multi-threading - Monitor wait() called on Condition,BUG,MAJOR,READY,multi-threading
findbugs:MSF_MUTABLE_SERVLET_FIELD,Multi-threading - Mutable servlet field,BUG,MAJOR,READY,multi-threading
findbugs:NN_NAKED_NOTIFY,Multi-threading - Naked notify,BUG,MAJOR,READY,multi-threading
findbugs:DC_DOUBLECHECK,Multi-threading - Possible double-check of field,BUG,MAJOR,READY,multi-threading
findbugs:DC_PARTIALLY_CONSTRUCTED,Multi-threading - Possible exposure of partially initialized object,BUG,MAJOR,READY,multi-threading
findbugs:RV_RETURN_VALUE_OF_PUTIFABSENT_IGNORED,"Multi-threading - Return value of putIfAbsent ignored, value passed to putIfAbsent reused",BUG,MAJOR,READY,multi-threading
findbugs:AT_OPERATION_SEQUENCE_ON_CONCURRENT_ABSTRACTION,Multi-threading - Sequence of calls to concurrent abstraction may not be atomic,BUG,MAJOR,READY,multi-threading
findbugs:STCAL_STATIC_CALENDAR_INSTANCE,Multi-threading - Static Calendar field,BUG,MAJOR,READY,multi-threading
findbugs:STCAL_STATIC_SIMPLE_DATE_FORMAT_INSTANCE,Multi-threading - Static DateFormat,BUG,MAJOR,READY,multi-threading
findbugs:DL_SYNCHRONIZATION_ON_BOOLEAN,Multi-threading - Synchronization on Boolean,BUG,MAJOR,READY,multi-threading
findbugs:DL_SYNCHRONIZATION_ON_BOXED_PRIMITIVE,Multi-threading - Synchronization on boxed primitive,BUG,MAJOR,READY,multi-threading
findbugs:DL_SYNCHRONIZATION_ON_UNSHARED_BOXED_PRIMITIVE,Multi-threading - Synchronization on boxed primitive values,BUG,MAJOR,READY,multi-threading
findbugs:ML_SYNC_ON_FIELD_TO_GUARD_CHANGING_THAT_FIELD,Multi-threading - Synchronization on field in futile attempt to guard that field,BUG,MAJOR,READY,multi-threading
findbugs:WL_USING_GETCLASS_RATHER_THAN_CLASS_LITERAL,Multi-threading - Synchronization on getClass rather than class literal,BUG,MAJOR,READY,multi-threading
findbugs:DL_SYNCHRONIZATION_ON_SHARED_CONSTANT,Multi-threading - Synchronization on interned String,BUG,MAJOR,READY,multi-threading
findbugs:JLM_JSR166_LOCK_MONITORENTER,Multi-threading - Synchronization performed on Lock,BUG,MAJOR,READY,multi-threading
findbugs:JLM_JSR166_UTILCONCURRENT_MONITORENTER,Multi-threading - Synchronization performed on util.concurrent instance,BUG,MAJOR,READY,multi-threading
findbugs:NP_SYNC_AND_NULL_CHECK_FIELD,Multi-threading - Synchronize and null check on the same field.,BUG,MAJOR,READY,multi-threading
findbugs:UW_UNCOND_WAIT,Multi-threading - Unconditional wait,BUG,MAJOR,READY,multi-threading
findbugs:UG_SYNC_SET_UNSYNC_GET,"Multi-threading - Unsynchronized get method, synchronized set method",BUG,MAJOR,READY,multi-threading
findbugs:JML_JSR166_CALLING_WAIT_RATHER_THAN_AWAIT,Multi-threading - Using monitor style wait methods on util.concurrent abstraction,BUG,MAJOR,READY,multi-threading
findbugs:NO_NOTIFY_NOT_NOTIFYALL,Multi-threading - Using notify() rather than notifyAll(),BUG,MAJOR,READY,multi-threading
findbugs:WA_NOT_IN_LOOP,Multi-threading - Wait not in loop,BUG,MAJOR,READY,multi-threading
findbugs:TLW_TWO_LOCK_WAIT,Multi-threading - Wait with two locks held,BUG,MAJOR,READY,multi-threading
infer-java:NULL_DEREFERENCE,null dereference,BUG,CRITICAL,READY,
java:S5679,OpenSAML2 should be configured to prevent authentication bypass,VULNERABILITY,MAJOR,READY,owasp-a2;owasp-a9;spring
java:S5344,Passwords should not be stored in plain-text or with a fast hashing algorithm,VULNERABILITY,CRITICAL,READY,cwe;owasp-a2;owasp-a3;owasp-a6;sans-top25-porous;spring
findbugs:BX_UNBOXING_IMMEDIATELY_REBOXED,Performance - Boxed value is unboxed and then immediately reboxed,BUG,MAJOR,READY,performance
findbugs:DM_BOXED_PRIMITIVE_FOR_COMPARE,Performance - Boxing a primitive to compare,BUG,MAJOR,READY,performance
findbugs:DM_BOXED_PRIMITIVE_FOR_PARSING,Performance - Boxing/unboxing to parse a primitive,BUG,MAJOR,READY,performance
findbugs:SIC_INNER_SHOULD_BE_STATIC_ANON,Performance - Could be refactored into a named static inner class,BUG,MAJOR,READY,performance
findbugs:SIC_INNER_SHOULD_BE_STATIC_NEEDS_THIS,Performance - Could be refactored into a static inner class,BUG,MAJOR,READY,performance
findbugs:DM_GC,Performance - Explicit garbage collection; extremely dubious except in benchmarking code,BUG,MAJOR,READY,performance
findbugs:HSC_HUGE_SHARED_STRING_CONSTANT,Performance - Huge string constants is duplicated across multiple class files,BUG,MAJOR,READY,performance
findbugs:WMI_WRONG_MAP_ITERATOR,Performance - Inefficient use of keySet iterator instead of entrySet iterator,BUG,MAJOR,READY,performance
findbugs:IIO_INEFFICIENT_INDEX_OF,Performance - Inefficient use of String.indexOf(String),BUG,MAJOR,READY,performance
findbugs:IIO_INEFFICIENT_LAST_INDEX_OF,Performance - Inefficient use of String.lastIndexOf(String),BUG,MAJOR,READY,performance
findbugs:DMI_COLLECTION_OF_URLS,Performance - Maps and sets of URLs can be performance hogs,BUG,MAJOR,READY,performance
findbugs:IMA_INEFFICIENT_MEMBER_ACCESS,Performance - Method accesses a private member variable of owning class,BUG,MAJOR,READY,performance
findbugs:DM_BOXED_PRIMITIVE_TOSTRING,Performance - Method allocates a boxed primitive just to call toString,BUG,MAJOR,READY,performance
findbugs:DM_NEW_FOR_GETCLASS,"Performance - Method allocates an object, only to get the class object",BUG,MAJOR,READY,performance
findbugs:IIL_PATTERN_COMPILE_IN_LOOP,Performance - Method calls Pattern.compile in a loop,BUG,MAJOR,READY,performance
findbugs:IIL_PREPARE_STATEMENT_IN_LOOP,Performance - Method calls prepareStatement in a loop,BUG,MAJOR,READY,performance
findbugs:UM_UNNECESSARY_MATH,Performance - Method calls static Math class method on a constant value,BUG,MAJOR,READY,performance
findbugs:IIL_PATTERN_COMPILE_IN_LOOP_INDIRECT,Performance - Method compiles the regular expression in a loop,BUG,MAJOR,READY,performance
findbugs:SBSC_USE_STRINGBUFFER_CONCATENATION,Performance - Method concatenates strings using + in a loop,BUG,MAJOR,READY,performance
findbugs:DM_BOOLEAN_CTOR,Performance - Method invokes inefficient Boolean constructor; use Boolean.valueOf(...) instead,BUG,MAJOR,READY,performance
findbugs:DM_FP_NUMBER_CTOR,Performance - Method invokes inefficient floating-point Number constructor; use static valueOf instead,BUG,MAJOR,READY,performance
findbugs:DM_STRING_VOID_CTOR,Performance - Method invokes inefficient new String() constructor,BUG,MAJOR,READY,performance
findbugs:DM_STRING_CTOR,Performance - Method invokes inefficient new String(String) constructor,BUG,MAJOR,READY,performance
findbugs:DM_NUMBER_CTOR,Performance - Method invokes inefficient Number constructor; use static valueOf instead,BUG,MAJOR,READY,performance
findbugs:DM_STRING_TOSTRING,Performance - Method invokes toString() method on a String,BUG,MAJOR,READY,performance
findbugs:ITA_INEFFICIENT_TO_ARRAY,Performance - Method uses toArray() with zero-length array argument,BUG,MAJOR,READY,performance
findbugs:IIL_ELEMENTS_GET_LENGTH_IN_LOOP,Performance - NodeList.getLength() called in a loop,BUG,MAJOR,READY,performance
findbugs:BX_BOXING_IMMEDIATELY_UNBOXED,Performance - Primitive value is boxed and then immediately unboxed,BUG,MAJOR,READY,performance
findbugs:BX_BOXING_IMMEDIATELY_UNBOXED_TO_PERFORM_COERCION,Performance - Primitive value is boxed then unboxed to perform primitive coercion,BUG,MAJOR,READY,performance
findbugs:BX_UNBOXED_AND_COERCED_FOR_TERNARY_OPERATOR,Performance - Primitive value is unboxed and coerced for ternary operator,BUG,MAJOR,READY,performance
findbugs:UPM_UNCALLED_PRIVATE_METHOD,Performance - Private method is never called,BUG,MAJOR,READY,performance
findbugs:SIC_INNER_SHOULD_BE_STATIC,Performance - Should be a static inner class,BUG,MAJOR,READY,performance
findbugs:DMI_BLOCKING_METHODS_ON_URL,Performance - The equals and hashCode methods of URL are blocking,BUG,MAJOR,READY,performance
findbugs:URF_UNREAD_FIELD,Performance - Unread field,BUG,MAJOR,READY,performance
findbugs:SS_SHOULD_BE_STATIC,Performance - Unread field: should this field be static?,BUG,MAJOR,READY,performance
findbugs:UUF_UNUSED_FIELD,Performance - Unused field,BUG,MAJOR,READY,performance
findbugs:DM_NEXTINT_VIA_NEXTDOUBLE,Performance - Use the nextInt method of Random rather than nextDouble to generate a random integer,BUG,MAJOR,READY,performance
findbugs:SQL_PREPARED_STATEMENT_GENERATED_FROM_NONCONSTANT_STRING,Security - A prepared statement is generated from a nonconstant String,VULNERABILITY,MAJOR,READY,injection;owasp-a1
findbugs:PT_ABSOLUTE_PATH_TRAVERSAL,Security - Absolute path traversal in servlet,VULNERABILITY,MAJOR,READY,cwe
findsecbugs:PADDING_ORACLE,Security - Cipher is susceptible to Padding Oracle,VULNERABILITY,MAJOR,READY,cryptography;cwe;owasp-a6
findsecbugs:CIPHER_INTEGRITY,Security - Cipher with no integrity,VULNERABILITY,MAJOR,READY,cryptography;cwe;owasp-a6
findsecbugs:HTTPONLY_COOKIE,Security - Cookie without the HttpOnly flag,VULNERABILITY,MAJOR,READY,
findsecbugs:INSECURE_COOKIE,Security - Cookie without the secure flag,VULNERABILITY,MAJOR,READY,cwe
findsecbugs:DEFAULT_HTTP_CLIENT,Security - DefaultHttpClient with default constructor is not compatible with TLS 1.2,VULNERABILITY,MAJOR,READY,cryptography;owasp-a6
findsecbugs:DES_USAGE,Security - DES is insecure,VULNERABILITY,MAJOR,READY,cryptography;cwe;owasp-a6
findsecbugs:TDES_USAGE,Security - DESede is insecure,VULNERABILITY,MAJOR,READY,cryptography;cwe;owasp-a6
findsecbugs:ECB_MODE,Security - ECB mode is insecure,VULNERABILITY,MAJOR,READY,cryptography;owasp-a6
findbugs:DMI_EMPTY_DB_PASSWORD,Security - Empty database password,VULNERABILITY,MAJOR,READY,
findsecbugs:ANDROID_EXTERNAL_FILE_ACCESS,Security - External file access (Android),VULNERABILITY,MAJOR,READY,android;cwe
findsecbugs:HARD_CODE_KEY,Security - Hard coded key,VULNERABILITY,MAJOR,READY,cwe
findsecbugs:HARD_CODE_PASSWORD,Security - Hard coded password,VULNERABILITY,MAJOR,READY,cryptography;cwe;owasp-a6
findbugs:DMI_CONSTANT_DB_PASSWORD,Security - Hardcoded constant database password,VULNERABILITY,MAJOR,READY,
findsecbugs:WEAK_HOSTNAME_VERIFIER,Security - HostnameVerifier that accept any signed certificates,VULNERABILITY,MAJOR,READY,cryptography;cwe;owasp-a6;wasc
findbugs:HRS_REQUEST_PARAMETER_TO_COOKIE,Security - HTTP cookie formed from untrusted input,VULNERABILITY,MAJOR,READY,
findsecbugs:HTTP_PARAMETER_POLLUTION,Security - HTTP Parameter Pollution,VULNERABILITY,MAJOR,READY,
findbugs:HRS_REQUEST_PARAMETER_TO_HTTP_HEADER,Security - HTTP Response splitting vulnerability,VULNERABILITY,MAJOR,READY,
findsecbugs:BEAN_PROPERTY_INJECTION,Security - JavaBeans Property Injection,VULNERABILITY,CRITICAL,READY,cwe
findsecbugs:WEAK_MESSAGE_DIGEST_MD5,"Security - MD2, MD4 and MD5 are weak hash functions",VULNERABILITY,MAJOR,READY,cryptography;cwe;owasp-a6
findbugs:SQL_NONCONSTANT_STRING_PASSED_TO_EXECUTE,Security - Nonconstant string passed to execute or addBatch method on an SQL statement,VULNERABILITY,MAJOR,READY,injection;owasp-a1
findsecbugs:PERMISSIVE_CORS,Security - Overly permissive CORS policy,VULNERABILITY,MAJOR,READY,
findsecbugs:COOKIE_PERSISTENT,Security - Persistent Cookie Usage,VULNERABILITY,MAJOR,READY,cwe
findsecbugs:COMMAND_INJECTION,Security - Potential Command Injection,VULNERABILITY,CRITICAL,READY,cwe;injection;owasp-a1
findsecbugs:HTTP_RESPONSE_SPLITTING,Security - Potential HTTP Response Splitting,VULNERABILITY,INFO,READY,cwe;injection;owasp-a1
findsecbugs:SQL_INJECTION_JDBC,Security - Potential JDBC Injection,VULNERABILITY,CRITICAL,READY,cwe;injection;owasp-a1;wasc
findsecbugs:SQL_INJECTION_SPRING_JDBC,Security - Potential JDBC Injection (Spring JDBC),VULNERABILITY,CRITICAL,READY,cwe;injection;owasp-a1;wasc
findsecbugs:PREDICTABLE_RANDOM,Security - Predictable pseudorandom number generator,VULNERABILITY,MAJOR,READY,cwe
findbugs:PT_RELATIVE_PATH_TRAVERSAL,Security - Relative path traversal in servlet,VULNERABILITY,MAJOR,READY,cwe
findbugs:XSS_REQUEST_PARAMETER_TO_SERVLET_WRITER,Security - Servlet reflected cross site scripting vulnerability,VULNERABILITY,MAJOR,READY,owasp-a3
findbugs:XSS_REQUEST_PARAMETER_TO_SEND_ERROR,Security - Servlet reflected cross site scripting vulnerability in error page,VULNERABILITY,MAJOR,READY,owasp-a3
findsecbugs:SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING,Security - Spring CSRF unrestricted RequestMapping,VULNERABILITY,MAJOR,READY,cwe
java:S4830,Server certificates should be verified during SSL/TLS connections,VULNERABILITY,CRITICAL,READY,cert;cwe;owasp-a3;owasp-a6;privacy;ssl
java:S5527,Server hostnames should be verified during SSL/TLS connections,VULNERABILITY,CRITICAL,READY,cwe;owasp-a3;owasp-a6;privacy;ssl
findbugs:USM_USELESS_ABSTRACT_METHOD,Style - Abstract Method is already defined in implemented interface,CODE_SMELL,INFO,READY,style
findbugs:DMI_UNSUPPORTED_METHOD,Style - Call to unsupported method,CODE_SMELL,INFO,READY,style
findbugs:IM_BAD_CHECK_FOR_ODD,Style - Check for oddness that won't work for negative numbers,CODE_SMELL,INFO,READY,style
findbugs:EQ_DOESNT_OVERRIDE_EQUALS,Style - Class doesn't override equals in superclass,CODE_SMELL,INFO,READY,style
findbugs:PS_PUBLIC_SEMAPHORES,Style - Class exposes synchronization and semaphores in its public interface,CODE_SMELL,INFO,READY,style
findbugs:MTIA_SUSPECT_SERVLET_INSTANCE_FIELD,Style - Class extends Servlet class and uses instance variables,CODE_SMELL,INFO,READY,style
findbugs:MTIA_SUSPECT_STRUTS_INSTANCE_FIELD,Style - Class extends Struts Action class and uses instance variables,CODE_SMELL,INFO,READY,style
findbugs:RI_REDUNDANT_INTERFACES,Style - Class implements same interface as superclass,CODE_SMELL,INFO,READY,style
findbugs:CI_CONFUSED_INHERITANCE,Style - Class is final but declares protected field,CODE_SMELL,INFO,READY,style
findbugs:DMI_HARDCODED_ABSOLUTE_FILENAME,Style - Code contains a hard coded reference to an absolute pathname,CODE_SMELL,INFO,READY,style
findbugs:QF_QUESTIONABLE_FOR_LOOP,"Style - Complicated, subtle or wrong increment in for-loop",CODE_SMELL,INFO,READY,style
findbugs:IM_AVERAGE_COMPUTATION_COULD_OVERFLOW,Style - Computation of average could overflow,CODE_SMELL,INFO,READY,style
findbugs:UC_USELESS_CONDITION,Style - Condition has no effect,CODE_SMELL,INFO,READY,style
findbugs:UC_USELESS_CONDITION_TYPE,Style - Condition has no effect due to the variable type,CODE_SMELL,INFO,READY,style
findbugs:PZLA_PREFER_ZERO_LENGTH_ARRAYS,Style - Consider returning a zero length array rather than null,CODE_SMELL,INFO,READY,style
findbugs:CAA_COVARIANT_ARRAY_FIELD,Style - Covariant array assignment to a field,CODE_SMELL,INFO,READY,style
findbugs:CAA_COVARIANT_ARRAY_LOCAL,Style - Covariant array assignment to a local variable,CODE_SMELL,INFO,READY,style
findbugs:CAA_COVARIANT_ARRAY_RETURN,Style - Covariant array is returned from the method,CODE_SMELL,INFO,READY,style
findbugs:DLS_DEAD_LOCAL_STORE_OF_NULL,Style - Dead store of null to local variable,CODE_SMELL,INFO,READY,style
findbugs:DLS_DEAD_LOCAL_STORE,Style - Dead store to local variable,CODE_SMELL,INFO,READY,style
findbugs:DLS_DEAD_LOCAL_STORE_SHADOWS_FIELD,Style - Dead store to local variable that shadows field,CODE_SMELL,INFO,READY,style
findbugs:NP_DEREFERENCE_OF_READLINE_VALUE,Style - Dereference of the result of readLine() without nullcheck,CODE_SMELL,INFO,READY,style
findbugs:SA_FIELD_DOUBLE_ASSIGNMENT,Style - Double assignment of field,CODE_SMELL,INFO,READY,style
findbugs:SA_LOCAL_DOUBLE_ASSIGNMENT,Style - Double assignment of local variable,CODE_SMELL,INFO,READY,style
findbugs:REC_CATCH_EXCEPTION,Style - Exception is caught when Exception is not thrown,CODE_SMELL,INFO,READY,style
findbugs:UWF_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR,Style - Field not initialized in constructor but dereferenced without null check,CODE_SMELL,INFO,READY,style
findbugs:NP_IMMEDIATE_DEREFERENCE_OF_READLINE,Style - Immediate dereference of the result of readLine(),CODE_SMELL,INFO,READY,style
findbugs:IC_INIT_CIRCULARITY,Style - Initialization circularity,CODE_SMELL,INFO,READY,style
findbugs:BC_VACUOUS_INSTANCEOF,Style - instanceof will always return true,CODE_SMELL,INFO,READY,style
findbugs:INT_BAD_REM_BY_1,Style - Integer remainder modulo 1,CODE_SMELL,INFO,READY,style
findbugs:ICAST_IDIV_CAST_TO_DOUBLE,Style - Integral division result cast to double or float,CODE_SMELL,INFO,READY,style
findbugs:DMI_USELESS_SUBSTRING,"Style - Invocation of substring(0), which returns the original value",CODE_SMELL,INFO,READY,style
findbugs:NP_LOAD_OF_KNOWN_NULL_VALUE,Style - Load of known null value,CODE_SMELL,INFO,READY,style
findbugs:RV_CHECK_FOR_POSITIVE_INDEXOF,Style - Method checks to see if result of String.indexOf is positive,CODE_SMELL,INFO,READY,style
findbugs:XFB_XML_FACTORY_BYPASS,Style - Method directly allocates a specific implementation of xml interfaces,CODE_SMELL,INFO,READY,style
findbugs:RV_DONT_JUST_NULL_CHECK_READLINE,Style - Method discards result of readLine after checking if it is non-null,CODE_SMELL,INFO,READY,style
findbugs:RV_RETURN_VALUE_IGNORED_INFERRED,"Style - Method ignores return value, is this OK?",CODE_SMELL,INFO,READY,style
findbugs:NP_METHOD_RETURN_RELAXING_ANNOTATION,Style - Method relaxes nullness annotation on return value,CODE_SMELL,INFO,READY,style
findbugs:USM_USELESS_SUBCLASS_METHOD,Style - Method superfluously delegates to parent class method,CODE_SMELL,INFO,READY,style
findbugs:NP_METHOD_PARAMETER_TIGHTENS_ANNOTATION,Style - Method tightens nullness annotation on parameter,CODE_SMELL,INFO,READY,style
findbugs:NP_METHOD_PARAMETER_RELAXING_ANNOTATION,Style - Method tightens nullness annotation on parameter,CODE_SMELL,INFO,READY,style
findbugs:DB_DUPLICATE_BRANCHES,Style - Method uses the same code for two branches,CODE_SMELL,INFO,READY,style
findbugs:DB_DUPLICATE_SWITCH_CLAUSES,Style - Method uses the same code for two switch clauses,CODE_SMELL,INFO,READY,style
findbugs:DMI_NONSERIALIZABLE_OBJECT_WRITTEN,Style - Non serializable object written to ObjectOutput,CODE_SMELL,INFO,READY,style
findbugs:DCN_NULLPOINTER_EXCEPTION,Style - NullPointerException caught,CODE_SMELL,INFO,READY,style
findbugs:NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE,Style - Parameter must be non-null but is marked as nullable,CODE_SMELL,INFO,READY,style
findbugs:NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE,Style - Possible null pointer dereference due to return value of called method,CODE_SMELL,INFO,READY,style
findbugs:NP_NULL_ON_SOME_PATH_MIGHT_BE_INFEASIBLE,Style - Possible null pointer dereference on branch that might be infeasible,CODE_SMELL,INFO,READY,style
findbugs:IA_AMBIGUOUS_INVOCATION_OF_INHERITED_OR_OUTER_METHOD,Style - Potentially ambiguous invocation of either an inherited or outer method,CODE_SMELL,INFO,READY,style
findbugs:NS_DANGEROUS_NON_SHORT_CIRCUIT,Style - Potentially dangerous use of non-short-circuit logic,CODE_SMELL,INFO,READY,style
findbugs:SE_PRIVATE_READ_RESOLVE_NOT_INHERITED,Style - Private readResolve method not inherited by subclasses,CODE_SMELL,INFO,READY,style
findbugs:BC_BAD_CAST_TO_ABSTRACT_COLLECTION,Style - Questionable cast to abstract collection,CODE_SMELL,INFO,READY,style
findbugs:BC_BAD_CAST_TO_CONCRETE_COLLECTION,Style - Questionable cast to concrete collection,CODE_SMELL,INFO,READY,style
findbugs:NS_NON_SHORT_CIRCUIT,Style - Questionable use of non-short-circuit logic,CODE_SMELL,INFO,READY,style
findbugs:NP_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD,Style - Read of unwritten public or protected field,CODE_SMELL,INFO,READY,style
findbugs:RCN_REDUNDANT_COMPARISON_OF_NULL_AND_NONNULL_VALUE,Style - Redundant comparison of non-null value to null,CODE_SMELL,INFO,READY,style
findbugs:RCN_REDUNDANT_COMPARISON_TWO_NULL_VALUES,Style - Redundant comparison of two null values,CODE_SMELL,INFO,READY,style
findbugs:RCN_REDUNDANT_NULLCHECK_OF_NONNULL_VALUE,Style - Redundant nullcheck of value known to be non-null,CODE_SMELL,INFO,READY,style
findbugs:RCN_REDUNDANT_NULLCHECK_OF_NULL_VALUE,Style - Redundant nullcheck of value known to be null,CODE_SMELL,INFO,READY,style
findbugs:RV_REM_OF_RANDOM_INT,Style - Remainder of 32-bit signed random integer,CODE_SMELL,INFO,READY,style
findbugs:RV_REM_OF_HASHCODE,Style - Remainder of hashCode could be negative,CODE_SMELL,INFO,READY,style
findbugs:ICAST_INTEGER_MULTIPLY_CAST_TO_LONG,Style - Result of integer multiplication cast to long,CODE_SMELL,INFO,READY,style
findbugs:RV_RETURN_VALUE_IGNORED_NO_SIDE_EFFECT,Style - Return value of method without side effect is ignored,CODE_SMELL,INFO,READY,style
findbugs:SA_LOCAL_SELF_ASSIGNMENT,Style - Self assignment of local variable,CODE_SMELL,INFO,READY,style
findbugs:SF_SWITCH_NO_DEFAULT,Style - Switch statement found where default case is missing,CODE_SMELL,INFO,READY,style
findbugs:SF_SWITCH_FALLTHROUGH,Style - Switch statement found where one case falls through to the next case,CODE_SMELL,INFO,READY,style
findbugs:CD_CIRCULAR_DEPENDENCY,Style - Test for circular dependencies among classes,CODE_SMELL,INFO,READY,style
findbugs:FE_FLOATING_POINT_EQUALITY,Style - Test for floating point equality,CODE_SMELL,INFO,READY,style
findbugs:DMI_THREAD_PASSED_WHERE_RUNNABLE_EXPECTED,Style - Thread passed where Runnable expected,CODE_SMELL,INFO,READY,style
findbugs:SE_TRANSIENT_FIELD_OF_NONSERIALIZABLE_CLASS,Style - Transient field of class that isn't Serializable.,CODE_SMELL,INFO,READY,style
findbugs:BC_UNCONFIRMED_CAST,Style - Unchecked/unconfirmed cast,CODE_SMELL,INFO,READY,style
findbugs:BC_UNCONFIRMED_CAST_OF_RETURN_VALUE,Style - Unchecked/unconfirmed cast of return value from method,CODE_SMELL,INFO,READY,style
findbugs:URF_UNREAD_PUBLIC_OR_PROTECTED_FIELD,Style - Unread public/protected field,CODE_SMELL,INFO,READY,style
findbugs:ICAST_QUESTIONABLE_UNSIGNED_RIGHT_SHIFT,Style - Unsigned right shift cast to short/byte,CODE_SMELL,INFO,READY,style
findbugs:UUF_UNUSED_PUBLIC_OR_PROTECTED_FIELD,Style - Unused public or protected field,CODE_SMELL,INFO,READY,style
findbugs:EQ_UNUSUAL,Style - Unusual equals method,CODE_SMELL,INFO,READY,style
findbugs:UWF_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD,Style - Unwritten public or protected field,CODE_SMELL,INFO,READY,style
findbugs:DLS_DEAD_LOCAL_STORE_IN_RETURN,Style - Useless assignment in return statement,CODE_SMELL,INFO,READY,style
findbugs:UCF_USELESS_CONTROL_FLOW,Style - Useless control flow,CODE_SMELL,INFO,READY,style
findbugs:UCF_USELESS_CONTROL_FLOW_NEXT_LINE,Style - Useless control flow to next line,CODE_SMELL,INFO,READY,style
findbugs:UC_USELESS_VOID_METHOD,Style - Useless non-empty void method,CODE_SMELL,INFO,READY,style
findbugs:UC_USELESS_OBJECT,Style - Useless object created,CODE_SMELL,INFO,READY,style
findbugs:UC_USELESS_OBJECT_STACK,Style - Useless object created on stack,CODE_SMELL,INFO,READY,style
findbugs:INT_VACUOUS_BIT_OPERATION,Style - Vacuous bit mask operation on integer value,CODE_SMELL,INFO,READY,style
