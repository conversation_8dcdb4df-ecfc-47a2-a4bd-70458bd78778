# ======== zt-py310-mantis ========
# Program:
# 	CentOS7.9.2009 add 「uv-3.10.11」 support.
# Build:
#	docker build -t mantis:2.112.1 .
# Tags:
#	docker tag mantis:2.112.1 harbor-test.inner.howbuy.com/pa/mantis
#	docker tag mantis:2.112.1 harbor-test.inner.howbuy.com/pa/mantis:2.112.1
# Run:
#	docker run -itd --name zt-mantis mantis:2.112.1
#	docker run -itd --name zt-mantis -v /data/ztws/zt-mantis/spider:/data/app/mantis/ mantis:2.112.1
#	docker run -itd --name zt-mantis -p 9000:9000 -v /data/ztws/zt-mantis/mantis:/data/app/mantis/ mantis:2.112.1
#	docker run -itd --name zt-mantis -p 9000:9000 -v /data/ztws/zt-mantis/mantis:/data/app/mantis/ -v /data/ztws/zt-mantis/mantis_logs:/data/logs/mantis/ mantis:2.112.1
# Cmd:
#	/data/app/.venv/bin/python3 manage.py runserver 0.0.0.0:9000
# History:
# 	2025-07-24	Zt	First release with uv 0.7.21
# ================================
FROM zt-py310:1.0.0

# 设置作者信息
LABEL maintainer="zt<<EMAIL>>"
LABEL author="zt<<EMAIL>>"

# 设置环境变量

# 设置工作目录
WORKDIR /data/app/mantis/

# 安装系统依赖
RUN yum install tree zip unzip wget sshpass -y

# 命令运行：
# 1、创建目录：
# RUN mkdir -p /data/logs/mantis/audit

# 2、资源复制
COPY mantis/.python-version /data/app/
COPY mantis/pyproject.toml /data/app/
COPY mantis/uv.lock /data/app/

# 3、uv同步（环境部署）：
RUN uv sync

# 4、额外操作
# 4-1、python3.x命令
#RUN ln -sf /data/app/.venv/bin/python3 /usr/local/bin/python3.x

# 使用 ENTRYPOINT
ENTRYPOINT ["/bin/bash", "-c", "/data/app/.venv/bin/python3 manage.py runserver 0.0.0.0:9000 --noreload > /data/logs/mantis/mantis-web.log 2>&1"]