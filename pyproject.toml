[project]
name = "mantis"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10,<3.11"
dependencies = [
    "beautifulsoup4~=4.12.2",
    "chinese_calendar>=1.10.0",
    "coreapi==2.3.3",
    "coreschema==0.0.4",
    "croniter==3.0.3",
    "cryptography==3.4.8",
    "diskcache==5.6.3",
    "django==4.1.13",
    "django-cors-headers==3.14.0",
    "django-python3-ldap==0.13.1",
    "djangorestframework==3.15.0",
    "djangorestframework-simplejwt==5.3.0",
    "mysql-connector-python~=8.0.33",
    "mysqlclient==2.1.0",
    "nacos-sdk-python==0.1.6",
    "numpy==1.26.0",
    "openai==1.59.4",
    "openpyxl==3.1.5",
    "pandas==2.2.3",
    "paramiko==2.7.2",
    "peewee==3.14.9",
    "pyecharts==1.9.1",
    "pymysql==1.0.2",
    "python-gitlab==2.5.0",
    "python-jenkins==1.8.0",
    "python-sonarqube-api>=1.3.6,<2.0.0",
    "rocketmq==0.4.4",
    "spider-common-utils>=0.1.25",
    "sqlalchemy==1.4.44",
    "treelib==1.5.5",
    "websocket-client~=1.6.1",
    "xlrd==1.2.0",
    "xlwt==1.3.0",
    "django-mcp-server==0.5.3",
]

[tool.uv.sources]
python-sonarqube-api = { index = "howbuy" }
spider-common-utils = { index = "howbuy" }

[[tool.uv.index]]
name = "howbuy"
url = "http://pypi.howbuy.pa/simple"

[dependency-groups]
dev = [
    "nexus3-cli~=4.1.8",
    "pylint>=3.3.6",
]
