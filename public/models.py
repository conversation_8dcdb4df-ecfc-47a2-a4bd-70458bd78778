from django.db import models
from django.utils import timezone


class BaseModels(models.Model):
    create_user = models.CharField(verbose_name='创建人', max_length=20)
    create_time = models.DateTimeField(verbose_name='创建时间', default=timezone.now)
    update_user = models.CharField(verbose_name='修改人', max_length=20)
    update_time = models.DateTimeField(verbose_name='修改时间', default=timezone.now)

    class Meta:
        abstract = True
