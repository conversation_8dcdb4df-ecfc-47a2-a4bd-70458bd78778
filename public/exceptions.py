# _*_ coding: utf-8 _*_
from __future__ import absolute_import
from rest_framework import exceptions
from rest_framework import status
from django.utils.translation import gettext_lazy as _


class SpiderException(exceptions.APIException):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    default_detail = _('spider server error occurred.')
    default_code = 'error'


class InvalidUsernameOrPassword(SpiderException):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    default_detail = _('Username Or Password Error')
    default_code = '4100'


class BadRequest(exceptions.APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = _('Bad Request Error')
    default_code = 'bad request'


class ConnectionError(exceptions.APIException):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    default_detail = _('Connection refused.')
    default_code = 'error'


class UnknownException(exceptions.APIException):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    default_detail = _('Unknown exception occurred.')
    default_code = 'error'

class InternalException(exceptions.APIException):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    default_detail = _('Internal Server Error.')
    default_code = 'error'

class SSHCmdException(exceptions.APIException):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    default_detail = _('Execute Remote Command Error.')
    default_code = 'error'
