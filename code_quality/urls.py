from django.urls import path, include
from code_quality import views, sonar_views
from rest_framework.routers import DefaultRouter

router = DefaultRouter()

router.register('analysis_ccn_report', views.AnalysisCcn, basename="analysis_ccn_report")
router.register('analysis_p3c_report', views.AnalysisP3c, basename="analysis_p3c_report")
router.register('ccn_entrance_guard', views.CcnEntranceGuard, basename="ccn_entrance_guard")
router.register('p3c_entrance_guard', views.P3cEntranceGuard, basename="p3c_entrance_guard")
router.register('analysis_sonar_report', sonar_views.AnalysisSonarReport, basename="analysis_sonar_report")
router.register('sonar_entrance_guard', sonar_views.SonarGuard, basename="sonar_entrance_guard")

urlpatterns = [
    path("", include(router.urls))
]