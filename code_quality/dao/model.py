from sqlalchemy import Column, Integer, String, DateTime, JSO<PERSON>
from mantis.pool import Base
from django.db import models


class CodeQualityDetailDataModel(models.Model):
    c_id = models.IntegerField()
    c_name = models.CharField(max_length=64)
    detail = models.JSONField()
    update_date = models.DateTimeField()

    class Meta:
        db_table = 'code_quality_detail_data'
        verbose_name = '质量明细数据'


class CodeQualityCcnDetailInfoService(models.Model):
    # id = models.IntegerField(verbose_name='id', blank=False, null=False)
    app_name = models.CharField(max_length=50, verbose_name='应用名')
    iteration_id = models.CharField(max_length=50, verbose_name='迭代id')
    report_url = models.CharField(max_length=256, verbose_name='报告地址')
    lib_md5 = models.CharField(max_length=256, verbose_name='制品md5')
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()

    class Meta:
        db_table = 'code_quality_ccn_record_info'
        verbose_name = 'ccn报告信息表'


class CodeQualityCcnIndexInfoService(models.Model):
    # id = models.IntegerField(verbose_name='ccn_index_id', blank=False, null=False)
    li_id = models.IntegerField(verbose_name='ccn_detail_id', blank=True, null=True)
    custom_index = models.CharField(max_length=50, verbose_name='分位数')
    index_value = models.IntegerField(verbose_name='指标值', blank=True, null=True)
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()

    class Meta:
        db_table = 'code_quality_ccn_result_info'
        verbose_name = 'ccn明细表'


class CodeQualityP3cReportInfo(Base, object):
    __tablename__ = 'code_quality_p3c_record_info'

    id = Column(Integer(), primary_key=True, nullable=False)
    app_name = Column(String(50))
    iteration_id = Column(String(50))
    report_url = Column(String(256))
    lib_md5 = Column(String(256))
    create_time = Column(DateTime())
    update_time = Column(DateTime())


class CodeQualityP3cReportInfoService(models.Model):
    # id = models.IntegerField(verbose_name='id', blank=False, null=False)
    app_name = models.CharField(max_length=50, verbose_name='应用名')
    iteration_id = models.CharField(max_length=50, verbose_name='应用名')
    report_url = models.CharField(max_length=256, verbose_name='报告地址')
    lib_md5 = models.CharField(max_length=256, verbose_name='制品md5')
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()

    class Meta:
        db_table = 'code_quality_p3c_record_info'
        verbose_name = 'p3c报告信息表'


class CodeQualityP3cIndexInfoService(models.Model):
    # id = models.IntegerField(verbose_name='id', blank=False, null=False)
    p_id = models.IntegerField(verbose_name='p3c_detail_id', blank=True, null=True)
    rule_num_sum = models.IntegerField(verbose_name='规则数', blank=True, null=True)
    priority = models.IntegerField(verbose_name='问题优先级', blank=True, null=True)
    create_time = models.DateTimeField()
    update_time = models.DateTimeField()

    class Meta:
        db_table = 'code_quality_p3c_result_info'
        verbose_name = 'p3c指标信息表'


class CodeQualityRuleRelationInfo(Base, object):
    __tablename__ = 'code_quality_rule_relation_info'

    id = Column(Integer(), primary_key=True, nullable=False)
    rule_class = Column(String(50))
    rule_file = Column(String(50))


class CodeQualityRuleRelationInfoService(models.Model):
    # id = models.IntegerField(verbose_name='id', blank=False, null=False)
    rule_class = models.CharField(max_length=50, verbose_name='规则类')
    rule_file = models.CharField(max_length=50, verbose_name='规则文件')

    class Meta:
        db_table = 'code_quality_rule_relation_info'
        verbose_name = '规则映射表'


class CodeQualityScanLog(Base, object):
    __tablename__ = 'code_quality_scan_log'

    id = Column(Integer(), primary_key=True, nullable=False)
    app_name = Column(String(50))
    scan_type = Column(String(50))
    iteration_id = Column(String(50))
    scan_status = Column(String(50))
    scan_result = Column(String(256))
    scan_time = Column(DateTime())


class CodeQualityScanLogService(models.Model):
    # id = models.IntegerField(verbose_name='id', blank=False, null=False)
    app_name = models.CharField(max_length=50, verbose_name='应用名')
    scan_type = models.CharField(max_length=50, verbose_name='扫描类型')
    iteration_id = models.CharField(max_length=50, verbose_name='迭代id')
    scan_status = models.IntegerField(verbose_name='扫描状态', blank=True, null=True)
    scan_result = models.CharField(max_length=256, verbose_name='扫描结果')
    scan_time = models.DateTimeField()

    class Meta:
        db_table = 'code_quality_scan_log'
        verbose_name = '代码质量扫描表'
