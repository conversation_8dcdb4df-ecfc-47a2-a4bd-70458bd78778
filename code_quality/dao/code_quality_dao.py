from mantis.pool import instance_db_session
from code_quality.dao.model import CodeQualityCcnDetailInfoService, CodeQualityCcnIndexInfoService, \
    CodeQualityRuleRelationInfoService, CodeQualityP3cIndexInfoService, CodeQualityP3cReportInfoService, \
    CodeQualityScanLogService, CodeQualityDetailDataModel
import datetime


def get_ccn_detail_info(app_name, iteration_id):
    info_obj = CodeQualityCcnDetailInfoService.objects.filter(app_name=app_name, iteration_id=iteration_id)
    return info_obj


def inser_or_update_lizard_report_info(app_name, iteration_id, report_url, ccn_detail, ccn_cnt, lib_md5):
    create_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    update_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    lizard_session = instance_db_session()
    lizard_report_info = get_ccn_detail_info(app_name, iteration_id)

    try:
        if lizard_report_info:
            id = lizard_report_info[0].id

            CodeQualityDetailDataModel.objects.filter(c_id=id, c_name="ccn_detail").update(detail=ccn_detail,
                                                                                           update_date=update_time)
            CodeQualityDetailDataModel.objects.filter(c_id=id, c_name="ccn_cnt").update(detail=ccn_cnt,
                                                                                        update_date=update_time)

            CodeQualityCcnDetailInfoService.objects.filter(app_name=app_name, iteration_id=iteration_id).update(
                report_url=report_url, lib_md5=lib_md5, update_time=update_time)


        else:
            ccn_detail_obj = CodeQualityCcnDetailInfoService.objects.create(app_name=app_name,
                                                                            iteration_id=iteration_id,
                                                                            report_url=report_url,
                                                                            lib_md5=lib_md5,
                                                                            create_time=create_time,
                                                                            update_time=update_time)
            id = ccn_detail_obj.id

            CodeQualityDetailDataModel.objects.create(c_id=id, c_name="ccn_detail", detail=ccn_detail)
            CodeQualityDetailDataModel.objects.create(c_id=id, c_name="ccn_cnt", detail=ccn_cnt)

    except Exception as err:
        raise Exception(err)
    return id


def get_ccn_index_info(li_id, custom_index=95):
    ccn_index_obj = CodeQualityCcnIndexInfoService.objects.filter(li_id=li_id, custom_index=custom_index)
    return ccn_index_obj


def insert_code_quality_ccn_index_info(li_id, custom_index, index_value):
    create_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    update_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    li_obj = get_ccn_index_info(li_id, custom_index)
    try:
        if li_obj:
            id = li_obj[0].id
            CodeQualityCcnIndexInfoService.objects.filter(li_id=li_id).update(custom_index=custom_index,
                                                                              index_value=index_value,
                                                                              update_time=update_time)
        else:
            ccn_index_obj = CodeQualityCcnIndexInfoService.objects.create(li_id=li_id, custom_index=custom_index,
                                                                          index_value=index_value,
                                                                          create_time=create_time,
                                                                          update_time=update_time)
            id = ccn_index_obj.id
    except Exception as err:
        raise Exception(err)
    return id


def get_rule_class_info():
    code_p3c_rule = CodeQualityRuleRelationInfoService.objects.filter()
    lizard_report_info_dict = {}
    for item in code_p3c_rule:
        lizard_report_info_dict[item.rule_class] = item.rule_file
    return lizard_report_info_dict


def get_p3c_detail_info(app_name, iteration_id):
    lizard_report_info = CodeQualityP3cReportInfoService.objects.filter(app_name=app_name, iteration_id=iteration_id)
    return lizard_report_info


def inser_or_update_p3c_report_info(app_name, iteration_id, report_url, lib_md5):
    create_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    update_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    lizard_report_info = get_p3c_detail_info(app_name, iteration_id)
    try:
        if lizard_report_info:
            id = lizard_report_info[0].id
            CodeQualityP3cReportInfoService.objects.filter(app_name=app_name,
                                                           iteration_id=iteration_id). \
                update(report_url=report_url, lib_md5=lib_md5, update_time=update_time)
        else:
            insert_sql = CodeQualityP3cReportInfoService.objects.create(app_name=app_name, iteration_id=iteration_id,
                                                                        report_url=report_url, lib_md5=lib_md5,
                                                                        create_time=create_time,
                                                                        update_time=update_time)
            id = insert_sql.id
    except Exception as err:
        raise Exception(err)
    return id


def get_p3c_index_info(p_id):
    p3c_index_info = CodeQualityP3cIndexInfoService.objects.filter(p_id=p_id)
    lizard_report_info_dict = {}
    for item in p3c_index_info:
        lizard_report_info_dict[item.priority] = item
    return lizard_report_info_dict


def code_quality_p3c_index_info(p_id, final_detail, count_data):
    create_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    update_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    lizard_report_info = get_p3c_index_info(p_id)
    try:
        for item in count_data:
            if int(item) in lizard_report_info:
                id = lizard_report_info[int(item)].id
                CodeQualityP3cIndexInfoService.objects.filter(p_id=p_id, priority=item).update(
                    rule_num_sum=len(count_data[item]), update_time=update_time)
                CodeQualityDetailDataModel.objects.filter(c_id=p_id,
                                                          c_name="p3c_priority_{}_detail".format(item)).update(
                    detail=final_detail[item], update_date=update_time)
                CodeQualityDetailDataModel.objects.filter(c_id=p_id, c_name="p3c_priority_{}_num".format(item)).update(
                    detail=count_data[item], update_date=update_time)
            else:
                insert_sql = CodeQualityP3cIndexInfoService.objects.create(p_id=p_id, priority=item,
                                                                           create_time=create_time,
                                                                           update_time=update_time,
                                                                           rule_num_sum=len(count_data[item]))

                id = insert_sql.id

                CodeQualityDetailDataModel.objects.create(c_id=p_id, c_name="p3c_priority_{}_detail".format(item),
                                                          detail=final_detail[item])
                CodeQualityDetailDataModel.objects.create(c_id=p_id, c_name="p3c_priority_{}_num".format(item),
                                                          detail=count_data[item])
    except Exception as err:
        raise Exception(err)
    return id


def ccn_last_archive_index_info(li_id):
    lizard_report_info = CodeQualityCcnIndexInfoService.objects.filter(li_id=li_id)
    return lizard_report_info[0]


def record_code_scan_info(app_name, iteration_id, scan_result, scan_status, scan_type):
    scan_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    insert_sql = CodeQualityScanLogService.objects.create(app_name=app_name, iteration_id=iteration_id,
                                                          scan_result=scan_result, scan_status=scan_status,
                                                          scan_type=scan_type,
                                                          scan_time=scan_time)
    return insert_sql.id
