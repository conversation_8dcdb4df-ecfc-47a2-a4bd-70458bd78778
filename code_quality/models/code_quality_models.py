from django.db import models
from public.models import BaseModels


class CodeQualitySonarRecordModel(BaseModels):
    app_name = models.CharField(max_length=64, verbose_name='应用名')
    br_name = models.CharField(max_length=64, verbose_name='分支名')
    iteration_id = models.CharField(max_length=64, verbose_name='迭代id')
    report_url = models.CharField(max_length=256, verbose_name='报告地址')
    task_id = models.CharField(max_length=64, verbose_name='制品md5')

    class Meta:
        db_table = 'code_quality_sonar_record_info'
        verbose_name = 'ccn报告信息表'


class CodeQualitySonarResultModel(BaseModels):
    p_id = models.IntegerField(verbose_name='record id')
    measure = models.CharField(max_length=64, verbose_name='指标')
    segment = models.CharField(max_length=64, verbose_name='细分指标')
    value = models.IntegerField(verbose_name='指标值')

    class Meta:
        db_table = 'code_quality_sonar_result_info'
        verbose_name = 'sonar 扫描结果'


class GuardRuleModel(BaseModels):
    measure = models.CharField(max_length=64, verbose_name='指标')
    segment = models.CharField(max_length=64, verbose_name='细分指标')

    class Meta:
        db_table = 'code_quality_guard_rule'
        verbose_name = '门禁规则'
