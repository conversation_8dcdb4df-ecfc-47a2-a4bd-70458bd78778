from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from mantis.settings import <PERSON><PERSON><PERSON><PERSON>ult, CODE_SCAN, logger
import datetime
from code_quality.utils.sonar_client import SonarClient
from code_quality.models.code_quality_models import CodeQualitySonarRecordModel, CodeQualitySonarResultModel
from code_quality.utils.sonar_guard import SonarGuard


class AnalysisSonarReport(ViewSet):
    __sonar_client = SonarClient()
    authentication_classes = []

    def create(self, request):
        app_name = request.data['app_name']
        iteration_id = request.data['iteration_id']
        br_name = request.data['br_name']
        report_url = request.data['report_url']
        task_id = request.data['task_id']

        obj, created = CodeQualitySonarRecordModel.objects.update_or_create(defaults={'task_id': task_id,
                                                                                      'report_url': report_url,
                                                                                      'update_time': datetime.datetime.now()},
                                                                            iteration_id=iteration_id,
                                                                            app_name=app_name,
                                                                            br_name=br_name)
        logger.info(obj.id)
        res = self.__sonar_client.get_analysis_result(app_name, br_name, task_id)
        logger.debug(res)
        data_list = []
        for row in res:
            logger.info(row.__dict__)
            data_list.append(row.__dict__)
            value = float(row.value)
            CodeQualitySonarResultModel.objects.update_or_create(defaults={'value': value,
                                                                           'update_time': datetime.datetime.now()},
                                                                 p_id=obj.id, measure=row.measure, segment=row.segment)

        return Response(data=ApiResult.success_dict(data=data_list, msg="分析成功"))


class SonarGuard(ViewSet):
    __sonar_guard = SonarGuard()
    authentication_classes = []

    def list(self, request):
        app_name = request.query_params.get('app_name')
        iteration_id = request.query_params.get('iteration_id')
        task_id = request.query_params.get('task_id')
        status, msg = self.__sonar_guard.main(iteration_id, app_name, task_id)
        if status:
            return Response(data=ApiResult.success_dict(data=msg, msg="分析成功"))
        else:
            return Response(data=ApiResult.failed_dict(data=msg, msg="分析成功"))
