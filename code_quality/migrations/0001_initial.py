# Generated by Django 3.2 on 2023-01-04 14:57

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CodeQualitySonarRecordModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_user', models.CharField(max_length=20, verbose_name='创建人')),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('update_user', models.CharField(max_length=20, verbose_name='修改人')),
                ('update_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='修改时间')),
                ('app_name', models.<PERSON>r<PERSON><PERSON>(max_length=64, verbose_name='应用名')),
                ('br_name', models.Char<PERSON><PERSON>(max_length=64, verbose_name='分支名')),
                ('iteration_id', models.Char<PERSON><PERSON>(max_length=64, verbose_name='迭代id')),
                ('report_url', models.CharField(max_length=256, verbose_name='报告地址')),
                ('task_id', models.CharField(max_length=64, verbose_name='制品md5')),
            ],
            options={
                'verbose_name': 'ccn报告信息表',
                'db_table': 'code_quality_sonar_record_info',
            },
        ),
        migrations.CreateModel(
            name='CodeQualitySonarResultModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_user', models.CharField(max_length=20, verbose_name='创建人')),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('update_user', models.CharField(max_length=20, verbose_name='修改人')),
                ('update_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='修改时间')),
                ('p_id', models.IntegerField(max_length=64, verbose_name='record id')),
                ('measure', models.CharField(max_length=16, verbose_name='指标')),
                ('segment', models.CharField(max_length=16, verbose_name='细分指标')),
                ('value', models.CharField(max_length=64, verbose_name='指标值')),
            ],
            options={
                'verbose_name': 'sonar 扫描结果',
                'db_table': 'code_quality_sonar_result_info',
            },
        ),
    ]
