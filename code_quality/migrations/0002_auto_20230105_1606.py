# Generated by Django 3.2 on 2023-01-05 16:06

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('code_quality', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='GuardRuleModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_user', models.CharField(max_length=20, verbose_name='创建人')),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('update_user', models.CharField(max_length=20, verbose_name='修改人')),
                ('update_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='修改时间')),
                ('measure', models.Char<PERSON>ield(max_length=64, verbose_name='指标')),
                ('segment', models.Char<PERSON>ield(max_length=64, verbose_name='细分指标')),
            ],
            options={
                'verbose_name': '门禁规则',
                'db_table': 'code_quality_guard_rule',
            },
        ),
        migrations.AlterField(
            model_name='codequalitysonarresultmodel',
            name='measure',
            field=models.CharField(max_length=64, verbose_name='指标'),
        ),
        migrations.AlterField(
            model_name='codequalitysonarresultmodel',
            name='segment',
            field=models.CharField(max_length=64, verbose_name='细分指标'),
        ),
    ]
