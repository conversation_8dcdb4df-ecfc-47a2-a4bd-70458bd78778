import codecs
import csv
import numpy as np
import os
from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from rest_framework import status
from mantis.settings import ApiR<PERSON>ult, CODE_SCAN, logger
from mantis.common_tool import get_report, count_ccn_entrance_guard_index, count_p3c_entrance_guard_index, \
    analysis_report, del_file, count_first_scan_entrance_guard_index
from code_quality.dao.code_quality_dao import inser_or_update_lizard_report_info,\
    get_ccn_index_info, insert_code_quality_ccn_index_info, get_rule_class_info, inser_or_update_p3c_report_info,\
    code_quality_p3c_index_info, get_p3c_index_info, get_p3c_detail_info, get_ccn_detail_info, record_code_scan_info

class AnalysisCcn(ViewSet):

    authentication_classes = []

    @staticmethod
    def __get_report(report_path, local_path):
        pass

    @staticmethod
    def __analysis_report(local_path):
        detail_list = []
        with codecs.open(local_path, 'r', encoding='utf-8') as f:
            for row in csv.DictReader(f, skipinitialspace=True):
                detail_list.append(row)
        return detail_list

    @staticmethod
    def __work_with_data(detail_data):
        """
        处理报告数据，生成最终落库数据
        finall_detail:明细数据
        count_data:计算分位明细数据
        """
        finall_detail = []
        count_data = []
        for item in detail_data:
            class_fun_list = item[6].split('::')
            if len(class_fun_list) == 2:
                fun_name = class_fun_list[1]
            else:
                fun_name = class_fun_list[0]
            finall_detail.append({'class': item[6], 'method': fun_name, 'ccn': item[1]})
            count_data.append(int(item[1]))
        # count_data = list(map(int, count_data))
        count_data.sort(reverse=True)
        count_data = count_data[0:200]
        return finall_detail, count_data

    @staticmethod
    def count_index(count_data, percent_num):
        """
        计算分位值
        count_data:需要计算的数据列表
        percent_num:具体的分位
        """
        pre_count_data = np.array(count_data)
        index_value = np.percentile(pre_count_data, percent_num)

        return index_value

    def list(self,request):
        pass

    def create(self, request):
        app_name = request.data['app_name']
        iteration_id = request.data['iteration_id']
        report_url = request.data['lib_url']
        lib_md5 = request.data['lib_md5']
        local_ccn_path = CODE_SCAN['local_ccn_path']
        local_report_path = os.path.join(local_ccn_path, "{}_{}_ccn_report.csv".
                                         format(iteration_id, app_name))
        if not os.path.exists(local_ccn_path):
            logger.info("local_ccn_path：{}不存在，先创建".format(local_ccn_path))
            os.makedirs(local_ccn_path, exist_ok=True)
            logger.info("os.makedirs({})".format(local_ccn_path))
        del_file(local_report_path)
        custom_index = int(CODE_SCAN['custom_index'])
        get_report(report_url, local_report_path)
        detail_list = analysis_report(local_report_path)
        final_detail, count_data = self.__work_with_data(detail_list)
        index_value = self.count_index(count_data, custom_index)
        li_id = inser_or_update_lizard_report_info(app_name, iteration_id, report_url, final_detail, count_data, lib_md5)
        insert_code_quality_ccn_index_info(li_id, custom_index, index_value)
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(data=index_value, msg="分析成功"))

class AnalysisP3c(ViewSet):

    authentication_classes = []

    @staticmethod
    def __analysis_report(local_path):
        detail_list = []
        with codecs.open(local_path, 'r', encoding='utf-8') as f:
            for row in csv.DictReader(f, skipinitialspace=True):
                detail_list.append(row)
        return detail_list

    @staticmethod
    def __work_with_data(detail_data):
        """
        处理报告数据，生成最终落库数据
        finall_detail:明细数据
        count_data:计算分位明细数据
        """
        finall_detail = {}
        finall_blocker_detail = []
        finall_critical_detail = []
        finall_major_detail = []
        blocker_count_data = []
        critical_count_data = []
        major_count_data = []
        count_data = {}
        for item in detail_data:
            if item['Priority'] == '1':
                blocker_count_data.append(int(item['Problem']))
                finall_blocker_detail.append({'Problem': item['Problem'], 'class': item['File'],
                                              'Rule_class': item['Rule'],
                                              'Rule_type': item['Rule_type'], 'Priority': item['Priority']})
            if item['Priority'] == '2':
                critical_count_data.append(int(item['Problem']))
                finall_critical_detail.append({'Problem': item['Problem'], 'class': item['File'],
                                               'Rule_class': item['Rule'],
                                               'Rule_type': item['Rule_type'], 'Priority': item['Priority']})
            if item['Priority'] == '3':
                major_count_data.append(int(item['Problem']))
                finall_major_detail.append({'Problem': item['Problem'], 'class': item['File'],
                                            'Rule_class': item['Rule'], 'Rule_type': item['Rule_type'],
                                            'Priority': item['Priority']})
        count_data['1'] = blocker_count_data
        count_data['2'] = critical_count_data
        count_data['3'] = major_count_data
        finall_detail['1'] = finall_blocker_detail
        finall_detail['2'] = finall_critical_detail
        finall_detail['3'] = finall_major_detail
        return finall_detail, count_data

    def count_index(self, count_data):
        sum_count_data = {}
        for item in count_data:
            sum_count_data[item] = len(count_data[item])
        return sum_count_data

    def list(self):
        pass

    def create(self, request):
        app_name = request.data['app_name']
        iteration_id = request.data['iteration_id']
        report_url = request.data['lib_url']
        lib_md5 = request.data['lib_md5']
        local_p3c_path = CODE_SCAN['local_p3c_path']
        local_report_path = os.path.join(local_p3c_path,
                                         "{}_{}_p3c_report.csv".format(iteration_id, app_name))
        if not os.path.exists(local_p3c_path):
            logger.info("local_p3c_path：{} 不存在，先创建".format(local_p3c_path))
            os.makedirs(local_p3c_path, exist_ok=True)
            logger.info("os.makedirs({})".format(local_p3c_path))

        get_report(report_url, local_report_path)
        detail_list = self.__analysis_report(local_report_path)  # 本地调试
        lizard_report_info_dict = get_rule_class_info()
        for item in detail_list:
            item['Rule_type'] = lizard_report_info_dict[item['Rule set']]
        final_detail, count_data = self.__work_with_data(detail_list)
        sum_count_data = self.count_index(count_data)
        p_id = inser_or_update_p3c_report_info(app_name, iteration_id, report_url, lib_md5)
        code_quality_p3c_index_info(p_id, final_detail, count_data)
        return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(data=sum_count_data, msg="分析成功"))

class CcnEntranceGuard(ViewSet):

    authentication_classes = []

    def list(self, request):
        app_name = request.query_params.get('app_name')
        iteration_id = request.query_params.get('iteration_id')
        lib_md5 = request.query_params.get('lib_md5')
        last_arc_iteration_id = request.query_params.get('last_arc_iteration_id')
        ccn_obj = get_ccn_detail_info(app_name, iteration_id)
        last_ccn_c_obj = get_ccn_detail_info(app_name, last_arc_iteration_id)
        ccn_p_id = ccn_obj[0].id
        ccn_url = ccn_obj[0].report_url
        # last_ccn_p_id = last_ccn_c_obj[0].id
        # last_index_info = get_ccn_index_info(last_ccn_p_id)[0].index_value
        index_info = get_ccn_index_info(ccn_p_id)[0].index_value
        if not last_ccn_c_obj or not last_arc_iteration_id:
            msg = "首次ccn扫描,本次报告url:{},95分位值{}".format(ccn_url, index_info)
            result = count_first_scan_entrance_guard_index(index_info, CODE_SCAN['ccn_first_scan'])
            if result:
                record_code_scan_info(app_name, iteration_id, msg, True, 'ccn')
                return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(data=iteration_id, msg=msg))
            else:
                msg = "首次ccn扫描,本次指标超过20，本次报告url:{},95分位值{}".format(ccn_url, index_info)
                record_code_scan_info(app_name, iteration_id, msg, False, 'ccn')
                return Response(status=status.HTTP_200_OK, data=ApiResult.failed_dict(data=result, msg=msg))
        if ccn_obj[0].lib_md5 != lib_md5:
            msg = "对比MD5失败".format(ccn_url)
            record_code_scan_info(app_name, iteration_id, msg, False, 'ccn')
            return Response(status=status.HTTP_200_OK, data=ApiResult.failed_dict(data=lib_md5, msg=msg))
        last_ccn_p_id = last_ccn_c_obj[0].id
        last_index_info = get_ccn_index_info(last_ccn_p_id)[0].index_value
        # ccn_url = ccn_obj[0].report_url
        result = count_ccn_entrance_guard_index(last_index_info, index_info, CODE_SCAN["ccn_lowest_threshold_value"])
        if result:
            msg = "ccn门禁通过,上一次的归档版本：{}本次报告url:{}".format(last_arc_iteration_id, ccn_url)
            record_code_scan_info(app_name, iteration_id, msg, result, 'ccn')
            return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(data=result, msg=msg))
        else:
            msg = "ccn门禁不通过,本次ccn95分位大于上一次ccn95分位,\n本次95分位值{},上一次归档版本95分位值{},上一次的归档版本：{}," \
                  "本次报告url:{}".format(index_info, last_index_info, last_arc_iteration_id,
                                                                                ccn_url)
            record_code_scan_info(app_name, iteration_id, msg, result, 'ccn')
            return Response(status=status.HTTP_200_OK, data=ApiResult.failed_dict(data=result,
                                                                                   msg=msg))

    def create(self, request):
        pass


class P3cEntranceGuard(ViewSet):
    authentication_classes = []

    @staticmethod
    def __count_entrance_guard_index(last_p3c_index, now_p3c_index):
        """
        计算门禁阈值
        """
        if len(last_p3c_index) > len(now_p3c_index):
            return True
        else:
            return False

    def list(self, request):
        app_name = request.query_params.get('app_name')
        iteration_id = request.query_params.get('iteration_id')
        lib_md5 = request.query_params.get('lib_md5')
        last_arc_iteration_id = request.query_params.get('last_arc_iteration_id')
        p3c_obj = get_p3c_detail_info(app_name, iteration_id)
        last_p3c_p_obj = get_p3c_detail_info(app_name, last_arc_iteration_id)
        p3c_p_id = p3c_obj[0].id
        report_url = p3c_obj[0].report_url
        # last_p3c_p_id = last_p3c_p_obj[0].id
        # last_p3c_index = get_p3c_index_info(last_p3c_p_id)[1].rule_num_sum
        now_p3c_index = get_p3c_index_info(p3c_p_id)[1].rule_num_sum
        if not last_p3c_p_obj or not last_arc_iteration_id:
            msg = "首次p3c扫描,本次报告url:{}".format(report_url)
            result = count_first_scan_entrance_guard_index(now_p3c_index, CODE_SCAN['p3c_first_scan'])
            if result:
                record_code_scan_info(app_name, iteration_id, msg, True, 'p3c')
                return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(data=iteration_id, msg=msg))
            else:
                msg = "首次p3c扫描不通过,本次指标大于0不通过,本次报告url:{}".format(report_url)
                record_code_scan_info(app_name, iteration_id, msg, False, 'p3c')
                return Response(status=status.HTTP_200_OK, data=ApiResult.failed_dict(data=result, msg=msg))
        if p3c_obj[0].lib_md5 != lib_md5:
            msg = "对比MD5失败"
            record_code_scan_info(app_name, iteration_id, msg, False, 'p3c')
            return Response(status=status.HTTP_200_OK, data=ApiResult.failed_dict(data=lib_md5, msg=msg))
        # last_p3c_p_id = last_p3c_p_obj[0].id
        # last_p3c_index = get_p3c_index_info(last_p3c_p_id)[1].rule_num_sum
        # now_p3c_index = get_p3c_index_info(p3c_p_id)[1].rule_num_sum
        last_p3c_p_id = last_p3c_p_obj[0].id
        last_p3c_index = get_p3c_index_info(last_p3c_p_id)[1].rule_num_sum
        result = count_p3c_entrance_guard_index(last_p3c_index, now_p3c_index)
        if result:
            msg = "p3c门禁通过,上一次的归档版本：{},本次报告url:{},本次blockerz总数：{}".format(last_arc_iteration_id,
                                                                          report_url, now_p3c_index)
            record_code_scan_info(app_name, iteration_id, msg, result, 'p3c')
            return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(data=result, msg=msg))
        else:
            msg = "p3c门禁不通过,本次blocker总数大于上一次归档版本blocker总数,\n,本次blocker总数：{},上一次归档版本blocker总数：{}," \
                  "上一次的归档版本：{}，本次报告url:{}".\
                format(now_p3c_index, last_p3c_index, last_arc_iteration_id, report_url)
            record_code_scan_info(app_name, iteration_id, msg, result, 'p3c')
            return Response(status=status.HTTP_200_OK, data=ApiResult.failed_dict(data=result,
                                                                                   msg=msg))


    def create(self, request):
        pass