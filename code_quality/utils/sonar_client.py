import time
from mantis.settings import logger, SONAR_QUBE
from sonarqube import SonarQubeClient
from enum import Enum
from code_quality.bo.sonar_bo import MeasureBo


class SonarClient:
    org_name = "com.howbuy"
    duplicated_key = "duplicated_files,duplicated_lines,duplicated_lines_density,duplicated_blocks"

    def __init__(self):
        try:
            self.sonar_client = SonarQubeClient(sonarqube_url=SONAR_QUBE["url"], token=SONAR_QUBE["token"])
        except Exception as e:
            logger.error(e)
            raise e

        self.search_strategy = {self.IssueType.CODE_SMELL.value: self.search_issues,
                                self.IssueType.BUG.value: self.search_issues,
                                self.IssueType.VULNERABILITY.value: self.search_issues,
                                self.IssueType.DUPLICATED.value: self.search_measures}

    @classmethod
    def instance(cls, *args, **kwargs):
        if not hasattr(SonarClient, "_instance"):
            SonarClient._instance = SonarClient(*args, **kwargs)
        return SonarClient._instance

    class IssueType(Enum):
        CODE_SMELL = "CODE_SMELL"
        BUG = "BUG"
        VULNERABILITY = "VULNERABILITY"
        DUPLICATED = "DUPLICATED"

    def is_task_end(self, task_id):
        logger.info('is_task_end')
        try:
            res = self.sonar_client.ce.get_task(task_id)
            logger.info(res)
            if res["task"]["status"] == "SUCCESS":
                return True
            else:
                return False
        except Exception as e:
            logger.error(e)


    def get_analysis_result(self, app_name: str, branch: str, task_id: str, retry_num=20)-> list[MeasureBo]:
        logger.info("开始分析sonar结果")
        while not self.is_task_end(task_id) and retry_num > 0:
            logger.info("任务未结束 开启重试")
            time.sleep(3)
            retry_num = retry_num - 1
        measures_list = []
        for type in self.IssueType:
            res_list = self.search_strategy[type.value](app_name, branch, type.value)
            measures_list.extend(res_list)
        logger.debug(measures_list)
        return measures_list

    def search_issues(self, app_name: str, branch: str, type: str)-> list[MeasureBo]:
        """
        获取 findbug 的统计的个数 缺陷/异味/漏洞
        :param app_name:
        :param branch:
        :param type:
        :return:
        """
        logger.info("{}:{}".format(self.org_name, app_name))
        logger.info(branch)
        res = self.sonar_client.issues.search_issues(branch=branch,
                                                componentKeys="{}:{}".format(self.org_name, app_name),
                                                facets="severities",
                                                types=type,
                                                additionalFields="_all",
                                                s="FILE_LINE",
                                                resolved="false")
        measures_list = []
        if isinstance(res, dict):
            for row in res["facets"][0]["values"]:
                measure_bo = MeasureBo(type, row["val"], row["count"])
                measures_list.append(measure_bo)
        return measures_list

    def search_measures(self, app_name: str, branch: str, type: str) ->list[MeasureBo]:
        """
        查询指标方法  DUPLICATED 重复度
        :param app_name:
        :param branch:
        :param type:
        :return:
        """
        logger.info("{}:{}:{}".format(app_name, branch, type))
        res = self.sonar_client.measures.get_component_with_specified_measures(branch=branch,
                                                                  component="{}:{}".format(self.org_name, app_name),
                                                                  additionalFields="period",
                                                                  metricKeys=self.duplicated_key)
        measures_list = []

        if isinstance(res, dict):
            for row in res["component"]["measures"]:
                measure_bo = MeasureBo(type, row["metric"], row["value"])
                measures_list.append(measure_bo)
        return measures_list


if __name__ == "__main__":
    sonar_client = SonarClient()
    res = sonar_client.is_task_end("AYWfOXlYmIKgb3vO38OV")
    logger.info(res)
    # if isinstance(res, dict):
    #     for key, value in res.items():
    #         logger.info("{} :{}".format(key, value))
    # else:
    #     for row in res:
    #         logger.info(row)