import json
from enum import Enum
import requests

from code_quality.models.code_quality_models import CodeQualitySonarResultModel
from mantis.settings import logger, SPIDER, CODE_SCAN


class SonarGuard:
    __archived_info_interface = SPIDER["url"]+"iter_mgt/get_app_archive_version/"

    class GuardResult(Enum):
        success = True
        failure = False

    def _get_iter_app_measure_value(self, iteration_id: str, app_name: str, task_id: str="") ->dict:
        """
        获取应用迭代的 分析指标
        :param iteration_id:
        :param app_name:
        :param task_id:
        :return:
        """
        measure_value_dict = {}
        sql = """SELECT i.id,i.measure,i.segment,i.value FROM code_quality_sonar_record_info r 
LEFT JOIN code_quality_sonar_result_info i ON r.id = i.p_id
LEFT JOIN code_quality_guard_rule l ON l.measure=i.measure AND l.segment=i.segment
WHERE r.iteration_id="{}" AND r.app_name = "{}" 
AND l.measure IS NOT NULL AND l.segment IS NOT  NULL
        """.format(iteration_id, app_name)
        if task_id != "":
            sql = sql + 'and r.task_id ="{}"'.format(task_id)
        logger.info(sql)
        for row in CodeQualitySonarResultModel.objects.raw(sql):
            measure_value_dict[(row.measure, row.segment)] = row.value
        return measure_value_dict

    def _get_app_archived_version(self, app_name: str) ->dict:
        """
        获取最近一次归档版本
        :param app_name:
        :return:
        """
        logger.info(self.__archived_info_interface)
        res = requests.get(self.__archived_info_interface, params={"app_name": app_name})
        if res.status_code == 200:
            res_dict = res.json()
        else:
            raise Exception("调用spider 归档接口失败 {}".format(self.__archived_info_interface))
        logger.info(res_dict["data"])
        return res_dict["data"]["iteration_id"]

    def _get_guard_threshold(self, app_name: str) ->dict:
        """
        获取应用的门禁阈值
        :param app_name:
        :return:
        """
        iteration_id = self._get_app_archived_version(app_name)
        if iteration_id == "":
            logger.info("没有找到归档版本，判定为新应用")
            return {}
        return self._get_iter_app_measure_value(iteration_id, app_name)

    def _get_need_check_measure_info(self, iteration_id: str, app_name: str, task_id: str):
        """
        需要检查的指标值
        :param iteration_id:
        :param app_name:
        :return:
        """
        return self._get_iter_app_measure_value(iteration_id, app_name, task_id)

    def _guard_check(self, threshold_dict: dict, be_check_dict: dict)->(str, str):
        """
        对比待检测数据和阈值数据，超过阈值数据将判定为不通过
        :param threshold_dict:
        :param be_check_dict:
        :return:
        """
        failure_results_dict = {}
        for key, value in threshold_dict.items():
            if key[0] == "DUPLICATED":
                if float(be_check_dict[key]) > float(CODE_SCAN["duplicated_standard"]) and float(be_check_dict[key]) > float(value):
                    failure_results_dict["{}-{}".format(key[0], key[1])] = "{} {}指标为{}，指标在阈值{}和{}之间并比上一次归档版本指标值{}大，门禁不通过".\
                        format(key[0], key[1], be_check_dict[key],
                               CODE_SCAN["duplicated_standard"], CODE_SCAN["DUPLICATED"], value)
                    continue
                elif float(be_check_dict[key]) < float(CODE_SCAN["duplicated_standard"]):
                    continue
            if float(be_check_dict[key]) > float(value):
                failure_results_dict["{}-{}".format(key[0], key[1])] = "{} {}指标为{}，大于阈值{}，门禁不通过".format(key[0], key[1], be_check_dict[key], value)
        if len(failure_results_dict) == 0:
            return self.GuardResult.success.value, "校验通过"
        else:
            failure_results_json = json.dumps(failure_results_dict, ensure_ascii=False)
            logger.info(failure_results_json)
            return self.GuardResult.failure.value, failure_results_json

    def _first_guard_check(self, be_check_dict: dict)->(str, str):
        sonar_guard_list = ["BUG", "VULNERABILITY", "DUPLICATED"]
        failure_results_dict = {}
        for key, value in be_check_dict.items():
            if key[0] in sonar_guard_list:
                if float(be_check_dict[key]) > float(CODE_SCAN[key[0]]):
                    failure_results_dict["{}-{}".format(key[0], key[1])] = "首次扫描{} {}指标为{}，大于阈值{}，门禁不通过".\
                        format(key[0], key[1], be_check_dict[key], CODE_SCAN[key[0]])
        if len(failure_results_dict) == 0:
            return self.GuardResult.success.value, "校验通过"
        else:
            failure_results_json = json.dumps(failure_results_dict, ensure_ascii=False)
            logger.info(failure_results_json)
            return self.GuardResult.failure.value, failure_results_json

    def main(self, iteration_id: str, app_name: str, task_id: str)->(bool, str):
        """
        执行sonar门禁
        :param iteration_id:
        :param app_name:
        :param task_id:
        :return:
        """
        threshold_dict = self._get_guard_threshold(app_name)
        # if len(threshold_dict) == 0:
            # return self.GuardResult.success.value, "没有阈值数据，无需对比"
        be_check_dict = self._get_need_check_measure_info(iteration_id, app_name, task_id)
        if len(threshold_dict) == 0:
            # return self.GuardResult.success.value, "没有阈值数据，无需对比"
            status, msg = self._first_guard_check(be_check_dict)
            if not status:
                return self.GuardResult.failure.value, msg
        if len(be_check_dict) == 0:
            return self.GuardResult.failure.value, "未找到{}迭代，{}应用，{}分析结果".format(iteration_id,
                                                                                app_name,
                                                                                task_id)
        return self._guard_check(threshold_dict, be_check_dict)


if __name__ == "__main__":
    sonar_guard = SonarGuard()
    res = sonar_guard.main("tms_1.0", "cgi-ehowbuy-container")

