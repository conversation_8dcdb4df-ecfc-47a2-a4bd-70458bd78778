# TAPD Gateway 项目说明文档

## 项目概述

TAPD Gateway 是一个基于 Django 的中间件系统，用于实现与腾讯 TAPD（Tencent Agile Product Development）平台的双向数据同步。该项目作为 Mantis 系统的一个重要组成部分，提供了从 TAPD 获取数据和向 TAPD 推送数据的完整解决方案。

## 项目架构

### 整体架构设计

```
tapd_gateway/
├── from_tapd/          # 从TAPD获取数据模块
├── to_tapd/            # 向TAPD推送数据模块  
├── tapd_req_mgt/       # TAPD请求管理模块
└── urls.py             # 路由配置
```

### 技术栈

- **后端框架**: Django + Django REST Framework
- **数据库**: MySQL
- **HTTP客户端**: requests
- **认证方式**: HTTP Basic Auth
- **数据格式**: JSON

## 核心模块详解

### 1. from_tapd 模块 - 数据获取

#### 功能概述
从 TAPD 平台同步各类业务数据到本地数据库，支持增量同步和全量同步。

#### 主要组件

**API层 (`api/views.py`)**
- `TapdEntryView`: 统一的数据同步入口
- 支持的业务类型：
  - `iteration`: 迭代数据
  - `story`: 需求数据
  - `bug`: 缺陷数据
  - `test_plan`: 测试计划
  - `task`: 任务数据
  - `launch_form`: 发布评审
  - `timesheet`: 工时数据

**服务层 (`service/`)**
- `tapd_req_srv.py`: 核心服务类，包含各业务实体的CRUD操作
- `tapd_req_strategy.py`: 策略模式实现，支持不同业务类型的数据同步策略
- `tapd_entry_*_ser.py`: 各业务实体的专门服务类

**数据访问层 (`dao/`)**
- `tapd_req_dao.py`: 数据库访问对象，封装SQL查询操作

**数据模型 (`model/models.py`)**
- `TapdSyncBatch`: 同步批次记录
- `TapdEntry*`: 各业务实体模型（Bug、Story、Task等）
- `TapdEntry*ResJson`: 原始响应数据存储

#### 核心特性

1. **批量同步机制**
   - 支持分页获取数据
   - 批次管理和状态跟踪
   - 增量同步基于修改时间

2. **策略模式设计**
   ```python
   class StoreEntitiesFromTapdStrategy(ABC):
       @abstractmethod
       def sync_data_from_tapd(self):
           pass
   ```

3. **异常处理机制**
   - 自定义异常类型
   - 重试机制
   - 错误日志记录

### 2. to_tapd 模块 - 数据推送

#### 功能概述
将本地系统数据同步到 TAPD 平台，支持创建和更新操作。

#### 主要组件

**API层 (`api/views.py`)**
- `ToTapdEntryView`: 自定义字段同步
- `TapdCreateEntryView`: 创建TAPD条目
- `CreateTestStoryFromDevTestPlan`: 从开发测试计划创建测试需求

**服务层 (`service/`)**
- `sync_to_dev_story_info.py`: 开发需求同步服务
- `custom_field_strategy.py`: 自定义字段更新策略
- `sync_to_*_select_field.py`: 选择字段同步服务

**数据模型 (`model/`)**
- `bug_model.py`: Bug创建相关模型
- `field_options_model.py`: 字段选项模型

#### 核心特性

1. **自定义字段管理**
   - 支持公司项目、应用信息、迭代信息的字段同步
   - 动态字段选项更新

2. **业务数据创建**
   - Bug自动创建
   - 测试需求生成
   - 需求复制和更新

### 3. tapd_req_mgt 模块 - 请求管理

#### 功能概述
管理与 TAPD API 的所有HTTP请求，包括限流、日志记录、异常处理等。

#### 主要组件

**核心工具 (`utils/tapd_req_mgt.py`)**
- `TapdReqMgt`: 请求管理核心类
- 支持GET和POST请求
- 集成认证和限流机制

**服务层 (`service/`)**
- `tapd_request_limit_ser.py`: 请求限制服务

**数据模型 (`models/tapd_req_models.py`)**
- `TapdGetLog`: GET请求日志
- `TapdPostLog`: POST请求日志
- `TapdRequestLimit`: 请求限制记录

#### 核心特性

1. **请求限流机制**
   ```python
   # 配置参数
   request_limit_number = 30      # 限制数量
   request_limit_seconds = 60     # 时间窗口
   request_limit_retry = 3        # 重试次数
   ```

2. **完整的日志记录**
   - 请求参数记录
   - 响应数据存储
   - 耗时统计
   - 错误信息记录

3. **异常处理**
   - HTTP 429 (Too Many Requests) 处理
   - 网络异常重试
   - 自定义异常类型

## 数据流程

### 数据同步流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as TAPD Gateway
    participant TAPD as TAPD平台
    participant DB as 数据库
    
    Client->>Gateway: 发起同步请求
    Gateway->>DB: 检查上次同步时间
    Gateway->>TAPD: 请求增量数据
    TAPD-->>Gateway: 返回数据
    Gateway->>DB: 存储/更新数据
    Gateway->>DB: 记录同步日志
    Gateway-->>Client: 返回同步结果
```

### 请求限流流程

```mermaid
flowchart TD
    A[发起请求] --> B{检查限流}
    B -->|未超限| C[执行请求]
    B -->|超限| D[等待或抛异常]
    C --> E[记录请求日志]
    E --> F[返回结果]
    D --> G[重试或失败]
```

## API接口文档

### 数据获取接口

#### 1. 从 TAPD 同步数据

**接口地址**: `POST /mantis/tapd_gateway/from_tapd/sync_data_from_tapd/`

**功能描述**: 从 TAPD 系统同步各种业务类型的数据到本地数据库

**核心业务逻辑**:
- 支持7种业务类型的数据同步：迭代、需求、缺陷、测试计划、任务、发布评审、工时
- 采用策略模式，根据业务类型选择对应的同步策略
- 自动获取上次同步时间，支持增量同步
- 记录同步成功日志，便于追踪同步状态
- 批量处理数据，支持插入、更新、删除操作

**请求参数**:
- `workspace_id` (必填): TAPD 工作空间 ID
- `modified` (可选): 修改时间，格式为时间戳，不传则自动获取上次同步时间
- `biz_type` (必填): 业务类型，支持以下值：
  - `iteration`: 迭代数据
  - `story`: 需求数据
  - `bug`: 缺陷数据
  - `test_plan`: 测试计划数据
  - `task`: 任务数据
  - `launch_form`: 发布评审数据
  - `timesheet`: 工时数据

**数据模型**:
- `TapdEntryIteration`: 迭代数据模型
- `TapdEntryStory`: 需求数据模型
- `TapdEntryBug`: 缺陷数据模型
- `TapdEntryTestPlan`: 测试计划数据模型
- `TapdEntryTask`: 任务数据模型
- `TapdEntryLaunchForm`: 发布评审数据模型
- `TapdEntryTimeSheet`: 工时数据模型

**外部依赖**:
- TAPD API 服务
- TapdReqMgt 请求管理工具
- TapdEntryDataSyncLogSer 同步日志服务

**返回结果**:
```json
{
    "success": true,
    "message": "同步TAPD成功，插入X条，更新Y条。删除Z条",
    "data": null
}
```

### 数据推送接口

#### 1. 同步数据到 TAPD 自定义字段

**接口地址**: `POST /mantis/tapd_gateway/sync_data_to_tapd_custom_field/`

**功能描述**: 将平台数据同步到 TAPD 的自定义字段，支持公司项目、应用列表、迭代流水线等数据的同步

**核心业务逻辑**:
- 支持三种业务类型的自定义字段同步
- 公司项目：同步年度项目列表到需求自定义字段
- 好买应用：从Spider系统获取应用列表，同步到缺陷自定义字段
- 好买迭代：从Spider系统获取流水线列表，同步到需求自定义字段
- 采用策略模式，支持不同字段类型的同步策略
- 批量处理多个团队的数据同步

**请求参数**:
- `workspace_id` (必填): TAPD 工作空间 ID
- `biz_type` (必填): 业务类型
  - `company_project`: 公司项目同步
  - `howbuy_app`: 好买应用同步
  - `howbuy_iteration`: 好买迭代同步
- `year` (可选): 年份，仅对 company_project 类型有效

**数据源**:
- `TapdCompanyProject`: 公司项目数据表
- Spider系统API：应用和流水线数据
- TAPD自定义字段配置

**同步策略**:
- `SyncToStorySelectFieldStrategy`: 需求字段同步策略
- `SyncToBugSelectFieldStrategy`: 缺陷字段同步策略
- `UpdateCustomFieldContext`: 自定义字段更新上下文

**外部依赖**:
- Spider系统API服务
- TAPD API服务
- TapdReqMgt 请求管理工具

**返回结果**:
```json
{
    "success": true,
    "message": "同步{biz_type}到TAPD成功",
    "data": null
}
```

#### 2. 从开发测试计划创建测试需求

**接口地址**: `POST /mantis/tapd_gateway/create_test_story_form_dev_test_plan/`

**功能描述**: 根据开发测试计划自动创建对应的测试需求，建立测试计划与需求的关联关系

**核心业务逻辑**:
- 查询需要创建测试需求的开发测试计划
- 为每个测试计划创建对应的测试需求
- 设置需求的基本信息：名称、描述、负责人等
- 通过自定义字段关联测试计划ID和链接
- 更新测试计划与需求的绑定关系表
- 统计创建成功和失败的数量

**请求参数**: 无

**数据模型**:
- `TapdEntryTestPlanBindStory`: 测试计划与需求绑定关系
- TAPD需求工作项模型

**业务规则**:
- 需求名称：基于测试计划名称生成
- 需求负责人：继承测试计划负责人
- 自定义字段22：存储测试计划ID
- 自定义字段23：存储测试计划链接

**外部依赖**:
- TAPD API服务
- TapdReqMgt 请求管理工具
- TapdModuleSrv 模块服务

**返回结果**:
```json
{
    "success": true,
    "message": "创建测试需求， 成功X条，失败Y条",
    "data": null
}
```

#### 3. 自动创建 TAPD 业务实体

**接口地址**: `POST /mantis/tapd_gateway/create_tapd_entry/`

**功能描述**: 根据请求参数自动创建TAPD业务实体（主要是缺陷），支持外部系统集成

**核心业务逻辑**:
- 接收外部系统的创建请求
- 验证请求UUID的唯一性
- 调用TAPD API创建业务实体
- 返回创建的实体URL
- 记录创建过程的日志

**请求参数**:
- `request_uuid` (必填): 请求唯一标识符
- 其他参数：根据具体业务实体类型而定

**外部依赖**:
- TapdServiceResult 服务
- TAPD API服务

**返回结果**:
```json
{
    "success": true,
    "message": "创建成功",
    "data": {
        "entry_url": "https://tapd.cn/..."
    }
}
```

#### 4. 查询 TAPD 实体状态

**接口地址**: `POST /mantis/tapd_gateway/get_tapd_entry_status/`

**功能描述**: 查询指定TAPD实体的当前状态信息

**核心业务逻辑**:
- 接收实体查询请求
- 调用TAPD API获取实体状态
- 返回实体的详细状态信息
- 支持多种实体类型的状态查询

**请求参数**:
- 实体相关参数（具体参数根据实体类型而定）

**外部依赖**:
- TapdServiceResult 服务
- TAPD API服务

**返回结果**:
```json
{
    "success": true,
    "message": "获取tapd状态成功",
    "data": {
        // 实体状态信息
    }
}
```

### 管理接口

#### 1. 清理请求限制数据

**接口地址**: `GET /mantis/tapd_gateway/delete_request_limit_data/`

**功能描述**: 删除最近一小时前的 TAPD 请求限制记录，用于清理过期的限流数据

**核心业务逻辑**:
- 查询一小时前的请求限制记录
- 批量删除过期的限流记录
- 释放数据库存储空间
- 维护请求限制表的数据量
- 记录删除的数据条数

**请求参数**: 无

**数据模型**:
- `TapdRequestLimit`: 请求限制记录模型

**业务规则**:
- 删除时间：当前时间前1小时
- 删除策略：物理删除
- 日志记录：记录删除条数

**外部依赖**:
- TapdRequestLimitSer 请求限制服务

**返回结果**:
```json
{
    "success": true,
    "message": "执行成功",
    "data": null
}
```

## 配置说明

### TAPD配置 (settings.ini)

```ini
[TAPD]
api_user = m?WYBPmq
api_password = AA73FEF3-4A72-17C4-0B46-1400C4D4DF2A
host_url = https://api.tapd.cn
sync_user = howbuyscm
req_limit = 200
req_page = 1
retry_max = 3
request_limit_number = 30
request_limit_seconds = 60
request_limit_retry = 3
# 工作空间配置
prod_bug_workspace_id = 30324855
pa_one_workspace_id = 57711941
test_mgt_workspace_id = 55014084
# 工作项类型配置
workitem_type_id = 1155014084001000146
test_submit_story_workitem_type_id = 1155014084001000252
launch_template_id = 1155014084001000048
# 同步字段配置
sync_fields = name,description,priority,attachment,business_value,status
```

### 关键配置参数说明

- `api_user/api_password`: TAPD API认证信息
- `req_limit`: 单次请求数据条数限制
- `request_limit_number`: 请求频率限制（每分钟30次）
- `retry_max`: 请求失败重试次数
- `workspace_id`: 不同环境的工作空间ID

## 部署指南

### 环境要求

- Python 3.8+
- Django 3.2+
- MySQL 5.7+
- Redis (可选，用于缓存)

### 安装步骤

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **数据库配置**
   ```python
   # settings.py
   DATABASES = {
       'default': {
           'ENGINE': 'django.db.backends.mysql',
           'NAME': 'mantis',
           'USER': 'scm',
           'PASSWORD': '123456',
           'HOST': '**************',
           'PORT': '3306',
       }
   }
   ```

3. **数据库迁移**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

4. **启动服务**
   ```bash
   python manage.py runserver 0.0.0.0:8000
   ```

### 监控和维护

1. **日志监控**
   - 查看 `logs/mantis_info.log` 和 `logs/mantis_error.log`
   - 监控同步批次状态
   - 关注请求限流情况

2. **数据库维护**
   - 定期清理历史日志数据
   - 监控数据库性能
   - 备份重要业务数据

3. **性能优化**
   - 调整批次大小和并发数
   - 优化数据库索引
   - 使用缓存减少重复请求

## 常见问题

### Q1: 同步数据时出现429错误
**A**: 这是TAPD API的限流机制，系统会自动重试。如果频繁出现，可以调整 `request_limit_number` 参数。

### Q2: 数据同步不完整
**A**: 检查以下几点：
- 确认workspace_id正确
- 检查modified时间参数
- 查看同步日志中的错误信息
- 验证TAPD API权限

### Q3: 自定义字段同步失败
**A**: 确认：
- 字段配置正确
- 工作项类型ID匹配
- 用户有相应权限

## 服务层详解

### 数据同步服务

#### 1. TapdReqMgt - 请求管理核心服务

**功能描述**: TAPD API请求的统一管理工具，负责所有与TAPD的HTTP通信

**核心方法**:
- `tapd_get()`: 执行GET请求，支持分页和参数传递
- `tapd_post()`: 执行POST请求，支持数据创建和更新
- `_check_request_limit()`: 检查请求限流状态
- `_handle_429_error()`: 处理429限流错误

**业务逻辑**:
- 集成TAPD API认证机制
- 实现请求限流控制（30次/分钟）
- 支持自动重试机制（最多3次）
- 记录请求日志和错误信息
- 处理TAPD API的各种异常情况

**依赖关系**:
- `TapdRequestLimit`: 请求限制记录模型
- `TapdGetLog/TapdPostLog`: 请求日志模型
- TAPD API认证配置

#### 2. StoreEntitiesFromTapdStrategy - 数据同步策略基类

**功能描述**: 采用策略模式设计的数据同步抽象基类，定义统一的同步接口

**核心方法**:
- `sync_data_from_tapd()`: 抽象方法，子类实现具体同步逻辑
- `execute_strategy()`: 策略执行入口

**具体策略实现**:
- `GetIterationFromStrategy`: 迭代数据同步策略
- `GetStoryFromTapdStrategy`: 需求数据同步策略
- `GetBugTapdStrategy`: 缺陷数据同步策略
- `GetTestPlanFromTapdStrategy`: 测试计划同步策略
- `GetTaskFromTapdStrategy`: 任务数据同步策略
- `GetLaunchFormFromTapdStrategy`: 发布评审同步策略
- `GetTimeSheetFromTapdStrategy`: 工时数据同步策略

**业务逻辑**:
- 支持分页数据获取
- 实现批量数据处理（插入、更新、删除）
- 基于修改时间的增量同步
- 异常处理和重试机制

#### 3. TapdBaseSrv - 业务实体服务基类

**功能描述**: 各业务实体服务的基础类，提供通用的CRUD操作

**核心子类**:
- `TapdIterationSrv`: 迭代服务
- `TapdStorySrv`: 需求服务
- `TapdBugSrv`: 缺陷服务
- `TapdTestPlanSrv`: 测试计划服务
- `TapdTaskSrv`: 任务服务
- `TapdLaunchFormSrv`: 发布评审服务
- `TapdTimeSheetSrv`: 工时服务

**业务逻辑**:
- 封装数据库操作
- 提供批量处理能力
- 支持数据验证和转换
- 实现业务规则检查

### 自定义字段同步服务

#### 1. UpdateCustomFieldContext - 自定义字段更新上下文

**功能描述**: 管理TAPD自定义字段的更新操作，支持不同字段类型的同步策略

**核心方法**:
- `set_strategy()`: 设置同步策略
- `set_data_bo()`: 设置数据对象
- `do_strategy()`: 执行同步策略

**支持策略**:
- `SyncToStorySelectFieldStrategy`: 需求选择字段同步
- `SyncToBugSelectFieldStrategy`: 缺陷选择字段同步

**业务逻辑**:
- 动态字段选项更新
- 支持多种字段类型
- 批量字段同步处理
- 错误处理和回滚机制

#### 2. TapdServiceResult - TAPD业务结果服务

**功能描述**: 处理TAPD业务实体的创建和状态查询

**核心方法**:
- `create_entry()`: 创建TAPD业务实体
- `get_tapd_entry_status()`: 获取实体状态

**业务逻辑**:
- 支持缺陷自动创建
- 实体状态实时查询
- 创建结果URL返回
- 异常处理和日志记录

### 数据访问服务

#### 1. TapdRequestLimitSer - 请求限制服务

**功能描述**: 管理TAPD API请求的限流记录

**核心方法**:
- `delete_request_limit_data()`: 清理过期限流记录
- `check_request_limit()`: 检查当前限流状态

**业务逻辑**:
- 维护请求限制表数据
- 自动清理过期记录
- 支持限流状态查询
- 优化数据库性能

#### 2. TapdEntryDataSyncLogSer - 数据同步日志服务

**功能描述**: 管理数据同步的日志记录和状态跟踪

**核心方法**:
- `get_modified()`: 获取上次同步时间
- `save_success_log()`: 保存同步成功日志

**业务逻辑**:
- 跟踪同步历史记录
- 支持增量同步时间计算
- 记录同步成功状态
- 提供同步统计信息

### 实体专门服务

#### 1. TapdEntityIterationSer - 迭代实体服务

**功能描述**: 专门处理TAPD迭代数据的业务逻辑

**核心方法**:
- `get_iteration_by_workspace()`: 按工作空间获取迭代
- `sync_iteration_data()`: 同步迭代数据

#### 2. TapdEntityStorySer - 需求实体服务

**功能描述**: 专门处理TAPD需求数据的业务逻辑

**核心方法**:
- `get_story_by_iteration()`: 按迭代获取需求
- `sync_story_data()`: 同步需求数据

#### 3. TapdEntityBugSer - 缺陷实体服务

**功能描述**: 专门处理TAPD缺陷数据的业务逻辑

**核心方法**:
- `get_bug_by_iteration()`: 按迭代获取缺陷
- `sync_bug_data()`: 同步缺陷数据

#### 4. TapdEntityTestPlanSer - 测试计划实体服务

**功能描述**: 专门处理TAPD测试计划数据的业务逻辑

**核心方法**:
- `get_test_plan_by_iteration()`: 按迭代获取测试计划
- `sync_test_plan_data()`: 同步测试计划数据

#### 5. TapdEntityTaskSer - 任务实体服务

**功能描述**: 专门处理TAPD任务数据的业务逻辑

**核心方法**:
- `get_task_by_iteration()`: 按迭代获取任务
- `sync_task_data()`: 同步任务数据

#### 6. TapdEntityLaunchFormSer - 发布评审实体服务

**功能描述**: 专门处理TAPD发布评审数据的业务逻辑

**核心方法**:
- `get_launch_form_by_iteration()`: 按迭代获取发布评审
- `sync_launch_form_data()`: 同步发布评审数据

## 扩展开发

### 添加新的业务类型

1. **创建数据模型**
   ```python
   class TapdEntryNewType(models.Model):
       # 定义字段
       pass
   ```

2. **实现服务类**
   ```python
   class TapdNewTypeSrv(TapdBaseSrv):
       def get_new_type_by_workspace_id(self, workspace_id, ...):
           # 实现获取逻辑
           pass
   ```

3. **创建同步策略**
   ```python
   class GetNewTypeFromTapdStrategy(StoreEntitiesFromTapdStrategy):
       def sync_data_from_tapd(self):
           # 实现同步逻辑
           pass
   ```

4. **更新视图和路由**

### 自定义同步策略

继承 `StoreEntitiesFromTapdStrategy` 基类，实现自定义的同步逻辑：

```python
class CustomSyncStrategy(StoreEntitiesFromTapdStrategy):
    def sync_data_from_tapd(self):
        # 自定义同步逻辑
        pass
```

## 版本历史

- **v1.0.0**: 基础功能实现，支持主要业务类型同步
- **v1.1.0**: 添加请求限流和异常处理机制
- **v1.2.0**: 支持自定义字段同步
- **v1.3.0**: 优化批量处理和性能

## 与其他模块的集成

### 与 Measurement 模块的集成

#### 数据流向关系
```
TAPD API ←→ tapd_gateway ←→ measurement
    ↓              ↓            ↓
工时数据      任务数据      效能指标
缺陷数据      迭代数据      质量度量
```

#### 关联功能说明

##### 1. 工时数据支持
- **提供数据**: `tapd_entry_task` 和 `tapd_entry_timesheet` 表数据
- **用途**: 支持 measurement 模块的个人效能计算
- **数据内容**: 任务工时记录、工时分配、任务类型等

##### 2. 任务和缺陷数据
- **提供数据**: TAPD 任务、缺陷、迭代等核心数据
- **用途**: 支持质量度量和效能分析
- **数据流程**: tapd_gateway 同步 → measurement 二次分析

##### 3. 配置共享
- **共享文件**: `mantis/settings.ini`
- **共享内容**: 
  - TAPD API 认证信息
  - 工作空间配置
  - 同步用户设置
  - 请求限制参数

##### 4. 业务协同
- **数据一致性**: 确保两个模块使用相同的 TAPD 数据源
- **同步策略**: 协调数据同步时间，避免冲突
- **错误处理**: 共享异常处理和重试机制

#### 集成架构图
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   TAPD API      │◄──►│  tapd_gateway    │◄──►│  measurement    │
│                 │    │                  │    │                 │
│ • 任务数据      │    │ • 数据同步       │    │ • 效能计算      │
│ • 工时数据      │    │ • 数据存储       │    │ • 质量分析      │
│ • 缺陷数据      │    │ • 接口管理       │    │ • 报表生成      │
│ • 迭代数据      │    │ • 限流控制       │    │ • 个人看板      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

#### 数据表关联

##### 核心关联表
1. **tapd_entry_task**: 任务基础信息
   - 被 measurement 用于工时统计和任务分析
   
2. **tapd_entry_timesheet**: 工时记录
   - 被 measurement 用于个人效能计算
   
3. **tapd_entry_bug**: 缺陷信息
   - 被 measurement 用于质量度量分析
   
4. **tapd_entry_iteration**: 迭代信息
   - 被 measurement 用于迭代效能分析

##### 配置表关联
- **TapdRequestLimit**: 请求限制记录，两个模块共享限流策略
- **TapdSyncBatch**: 同步批次管理，协调数据同步

#### 开发协调

##### 1. 数据模型变更
- tapd_gateway 数据模型变更需要通知 measurement 团队
- 确保向后兼容性，避免破坏性变更

##### 2. API 接口协调
- 新增 TAPD 数据同步功能时，考虑 measurement 的需求
- 提供稳定的数据访问接口

##### 3. 性能优化
- 协调数据库查询优化，避免相互影响
- 共享缓存策略，提高整体性能

### 与 Test_Report 模块的集成

#### 数据流向关系
```
TAPD API ←→ tapd_gateway ←→ test_report
    ↓              ↓            ↓
缺陷数据      数据服务      测试报告
需求数据      模型共享      质量分析
```

#### 关联功能说明

##### 1. 数据服务提供
- **缺陷数据**: 通过 `TapdEntityBugSer` 服务提供缺陷信息
- **需求数据**: 通过 `TapdEntityStorySer` 服务提供需求信息
- **任务数据**: 通过 `TapdEntityTaskSer` 服务提供任务信息
- **用途**: 支持 test_report 模块的报告数据生成

##### 2. 模型共享
- **共享模型**: `TapdEntryBug`、`TapdEntryStory`、`TapdEntryTask` 等
- **数据一致性**: 确保两个模块使用相同的数据结构
- **版本兼容**: 模型变更时保持向后兼容

##### 3. 服务调用关系
- `test_report.TAPDBugs` → `tapd_gateway.TapdEntityBugSer`
- `test_report.TAPDStories` → `tapd_gateway.TapdEntityStorySer`
- `test_report.TAPDTasks` → `tapd_gateway.TapdEntityTaskSer`
- `test_report.ReportDataETL` → `tapd_gateway` 数据同步策略

##### 4. 业务协同
- **报告数据源**: tapd_gateway 为 test_report 提供 TAPD 原始数据
- **数据转换**: test_report 基于 tapd_gateway 的数据进行 ETL 处理
- **质量报告**: 支持软件质量报告和测试报告的数据需求

#### 集成架构图
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   TAPD API      │◄──►│  tapd_gateway    │◄──►│  test_report    │
│                 │    │                  │    │                 │
│ • 缺陷数据      │    │ • 数据同步       │    │ • 测试报告      │
│ • 需求数据      │    │ • 服务接口       │    │ • 质量分析      │
│ • 任务数据      │    │ • 模型定义       │    │ • 数据ETL       │
│ • 测试计划      │    │ • 数据存储       │    │ • 报告生成      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

#### 数据表关联

##### 核心关联表
1. **tapd_entry_bug**: 缺陷信息
   - 被 test_report 用于缺陷报告生成
   
2. **tapd_entry_story**: 需求信息
   - 被 test_report 用于需求完成度报告
   
3. **tapd_entry_task**: 任务信息
   - 被 test_report 用于任务执行报告
   
4. **tapd_entry_test_plan**: 测试计划
   - 被 test_report 用于测试计划报告

5. **tapd_entry_iteration**: 迭代信息
   - 被 test_report 用于迭代质量报告

6. **tapd_entry_launch_form**: 发布评审
   - 被 test_report 用于发布评审报告

#### 开发协调

##### 1. 服务接口标准
- 保持服务接口的稳定性和一致性
- 新增接口时考虑 test_report 的需求
- 提供完整的接口文档和示例

##### 2. 数据模型协调
- 模型变更前通知 test_report 团队
- 确保数据字段的完整性和准确性
- 维护数据字典和映射关系

##### 3. 性能优化
- 优化数据查询性能，支持大数据量报告生成
- 提供数据缓存机制，减少重复查询
- 协调数据同步时间，避免影响报告生成

## 联系方式

- **项目负责人**: howbuyscm
- **关联模块**: Measurement 研发效能团队
- **技术支持**: 开发团队
- **文档维护**: 系统管理员

---

*本文档最后更新时间: 2024年*