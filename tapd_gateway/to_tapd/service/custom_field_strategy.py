from abc import ABCMeta, abstractmethod


class CustomFieldPostToTapdBaseObject:

    def __init__(self, workspace_id=None, tapd_id=None, option_list=None):
        self.workspace_id = workspace_id
        self.tapd_id = tapd_id
        self.option_list = option_list


class CustomFieldPostToTapdStrategy(metaclass=ABCMeta):

    @abstractmethod
    def post_to_tapd(self, data_bo: CustomFieldPostToTapdBaseObject):
        pass


class UpdateCustomFieldContext:
    def __init__(self, strategy, customFieldPostToTapdBaseObject: CustomFieldPostToTapdBaseObject):
        self.strategy = strategy
        self.data_bo = customFieldPostToTapdBaseObject

    def set_strategy(self, strategy):
        self.strategy = strategy

    def set_data_bo(self, data_bo):
        self.data_bo = data_bo

    def do_strategy(self):
        return self.strategy.post_to_tapd(self.data_bo)
