from mantis.settings import logger, TAPD
from tapd_gateway.tapd_req_mgt.utils.tapd_req_mgt import TapdReqMgt
from task_mgt.tapd_mgt.config.analysis_ini import LoadConfig


class SyncToDevStory:

    def post_to_tapd(self, workspace_id, src_story_id, dst_workspace_id, sync_fields, new_creator, dst_work_item_id=None):
        interface_name = "copy_story"
        lc = LoadConfig()
        res = lc.loading("external_interface")[interface_name]

        trm = TapdReqMgt()
        url = res.get("request_address")
        param_json = {"workspace_id": workspace_id, "src_story_id": src_story_id, "dst_workspace_id": dst_workspace_id,
                      "sync_fields": sync_fields, "new_creator": new_creator}
        if dst_work_item_id:
            param_json["dst_workitem_type_id"] = dst_work_item_id
        ret = trm.tapd_post(TAPD.get("host_url") + url, param_json)
        return ret


class UpdateTapdStory:

    def post_to_tapd(self, param_json):
        interface_name = "update_story"
        lc = LoadConfig()
        res = lc.loading("external_interface")[interface_name]

        trm = TapdReqMgt()
        url = res.get("request_address")
        ret = trm.tapd_post(TAPD.get("host_url") + url, param_json)
        return ret
