from mantis.settings import logger, TAPD
from tapd_gateway.tapd_req_mgt.utils.tapd_req_mgt import TapdReqMgt
from tapd_gateway.to_tapd.service.custom_field_strategy import CustomFieldPostToTapdStrategy, \
    CustomFieldPostToTapdBaseObject
from task_mgt.tapd_mgt.config.analysis_ini import LoadConfig


class SyncToStorySelectFieldStrategy(CustomFieldPostToTapdStrategy):

    def post_to_tapd(self, data_bo: CustomFieldPostToTapdBaseObject):
        interface_name = "update_story_select_field_options"
        lc = LoadConfig()
        res = lc.loading("external_interface")[interface_name]

        trm = TapdReqMgt()
        url = res.get("request_address")
        param_json = {"workspace_id": data_bo.workspace_id, "options": '|'.join(data_bo.option_list),
                      "id": data_bo.tapd_id}
        ret = trm.tapd_post(TAPD.get("host_url") + url, param_json)

        if ret.get("status") == 1:
            logger.info("更新tapd需求自定义字段成功，param_json为：{}".format(param_json))
            return True
        else:
            logger.error("更新tapd需求自定义字段失败，param_json为：{}".format(param_json))
            return False

