import datetime
from abc import ABC, abstractmethod
from collections import namedtuple
from mantis.settings import logger as log
from mantis.settings import TAPD
from tapd_gateway.from_tapd.api.views import BizType
from tapd_gateway.from_tapd.model.models import TapdEntryBug
from tapd_gateway.from_tapd.service.tapd_req_srv import TapdBugSrv
from tapd_gateway.to_tapd.model.bug_model import TapdServiceResults


class TapdServiceResult:

    def get_tapd_entry_strategy(self, biz_type):
        if BizType.BUG.value == biz_type:
            tapd_strategy = TapdEntryBugSer()
        else:
            raise Exception("不支持的业务类型,biz_type:{}".format(biz_type))
        return TapdCreateEntryStrategyContext(tapd_strategy)

    def create_entry(self, params):
        title = params.get('title')
        workspace_id = TAPD['prod_bug_workspace_id']
        description = params.get('description')
        uuid = params.get('request_uuid')
        create_source = params.get('create_source')
        biz_type = params.get("biz_type")
        current_time = datetime.datetime.now()
        context = self.get_tapd_entry_strategy(biz_type)

        TapdServiceResults.objects.update_or_create(
            request_uuid=uuid,
            defaults={
                "create_user": 'howbuyscm',
                "create_time": current_time,
                "update_user": 'howbuyscm',
                "update_time": current_time,
                "tapd_workspace_id": workspace_id,
                "tapd_entry_type": biz_type,
                "request_source": create_source,
                "request_title": title,
                "create_status": 2,
                "create_param_json": params,
                "create_start_time": current_time,
                "create_desc": description,
            })
        entry_url = context.create_entry(params)
        return entry_url

    def get_tapd_entry_status(self, params):

        biz_type = params.get("biz_type")
        uuid_list = params.get('request_uuid')
        uuid_list = uuid_list.split(',')
        context = self.get_tapd_entry_strategy(biz_type)
        result = context.get_status(uuid_list)
        return result


class TapdCreateEntryStrategy(ABC):

    @abstractmethod
    def create_entry(self):
        pass

    @abstractmethod
    def get_entry_status(self):
        pass


class TapdCreateEntryStrategyContext:
    def __init__(self, strategy):
        self.strategy = strategy

    def create_entry(self, params):
        return self.strategy.create_entry(params)

    def get_status(self, uuid_list):
        return self.strategy.get_entry_status(uuid_list)


class TapdEntryBugSer(TapdCreateEntryStrategy):

    Priority = namedtuple('Priority', ['id', 'name', 'value'])
    URGENCY = Priority(1, "紧急", 2)
    HIGH = Priority(2, "高", 5)
    MIDDLE = Priority(3, "中", 10)
    LOW = Priority(4, "低", 48)
    TRIVIAL = Priority(5, "无关紧要", -1)

    def create_entry(self, params):
        workspace_id = TAPD['prod_bug_workspace_id']
        reporter = params.get('reporter')
        priority = params.get('priority')
        current_owner = params.get('current_owner')
        title = params.get('title')
        description = params.get('description')
        uuid = params.get('request_uuid')
        bugtype = params.get("bugtype")
        # 添加「缺陷来源」的支持。zt@2025-01-10
        bug_source = params.get("bug_source")
        current_time = datetime.datetime.now()

        tapd_res = TapdServiceResults.objects.filter(request_uuid=uuid).values('tapd_entry_id').last()
        if tapd_res and tapd_res.get('tapd_entry_id'):
            bug_url = self.__get_bug_url(workspace_id, tapd_res.get('tapd_entry_id'))
            return bug_url

        data = {"title": title, "current_owner": current_owner, "bugtype": bugtype,
                "priority": getattr(self, priority).name, "reporter": reporter,
                "description": description, "workspace_id": workspace_id, "custom_field_88": uuid,
                "custom_field_6": bug_source}

        log.debug('tapd创建bug-------->,workspace_id: {}, params:{}'.format(workspace_id, data))
        bug_ser = TapdBugSrv()
        bug_result = bug_ser.create_bug_by_workspace_id(workspace_id, data)
        if bug_result:
            bug_id = None
            try:
                end_current_time = datetime.datetime.now()
                delta = end_current_time - current_time
                milliseconds = delta.total_seconds() * 1000
                bug_ser.write_bugs_to_db([bug_result])
                bug_id = bug_result.get('Bug').get('id')
                if bug_id:
                    TapdServiceResults.objects.filter(request_uuid=uuid).update(
                        tapd_entry_id=bug_id,
                        create_req_json=data,
                        create_res_json=bug_result,
                        create_end_time=end_current_time,
                        create_cost_time=milliseconds,
                        create_status=3)

            except Exception as e:
                log.error("tapd创建bug回填信息失败.uuid:{},bug_id: {} ".format(uuid, bug_id))
                log.error(e)
            finally:
                return self.__get_bug_url(workspace_id, bug_id)

    def get_entry_status(self, uuid_list):
        bug_id_info = {}
        entry_id_list = []
        statu_info = {}
        objs = TapdServiceResults.objects.filter(request_uuid__in=uuid_list)
        for obj in objs:
            bug_id_info[obj.tapd_entry_id] = {'request_uuid': obj.request_uuid, 'tapd_entry_id': obj.tapd_entry_id}
            entry_id_list.append(obj.tapd_entry_id)
        entry_bug_objs = TapdEntryBug.objects.filter(tapd_entry_id__in=entry_id_list)
        for entry_bug_obj in entry_bug_objs:
            bug_id_info[entry_bug_obj.tapd_entry_id]['tapd_entry_status'] = entry_bug_obj.tapd_bug_status
            statu_info[bug_id_info[entry_bug_obj.tapd_entry_id]['request_uuid']] = bug_id_info[
                entry_bug_obj.tapd_entry_id]
        return statu_info

    def __get_bug_url(self, workspace_id, bug_id):
        return "https://tapd.cn/" + str(workspace_id) + "/bugtrace/bugs/view?bug_id=" + str(bug_id)
