from django.db import models
from public.models import BaseModels

class BizBugCreate(BaseModels):
    create_user = models.Char<PERSON>ield(verbose_name='create_user', max_length=100)
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(verbose_name='update_user', max_length=100)
    update_time =models.DateTimeField(blank=True, null=True, verbose_name='更新时间')
    stamp = models.BigIntegerField(verbose_name="版本")
    # id = models.BigIntegerField(verbose_name="id", primary_key=True)
    tapd_workspace_id = models.BigIntegerField(verbose_name="tapd_workspace_id")
    tapd_bug_id = models.BigIntegerField(verbose_name="tapd_bug_id")
    bug_create_uuid = models.Char<PERSON>ield(verbose_name='bug_create_uuid', max_length=100)
    bug_create_source = models.Char<PERSON><PERSON>(verbose_name='bug_create_source', max_length=100)
    bug_create_title = models.CharField(verbose_name='bug_create_title', max_length=100)
    bug_create_status = models.IntegerField(verbose_name='bug_create_status')
    bug_create_count = models.IntegerField(verbose_name='bug_create_count')
    bug_create_result = models.BooleanField(verbose_name='bug_create_result')
    bug_create_param_json = models.JSONField(verbose_name='bug_create_param_json')
    bug_create_req_json = models.JSONField(verbose_name='bug_create_req_json')
    bug_create_res_json = models.JSONField(verbose_name='bug_create_res_json')
    bug_create_start_time = models.DateTimeField(blank=True, null=True, verbose_name='请求起始时间')
    bug_create_end_time = models.DateTimeField(blank=True, null=True, verbose_name='请求截至时间')
    bug_create_cost_time = models.FloatField(verbose_name='bug_create_cost_time')
    bug_create_desc = models.CharField(verbose_name='bug_create_desc', max_length=100)

    class Meta:
        db_table = 'biz_bug_create'
        verbose_name = 'bug 创建表'


class TapdServiceResults(BaseModels):
    create_user = models.CharField(verbose_name='create_user', max_length=100)
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(verbose_name='update_user', max_length=100)
    update_time =models.DateTimeField(blank=True, null=True, verbose_name='更新时间')
    stamp = models.BigIntegerField(verbose_name="版本")
    # id = models.BigIntegerField(verbose_name="id", primary_key=True)
    tapd_workspace_id = models.BigIntegerField(verbose_name="项目ID")
    tapd_entry_id = models.BigIntegerField(verbose_name="业务ID")
    tapd_entry_type = models.CharField(verbose_name='实体类型', max_length=20)
    request_uuid = models.CharField(verbose_name='业务创建uuid', max_length=200)
    request_source = models.CharField(verbose_name='业务来源', max_length=100)
    request_title = models.CharField(verbose_name='标题', max_length=200)
    create_status = models.IntegerField(verbose_name='创建状态：1-已缓存、2-创建中、3-已完成')
    create_param_json = models.JSONField(verbose_name='参数Json')
    create_req_json = models.JSONField(verbose_name='请求Json')
    create_res_json = models.JSONField(verbose_name='返回Json')
    create_start_time = models.DateTimeField(blank=True, null=True, verbose_name='请求起始时间')
    create_end_time = models.DateTimeField(blank=True, null=True, verbose_name='请求截至时间')
    create_cost_time = models.FloatField(verbose_name='请求耗时（秒')
    create_desc = models.CharField(verbose_name='创建说明', max_length=255)

    class Meta:
        db_table = 'tapd_service_results'
        verbose_name = 'tapd业务创建表'

class BizBugCreateLog(BaseModels):
    create_user = models.CharField(verbose_name='create_user', max_length=100)
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(verbose_name='update_user', max_length=100)
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='更新时间')
    stamp = models.BigIntegerField(verbose_name="版本")
    id = models.BigIntegerField(verbose_name="id", primary_key=True)
    bug_create_id = models.BigIntegerField(verbose_name="id")
    bug_create_count = models.IntegerField(verbose_name='bug_create_count')
    bug_create_result = models.BooleanField(verbose_name='bug_create_result')
    bug_create_res_json = models.JSONField(verbose_name='bug_create_res_json')
    bug_create_start_time = models.DateTimeField(blank=True, null=True, verbose_name='请求起始时间')
    bug_create_end_time = models.DateTimeField(blank=True, null=True, verbose_name='请求起始时间')
    bug_create_cost_time = models.FloatField(verbose_name='bug_create_cost_time')
    bug_create_desc = models.CharField(verbose_name='bug_create_desc', max_length=100)

    class Meta:
        db_table = 'biz_bug_create_log'
        verbose_name = 'bug 创建log表'