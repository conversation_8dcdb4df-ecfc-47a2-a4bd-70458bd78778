from django.db import models
from public.models import BaseModels


class TapdCompanyProject(BaseModels):
    create_user = models.CharField(verbose_name='create_user', max_length=100)
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(verbose_name='update_user', max_length=100)
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='更新时间')
    stamp = models.BigIntegerField(verbose_name="版本")
    id = models.BigIntegerField(verbose_name="id", primary_key=True)
    project_year = models.CharField(verbose_name="项目年份", max_length=10)
    project_name = models.CharField(verbose_name="项目名称", max_length=500)
    project_is_active = models.BooleanField(verbose_name="是否有效")

    class Meta:
        db_table = 'tapd_company_project'
        verbose_name = '好买公司项目表'
