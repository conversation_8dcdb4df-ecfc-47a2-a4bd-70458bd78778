# Create your views here

import datetime
from enum import Enum

import requests
from rest_framework import status
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from mantis.settings import ApiResult
from mantis.settings import SPIDER, TEST_REPORT, TAPD
from mantis.settings import logger as log
from tapd_gateway.from_tapd.model.models import TapdEntryTestPlanBindStory
from tapd_gateway.from_tapd.service.tapd_req_srv import dict_fetchall, TapdModuleSrv, TapdModuleEnum
from tapd_gateway.tapd_req_mgt.utils.tapd_req_mgt import TapdReqMgt
from tapd_gateway.to_tapd.service.sync_to_bug_select_field import SyncToBugSelectFieldStrategy
from tapd_gateway.to_tapd.service.sync_to_story_select_field import SyncToStorySelectFieldStrategy
from tapd_gateway.to_tapd.service.tapd_service_result_ser import TapdServiceResult
from tapd_gateway.to_tapd.model.field_options_model import TapdCompanyProject
from tapd_gateway.to_tapd.service.custom_field_strategy import UpdateCustomFieldContext, CustomFieldPostToTapdBaseObject
from tapd_gateway.from_tapd.dao.tapd_req_dao import get_tapd_id_by_tapd_name, get_need_create_story_list


class BizType(Enum):
    COMPANY_PROJECT = "company_project"
    HOWBUY_ITERATION = "howbuy_iteration"
    HOWBUY_APP = "howbuy_app"


class ToTapdEntryView(ViewSet):
    """
    从平台同步数据到tapd自定义字段
    """
    authentication_classes = []

    def create(self, request):
        """从tapd同步数据"""
        workspace_id = request.data.get("workspace_id")
        biz_type = request.data.get("biz_type")
        year = request.data.get("year")

        # 参数检查
        if not workspace_id:
            return Response(status=status.HTTP_200_OK, data=ApiResult.failed_dict("workspace_id不能为空"))

        tapd_bo = CustomFieldPostToTapdBaseObject()
        tapd_strategy = SyncToStorySelectFieldStrategy()
        context = UpdateCustomFieldContext(tapd_strategy, tapd_bo)
        if biz_type == BizType.COMPANY_PROJECT.value:
            tapd_bo = self.get_company_project_bo(workspace_id, year)
            context.set_data_bo(tapd_bo)
            result = context.do_strategy()
            if not result:
                return Response(status=status.HTTP_200_OK,
                                data=ApiResult.failed_dict("同步{}到TAPD失败".format(biz_type)))
        elif biz_type == BizType.HOWBUY_APP.value:
            app_info_list = self.get_app_info_list_from_spider()
            for app_info in app_info_list:
                tapd_bo = self.get_app_list_bo(workspace_id, app_info.get("tapd_name"), app_info.get("app_list"))
                context.set_strategy(SyncToBugSelectFieldStrategy())
                context.set_data_bo(tapd_bo)
                result = context.do_strategy()
                if not result:
                    return Response(status=status.HTTP_200_OK,
                                    data=ApiResult.failed_dict("同步{}到TAPD失败".format(biz_type)))
        elif biz_type == BizType.HOWBUY_ITERATION.value:
            pipeline_list = self.get_pipeline_list_from_spider()
            for pipeline_info in pipeline_list:
                tapd_bo = self.get_pipeline_list_bo(workspace_id, pipeline_info.get("tapd_name"),
                                                    pipeline_info.get("pipeline_list"))
                context.set_strategy(SyncToStorySelectFieldStrategy())
                context.set_data_bo(tapd_bo)
                result = context.do_strategy()
                if not result:
                    return Response(status=status.HTTP_200_OK,
                                    data=ApiResult.failed_dict("同步{}到TAPD失败".format(biz_type)))

        return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(msg="同步{}到TAPD成功".format(biz_type)))

    def get_company_project_bo(self, workspace_id, year):
        option_list = []
        tapd_name = '2024公司项目'
        if year:
            obj = TapdCompanyProject.objects.filter(project_year=year).values('project_name')
        else:
            obj = TapdCompanyProject.objects.filter().values('project_name')
        for item in obj:
            project_name = item.get('project_name')
            option_list.append(project_name)

        cursor = get_tapd_id_by_tapd_name(workspace_id, tapd_name)
        dict_list = dict_fetchall(cursor)
        tapd_id = dict_list[0].get('tapd_id')

        return CustomFieldPostToTapdBaseObject(workspace_id, tapd_id, option_list)

    def get_app_info_list_from_spider(self):
        url = '{}tapd_mgt/get_app_list_by_team_id'.format(SPIDER["url"])
        response = requests.get(url)
        app_info_list = response.json()
        return app_info_list

    def get_app_list_bo(self, workspace_id, tapd_name, app_list, tapd_entry_type='bug'):
        cursor = get_tapd_id_by_tapd_name(workspace_id, tapd_name, tapd_entry_type)
        dict_list = dict_fetchall(cursor)
        tapd_id = dict_list[0].get('tapd_id')
        return CustomFieldPostToTapdBaseObject(workspace_id, tapd_id, app_list)

    def get_pipeline_list_from_spider(self):
        url = '{}tapd_mgt/get_pipeline_list_by_team_id/'.format(SPIDER["url"])
        response = requests.get(url)
        pipeline_list = response.json()
        return pipeline_list

    def get_pipeline_list_bo(self, workspace_id, tapd_name, pipeline_list, tapd_entry_type='story'):
        cursor = get_tapd_id_by_tapd_name(workspace_id, tapd_name, tapd_entry_type)
        dict_list = dict_fetchall(cursor)
        tapd_id = dict_list[0].get('tapd_id')
        return CustomFieldPostToTapdBaseObject(workspace_id, tapd_id, pipeline_list)


class CreateTestStoryFromDevTestPlan(ViewSet):

    def create(self, request):
        """
        从开发测试计划创建测试需求
        """
        success_count = 0
        fail_count = 0
        cursor = get_need_create_story_list()
        need_create_story_list = dict_fetchall(cursor)
        if not need_create_story_list:
            return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(msg="无可创建测试的需求"))
        for item in need_create_story_list:
            param_json = {
                "workspace_id": TEST_REPORT.get("workspace_id"),
                "workitem_type_id": TEST_REPORT.get("story_workitem_type_id"),
                "name": item.get("story_name"),
                "description": item.get('description'),
                "owner": item.get("tapd_test_plan_owner"),
                "custom_field_22": item.get("tapd_test_plan_id"),
                "custom_field_23": TAPD['host_url'] + '/' + str(item.get(
                    "tapd_workspace_id")) + "/sparrow/test_plan/view/" + str(item.get("tapd_test_plan_id"))
            }
            if self.create_story(param_json, item.get("tapd_test_plan_id")):
                success_count += 1
            else:
                fail_count += 1

        return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(
            msg="创建测试需求， 成功{}条，失败{}条".format(success_count, fail_count)))

    def create_story(self, param_json, tapd_test_plan_id):
        tapd_req_mgt = TapdReqMgt()
        workspace_id = param_json.get('workspace_id')
        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.STORY, workspace_id)
        tapd_data = tapd_req_mgt.tapd_post(module_url, param_json)
        story_id = None
        result = True
        if tapd_data:
            story_id = tapd_data.get("Story").get("id") if tapd_data.get("Story") else None
        else:
            result = False
            log.error("创建测试需求失败，tapd_test_plan_id为：{}".format(tapd_test_plan_id))

        if story_id:
            try:
                log.info("更新更新测试计划关联需求表，tapd_test_plan_id为：{}".format(tapd_test_plan_id))
                TapdEntryTestPlanBindStory.objects.update_or_create(
                    tapd_test_plan_id=tapd_test_plan_id,
                    defaults={
                        "relative_test_story_id": tapd_test_plan_id,
                        "update_time": datetime.datetime.now()
                    }
                )
            except Exception as e:
                log.error("更新测试计划关联需求表失败，tapd_test_plan_id为：{}".format(tapd_test_plan_id))
                log.error(str(e))
        else:
            log.error("创建测试需求失败，tapd_test_plan_id为：{}".format(tapd_test_plan_id))
        return result


class TapdCreateEntryView(ViewSet):
    authentication_classes = []
    """
    自动创建tapd业务(bug)
    """

    def create(self, request):
        uuid = request.data.get('request_uuid')
        if not uuid:
            return Response(data=ApiResult.failed_dict(msg='没有uuid请确认'))
        entry_url = None
        try:
            tapd_ser = TapdServiceResult()
            entry_url = tapd_ser.create_entry(request.data)
            bug_obj = {'entry_url': entry_url}
            return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(data=bug_obj, msg="创建成功"))
        except Exception as e:
            log.error("tapd创建实体失败：{}".format(entry_url))
            log.error(e)
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict(msg="创建失败"))


class TapdEntryStatusView(ViewSet):
    """
    查询实体状态
    """
    authentication_classes = []

    def create(self, request):
        statu_info = None
        try:
            tapd_ser = TapdServiceResult()
            statu_info = tapd_ser.get_tapd_entry_status(request.data)
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.success_dict(data=statu_info, msg="获取tapd状态成功"))
        except Exception as e:
            log.error("查询实体状态失败：{}".format(statu_info))
            log.error(e)
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict(msg="获取tapd状态失败"))
