from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from tapd_gateway.tapd_req_mgt.api.views import TapdRequestLimitView
from tapd_gateway.to_tapd.api.views import CreateTestStoryFromDevTestPlan, ToTapdEntryView, TapdCreateEntryView, TapdEntryStatusView

router = DefaultRouter()

router.register(r'create_tapd_entry', TapdCreateEntryView, basename="create_tapd_entry/")
router.register(r'get_tapd_entry_status', TapdEntryStatusView, basename="get_tapd_entry_status/")
router.register(r'create_test_story_form_dev_test_plan', CreateTestStoryFromDevTestPlan,
                basename="create_test_story_form_dev_test_plan")
router.register(r'sync_data_to_tapd_custom_field', ToTapdEntryView, basename="sync_data_to_tapd_custom_field")
router.register(r'delete_request_limit_data', TapdRequestLimitView, basename="delete_request_limit_data")

urlpatterns = [
    path("", include(router.urls))
]
