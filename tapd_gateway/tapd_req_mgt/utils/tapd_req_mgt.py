import json

import requests
import datetime
import time
import enum

from django.db.models.expressions import RawSQL

from mantis.settings import TAPD
from mantis.settings import logger as log

from tapd_gateway.tapd_req_mgt.models.tapd_req_models import TapdGetLog, TapdPostLog, TapdRequestLog, TapdRequestLimit
from tapd_gateway.from_tapd.service.tapd_req_exp import TapdReqMgtExp, TapdReqMgt429Exp, TapdReqRetryMaxExp, TapdReqLimitExp


@enum.unique
class TapdRequestEnum(enum.Enum):
    """
    tapd请求类型枚举
    """
    GET = "GET"
    POST = "POST"


class TapdReqMgtResult:
    def __init__(self, code=0, data=None):
        self.code = code
        self.data = data


class TapdReqMgt:
    def __init__(self, api_user=TAPD["api_user"], api_password=TAPD["api_password"],
                 batch_number=None,
                 ups_biz_id=None,
                 ups_biz_name=None):
        """
        Tapd请求管理
        """
        self.api_user = api_user
        self.api_password = api_password
        self.batch_number = batch_number
        self.ups_biz_id = ups_biz_id
        self.ups_biz_name = ups_biz_name

    def request(self, req_enum: TapdRequestEnum, url, params, s_time=None):
        """
        tapd请求
        """
        if not s_time:
            s_time = datetime.datetime.now()
        # 参数解析
        method = req_enum.value
        # 1-限流
        if not self.__tapd_req_limit_db(method):
            raise TapdReqLimitExp(TAPD["request_limit_number"])
        # 2-请求
        log.info('request_url----------->:{}, param ----------->:{}'.format(url, params))
        if req_enum == TapdRequestEnum.GET:
            ret = requests.request(method, url, params=params, auth=(self.api_user, self.api_password))
        elif req_enum == TapdRequestEnum.POST:
            ret = requests.request(method, url, data=params, auth=(self.api_user, self.api_password))
        else:
            ret = requests.request(method, url, params=params, auth=(self.api_user, self.api_password))
        # 3-耗时计算
        e_time = datetime.datetime.now()
        # 4-写日志
        tapd_data, data_count, tapd_request_log_id = self.__tapd_req_write_db(url, method, params, s_time, e_time, ret)

        # 异常
        if ret.status_code != 200:
            log.error("tapd请求失败，响应结果是：{}".format(ret.content))
            if ret.status_code == 429:
                raise TapdReqMgt429Exp()
            else:
                err_msg = "tapd get请求异常"
                raise TapdReqMgtExp(code=ret.status_code, msg=err_msg)
        # 返回
        return tapd_data, data_count, tapd_request_log_id

    def tapd_get(self, url, params):
        """
        tapd get请求
        """
        s_time = datetime.datetime.now()
        # 数据条数限制
        tapd_req_limit = params.get("limit")
        if not tapd_req_limit:
            tapd_req_limit = TAPD["req_limit"]
            params["limit"] = tapd_req_limit
        # 页码
        tapd_req_page = params.get("page")
        if not tapd_req_page:
            tapd_req_page = TAPD["req_page"]
            params["page"] = tapd_req_page

        tapd_data, data_count, tapd_request_log_id = self.request(TapdRequestEnum.GET, url, params, s_time)

        return tapd_data

    def tapd_post(self, url, param_json):
        """
        tapd post请求
        """
        s_time = datetime.datetime.now()
        tapd_data, data_count, tapd_request_log_id = self.request(TapdRequestEnum.POST, url, param_json, s_time)
        return tapd_data

    def get_tapd_data_by_url(self,  url, api_params, retry=0):
        """
        通过url获取tapd数据
        """
        if not retry:
            retry = 0
        else:
            if retry < int(TAPD["retry_max"]):
                time.sleep(retry)
            else:
                raise TapdReqRetryMaxExp()

        res = None
        try:
            res = TapdReqMgt().tapd_get(url, api_params)
        except TapdReqMgt429Exp as ex:
            log.error("TapdReqMgt429Exp: {}".format(ex.msg))
            time.sleep(10)
            self.get_tapd_data_by_url(url, api_params, retry + 1)
        except Exception as e:
            log.error(e)
            self.get_tapd_data_by_url(url, api_params, retry + 1)

        return res

    def __tapd_req_limit_db(self, req_type):
        # 配置
        request_limit_number = TAPD["request_limit_number"]
        request_limit_seconds = TAPD["request_limit_seconds"]
        request_limit_retry = TAPD["request_limit_retry"]
        # 重试
        for idx in range(1, int(request_limit_retry) + 1):
            # 计算时间
            s_time = datetime.datetime.now()
            req_time = s_time - datetime.timedelta(seconds=int(request_limit_seconds))
            # 查询
            req_limit = TapdRequestLimit.objects.filter(req_time__gt=req_time).count()
            if req_limit <= int(request_limit_number):
                # 写入
                obj = TapdRequestLimit.objects.create(
                    req_type=req_type,
                    req_time=s_time,
                    req_limit=req_limit
                )
                # log.info(">>>>>tapd_request_limit.id(生成限流id) = {}".format(obj.id))
                write_ok = True
                break
            else:
                time.sleep(idx * 8)
        else:
            write_ok = False

        return write_ok

    def __tapd_req_write_db(self, req_url, req_type, req_json, s_time, e_time, ret):
        tapd_status = None
        tapd_info = None
        tapd_data = None
        data_count = 0
        # 参数填充
        # 工作空间ID
        tapd_workspace_id = req_json.get("workspace_id")
        cost_time = round((e_time - s_time).total_seconds(), 2)

        rst_status = ret.status_code
        ret_json = None
        ret_text = None
        res_result = None
        if rst_status == 200:
            res_result = True
            ret_json = ret.json()
            tapd_status = ret_json.get('status')
            tapd_info = ret_json.get('info')
            tapd_data = ret_json.get('data')
            if tapd_data:
                if isinstance(tapd_data, list):
                    data_count = len(tapd_data)
                elif isinstance(tapd_data, dict):
                    data_count = 1
                else:
                    data_count = 0
        else:
            res_result = False
            ret.encoding = "unicode_escape"
            ret_text = ret.text
            if ret_text and len(ret_text) > 1000:
                ret_text = ret_text[:997] + "..."
        # Log
        create_user = TAPD["sync_user"]
        create_time = s_time
        update_user = create_user
        update_time = create_time
        stamp = 0
        req_url = req_url
        req_type = req_type
        req_json = req_json
        req_start_time = s_time
        req_end_time = e_time
        req_cost_time = cost_time
        res_status = ret.status_code
        res_json = ret_json
        res_err_msg = ret_text
        tapd_req_desc = "tapd请求(新)"

        # DB
        obj = TapdRequestLog.objects.create(
            create_user=create_user,
            create_time=create_time,
            update_user=update_user,
            update_time=update_time,
            stamp=stamp,
            batch_number=self.batch_number,
            ups_biz_id=self.ups_biz_id,
            ups_biz_name=self.ups_biz_name,
            tapd_workspace_id=tapd_workspace_id,
            req_url=req_url,
            req_type=req_type,
            req_json_b=RawSQL("COMPRESS(%s)", (json.dumps(req_json),)),
            req_start_time=req_start_time,
            req_end_time=req_end_time,
            req_cost_time=req_cost_time,
            res_result=res_result,
            res_status=res_status,
            res_json_b=RawSQL("COMPRESS(%s)", (json.dumps(res_json),)),
            tapd_status=tapd_status,
            tapd_info=tapd_info,
            tapd_data_b=RawSQL("COMPRESS(%s)", (json.dumps(tapd_data),)),
            tapd_data_count=data_count,
            res_err_msg=res_err_msg,
            tapd_req_desc=tapd_req_desc
        )
        tapd_request_log_id = obj.id
        # log.info(">>>>>tapd_request_log_id = {}".format(tapd_request_log_id))

        return tapd_data, data_count, tapd_request_log_id


