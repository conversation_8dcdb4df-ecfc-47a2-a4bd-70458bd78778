from django.db import models


class TapdGetLog(models.Model):
    create_user = models.CharField(max_length=100, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=100, verbose_name='修改人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')
    id = models.AutoField(verbose_name='主键ID', primary_key=True)

    batch_number = models.IntegerField(verbose_name='请求批次')
    tapd_workspace_id = models.IntegerField(verbose_name='项目ID')
    tapd_req_limit = models.IntegerField(verbose_name='请求数量')
    tapd_req_page = models.IntegerField(verbose_name='请求页码')

    req_url = models.CharField(max_length=255, verbose_name='请求地址')
    req_type = models.CharField(max_length=10, verbose_name='请求类型')
    req_json = models.JSONField(verbose_name='请求Json')
    req_start_time = models.DateTimeField(blank=True, null=True, verbose_name='请求起始时间')
    req_end_time = models.DateTimeField(blank=True, null=True, verbose_name='请求截至时间')
    req_cost_time = models.FloatField(verbose_name='请求耗时（秒）')

    res_status = models.IntegerField(verbose_name='返回状态')
    res_json = models.JSONField(verbose_name='返回Json')
    tapd_status = models.CharField(max_length=100, verbose_name='返回Json中的状态')
    tapd_info = models.CharField(max_length=100, verbose_name='返回Json中的信息')
    tapd_data = models.JSONField(verbose_name='返回Json中的数据')
    data_count = models.IntegerField(verbose_name='数据条数')

    res_err_msg = models.CharField(max_length=1000, verbose_name='返回错误信息')
    tapd_get_desc = models.CharField(max_length=255, verbose_name='get请求说明')

    class Meta:
        db_table = 'tapd_get_log'
        verbose_name = 'get请求日志表'


class TapdPostLog(models.Model):
    create_user = models.CharField(max_length=100, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=100, verbose_name='修改人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')
    id = models.AutoField(verbose_name='主键ID', primary_key=True)

    batch_number = models.IntegerField(verbose_name='请求批次')
    tapd_workspace_id = models.IntegerField(verbose_name='项目ID')

    req_url = models.CharField(max_length=255, verbose_name='请求地址')
    req_type = models.CharField(max_length=10, verbose_name='请求类型')
    req_json = models.JSONField(verbose_name='请求Json')
    req_start_time = models.DateTimeField(blank=True, null=True, verbose_name='请求起始时间')
    req_end_time = models.DateTimeField(blank=True, null=True, verbose_name='请求截至时间')
    req_cost_time = models.FloatField(verbose_name='请求耗时（秒）')

    res_status = models.IntegerField(verbose_name='返回状态')
    res_json = models.JSONField(verbose_name='返回Json')
    tapd_status = models.CharField(max_length=100, verbose_name='返回Json中的状态')
    tapd_info = models.CharField(max_length=100, verbose_name='返回Json中的信息')
    tapd_data = models.JSONField(verbose_name='返回Json中的数据')
    data_count = models.IntegerField(verbose_name='数据条数')

    res_err_msg = models.CharField(max_length=1000, verbose_name='返回错误信息')
    tapd_post_desc = models.CharField(max_length=255, verbose_name='post请求说明')

    class Meta:
        db_table = 'tapd_post_log'
        verbose_name = 'post请求日志表'


class TapdRequestLog(models.Model):
    create_user = models.CharField(max_length=100, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=100, verbose_name='修改人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')
    id = models.AutoField(verbose_name='主键ID', primary_key=True)

    batch_number = models.IntegerField(verbose_name='请求批次')
    ups_biz_id = models.IntegerField(verbose_name='上游业务主键')
    ups_biz_name = models.CharField(max_length=10, verbose_name='上游业务名称')
    tapd_workspace_id = models.IntegerField(verbose_name='项目ID')

    req_url = models.CharField(max_length=255, verbose_name='请求地址')
    req_type = models.CharField(max_length=10, verbose_name='请求类型')
    req_module = models.CharField(max_length=100, verbose_name='请求模块')
    #req_json = models.JSONField(verbose_name='请求Json')
    req_json_b = models.BinaryField(verbose_name='请求Jsonb')
    req_start_time = models.DateTimeField(blank=True, null=True, verbose_name='请求起始时间')
    req_end_time = models.DateTimeField(blank=True, null=True, verbose_name='请求截至时间')
    req_cost_time = models.FloatField(verbose_name='请求耗时（秒）')

    res_result = models.BooleanField(verbose_name='返回结果：是否成功')
    res_status = models.IntegerField(verbose_name='返回状态')
    #res_json = models.JSONField(verbose_name='返回Json')
    res_json_b = models.BinaryField(verbose_name='返回Jsonb')

    tapd_status = models.CharField(max_length=100, verbose_name='返回Json中的状态')
    tapd_info = models.CharField(max_length=100, verbose_name='返回Json中的信息')
    #tapd_data = models.JSONField(verbose_name='返回Json中的数据')
    tapd_data_b = models.BinaryField(verbose_name='返回Json中的数据b')
    tapd_data_count = models.IntegerField(verbose_name='数据条数')

    res_err_msg = models.CharField(max_length=1000, verbose_name='返回错误信息')
    tapd_req_desc = models.CharField(max_length=255, verbose_name='请求说明')

    class Meta:
        db_table = 'tapd_request_log'
        verbose_name = 'tapd请求日志表'


class TapdRequestLimit(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)

    req_type = models.CharField(max_length=10, verbose_name='请求类型')
    req_time = models.DateTimeField(verbose_name='请求起始时间')
    req_limit = models.IntegerField(verbose_name='并发量')

    class Meta:
        db_table = 'tapd_request_limit'
        verbose_name = 'tapd请求限流表'
