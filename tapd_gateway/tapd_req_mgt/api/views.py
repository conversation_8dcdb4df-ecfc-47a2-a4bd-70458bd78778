# Create your views here

from rest_framework import status
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from mantis.settings import ApiResult
from mantis.settings import logger as log
from tapd_gateway.tapd_req_mgt.service.tapd_request_limit_ser import TapdRequestLimitSer


class TapdRequestLimitView(ViewSet):
    """
    删除最近一小时前tapd_request_limit记录
    """
    authentication_classes = []

    def list(self, request):
        request_limit_ser = TapdRequestLimitSer()
        del_rows = request_limit_ser.delete_request_limit_data()
        log.info('删除了：{} 条数据'.format(del_rows))
        return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(msg="执行成功"))
