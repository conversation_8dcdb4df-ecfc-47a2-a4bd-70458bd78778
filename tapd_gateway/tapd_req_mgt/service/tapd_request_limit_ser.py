from datetime import datetime
from mantis.settings import logger as log
from tapd_gateway.tapd_req_mgt.models.tapd_req_models import TapdRequestLimit
from datetime import timedelta


class TapdRequestLimitSer:

    def delete_request_limit_data(self):
        cur_time = datetime.now()
        log.info('系统当前时间是：{}'.format(cur_time))
        start_time = cur_time - timedelta(minutes=60)
        log.info('系统当前时间是：{},执行的删除时间：{}'.format(cur_time, start_time))
        deleted_count = TapdRequestLimit.objects.filter(req_time__lt=start_time).delete()[0]
        return deleted_count
