from mantis.settings import TAPD
from tapd_gateway.from_tapd.dao.tapd_req_dao import get_tapd_entity_iterations, \
    get_tapd_entity_iteration_by_iteration_ids


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class TapdEntityIterationSer:

    def get_iterations_by_workspace_id(self,workspace_id):
        launch_template_id = TAPD['launch_template_id']
        workitem_type_id = TAPD.get("workitem_type_id")
        cursor = get_tapd_entity_iterations(workspace_id, launch_template_id, workitem_type_id)
        dict_list = dict_fetchall(cursor)

        return dict_list

    def get_tapd_entity_iteration_by_iteration_ids(self, workspace_id, iteration_id_list):
        iteration_ids_str = "', '".join(iteration_id_list)
        cursor = get_tapd_entity_iteration_by_iteration_ids(workspace_id, iteration_ids_str)
        dict_list = dict_fetchall(cursor)

        return dict_list