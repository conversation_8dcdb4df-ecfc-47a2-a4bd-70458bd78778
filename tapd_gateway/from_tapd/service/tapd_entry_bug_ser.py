import datetime
from collections import namedtuple

from mantis.settings import TAPD
from tapd_gateway.from_tapd.dao.tapd_req_dao import get_tapd_bug_by_iteration_ids
from tapd_gateway.from_tapd.service.tapd_req_srv import TapdBugSrv
from tapd_gateway.to_tapd.model.bug_model import BizBugCreate


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class TapdEntityBugSer:

    def get_tapd_entity_bug_by_iteration_ids(self, workspace_id, iteration_id_list):
        iteration_ids_str = "', '".join(str(id) for id in iteration_id_list)
        cursor = get_tapd_bug_by_iteration_ids(workspace_id, iteration_ids_str)
        dict_list = dict_fetchall(cursor)

        return dict_list


    Priority = namedtuple('Priority', ['id', 'name', 'value'])
    URGENCY = Priority(1, "紧急", 2)
    HIGH = Priority(2, "高", 5)
    MIDDLE = Priority(3, "中", 10)
    LOW = Priority(4, "低", 48)
    TRIVIAL = Priority(5, "无关紧要", -1)
    
    def create_bug(self, params):
        reporter = params.get('reporter')
        priority = params.get('priority')
        current_owner = params.get('current_owner')
        title = params.get('title')
        workspace_id = TAPD['prod_bug_workspace_id']
        description = params.get('description')
        uuid = params.get('uuid')
        create_source = params.get('create_source')
        project_name = params.get('project_name')
        app_name = params.get('app_name')
        current_time = datetime.datetime.now()

        data = {"title": title, "current_owner": current_owner,
                "priority": getattr(self, priority).name, "reporter": reporter,
                "description": description, "workspace_id": workspace_id, "custom_field_88": uuid}

        request_param = {"project_name": project_name, "app_name": app_name, "title": title,
                         "current_owner": current_owner, "priority": "1", "reporter": reporter,
                         "description": "自动创建bug",
                         "workspace_id": workspace_id, "uuid": uuid, "create_source": create_source}
        BizBugCreate.objects.create(create_user='howbuyscm', create_time=current_time,
                                          update_user='howbuyscm',
                                          update_time=current_time,
                                          tapd_workspace_id=workspace_id, bug_create_uuid=uuid,
                                          bug_create_source=create_source, bug_create_title=title,
                                          bug_create_status=2, bug_create_count=1,
                                          bug_create_param_json=params, bug_create_start_time=current_time,
                                          bug_create_req_json=request_param, bug_create_desc='')
        bug_ser = TapdBugSrv()
        bug_result = bug_ser.create_bug_by_workspace_id(workspace_id, data)
        if bug_result:
            bug_id = bug_result.get('Bug').get('id')
            if bug_id:
                BizBugCreate.objects.filter(bug_create_uuid=uuid).update(
                    tapd_bug_id=bug_id,
                    bug_create_status=3)
                return TAPD['host_url'] + "/" + str(workspace_id) + "/bugtrace/bugs/view?bug_id=" + str(bug_id)
        return None