from mantis.settings import TAPD
from tapd_gateway.from_tapd.dao.tapd_req_dao import get_tapd_entity_story_name, get_tapd_entity_story, \
    get_tapd_entity_story_by_story_id, get_tapd_entity_story_by_iteration_ids


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class TapdEntityStorySer:

    def getStoryNameByWorkSpaceId(self, workspace_id):
        cursor = get_tapd_entity_story_name(workspace_id)
        dict_list = dict_fetchall(cursor)

        return dict_list


    def get_story_by_workspace_id(self, workspace_id):
        workitem_type_id = TAPD['workitem_type_id']
        cursor = get_tapd_entity_story(workspace_id, workitem_type_id)
        dict_list = dict_fetchall(cursor)

        return dict_list

    def get_story_by_story_id(self, story_id):
        cursor = get_tapd_entity_story_by_story_id(story_id)
        dict_list = dict_fetchall(cursor)

        return dict_list


    def get_tapd_entity_story_by_iteration_ids(self, workspace_id, iteration_id_list, workitem_type_id):
        iteration_ids_str = "', '".join(str(id) for id in iteration_id_list)
        cursor = get_tapd_entity_story_by_iteration_ids(workspace_id, iteration_ids_str, workitem_type_id)
        dict_list = dict_fetchall(cursor)

        return dict_list



class SendMessage:
    def sendMessage(self, message):
        print("Send message: ", message)


class TapdNotify:
    def power_on(self):
        pass


class TapdIterationNameNotify(SendMessage):
    def power_on(self):
        print("Power on with TapdIterationNameNotify")
        self.sendMessage("Iteration name notify")


class NotifyAdapter(TapdNotify):
    def __init__(self, notify):
        self.notify = notify

    def power_on(self):
        print("Converting TapdIterationNameNotify to TapdEntityStorySer")
        self.notify.power_on()


if __name__ == '__main__':
    notify = TapdIterationNameNotify()
    adapter = NotifyAdapter(notify)
    adapter.power_on()
