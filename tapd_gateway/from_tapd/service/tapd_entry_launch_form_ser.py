from mantis.settings import INTERFACE_URL
from tapd_gateway.from_tapd.dao.tapd_req_dao import get_tapd_launch_form_by_iteration_ids
from tapd_gateway.from_tapd.service.tapd_req_srv import TapdLaunchFormSrv


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class TapdEntityLaunchFormSer:

    def get_tapd_entity_launch_form_by_iteration_ids(self, workspace_id, iteration_id_list):
        iteration_ids_str = "', '".join(str(id) for id in iteration_id_list)
        cursor = get_tapd_launch_form_by_iteration_ids(workspace_id, iteration_ids_str)
        dict_list = dict_fetchall(cursor)

        return dict_list

    def create_launch_form(self, workspace_id, iteration):
        title = iteration.get("tapd_iteration_name")
        iteration_id = iteration.get("tapd_entry_id")
        signed_by = iteration.get("signed_by")
        archived_by = iteration.get("archived_by")
        template_id = iteration.get("template_id")
        creator = iteration.get("creator").split(";")[0].strip()
        team = iteration.get("team")
        create_report_url = self.get_create_report_url(iteration_id)
        report_url = self.get_report_url(iteration_id)
        tapd_launch_form_ser = TapdLaunchFormSrv()
        launch_form_list = tapd_launch_form_ser.get_launch_form_by_workspace_id(workspace_id, iteration_id)
        if launch_form_list:
            return launch_form_list[0]
        data = {"workspace_id": workspace_id, "title": title, "iteration_id": iteration_id,
                "signed_by": signed_by, "archived_by": archived_by,
                "creator": creator, "template_id": template_id, "custom_field_two": create_report_url,
                "custom_field_three": report_url, "custom_field_one": team}

        return tapd_launch_form_ser.create_launch_form_by_workspace_id(workspace_id, data)

    def get_create_report_url(self, iteration_id):
        report_url = INTERFACE_URL['mantis'] + '/' + INTERFACE_URL['mantis_context'] + \
                            '/test_report/create_test_report/?iteration_id={}'.format(iteration_id)
        return report_url

    def get_report_url(self, iteration_id):
        report_url = INTERFACE_URL['mantis'] + '/' + INTERFACE_URL['mantis_context'] + \
                         '/test_report/get_test_report/?iteration_id={}'.format(iteration_id)
        return report_url
