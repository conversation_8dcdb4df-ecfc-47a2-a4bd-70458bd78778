from tapd_gateway.from_tapd.dao.tapd_req_dao import get_tapd_entity_task_by_iteration_ids


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class TapdEntityTaskSer:


    def get_tapd_entity_task_by_iteration_ids(self, workspace_id, iteration_id_list):
        iteration_ids_str = "', '".join(iteration_id_list)
        cursor = get_tapd_entity_task_by_iteration_ids(workspace_id, iteration_ids_str)
        dict_list = dict_fetchall(cursor)

        return dict_list