import datetime
from enum import Enum

from tapd_gateway.from_tapd.model.models import TapdEntryDataSyncLog
from tapd_gateway.from_tapd.service.tapd_req_srv import TapdBaseSrv


class TapdFunctionLogType(Enum):
    DATA_TO_GATEWAY = "sync_data_to_gateway"
    DATA_TO_BIZ = "sync_data_to_biz"
    DATA_FROM_GATEWAY_PRODUCT_STORY = "sync_data_from_gateway_product_story"
    DATA_FROM_DEV_STORY_PRODUCT_STORY = "sync_dev_story_status_to_product_story"


class TapdEntryDataSyncLogSer:

    def get_modified(self, workspace_id, biz_type, req_type, current_time):
        data_sync_log = TapdEntryDataSyncLog.objects.filter(req_module=biz_type,
                                                            workspace_id=workspace_id,
                                                            req_type=req_type) \
            .values('success_time')
        modified = None
        if data_sync_log:
            modified = data_sync_log[0].get('success_time')

        if not modified:
            s_time = current_time
            modified = TapdBaseSrv.get_def_modified(s_time)
        modified = modified.strftime("%Y-%m-%d %H:%M:%S")
        return modified

    def save_success_log(self, biz_type, req_type, workspace_id, success_time):
        TapdEntryDataSyncLog.objects.update_or_create(defaults={
            "success_time": success_time,
            "create_user": 'howbuyscm',
            "create_time": datetime.datetime.now(),
            "update_user": 'howbuyscm',
            "update_time": datetime.datetime.now()},
            req_module=biz_type, workspace_id=workspace_id, req_type=req_type)
