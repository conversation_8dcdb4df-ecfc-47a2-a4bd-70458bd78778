import datetime
from enum import Enum
from mantis.settings import TAPD
from abc import ABC, abstractmethod
from mantis.settings import logger as log
from tapd_gateway.from_tapd.model.models import TapdEntryTestPlanBindStory
from tapd_gateway.from_tapd.service.tapd_req_srv import TapdIterationSrv, TapdStorySrv, TapdTestPlanSrv, TapdTaskSrv, \
    TapdLaunchFormSrv, TapdTimeSheetSrv, TapdBugSrv, TapdModuleSrv, TapdModuleEnum
from tapd_gateway.tapd_req_mgt.utils.tapd_req_mgt import TapdReqMgt


class StoreEntitiesFromTapdStrategy(ABC):

    @abstractmethod
    def sync_data_from_tapd(self):
        pass


class BatchTransferEntitiesTemplate():

    def get_list(self, workspace_id, modified, current_page, iteration_id):
        """
        return list, next_page
        """
        pass

    def save_list(self, data_list):
        pass

    def delete_data(self, workspace_id, modified):
        """
         return delete_rows
        """
        pass


class BatchTransferStoryTemplate(BatchTransferEntitiesTemplate):

    def delete_data(self, workspace_id, deleted):
        story_srv = TapdStorySrv()
        delete_rows = story_srv.delete_stories_by_workspace_id(workspace_id, deleted)
        return delete_rows

    def get_list(self, workspace_id, modified, current_page, iteration_id):
        story_srv = TapdStorySrv()
        data_list, next_page = story_srv.get_stories_by_workspace_id(workspace_id, current_page, modified, iteration_id)
        return data_list, next_page

    def save_list(self, data_list):
        story_srv = TapdStorySrv()
        ins_objs, upd_rows = story_srv.write_stories_to_db(data_list)
        ins_objs = ins_objs if ins_objs else 0
        upd_rows = upd_rows if upd_rows else 0
        return ins_objs, upd_rows


class BatchTransferBugTemplate(BatchTransferEntitiesTemplate):

    def delete_data(self, workspace_id, deleted):
        story_srv = TapdBugSrv()
        delete_rows = story_srv.delete_bugs_by_workspace_id(workspace_id, deleted)
        return delete_rows

    def get_list(self, workspace_id, modified, current_page, iteration_id):
        story_srv = TapdBugSrv()
        data_list, next_page = story_srv.get_bugs_by_workspace_id(workspace_id, modified, current_page, iteration_id)
        return data_list, next_page

    def save_list(self, data_list):
        story_srv = TapdBugSrv()
        ins_objs, upd_rows = story_srv.write_bugs_to_db(data_list)
        ins_objs = ins_objs if ins_objs else 0
        upd_rows = upd_rows if upd_rows else 0
        return ins_objs, upd_rows


class BatchTransferTaskTemplate(BatchTransferEntitiesTemplate):

    def delete_data(self, workspace_id, deleted):
        story_srv = TapdTaskSrv()
        delete_rows = story_srv.delete_tasks_by_workspace_id(workspace_id, deleted)
        return delete_rows

    def get_list(self, workspace_id, modified, current_page, iteration_id):
        story_srv = TapdTaskSrv()
        data_list, next_page = story_srv.get_tasks_by_workspace_id(workspace_id, modified, current_page, iteration_id)
        return data_list, next_page

    def save_list(self, data_list):
        story_srv = TapdTaskSrv()
        ins_objs, upd_rows = story_srv.write_tasks_to_db(data_list)
        ins_objs = ins_objs if ins_objs else 0
        upd_rows = upd_rows if upd_rows else 0
        return ins_objs, upd_rows


class BatchTransferLaunchFormTemplate(BatchTransferEntitiesTemplate):

    def delete_data(self, workspace_id, deleted):
        # todo 发布计划没有删除接口
        return 0

    def get_list(self, workspace_id, modified, current_page, iteration_id):
        launch_form_srv = TapdLaunchFormSrv()
        data_list, next_page = launch_form_srv.get_launch_forms_by_workspace_id(workspace_id, modified, current_page,
                                                                                iteration_id)
        return data_list, next_page

    def save_list(self, data_list):
        launch_form_srv = TapdLaunchFormSrv()
        ins_objs, upd_rows = launch_form_srv.write_launch_forms_to_db(data_list)
        ins_objs = ins_objs if ins_objs else 0
        upd_rows = upd_rows if upd_rows else 0
        return ins_objs, upd_rows


class BatchTransferTimeSheetTemplate(BatchTransferEntitiesTemplate):

    def delete_data(self, workspace_id, deleted):
        timesheet_srv = TapdTimeSheetSrv()
        delete_rows = timesheet_srv.delete_timesheet_by_workspace_id(workspace_id, deleted)
        return delete_rows

    def get_list(self, workspace_id, modified, current_page, iteration_id):
        timesheet_srv = TapdTimeSheetSrv()
        data_list, next_page = timesheet_srv.get_time_sheets_by_workspace_id(workspace_id, modified, current_page)
        return data_list, next_page

    def save_list(self, data_list):
        timesheet_srv = TapdTimeSheetSrv()
        ins_objs, upd_rows = timesheet_srv.write_time_sheets_to_db(data_list)
        ins_objs = ins_objs if ins_objs else 0
        upd_rows = upd_rows if upd_rows else 0
        return ins_objs, upd_rows


class BatchTransferIterationTemplate(BatchTransferEntitiesTemplate):
    def delete_data(self, workspace_id, deleted):
        # todo 迭代没有删除接口
        return 0

    def get_list(self, workspace_id, modified, current_page, iteration_id):
        iteration_srv = TapdIterationSrv()
        data_list, next_page = iteration_srv.get_iterations_by_workspace_id(workspace_id, modified, iteration_id,
                                                                            current_page)
        return data_list, next_page

    def save_list(self, data_list):
        iteration_srv = TapdIterationSrv()

        ins_objs, upd_rows = iteration_srv.write_iterations_to_db(data_list)
        ins_objs = ins_objs if ins_objs else 0
        upd_rows = upd_rows if upd_rows else 0
        return ins_objs, upd_rows


class BatchTransferTestPlanTemplate(BatchTransferEntitiesTemplate):

    def delete_data(self, workspace_id, created):
        # 测试计划用创建时间来进行删除逻辑
        test_plan_srv = TapdTestPlanSrv()
        delete_rows = test_plan_srv.delete_test_plans_by_workspace_id(workspace_id, created)
        return delete_rows

    def get_list(self, workspace_id, modified, current_page, iteration_id):
        test_plan_srv = TapdTestPlanSrv()
        data_list, next_page = test_plan_srv.get_test_plans_by_workspace_id(workspace_id, modified, iteration_id,
                                                                            current_page)
        return data_list, next_page

    def save_list(self, data_list):
        test_plan_srv = TapdTestPlanSrv()
        ins_objs, upd_rows = test_plan_srv.write_test_plans_to_db(data_list)
        ins_objs = ins_objs if ins_objs else 0
        upd_rows = upd_rows if upd_rows else 0
        return ins_objs, upd_rows


class GetIterationFromStrategy(StoreEntitiesFromTapdStrategy):

    def __init__(self, workspace_id, modified, iteration_id):
        self.workspace_id = workspace_id
        self.modified = modified
        self.iteration_id = iteration_id

    def sync_data_from_tapd(self):
        log.info("迭代数据同步到实体")
        current_page = int(TAPD["req_page"])
        ins_batch_size = 0
        upd_batch_size = 0
        btst = BatchTransferIterationTemplate()
        del_rows = btst.delete_data(self.workspace_id, self.modified)
        while True:
            data_list, next_page = btst.get_list(self.workspace_id, self.modified, current_page, self.iteration_id)
            ins_size, upd_size = btst.save_list(data_list)
            ins_batch_size += ins_size
            upd_batch_size += upd_size
            if current_page == next_page:
                break
            else:
                current_page = next_page
        return ins_batch_size, upd_batch_size, del_rows


class GetBugTapdStrategy(StoreEntitiesFromTapdStrategy):

    def __init__(self, workspace_id, modified, iteration_id):
        self.workspace_id = workspace_id
        self.modified = modified
        self.iteration_id = iteration_id

    def sync_data_from_tapd(self):
        log.info("缺陷同步")
        current_page = int(TAPD["req_page"])
        ins_batch_size = 0
        upd_batch_size = 0
        btst = BatchTransferBugTemplate()
        del_rows = btst.delete_data(self.workspace_id, self.modified)
        while True:
            data_list, next_page = btst.get_list(self.workspace_id, self.modified, current_page, self.iteration_id)
            ins_size, upd_size = btst.save_list(data_list)
            ins_batch_size += ins_size
            upd_batch_size += upd_size
            if current_page == next_page:
                break
            else:
                current_page = next_page
        return ins_batch_size, upd_batch_size, del_rows


class GetStoryFromTapdStrategy(StoreEntitiesFromTapdStrategy):

    def __init__(self, workspace_id, modified, iteration_id):
        self.workspace_id = workspace_id
        self.modified = modified
        self.iteration_id = iteration_id

    def sync_data_from_tapd(self):
        log.info("需求同步")
        current_page = int(TAPD["req_page"])
        ins_batch_size = 0
        upd_batch_size = 0
        btst = BatchTransferStoryTemplate()
        del_rows = btst.delete_data(self.workspace_id, self.modified)
        while True:
            data_list, next_page = btst.get_list(self.workspace_id, self.modified, current_page, self.iteration_id)
            ins_size, upd_size = btst.save_list(data_list)
            ins_batch_size += ins_size
            upd_batch_size += upd_size
            if current_page == next_page:
                break
            else:
                current_page = next_page
        return ins_batch_size, upd_batch_size, del_rows


class GetTestPlanFromTapdStrategy(StoreEntitiesFromTapdStrategy):

    def __init__(self, workspace_id, modified, iteration_id):
        self.workspace_id = workspace_id
        self.modified = modified
        self.iteration_id = iteration_id

    def sync_data_from_tapd(self):
        log.info("测试计划同步")
        current_page = int(TAPD["req_page"])
        ins_batch_size = 0
        upd_batch_size = 0
        bttpt = BatchTransferTestPlanTemplate()
        del_rows = bttpt.delete_data(self.workspace_id, self.modified)
        while True:
            data_list, next_page = bttpt.get_list(self.workspace_id, self.modified, current_page, self.iteration_id)
            ins_size, upd_size = bttpt.save_list(data_list)
            ins_batch_size += ins_size
            upd_batch_size += upd_size
            self.sync_test_plan_relative_story(self.workspace_id, data_list)
            if current_page == next_page:
                break
            else:
                current_page = next_page
        return ins_batch_size, upd_batch_size, del_rows

    @staticmethod
    def sync_test_plan_relative_story(workspace_id, data_list):
        for test_plan_tmp in data_list:
            test_plan_obj = test_plan_tmp.get('TestPlan')
            tapd_test_plan_id = test_plan_obj.get('id')

            api_params = {
                "workspace_id": workspace_id,
                "test_plan_id": tapd_test_plan_id
            }

            module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.TEST_PLAN, workspace_id)
            tapd_data = TapdReqMgt().get_tapd_data_by_url(module_url + "/get_relative_stories",
                                                          api_params)
            story_ids_str = ",".join(tapd_data.get('story_ids')) if tapd_data.get('story_ids') else None

            try:
                TapdEntryTestPlanBindStory.objects.update_or_create(
                    tapd_test_plan_id=tapd_test_plan_id,
                    defaults={
                        "relative_dev_story_ids": story_ids_str,
                        "update_time": datetime.datetime.now()
                    }
                )
            except Exception as e:
                log.error("同步测试计划关联需求失败,测试计划id为：{}".format(tapd_test_plan_id))
                log.error(str(e))


class GetTaskFromTapdStrategy(StoreEntitiesFromTapdStrategy):

    def __init__(self, workspace_id, modified, iteration_id):
        self.workspace_id = workspace_id
        self.modified = modified
        self.iteration_id = iteration_id

    def sync_data_from_tapd(self):
        log.info("任务数据同步到实体")
        current_page = int(TAPD["req_page"])
        ins_batch_size = 0
        upd_batch_size = 0
        btst = BatchTransferTaskTemplate()
        del_rows = btst.delete_data(self.workspace_id, self.modified)
        while True:
            data_list, next_page = btst.get_list(self.workspace_id, self.modified, current_page, self.iteration_id)
            ins_size, upd_size = btst.save_list(data_list)
            ins_batch_size += ins_size
            upd_batch_size += upd_size
            if current_page == next_page:
                break
            else:
                current_page = next_page
        return ins_batch_size, upd_batch_size, del_rows


class GetLaunchFormFromTapdStrategy(StoreEntitiesFromTapdStrategy):

    def __init__(self, workspace_id, modified, iteration_id):
        self.workspace_id = workspace_id
        self.modified = modified
        self.iteration_id = iteration_id

    def sync_data_from_tapd(self):
        log.info("发布评审同步")
        current_page = int(TAPD["req_page"])
        ins_batch_size = 0
        upd_batch_size = 0
        btst = BatchTransferLaunchFormTemplate()
        del_rows = btst.delete_data(self.workspace_id, self.modified)
        while True:
            data_list, next_page = btst.get_list(self.workspace_id, self.modified, current_page, self.iteration_id)
            ins_size, upd_size = btst.save_list(data_list)
            ins_batch_size += ins_size
            upd_batch_size += upd_size
            if current_page == next_page:
                break
            else:
                current_page = next_page
        return ins_batch_size, upd_batch_size, del_rows


class GetTimeSheetFromTapdStrategy(StoreEntitiesFromTapdStrategy):

    def __init__(self, workspace_id, modified, iteration_id):
        self.workspace_id = workspace_id
        self.modified = modified
        self.iteration_id = iteration_id

    def sync_data_from_tapd(self):
        log.info("工时同步")
        current_page = int(TAPD["req_page"])
        ins_batch_size = 0
        upd_batch_size = 0
        btst = BatchTransferTimeSheetTemplate()
        del_rows = btst.delete_data(self.workspace_id, self.modified)
        while True:
            data_list, next_page = btst.get_list(self.workspace_id, self.modified, current_page, self.iteration_id)
            ins_size, upd_size = btst.save_list(data_list)
            ins_batch_size += ins_size
            upd_batch_size += upd_size
            if current_page == next_page:
                break
            else:
                current_page = next_page
        return ins_batch_size, upd_batch_size, del_rows


class StrategyContext:
    def __init__(self, strategy):
        self.strategy = strategy

    def execute_strategy(self):
        return self.strategy.sync_data_from_tapd()
