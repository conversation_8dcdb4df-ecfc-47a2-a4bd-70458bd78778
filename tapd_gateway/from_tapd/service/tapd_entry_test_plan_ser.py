from tapd_gateway.from_tapd.dao.tapd_req_dao import get_dev_testings, get_test_plan_by_iteration_ids, \
    get_dev_submit_id_by_story_id


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class TapdEntityTestPlanSer:

    def get_biz_dev_testing(self):
        cursor = get_dev_testings()
        dict_list = dict_fetchall(cursor)

        return dict_list

    def get_test_plan_by_iteration_ids(self, iteration_ids):
        iteration_ids_str = "', '".join(str(id) for id in iteration_ids)
        cursor = get_test_plan_by_iteration_ids(iteration_ids_str)
        data_list = dict_fetchall(cursor)
        return data_list

    def get_dev_submit_id_by_story_id(self, story_id):
        cursor = get_dev_submit_id_by_story_id(story_id)
        data_list = dict_fetchall(cursor)
        return data_list
