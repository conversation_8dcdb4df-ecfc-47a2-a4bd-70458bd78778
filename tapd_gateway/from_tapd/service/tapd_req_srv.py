import json
import enum
import time
import datetime
from datetime import timedel<PERSON>

from django.db import transaction

from dev_effective.utils.biz_utils import TapdUtils
from mantis.settings import TAPD
from mantis.settings import logger as log

from tapd_gateway.from_tapd.model.models import TapdSyncBatch, TapdEntryLaunchForm, TapdEntryTestPlanBindStory, \
    TapdEntryIterationResJson, TapdEntryBugResJson, TapdEntryStoryResJson, TapdEntryTestPlanResJson, \
    TapdEntryTaskResJson, TapdEntryLaunchFormResJson, TapdEntryTimesheetResJson
from tapd_gateway.from_tapd.model.models import TapdEntryBug, TapdEntryIteration, TapdEntryStory, TapdEntryTestPlan
from tapd_gateway.from_tapd.model.models import TapdEntryTask, TapdEntryTimeSheet
from tapd_gateway.from_tapd.service.tapd_req_exp import TapdModuleNoneExp, TapdModuleUrlNoneExp
from tapd_gateway.from_tapd.service.tapd_req_exp import TapdBatchRetryMaxExp, TapdBatchLoopMaxExp, TapdDBDataExp
from tapd_gateway.from_tapd.dao.tapd_req_dao import get_tapd_workspace_module, get_max_idx_by_time_batch
from tapd_gateway.tapd_req_mgt.utils.tapd_req_mgt import TapdReqMgt


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


def get_now_time():
    return datetime.datetime.now()


class ModifyTime:

    @staticmethod
    def modify_time(nowTime, date_range=None):
        if nowTime:
            if date_range:
                #     当前所在日期
                now_date = datetime.datetime.now().date()
                start_date = datetime.datetime.strptime(nowTime, '%Y-%m-%d %H:%M:%S').date() - timedelta(
                    days=date_range)
                return str(start_date) + '~' + str(now_date)
            else:
                # 获取当前时间
                current_time = datetime.datetime.strptime(nowTime, '%Y-%m-%d %H:%M:%S')
                # 获取凌晨0点的时间
                midnight = datetime.datetime.combine(current_time.date(), datetime.time.min)
                # 获取1点的时间
                one_am = datetime.datetime.combine(current_time.date(), datetime.time(1))
                # 判断当前时间是否在凌晨0点至1点之间
                if midnight <= current_time < one_am:
                    # 将当前时间更改为前一天的0点0分
                    modified_time = current_time - timedelta(days=1)
                    modified_time = datetime.datetime.combine(modified_time.date(), datetime.time.min)
                    return str(modified_time)

                return str(current_time)


@enum.unique
class TapdModuleEnum(enum.Enum):
    """
    tapd模块枚举
    """
    WORKSPACE = ("workspace", "项目")
    ITERATION = ("iteration", "迭代")
    STORY = ("story", "需求")
    TASK = ("task", "任务")
    BUG = ("bug", "缺陷")
    LAUNCH_FORM = ("launch_form", "发布评审")
    TEST_PLAN = ("test_plan", "测试计划")
    TIMESHEET = ("timesheet", "工时")

    def __init__(self, module_name, module_desc):
        self.module_name = module_name
        self.module_desc = module_desc


@enum.unique
class TapdSyncStatusEnum(enum.Enum):
    """
    tapd模块枚举
    """
    READY = ("Ready", "准备好")
    RUNNING = ("Running", "运行中")
    COMPLETED = ("Completed", "已完成")

    def __init__(self, status_en_name, status_cn_name):
        self.status_en_name = status_en_name
        self.status_cn_name = status_cn_name


class TapdBaseSrv:

    @staticmethod
    def get_def_modified(s_time):
        # 判断s_time是否是日期类型的
        if isinstance(s_time, str):
            s_time = datetime.datetime.strptime(s_time, "%Y-%m-%d %H:%M:%S")
        start_time = s_time - timedelta(minutes=10)
        start_time_minute = start_time.minute - start_time.minute % 10
        start_time = start_time.replace(minute=start_time_minute, second=0, microsecond=0)
        return start_time


class TapdModuleSrv:
    def __init__(self, ):
        """
        Tapd模块服务
        """

    def get_module_url(self, module_enum, workspace_id):
        """
        获取tapd模块url
        """
        cursor = get_tapd_workspace_module(module_enum.module_name, workspace_id)
        dict_list = dict_fetchall(cursor)
        if not dict_list:
            raise TapdModuleNoneExp()
        module_dict = dict_list[0]
        module_url = module_dict.get("module_url")
        if not module_url:
            raise TapdModuleUrlNoneExp()

        url = "{}/{}".format(TAPD["host_url"], module_url)

        return url


class TapdBatchSrv:
    def __init__(self, ):
        """
        Tapd批次服务
        """

    def get_batch_num(self, start_time=None):
        """
        获取tapd模块url
        """
        if not start_time:
            start_time = get_now_time()

        curr_minutes = start_time.minute
        batch_minutes = curr_minutes - curr_minutes % 10
        batch_time = start_time.replace(minute=batch_minutes, second=0, microsecond=0)

        time_batch_int = int(batch_time.strftime("%Y%m%d%H%M%S"))
        batch_num = self.__write_batch_num_loop(time_batch_int, start_time)

        return batch_num

    def __write_batch_num_loop(self, time_batch, start_time):
        batch_num = None

        for i in range(3):
            dict_list = dict_fetchall(get_max_idx_by_time_batch(time_batch))
            if not dict_list:
                raise TapdDBDataExp("获取tapd_sync_batch失败")
            obj_dict = dict_list[0]
            max_idx_batch = obj_dict.get("max_idx_batch")
            next_id_batch = None
            try:
                ins_obj_id, next_id_batch = self.__write_batch_num_retry(time_batch, max_idx_batch, start_time)
            except TapdBatchRetryMaxExp:
                log.error(">>>>>获取batch_num失败，retry失败：{}".format(i))

            if next_id_batch:
                batch_num = time_batch * 10000 + next_id_batch
                break

        if not batch_num:
            raise TapdBatchLoopMaxExp()

        return batch_num

    def __write_batch_num_retry(self, time_batch, max_idx_batch, start_time=None, retry=0):
        if not retry:
            retry = 0
        else:
            if retry < int(TAPD["retry_max"]):
                time.sleep(retry)
            else:
                raise TapdBatchRetryMaxExp()

        next_id_batch = max_idx_batch + 1
        try:
            ins_obj_id = self.__write_batch_num_to_db(time_batch, next_id_batch, start_time)
        except Exception as e:
            log.error(e)
            ins_obj_id, next_id_batch = self.__write_batch_num_retry(time_batch, next_id_batch, start_time, retry + 1)

        return ins_obj_id, next_id_batch

    def __write_batch_num_to_db(self, time_batch, idx_batch, start_time=None):
        ins_obj_id = None
        if not start_time:
            start_time = get_now_time()

        create_user = TAPD["sync_user"]
        create_time = start_time
        update_user = create_user
        update_time = create_time
        stamp = 0

        opt = TapdSyncBatch()
        opt.create_user = create_user
        opt.create_time = create_time
        opt.update_user = update_user
        opt.update_time = update_time
        opt.stamp = stamp
        opt.time_batch = time_batch
        opt.idx_batch = idx_batch
        opt.sync_status = TapdSyncStatusEnum.READY.status_en_name

        ins_obj = TapdSyncBatch.objects.create(
            create_user=create_user,
            create_time=create_time,
            update_user=update_user,
            update_time=update_time,
            stamp=stamp,
            time_batch=time_batch,
            idx_batch=idx_batch,
            sync_status=TapdSyncStatusEnum.READY.status_en_name,
        )
        if ins_obj:
            ins_obj_id = ins_obj.id
            # log.info(">>>>>tapd_sync_batch_id = {}".format(ins_obj_id))

        return ins_obj_id


class TapdBugSrv(TapdBaseSrv):
    def __init__(self):
        """
        Tapd缺陷服务
        """

    def delete_bugs_by_workspace_id(self, workspace_id, deleted):
        current_page = int(TAPD["req_page"])
        page_size = int(TAPD["req_limit"])
        api_params = {
            "workspace_id": workspace_id,
            "page": current_page,
            "limit": page_size
        }

        if deleted:
            date_range = ModifyTime().modify_time(deleted, 1)
            api_params["modified"] = date_range

        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.BUG, workspace_id)
        total_delete_bug_list = []
        while True:
            api_params["page"] = current_page
            delete_bug_list = TapdReqMgt().get_tapd_data_by_url(module_url + "/get_removed_bugs",
                                                                api_params)
            total_delete_bug_list.extend(delete_bug_list)
            if len(delete_bug_list) == 200:
                current_page += 1
            else:
                break

        upd_rows = None
        if total_delete_bug_list:
            bug_map = {}
            for bug_tmp in total_delete_bug_list:
                bug_obj = bug_tmp.get('RemovedBug')
                tapd_bug_id = bug_obj.get('id')
                if not tapd_bug_id:
                    err_msg = ">>>>> tapd bug id为空：{}".format(
                        json.dumps(bug_obj, indent=4, sort_keys=True, ensure_ascii=False))
                    log.error(err_msg)
                    continue
                bug_map[tapd_bug_id] = bug_obj
            if bug_map:
                tapd_bug_list = TapdEntryBug.objects.filter(tapd_entry_id__in=bug_map.keys())
                db_bug_id_map = {bug.tapd_entry_id: bug.id for bug in tapd_bug_list}
                del_batch_list = []
                for k, v in bug_map.items():
                    k = int(k)
                    db_id = None
                    if k in db_bug_id_map.keys():
                        db_id = db_bug_id_map.get(k)
                    # 属性获取
                    if db_id:
                        opt = TapdEntryBug()
                        opt.tapd_workspace_id = workspace_id
                        opt.tapd_entry_id = v.get('id')
                        opt.del_user = v.get('operation_user')
                        opt.del_time = v.get('modified')
                        opt.id = db_id
                        opt.entry_status = 3
                        del_batch_list.append(opt)

                if del_batch_list:
                    upd_fields = [
                        'del_user',
                        'del_time',
                        'entry_status',
                    ]
                    upd_rows = TapdEntryBug.objects.bulk_update(del_batch_list, upd_fields)
                    log.info(">>>>>删除tapd bug：{}".format(upd_rows))
        return upd_rows

    def get_bugs_by_workspace_id(self, workspace_id, modified, current_page, iteration_id):
        """
        tapd get请求
        """
        page_size = int(TAPD["req_limit"])
        api_params = {
            "workspace_id": workspace_id,
            "page": current_page,
            "limit": page_size
        }
        if iteration_id:
            api_params['iteration_id'] = iteration_id
        if modified:
            api_params['modified'] = ">{}".format(modified)
        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.BUG, workspace_id)
        bug_list = TapdReqMgt().get_tapd_data_by_url(module_url, api_params)
        count_num = len(bug_list) if len(bug_list) > 0 else 0
        next_page = current_page if count_num < page_size else current_page + 1
        return bug_list, next_page

    @staticmethod
    def write_bugs_to_db(bug_list, start_time=None):
        ins_objs = None
        upd_rows = None

        if not start_time:
            start_time = get_now_time()

        batch_number = TapdBatchSrv().get_batch_num(start_time)
        bug_map = {}
        if bug_list:
            for bug_tmp in bug_list:
                bug_obj = bug_tmp.get('Bug')
                tapd_entry_id = bug_obj.get('id')

                if not tapd_entry_id:
                    err_msg = ">>>>> tapd缺陷id为空：{}".format(
                        json.dumps(bug_obj, indent=4, sort_keys=True, ensure_ascii=False))
                    log.error(err_msg)
                    continue
                bug_map[tapd_entry_id] = bug_obj

            if bug_map:
                ins_objs, upd_rows = TapdBugSrv.__create_or_update_with_map(bug_map, batch_number, start_time)
        return ins_objs, upd_rows

    @staticmethod
    def __create_or_update_with_map(bug_map, batch_number, opt_time=None):
        if bug_map:
            if not opt_time:
                opt_time = get_now_time()

            create_user = TAPD["sync_user"]
            create_time = opt_time
            update_user = TAPD["sync_user"]
            update_time = opt_time
            stamp = 0

            tapd_entry_bug_list = TapdEntryBug.objects.filter(tapd_entry_id__in=bug_map.keys())

            db_bug_id_map = {bug.tapd_entry_id: bug.id for bug in tapd_entry_bug_list}
            ins_batch_list = []
            ins_json_batch_list = []
            upd_batch_list = []
            upd_json_batch_list = []
            for k, v in bug_map.items():
                k = int(k)
                db_id = None
                if k in db_bug_id_map.keys():
                    db_id = db_bug_id_map.get(k)
                # 属性获取
                tapd_workspace_id = v.get('workspace_id')
                tapd_iteration_id = v.get('iteration_id')
                tapd_entry_id = v.get('id')
                tapd_bug_title = v.get('title')
                tapd_bug_status = v.get('status')
                entry_status = 2
                upd_batch_number = None
                ins_batch_number = None
                if tapd_bug_status == "new":
                    entry_status = 1
                    ins_batch_number = batch_number

                opt = TapdEntryBug()
                opt.update_user = update_user
                opt.update_time = update_time
                opt.entry_status = entry_status
                opt.upd_batch_number = upd_batch_number
                # 变化的属性
                opt.tapd_bug_title = tapd_bug_title
                opt.tapd_bug_status = tapd_bug_status
                opt.tapd_workspace_id = tapd_workspace_id
                opt.tapd_bug_iteration_id = tapd_iteration_id
                opt.tapd_bug_priority = v.get('priority')
                opt.tapd_bug_created = v.get('created')
                opt.tapd_bug_modified = v.get('modified')
                opt.tapd_bug_severity = v.get('severity')
                opt.tapd_bug_bugtype = v.get('bugtype')
                fixer = TapdUtils.str_split(v.get('fixer'), '_')
                opt.tapd_bug_fixer = fixer
                opt.tapd_bug_resolved = v.get('resolved')
                opt.tapd_bug_closed = v.get('closed')
                opt.tapd_bug_flows = v.get('flows')
                opt.tapd_bug_reporter = v.get('reporter')
                opt.tapd_bug_current_owner = v.get('current_owner')
                opt.tapd_bug_custom_field_one = v.get('custom_field_one')
                opt.tapd_bug_custom_field_two = v.get('custom_field_two')
                opt.tapd_bug_custom_field_three = v.get('custom_field_three')
                opt.tapd_bug_custom_field_four = v.get('custom_field_four')
                opt.tapd_bug_custom_field_five = v.get('custom_field_five')
                opt.tapd_bug_custom_field_6 = v.get('custom_field_6')
                opt.tapd_bug_custom_field_7 = v.get('custom_field_7')
                opt.tapd_bug_custom_field_8 = v.get('custom_field_8')
                opt.tapd_bug_custom_field_9 = v.get('custom_field_9')
                opt.tapd_bug_custom_field_10 = v.get('custom_field_10')
                opt.tapd_bug_custom_field_11 = v.get('custom_field_11')
                opt.tapd_bug_custom_field_12 = v.get('custom_field_12')
                opt.tapd_bug_custom_field_13 = v.get('custom_field_13')
                opt.tapd_bug_custom_field_14 = v.get('custom_field_14')
                opt.tapd_bug_custom_field_15 = v.get('custom_field_15')

                obj = TapdEntryBugResJson.objects.filter(tapd_entry_id=tapd_entry_id).values('id').first()
                res_json_obj = TapdEntryBugResJson()
                if obj and obj.get('id'):
                    res_json_obj.id = obj.get('id')
                res_json_obj.tapd_entry_id = tapd_entry_id
                res_json_obj.res_json = v

                # 继续填充。。。
                if not db_id:
                    opt.create_user = create_user
                    opt.create_time = create_time
                    opt.stamp = stamp
                    opt.ins_batch_number = ins_batch_number
                    # 不会变的属性
                    opt.tapd_workspace_id = tapd_workspace_id
                    opt.tapd_entry_id = tapd_entry_id
                    opt.tapd_bug_iteration_id = tapd_iteration_id

                    ins_batch_list.append(opt)
                    ins_json_batch_list.append(res_json_obj)

                else:
                    opt.id = db_id
                    opt.upd_batch_number = batch_number
                    upd_batch_list.append(opt)
                    upd_json_batch_list.append(res_json_obj)

            if ins_batch_list:
                TapdEntryBug.objects.bulk_create(ins_batch_list)
            if upd_batch_list:
                upd_fields = [
                    'update_user',
                    'update_time',
                    'entry_status',
                    'upd_batch_number',
                    'tapd_bug_title',
                    'tapd_bug_status',
                    'tapd_workspace_id',
                    'tapd_bug_iteration_id',
                    'tapd_bug_severity',
                    'tapd_bug_bugtype',
                    'tapd_bug_reporter',
                    'tapd_bug_fixer',
                    'tapd_bug_priority',
                    'tapd_bug_created',
                    'tapd_bug_modified',
                    'tapd_bug_current_owner',
                    'tapd_bug_resolved',
                    'tapd_bug_closed',
                    'tapd_bug_flows',
                    'tapd_bug_custom_field_one',
                    'tapd_bug_custom_field_two',
                    'tapd_bug_custom_field_three',
                    'tapd_bug_custom_field_four',
                    'tapd_bug_custom_field_five',
                    'tapd_bug_custom_field_6',
                    'tapd_bug_custom_field_7',
                    'tapd_bug_custom_field_10',
                    'tapd_bug_custom_field_11',
                    'tapd_bug_custom_field_12',
                    'tapd_bug_custom_field_13',
                    'tapd_bug_custom_field_14',
                    'tapd_bug_custom_field_15',

                ]
                TapdEntryBug.objects.bulk_update(upd_batch_list, upd_fields)
            for obj in ins_json_batch_list + upd_json_batch_list:
                TapdEntryBugResJson.objects.update_or_create(defaults={'res_json': obj.res_json},
                                                             tapd_entry_id=obj.tapd_entry_id)
                # log.info(">>>>>更新tapd缺陷：{}".format(upd_rows))
            return len(ins_batch_list), len(upd_batch_list)

    """
    创建产线bug
    """

    def create_bug_by_workspace_id(self, workspace_id, api_params):
        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.BUG, workspace_id)
        result = TapdReqMgt().tapd_post(module_url, api_params)
        return result


class TapdIterationSrv:
    def __init__(self):
        """
        Tapd迭代服务
        """

    def get_iterations_by_workspace_id(self, workspace_id, modified, iteration_id, current_page):
        """
        tapd get请求
        """
        page_size = int(TAPD["req_limit"])
        api_params = {
            "workspace_id": workspace_id,
            "page": current_page,
            "limit": page_size
        }
        if modified:
            api_params["modified"] = ">{}".format(modified)

        if iteration_id:
            api_params["iteration_id"] = iteration_id
        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.ITERATION, workspace_id)
        iteration_list = TapdReqMgt().get_tapd_data_by_url(module_url, api_params)
        count_num = len(iteration_list) if len(iteration_list) > 0 else 0
        next_page = current_page if count_num < page_size else current_page + 1
        return iteration_list, next_page

    def create_iteration(self, workspace_id, api_params):
        startdate = api_params.get('startdate')
        if not startdate:
            api_params['startdate'] = datetime.now().strftime("%Y-%m-%d")
        enddate = api_params.get('enddate')
        if not enddate:
            api_params['enddate'] = datetime.now().strftime("%Y-%m-%d")
        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.ITERATION, workspace_id)
        iteration_result = TapdReqMgt().tapd_post(module_url, api_params)
        return iteration_result

    @staticmethod
    def get_def_modified(s_time):
        start_time = s_time - timedelta(minutes=10)
        start_time_minute = start_time.minute - start_time.minute % 10
        start_time = start_time.replace(minute=start_time_minute, second=0, microsecond=0)
        return start_time

    @staticmethod
    def write_iterations_to_db(iteration_list, start_time=None):
        ins_objs = None
        upd_rows = None

        if not start_time:
            start_time = get_now_time()
        batch_number = TapdBatchSrv().get_batch_num(start_time)
        iteration_map = {}
        if iteration_list:
            for bug_tmp in iteration_list:
                iteration_obj = bug_tmp.get('Iteration')
                tapd_iteration_id = iteration_obj.get('id')

                if not tapd_iteration_id:
                    err_msg = ">>>>> tapd迭代id为空：{}".format(
                        json.dumps(iteration_obj, indent=4, sort_keys=True, ensure_ascii=False))
                    log.error(err_msg)
                    continue
                iteration_map[tapd_iteration_id] = iteration_obj

            if iteration_map:
                ins_objs, upd_rows = TapdIterationSrv.__create_or_update_with_map(iteration_map, batch_number,
                                                                                  start_time)
        return ins_objs, upd_rows

    @staticmethod
    def __create_or_update_with_map(iteration_map, batch_number, opt_time=None):
        if iteration_map:
            if not opt_time:
                opt_time = get_now_time()

            create_user = TAPD["sync_user"]
            create_time = opt_time
            update_user = TAPD["sync_user"]
            update_time = opt_time
            stamp = 0

            tapd_iteration_list = TapdEntryIteration.objects.filter(tapd_entry_id__in=iteration_map.keys())

            db_iteration_id_map = {iteration.tapd_entry_id: iteration.id for iteration in tapd_iteration_list}
            ins_batch_list = []
            ins_json_batch_list = []
            upd_batch_list = []
            upd_json_batch_list = []
            for k, v in iteration_map.items():
                k = int(k)
                db_id = None
                if k in db_iteration_id_map.keys():
                    db_id = db_iteration_id_map.get(k)
                # 属性获取
                tapd_workspace_id = v.get('workspace_id')
                tapd_entry_id = v.get('id')
                tapd_iteration_name = v.get('name')
                tapd_iteration_status = v.get('status')
                tapd_startdate = v.get('startdate')
                tapd_enddate = v.get('enddate')

                opt = TapdEntryIteration()
                opt.update_user = update_user
                opt.update_time = update_time
                # 变化的属性
                opt.tapd_iteration_name = tapd_iteration_name
                opt.tapd_iteration_startdate = tapd_startdate
                opt.tapd_iteration_enddate = tapd_enddate
                opt.tapd_iteration_status = tapd_iteration_status
                opt.tapd_iteration_modified = v.get('modified')
                opt.tapd_iteration_completed = v.get('completed')
                opt.tapd_iteration_custom_field_3 = v.get('custom_field_3')
                opt.tapd_iteration_custom_field_5 = v.get('custom_field_5')
                opt.tapd_iteration_creator = v.get('creator').replace('"', '')

                obj = TapdEntryIterationResJson.objects.filter(tapd_entry_id=tapd_entry_id).values('id').first()
                res_json_obj = TapdEntryIterationResJson()
                if obj and obj.get('id'):
                    res_json_obj.id = obj.get('id')
                res_json_obj.tapd_entry_id = tapd_entry_id
                res_json_obj.res_json = v

                # 继续填充。。。
                if not db_id:
                    opt.create_user = create_user
                    opt.create_time = create_time
                    opt.stamp = stamp
                    opt.ins_batch_number = batch_number
                    opt.entry_status = 1
                    # 不会变的属性
                    opt.tapd_workspace_id = tapd_workspace_id
                    opt.tapd_entry_id = tapd_entry_id

                    ins_batch_list.append(opt)
                    ins_json_batch_list.append(res_json_obj)
                else:
                    opt.upd_batch_number = batch_number
                    opt.id = db_id
                    opt.entry_status = 2
                    opt.del_time = None
                    opt.del_user = None
                    upd_batch_list.append(opt)
                    upd_json_batch_list.append(res_json_obj)

            if ins_batch_list:
                TapdEntryIteration.objects.bulk_create(ins_batch_list)
            if upd_batch_list:
                upd_fields = [
                    'update_user',
                    'update_time',
                    'entry_status',
                    'del_time',
                    'del_user',
                    'tapd_iteration_name',
                    'tapd_iteration_startdate',
                    'tapd_iteration_enddate',
                    'tapd_iteration_status',
                    'tapd_iteration_status',
                    'upd_batch_number',
                    'tapd_iteration_creator',
                    'tapd_iteration_modified',
                    'tapd_iteration_completed',
                ]
                TapdEntryIteration.objects.bulk_update(upd_batch_list, upd_fields)
            for obj in ins_json_batch_list + upd_json_batch_list:
                TapdEntryIterationResJson.objects.update_or_create(defaults={'res_json': obj.res_json},
                                                                   tapd_entry_id=obj.tapd_entry_id)

            return len(ins_batch_list), len(upd_batch_list)


class TapdStorySrv:
    def __init__(self, ):
        """
        Tapd需求服务
        """

    def create_story_by_workspace_id(self, workspace_id, api_params):
        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.STORY, workspace_id)
        story_result = TapdReqMgt().tapd_post(module_url, api_params)
        return story_result

    def delete_stories_by_workspace_id(self, workspace_id, deleted):
        current_page = int(TAPD["req_page"])
        page_size = int(TAPD["req_limit"])
        api_params = {
            "workspace_id": workspace_id,
            "page": current_page,
            "limit": page_size
        }

        if deleted:
            date_range = ModifyTime().modify_time(deleted, 1)
            api_params["deleted"] = date_range

        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.STORY, workspace_id)
        total_delete_story_list = []
        while True:
            api_params["page"] = current_page
            delete_story_list = TapdReqMgt().get_tapd_data_by_url(module_url + "/get_removed_stories",
                                                                  api_params)
            total_delete_story_list.extend(delete_story_list)
            if len(delete_story_list) == 200:
                current_page += 1
            else:
                break

        upd_rows = None
        if total_delete_story_list:
            story_map = {}
            for story_tmp in total_delete_story_list:
                story_obj = story_tmp.get('RemovedStory')
                tapd_story_id = story_obj.get('id')
                if not tapd_story_id:
                    err_msg = ">>>>> tapd需求id为空：{}".format(
                        json.dumps(story_obj, indent=4, sort_keys=True, ensure_ascii=False))
                    log.error(err_msg)
                    continue
                story_map[tapd_story_id] = story_obj
            if story_map:
                tapd_story_list = TapdEntryStory.objects.filter(tapd_entry_id__in=story_map.keys())
                db_story_id_map = {story.tapd_entry_id: story.id for story in tapd_story_list}
                del_batch_list = []
                for k, v in story_map.items():
                    k = int(k)
                    db_id = None
                    if k in db_story_id_map.keys():
                        db_id = db_story_id_map.get(k)
                    # 属性获取
                    if db_id:
                        opt = TapdEntryStory()
                        opt.tapd_workspace_id = v.get('workspace_id')
                        opt.tapd_entry_id = v.get('id')
                        opt.del_user = v.get('operation_user')
                        opt.del_time = v.get('deleted')
                        opt.id = db_id
                        opt.entry_status = 3
                        del_batch_list.append(opt)

                if del_batch_list:
                    upd_fields = [
                        'del_user',
                        'del_time',
                        'entry_status',
                    ]
                    upd_rows = TapdEntryStory.objects.bulk_update(del_batch_list, upd_fields)
                    log.info(">>>>>删除tapd迭代：{}".format(upd_rows))
        return upd_rows

    def update_story_by_workspace_id(self, workspace_id, api_params):
        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.STORY, workspace_id)
        story_result = TapdReqMgt().tapd_post(module_url, api_params)
        return story_result

    def get_stories_by_workspace_id(self, workspace_id, current_page, modified=None, iteration_id=None, **kwargs):
        """
        tapd get请求
        """
        page_size = int(TAPD["req_limit"])
        api_params = {
            "workspace_id": workspace_id,
            "page": current_page,
            "limit": page_size
        }
        if modified:
            api_params['modified'] = ">{}".format(modified)
        if iteration_id:
            api_params['iteration_id'] = iteration_id
        if kwargs:
            for k, v in kwargs.items():
                api_params[k] = v

        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.STORY, workspace_id)
        story_list = TapdReqMgt().get_tapd_data_by_url(module_url, api_params)
        count_num = len(story_list) if len(story_list) > 0 else 0
        next_page = current_page if count_num < page_size else current_page + 1
        return story_list, next_page

    @staticmethod
    def write_stories_to_db(data_list, start_time=None):
        ins_objs = None
        upd_rows = None

        if not start_time:
            start_time = get_now_time()
        batch_number = TapdBatchSrv().get_batch_num(start_time)
        story_map = {}
        if data_list:
            for story_tmp in data_list:
                story_obj = story_tmp.get('Story')
                tapd_story_id = story_obj.get('id')
                if not tapd_story_id:
                    err_msg = ">>>>> tapd需求id为空：{}".format(
                        json.dumps(story_obj, indent=4, sort_keys=True, ensure_ascii=False))
                    log.error(err_msg)
                    continue
                story_map[tapd_story_id] = story_obj
            if story_map:
                ins_objs, upd_rows = TapdStorySrv.__create_or_update_with_map(story_map, batch_number,
                                                                              start_time)
        return ins_objs, upd_rows

    @staticmethod
    def __create_or_update_with_map(story_map, batch_number, opt_time=None):
        if story_map:
            if not opt_time:
                opt_time = get_now_time()

            create_user = TAPD["sync_user"]
            create_time = opt_time
            update_user = TAPD["sync_user"]
            update_time = opt_time
            stamp = 0

            tapd_story_list = TapdEntryStory.objects.filter(tapd_entry_id__in=story_map.keys())
            db_story_id_map = {story.tapd_entry_id: story.id for story in tapd_story_list}
            ins_batch_list = []
            ins_json_batch_list = []
            upd_batch_list = []
            upd_json_batch_list = []
            for k, v in story_map.items():
                k = int(k)
                db_id = None
                if k in db_story_id_map.keys():
                    db_id = db_story_id_map.get(k)
                # 属性获取
                tapd_workspace_id = v.get('workspace_id')
                tapd_entry_id = v.get('id')
                tapd_story_name = v.get('name')
                tapd_story_status = v.get('status')

                opt = TapdEntryStory()
                opt.update_user = update_user
                opt.update_time = update_time
                # 变化的属性
                opt.tapd_story_name = tapd_story_name
                opt.tapd_story_status = tapd_story_status
                opt.tapd_story_workitem_type_id = v.get('workitem_type_id')
                opt.tapd_story_priority = v.get('priority')
                developer = TapdUtils.str_split(v.get('developer'), ';')
                opt.tapd_story_developer = developer
                opt.tapd_story_iteration_id = v.get('iteration_id')
                owner = TapdUtils.str_split(v.get('owner'), ';')
                opt.tapd_story_owner = owner
                opt.tapd_story_cc = v.get('cc')
                opt.tapd_story_begin = v.get('begin')
                opt.tapd_story_due = v.get('due')
                opt.tapd_story_size = v.get('size')
                opt.tapd_story_modified = v.get('modified')
                opt.tapd_story_created = v.get('created')
                opt.tapd_story_type = v.get('type')
                opt.tapd_story_source = v.get('source')
                opt.tapd_story_module = v.get('module')
                opt.tapd_story_version = v.get('version')
                opt.tapd_story_completed = v.get('completed')
                opt.tapd_story_category_id = v.get('category_id')
                opt.tapd_story_path = v.get('path')
                opt.tapd_story_parent_id = v.get('parent_id')
                opt.tapd_story_children_id = v.get('children_id')
                opt.tapd_story_ancestor_id = v.get('ancestor_id')
                opt.tapd_story_level = v.get('level')
                opt.tapd_story_effort = v.get('effort')
                opt.tapd_story_effort_completed = v.get('effort_completed')
                opt.tapd_story_exceed = v.get('exceed')
                opt.tapd_story_remain = v.get('remain')
                opt.tapd_story_release_id = v.get('release_id')
                opt.tapd_story_bug_id = v.get('bug_id')
                opt.tapd_story_templated_id = v.get('templated_id')
                opt.tapd_story_created_from = v.get('created_from')
                opt.tapd_story_is_archived = v.get('is_archived')
                opt.tapd_story_creator = v.get('creator')
                opt.tapd_story_custom_field_one = v.get('custom_field_one')
                opt.tapd_story_custom_field_two = v.get('custom_field_two')
                opt.tapd_story_custom_field_three = v.get('custom_field_three')
                opt.tapd_story_custom_field_four = v.get('custom_field_four')
                opt.tapd_story_custom_field_five = v.get('custom_field_five')
                opt.tapd_story_custom_field_six = v.get('custom_field_six')
                opt.tapd_story_custom_field_seven = v.get("custom_field_seven")
                opt.tapd_story_custom_field_eight = v.get('custom_field_eight')
                opt.tapd_story_custom_field_9 = v.get('custom_field_9')
                opt.tapd_story_custom_field_10 = v.get('custom_field_10')
                opt.tapd_story_custom_field_11 = v.get('custom_field_11')
                opt.tapd_story_custom_field_12 = v.get('custom_field_12')
                opt.tapd_story_custom_field_13 = v.get('custom_field_13')
                opt.tapd_story_custom_field_14 = v.get('custom_field_14')
                opt.tapd_story_custom_field_15 = v.get('custom_field_15')
                opt.tapd_story_custom_field_16 = v.get('custom_field_16')
                opt.tapd_story_custom_field_17 = v.get('custom_field_17')
                opt.tapd_story_custom_field_18 = v.get('custom_field_18')
                opt.tapd_story_custom_field_19 = v.get('custom_field_19')
                opt.tapd_story_custom_field_20 = v.get('custom_field_20')
                opt.tapd_story_custom_field_21 = v.get('custom_field_21')
                opt.tapd_story_custom_field_22 = v.get('custom_field_22')
                opt.tapd_story_custom_field_23 = v.get('custom_field_23')
                opt.tapd_story_custom_field_24 = v.get('custom_field_24')
                opt.tapd_story_custom_field_25 = v.get('custom_field_25')
                opt.tapd_story_custom_field_26 = v.get('custom_field_26')
                opt.tapd_story_custom_field_27 = v.get('custom_field_27')
                opt.tapd_story_custom_field_28 = v.get('custom_field_28')
                opt.tapd_story_custom_field_29 = v.get('custom_field_29')
                opt.tapd_story_custom_field_30 = v.get('custom_field_30')
                opt.tapd_story_custom_field_50 = v.get('custom_field_50')

                obj = TapdEntryStoryResJson.objects.filter(tapd_entry_id=tapd_entry_id).values('id').first()
                res_json_obj = TapdEntryStoryResJson()
                if obj and obj.get('id'):
                    res_json_obj.id = obj.get('id')
                res_json_obj.tapd_entry_id = tapd_entry_id
                res_json_obj.res_json = v

                # 继续填充。。。
                if not db_id:
                    opt.create_user = create_user
                    opt.create_time = create_time
                    opt.stamp = stamp
                    opt.ins_batch_number = batch_number
                    opt.entry_status = 1
                    # 不会变的属性
                    opt.tapd_workspace_id = tapd_workspace_id
                    opt.tapd_entry_id = tapd_entry_id

                    ins_batch_list.append(opt)
                    ins_json_batch_list.append(res_json_obj)
                else:
                    opt.id = db_id
                    opt.entry_status = 2
                    opt.del_time = None
                    opt.del_user = None
                    opt.upd_batch_number = batch_number
                    upd_batch_list.append(opt)
                    upd_json_batch_list.append(res_json_obj)

            if ins_batch_list:
                TapdEntryStory.objects.bulk_create(ins_batch_list)

            if upd_batch_list:
                upd_fields = [
                    'update_user',
                    'update_time',
                    'tapd_story_name',
                    'tapd_story_status',
                    'entry_status',
                    'del_time',
                    'del_user',
                    'upd_batch_number',
                    'tapd_story_priority',
                    'tapd_story_workitem_type_id',
                    'tapd_story_developer',
                    'tapd_story_iteration_id',
                    'tapd_story_owner',
                    'tapd_story_cc',
                    'tapd_story_begin',
                    'tapd_story_due',
                    'tapd_story_size',
                    'tapd_story_modified',
                    'tapd_story_created',
                    'tapd_story_type',
                    'tapd_story_source',
                    'tapd_story_module',
                    'tapd_story_completed',
                    'tapd_story_category_id',
                    'tapd_story_path',
                    'tapd_story_parent_id',
                    'tapd_story_children_id',
                    'tapd_story_ancestor_id',
                    'tapd_story_level',
                    'tapd_story_effort',
                    'tapd_story_effort_completed',
                    'tapd_story_exceed',
                    'tapd_story_remain',
                    'tapd_story_is_archived',
                    'tapd_story_release_id',
                    'tapd_story_bug_id',
                    'tapd_story_templated_id',
                    'tapd_story_created_from',
                    'tapd_story_modified',
                    'tapd_story_module',
                    'tapd_story_source',
                    'tapd_story_custom_field_one',
                    'tapd_story_custom_field_two',
                    'tapd_story_custom_field_three',
                    'tapd_story_custom_field_four',
                    'tapd_story_custom_field_five',
                    'tapd_story_custom_field_six',
                    'tapd_story_custom_field_seven',
                    'tapd_story_custom_field_eight',
                    'tapd_story_custom_field_9',
                    'tapd_story_custom_field_10',
                    'tapd_story_custom_field_11',
                    'tapd_story_custom_field_12',
                    'tapd_story_custom_field_13',
                    'tapd_story_custom_field_14',
                    'tapd_story_custom_field_15',
                    'tapd_story_custom_field_16',
                    'tapd_story_custom_field_17',
                    'tapd_story_custom_field_18',
                    'tapd_story_custom_field_19',
                    'tapd_story_custom_field_20',
                    'tapd_story_custom_field_21',
                    'tapd_story_custom_field_22',
                    'tapd_story_custom_field_23',
                    'tapd_story_custom_field_24',
                    'tapd_story_custom_field_25',
                    'tapd_story_custom_field_26',
                    'tapd_story_custom_field_27',
                    'tapd_story_custom_field_28',
                    'tapd_story_custom_field_29',
                    'tapd_story_custom_field_30',
                    'tapd_story_custom_field_50'
                ]
                TapdEntryStory.objects.bulk_update(upd_batch_list, upd_fields)
            for obj in ins_json_batch_list + upd_json_batch_list:
                TapdEntryStoryResJson.objects.update_or_create(defaults={'res_json': obj.res_json},
                                                               tapd_entry_id=obj.tapd_entry_id)
                # log.info(">>>>>更新tapd迭代：{}".format(upd_rows))

            return len(ins_batch_list), len(upd_json_batch_list)


class TapdTestPlanSrv(TapdBaseSrv):
    def __init__(self, ):
        """
        Tapd测试计划服务
        """

    def delete_test_plans_by_workspace_id(self, workspace_id, created):

        current_page = int(TAPD["req_page"])
        page_size = int(TAPD["req_limit"])
        api_params = {
            "workspace_id": workspace_id,
            "page": current_page,
            "limit": page_size
        }

        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.TEST_PLAN, workspace_id)
        # 默认拿第一页的数据
        total_new_create_list = TapdReqMgt().get_tapd_data_by_url(module_url, api_params)

        ready_to_delete_id_list = []
        if total_new_create_list:

            last_created = total_new_create_list[-1].get('TestPlan').get('created')

            new_create_id_list = []
            if total_new_create_list:
                for test_plan_tmp in total_new_create_list:
                    test_plan_obj = test_plan_tmp.get('TestPlan')
                    new_create_id_list.append(int(test_plan_obj.get('id')))

            db_exist_id_list = TapdEntryTestPlan.objects.filter(tapd_test_plan_created__gte=last_created,
                                                                tapd_workspace_id=workspace_id).values_list(
                'tapd_entry_id', flat=True)

            if new_create_id_list:
                ready_to_delete_id_list = list(set(db_exist_id_list) - set(new_create_id_list))

            now_time = get_now_time()
            batch_number = TapdBatchSrv().get_batch_num(now_time)
            with transaction.atomic():
                TapdEntryTestPlan.objects.filter(tapd_entry_id__in=ready_to_delete_id_list).exclude(
                    entry_status=3).update(entry_status=3,
                                           del_batch_number=batch_number,
                                           del_user='howbuyscm',
                                           del_time=now_time)

                TapdEntryTestPlanBindStory.objects.filter(tapd_test_plan_id__in=ready_to_delete_id_list).delete()

        return len(ready_to_delete_id_list)

    def get_test_plans_by_workspace_id(self, workspace_id, modified, iteration_id, current_page):
        """
        tapd get请求
        """
        page_size = int(TAPD["req_limit"])
        api_params = {
            "workspace_id": workspace_id,
            "page": current_page,
            "limit": page_size
        }
        if iteration_id:
            api_params["iteration_id"] = iteration_id
        if modified:
            api_params["modified"] = ">{}".format(modified)
        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.TEST_PLAN, workspace_id)
        test_plans_list = TapdReqMgt().get_tapd_data_by_url(module_url, api_params)
        count_num = len(test_plans_list) if len(test_plans_list) > 0 else 0
        next_page = current_page if count_num < page_size else current_page + 1
        return test_plans_list, next_page

    @staticmethod
    def write_test_plans_to_db(data_list, start_time=None):
        ins_objs = None
        upd_rows = None

        if not start_time:
            start_time = get_now_time()
        batch_number = TapdBatchSrv().get_batch_num(start_time)
        test_plan_map = {}
        if data_list:
            for test_plan_tmp in data_list:
                test_plan_obj = test_plan_tmp.get('TestPlan')
                tapd_test_plan_id = test_plan_obj.get('id')
                if not tapd_test_plan_id:
                    err_msg = ">>>>> tapd test plan id为空：{}".format(
                        json.dumps(test_plan_obj, indent=4, sort_keys=True, ensure_ascii=False))
                    log.error(err_msg)
                    continue
                test_plan_map[tapd_test_plan_id] = test_plan_obj
            if test_plan_map:
                ins_objs, upd_rows = TapdTestPlanSrv.__create_or_update_with_map(test_plan_map, batch_number,
                                                                                 start_time)
        return ins_objs, upd_rows

    @staticmethod
    def __create_or_update_with_map(test_plan_map, batch_number, opt_time=None):
        if test_plan_map:
            if not opt_time:
                opt_time = get_now_time()

            create_user = TAPD["sync_user"]
            create_time = opt_time
            update_user = TAPD["sync_user"]
            update_time = opt_time
            stamp = 0

            tapd_test_plan_list = TapdEntryTestPlan.objects.filter(tapd_entry_id__in=test_plan_map.keys())
            db_test_plan_id_map = {test_plan.tapd_entry_id: test_plan.id for test_plan in tapd_test_plan_list}
            ins_batch_list = []
            ins_json_batch_list = []
            upd_batch_list = []
            upd_json_batch_list = []
            for k, v in test_plan_map.items():
                k = int(k)
                db_id = None
                if k in db_test_plan_id_map.keys():
                    db_id = db_test_plan_id_map.get(k)
                # 属性获取
                tapd_workspace_id = v.get('workspace_id')
                tapd_entry_id = v.get('id')
                tapd_test_plan_name = v.get('name')
                tapd_test_plan_status = v.get('status')

                opt = TapdEntryTestPlan()
                opt.update_user = update_user
                opt.update_time = update_time
                # 变化的属性
                opt.tapd_test_plan_name = tapd_test_plan_name
                opt.tapd_test_plan_status = tapd_test_plan_status
                opt.tapd_test_plan_iteration_id = v.get('iteration_id')
                owner = TapdUtils.str_split(v.get('owner'), ';')
                opt.tapd_test_plan_owner = owner
                opt.tapd_test_plan_creator = v.get('creator')
                opt.tapd_test_plan_created = v.get('created')
                opt.tapd_test_plan_modifier = v.get('modifier')
                opt.tapd_test_plan_modified = v.get('modified')
                opt.tapd_test_plan_custom_field_1 = v.get('custom_field_1')
                opt.tapd_test_plan_custom_field_2 = v.get('custom_field_2')
                opt.tapd_test_plan_custom_field_3 = v.get('custom_field_3')
                opt.tapd_test_plan_custom_field_4 = v.get('custom_field_4')
                opt.tapd_test_plan_custom_field_5 = v.get('custom_field_5')
                opt.tapd_test_plan_custom_field_6 = v.get('custom_field_6')
                opt.tapd_test_plan_custom_field_7 = v.get('custom_field_7')
                opt.tapd_test_plan_custom_field_8 = v.get('custom_field_8')
                opt.tapd_test_plan_custom_field_9 = v.get('custom_field_9')
                opt.tapd_test_plan_custom_field_10 = v.get('custom_field_10')
                opt.tapd_test_plan_description = v.get('description')

                obj = TapdEntryTestPlanResJson.objects.filter(tapd_entry_id=tapd_entry_id).values('id').first()
                res_json_obj = TapdEntryTestPlanResJson()
                if obj and obj.get('id'):
                    res_json_obj.id = obj.get('id')
                res_json_obj.tapd_entry_id = tapd_entry_id
                res_json_obj.res_json = v

                # 继续填充。。。
                if not db_id:
                    opt.create_user = create_user
                    opt.create_time = create_time
                    opt.stamp = stamp
                    opt.ins_batch_number = batch_number
                    opt.entry_status = 1
                    # 不会变的属性
                    opt.tapd_workspace_id = tapd_workspace_id
                    opt.tapd_entry_id = tapd_entry_id

                    ins_batch_list.append(opt)
                    ins_json_batch_list.append(res_json_obj)
                else:
                    opt.id = db_id
                    opt.entry_status = 2
                    opt.del_time = None
                    opt.del_user = None
                    opt.upd_batch_number = batch_number
                    upd_batch_list.append(opt)
                    upd_json_batch_list.append(res_json_obj)

            if ins_batch_list:
                TapdEntryTestPlan.objects.bulk_create(ins_batch_list)
            if upd_batch_list:
                upd_fields = [
                    'update_user',
                    'update_time',
                    'tapd_test_plan_name',
                    'tapd_test_plan_status',
                    'entry_status',
                    'del_time',
                    'del_user',
                    'upd_batch_number',
                    'tapd_test_plan_iteration_id',
                    'tapd_test_plan_owner',
                    'tapd_test_plan_creator',
                    'tapd_test_plan_created',
                    'tapd_test_plan_modifier',
                    'tapd_test_plan_modified',
                    'tapd_test_plan_custom_field_1',
                    'tapd_test_plan_custom_field_2',
                    'tapd_test_plan_custom_field_3',
                    'tapd_test_plan_custom_field_4',
                    'tapd_test_plan_custom_field_5',
                    'tapd_test_plan_custom_field_6',
                    'tapd_test_plan_custom_field_7',
                    'tapd_test_plan_custom_field_8',
                    'tapd_test_plan_custom_field_9',
                    'tapd_test_plan_custom_field_10'
                ]
                TapdEntryTestPlan.objects.bulk_update(upd_batch_list, upd_fields)
            for obj in ins_json_batch_list + upd_json_batch_list:
                TapdEntryTestPlanResJson.objects.update_or_create(defaults={'res_json': obj.res_json},
                                                                  tapd_entry_id=obj.tapd_entry_id)

            return len(ins_batch_list), len(upd_json_batch_list)


class TapdTaskSrv:
    def __init__(self, ):
        """
        Tapd任务服务
        """

    def delete_tasks_by_workspace_id(self, workspace_id, deleted):
        current_page = int(TAPD["req_page"])
        page_size = int(TAPD["req_limit"])
        api_params = {
            "workspace_id": workspace_id,
            "page": current_page,
            "limit": page_size
        }
        if deleted:
            date_range = ModifyTime().modify_time(deleted, 1)
            api_params["modified"] = date_range

        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.TASK, workspace_id)
        total_delete_task_list = []
        while True:
            api_params["page"] = current_page
            delete_task_list = TapdReqMgt().get_tapd_data_by_url(module_url + "/get_removed_tasks", api_params)
            total_delete_task_list.extend(delete_task_list)
            if len(delete_task_list) == 200:
                current_page += 1
            else:
                break

        upd_rows = None
        if total_delete_task_list:
            task_map = {}
            for task_tmp in total_delete_task_list:
                task_obj = task_tmp.get('RemovedTask')
                tapd_task_id = task_obj.get('id')
                task_map[tapd_task_id] = task_obj

            tapd_task_list = TapdEntryTask.objects.filter(tapd_entry_id__in=task_map.keys())
            log.info(tapd_task_list)
            db_task_id_map = {task.tapd_entry_id: task.id for task in tapd_task_list}
            del_batch_list = []
            for k, v in task_map.items():
                k = int(k)
                db_id = None
                if k in db_task_id_map.keys():
                    db_id = db_task_id_map.get(k)
                # 属性获取
                if db_id:
                    opt = TapdEntryTask()
                    opt.del_user = v.get('operation_user')
                    opt.del_time = v.get('modified')
                    opt.id = db_id
                    opt.entry_status = 3
                    del_batch_list.append(opt)

            if del_batch_list:
                upd_fields = [
                    'del_user',
                    'del_time',
                    'entry_status',
                ]
                upd_rows = TapdEntryTask.objects.bulk_update(del_batch_list, upd_fields)
                log.info(">>>>>删除tapd任务：{}".format(upd_rows))
        return upd_rows

    def get_tasks_by_workspace_id(self, workspace_id, modified, current_page, iteration_id):
        """
        tapd get请求
        """
        page_size = int(TAPD["req_limit"])
        api_params = {
            "workspace_id": workspace_id,
            "page": current_page,
            "limit": page_size
        }
        if iteration_id:
            api_params['iteration_id'] = iteration_id
        if modified:
            api_params['modified'] = ">{}".format(modified)
        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.TASK, workspace_id)
        data_list = TapdReqMgt().get_tapd_data_by_url(module_url, api_params)
        count_num = len(data_list) if len(data_list) > 0 else 0
        next_page = current_page if count_num < page_size else current_page + 1
        return data_list, next_page

    @staticmethod
    def write_tasks_to_db(data_list, start_time=None):
        ins_objs = None
        upd_rows = None

        if not start_time:
            start_time = get_now_time()
        batch_number = TapdBatchSrv().get_batch_num(start_time)
        task_map = {}
        if data_list:
            for task_tmp in data_list:
                task_obj = task_tmp.get('Task')
                tapd_task_id = task_obj.get('id')
                if not tapd_task_id:
                    err_msg = ">>>>> tapd任务id为空：{}".format(
                        json.dumps(task_obj, indent=4, sort_keys=True, ensure_ascii=False))
                    log.error(err_msg)
                    continue
                task_map[tapd_task_id] = task_obj
            if task_map:
                ins_objs, upd_rows = TapdTaskSrv.__create_or_update_with_map(task_map, batch_number,
                                                                             start_time)
        return ins_objs, upd_rows

    @staticmethod
    def __create_or_update_with_map(task_map, batch_number, opt_time=None):
        if task_map:

            create_user = TAPD["sync_user"]
            create_time = opt_time
            update_user = TAPD["sync_user"]
            update_time = opt_time
            stamp = 0

            tapd_task_list = TapdEntryTask.objects.filter(tapd_entry_id__in=task_map.keys())
            db_task_id_map = {task.tapd_entry_id: task.id for task in tapd_task_list}
            ins_batch_list = []
            ins_json_batch_list = []
            upd_batch_list = []
            upd_json_batch_list = []
            for k, v in task_map.items():
                k = int(k)
                db_id = None
                if k in db_task_id_map.keys():
                    db_id = db_task_id_map.get(k)
                # 属性获取
                tapd_workspace_id = v.get('workspace_id')
                tapd_entry_id = v.get('id')
                tapd_task_name = v.get('name')
                tapd_task_status = v.get('status')

                opt = TapdEntryTask()
                opt.update_user = update_user
                opt.update_time = update_time
                # 变化的属性
                opt.tapd_task_name = tapd_task_name
                opt.tapd_task_status = tapd_task_status
                opt.tapd_workspace_id = tapd_workspace_id
                owner = TapdUtils.str_split(v.get('owner'), ';')
                opt.tapd_task_owner = owner
                opt.tapd_task_cc = v.get('cc')
                opt.tapd_task_begin = v.get('begin')
                opt.tapd_task_due = v.get('due')
                opt.tapd_task_story_id = v.get('story_id')
                opt.tapd_task_iteration_id = v.get('iteration_id')
                opt.tapd_task_priority = v.get('priority')
                opt.tapd_task_progress = v.get('progress')
                opt.tapd_task_completed = v.get('completed')
                opt.tapd_task_effort_completed = v.get('effort_completed')
                opt.tapd_task_exceed = v.get('exceed')
                opt.tapd_task_remain = v.get('remain')
                opt.tapd_task_effort = v.get('effort')
                opt.tapd_task_release_id = v.get('release_id')
                opt.tapd_task_creator = v.get('creator')
                opt.tapd_task_created = v.get('created')
                opt.tapd_task_priority_label = v.get('has_attachment')
                opt.tapd_task_custom_field_one = v.get('custom_field_one')
                opt.tapd_task_custom_field_two = v.get('custom_field_two')
                opt.tapd_task_custom_field_three = v.get('custom_field_three')
                opt.tapd_task_custom_field_four = v.get('custom_field_four')
                opt.tapd_task_custom_field_five = v.get('custom_field_five')
                opt.tapd_task_custom_field_six = v.get('custom_field_six')
                opt.tapd_task_custom_field_seven = v.get('custom_field_seven')
                opt.tapd_task_custom_field_eight = v.get('custom_field_eight')
                opt.tapd_task_custom_field_9 = v.get('custom_field_9')
                opt.tapd_task_custom_field_10 = v.get('custom_field_10')

                obj = TapdEntryTaskResJson.objects.filter(tapd_entry_id=tapd_entry_id).values('id').first()
                res_json_obj = TapdEntryTaskResJson()
                if obj and obj.get('id'):
                    res_json_obj.id = obj.get('id')
                res_json_obj.tapd_entry_id = tapd_entry_id
                res_json_obj.res_json = v

                # 继续填充。。。
                if not db_id:
                    opt.create_user = create_user
                    opt.create_time = create_time
                    opt.stamp = stamp
                    opt.ins_batch_number = batch_number
                    opt.entry_status = 1
                    # 不会变的属性
                    opt.tapd_workspace_id = tapd_workspace_id
                    opt.tapd_entry_id = tapd_entry_id

                    ins_batch_list.append(opt)
                    ins_json_batch_list.append(res_json_obj)
                else:
                    opt.id = db_id
                    opt.upd_batch_number = batch_number
                    opt.entry_status = 2
                    opt.del_time = None
                    opt.del_user = None
                    upd_batch_list.append(opt)
                    upd_json_batch_list.append(res_json_obj)

            if ins_batch_list:
                TapdEntryTask.objects.bulk_create(ins_batch_list)
            if upd_batch_list:
                upd_fields = [
                    'update_user',
                    'update_time',
                    'tapd_task_name',
                    'tapd_task_status',
                    'entry_status',
                    'del_time',
                    'del_user',
                    'upd_batch_number',
                    'tapd_workspace_id',
                    'tapd_task_priority',
                    'tapd_task_story_id',
                    'tapd_task_iteration_id',
                    'tapd_task_begin',
                    'tapd_task_due',
                    'tapd_task_created',
                    'tapd_task_progress',
                    'tapd_task_completed',
                    'tapd_task_effort',
                    'tapd_task_effort_completed',
                    'tapd_task_exceed',
                    'tapd_task_remain',
                    'upd_batch_number',
                    'tapd_task_owner',
                    'tapd_task_cc',
                    'tapd_task_custom_field_one',
                    'tapd_task_custom_field_two',
                    'tapd_task_custom_field_three',
                    'tapd_task_custom_field_four',
                    'tapd_task_custom_field_five',
                    'tapd_task_custom_field_six',
                    'tapd_task_custom_field_seven',
                    'tapd_task_custom_field_eight',
                    'tapd_task_custom_field_9',
                    'tapd_task_custom_field_10'
                ]
                TapdEntryTask.objects.bulk_update(upd_batch_list, upd_fields)
            for obj in ins_json_batch_list + upd_json_batch_list:
                TapdEntryTaskResJson.objects.update_or_create(defaults={'res_json': obj.res_json},
                                                              tapd_entry_id=obj.tapd_entry_id)

                # log.info(">>>>>更新tapd任务：{}".format(upd_rows))

            return len(ins_batch_list), len(upd_batch_list)


class TapdLaunchFormSrv:
    def __init__(self, ):
        """
        Tapd发布评审服务
        """

    def get_launch_forms_by_workspace_id(self, workspace_id, modified, current_page, iteration_id):
        """
        tapd get请求
        """
        page_size = int(TAPD["req_limit"])
        api_params = {
            "workspace_id": workspace_id,
            "page": current_page,
            "limit": page_size
        }
        # 如果用iteration_id查询，则不需要用更新时间来限定查询范围
        if iteration_id:
            api_params['iteration_id'] = iteration_id
        else:
            api_params['modified'] = ">{}".format(modified)
        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.LAUNCH_FORM, workspace_id)
        launch_form_list = TapdReqMgt().get_tapd_data_by_url(module_url, api_params)
        count_num = len(launch_form_list) if len(launch_form_list) > 0 else 0
        next_page = current_page if count_num < page_size else current_page + 1
        return launch_form_list, next_page

    def create_launch_form_by_workspace_id(self, workspace_id, api_params):
        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.LAUNCH_FORM, workspace_id)
        launch_form_result = TapdReqMgt().tapd_post(module_url, api_params)
        return launch_form_result

    def get_launch_form_by_workspace_id(self, workspace_id, iteration_id):
        api_params = {
            "workspace_id": workspace_id,
            "iteration_id": iteration_id
        }
        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.LAUNCH_FORM, workspace_id)
        launch_form_list = TapdReqMgt().get_tapd_data_by_url(module_url, api_params)
        return launch_form_list

    @staticmethod
    def write_launch_forms_to_db(data_list, start_time=None):
        ins_objs = None
        upd_rows = None

        if not start_time:
            start_time = get_now_time()
        batch_number = TapdBatchSrv().get_batch_num(start_time)
        launch_form_map = {}
        if data_list:
            for launch_form_tmp in data_list:
                launch_form_obj = launch_form_tmp.get('LaunchForm')
                tapd_launch_form_id = launch_form_obj.get('id')
                if not tapd_launch_form_id:
                    err_msg = ">>>>> tapd发布评审id为空：{}".format(
                        json.dumps(launch_form_obj, indent=4, sort_keys=True, ensure_ascii=False))
                    log.error(err_msg)
                    continue
                launch_form_map[tapd_launch_form_id] = launch_form_obj
            if launch_form_map:
                ins_objs, upd_rows = TapdLaunchFormSrv.__create_or_update_with_map(launch_form_map, batch_number,
                                                                                   start_time)
        return ins_objs, upd_rows

    @staticmethod
    def __create_or_update_with_map(launch_form_map, batch_number, opt_time=None):
        if launch_form_map:

            create_user = TAPD["sync_user"]
            create_time = opt_time
            update_user = TAPD["sync_user"]
            update_time = opt_time
            stamp = 0

            tapd_launch_form_list = TapdEntryLaunchForm.objects.filter(tapd_entry_id__in=launch_form_map.keys())
            db_launch_form_id_map = {launch_form.tapd_entry_id: launch_form.id for launch_form in tapd_launch_form_list}
            ins_batch_list = []
            ins_json_batch_list = []
            upd_batch_list = []
            upd_json_batch_list = []
            for k, v in launch_form_map.items():
                k = int(k)
                db_id = None
                if k in db_launch_form_id_map.keys():
                    db_id = db_launch_form_id_map.get(k)
                # 属性获取
                tapd_workspace_id = v.get('workspace_id')
                tapd_entry_id = v.get('id')
                tapd_launch_form_title = v.get('title')
                tapd_launch_form_status = v.get('status')

                opt = TapdEntryLaunchForm()
                opt.update_user = update_user
                opt.update_time = update_time
                # 变化的属性
                opt.tapd_launch_form_title = tapd_launch_form_title
                opt.tapd_launch_form_status = tapd_launch_form_status
                opt.tapd_launch_form_version_type = v.get('version_type')
                opt.tapd_launch_form_baseline = v.get('baseline')
                opt.tapd_launch_form_release_model = v.get('release_model')
                opt.tapd_launch_form_roadmap_version = v.get('roadmap_version')
                opt.tapd_launch_form_release_type = v.get('release_type')
                opt.tapd_launch_form_change_type = v.get('change_type')
                signed_by = TapdUtils.str_split(v.get('signed_by'), ';')
                opt.tapd_launch_form_signed_by = signed_by
                opt.tapd_launch_form_archived_by = v.get('archived_by')
                opt.tapd_launch_form_signed = v.get('signed')
                opt.tapd_launch_form_archived = v.get('archived')
                opt.tapd_launch_form_release_result = v.get('release_result')
                opt.tapd_launch_form_participator = v.get('participator')
                opt.tapd_launch_form_template_id = v.get('template_id')
                opt.tapd_launch_form_iteration_id = v.get('iteration_id')
                opt.tapd_launch_form_release_id = v.get('release_id')
                opt.tapd_launch_form_flows = v.get('flows')
                opt.tapd_launch_form_modified = v.get('modified')
                opt.tapd_launch_form_release_result = v.get('release_result')
                opt.tapd_launch_form_custom_field_one = v.get('custom_field_one')
                opt.tapd_launch_form_custom_field_two = v.get('custom_field_two')
                opt.tapd_launch_form_custom_field_three = v.get('custom_field_three')

                obj = TapdEntryLaunchFormResJson.objects.filter(tapd_entry_id=tapd_entry_id).values('id').first()
                res_json_obj = TapdEntryLaunchFormResJson()
                if obj and obj.get('id'):
                    res_json_obj.id = obj.get('id')
                res_json_obj.tapd_entry_id = tapd_entry_id
                res_json_obj.res_json = v

                # 继续填充。。。
                if not db_id:
                    opt.create_user = create_user
                    opt.create_time = create_time
                    opt.stamp = stamp
                    opt.ins_batch_number = batch_number
                    opt.entry_status = 1
                    # 不会变的属性
                    opt.tapd_workspace_id = tapd_workspace_id
                    opt.tapd_entry_id = tapd_entry_id
                    opt.tapd_launch_form_created = v.get('created')
                    opt.tapd_launch_form_creator = v.get('creator')

                    ins_batch_list.append(opt)
                    ins_json_batch_list.append(res_json_obj)
                else:
                    opt.id = db_id
                    opt.upd_batch_number = batch_number
                    opt.entry_status = 2
                    opt.del_time = None
                    opt.del_user = None
                    upd_batch_list.append(opt)
                    upd_json_batch_list.append(res_json_obj)

            if ins_batch_list:
                TapdEntryLaunchForm.objects.bulk_create(ins_batch_list)

            if upd_batch_list:
                upd_fields = [
                    'update_user',
                    'update_time',
                    'tapd_launch_form_title',
                    'tapd_launch_form_status',
                    'entry_status',
                    'del_time',
                    'del_user',
                    'upd_batch_number',
                    'tapd_launch_form_version_type',
                    'tapd_launch_form_baseline',
                    'tapd_launch_form_release_model',
                    'tapd_launch_form_roadmap_version',
                    'tapd_launch_form_release_type',
                    'tapd_launch_form_change_type',
                    'tapd_launch_form_signed_by',
                    'tapd_launch_form_archived_by',
                    'tapd_launch_form_signed',
                    'tapd_launch_form_archived',
                    'tapd_launch_form_release_result',
                    'tapd_launch_form_participator',
                    'tapd_launch_form_flows',
                    'tapd_launch_form_modified',
                    'tapd_launch_form_release_result',
                    'tapd_launch_form_custom_field_one',
                    'tapd_launch_form_custom_field_two',
                    'tapd_launch_form_custom_field_three',
                ]
                TapdEntryLaunchForm.objects.bulk_update(upd_batch_list, upd_fields)
            for obj in ins_json_batch_list + upd_json_batch_list:
                TapdEntryLaunchFormResJson.objects.update_or_create(defaults={'res_json': obj.res_json},
                                                                    tapd_entry_id=obj.tapd_entry_id)
                # log.info(">>>>>更新tapd发布评审：{}".format(upd_rows))

            return len(ins_batch_list), len(upd_batch_list)


class TapdTimeSheetSrv:
    def __init__(self, ):
        """
        Tapd工时服务
        """

    def delete_timesheet_by_workspace_id(self, workspace_id, deleted):
        current_page = int(TAPD["req_page"])
        page_size = int(TAPD["req_limit"])
        api_params = {
            "workspace_id": workspace_id,
            "page": current_page,
            "limit": page_size
        }

        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.TIMESHEET, workspace_id)
        # 默认拿第一页的数据
        total_new_create_list = TapdReqMgt().get_tapd_data_by_url(module_url, api_params)

        last_created = total_new_create_list[-1].get('Timesheet').get('created')

        new_create_id_list = []
        if total_new_create_list:
            for timesheet_tmp in total_new_create_list:
                timesheet_obj = timesheet_tmp.get('Timesheet')
                new_create_id_list.append(int(timesheet_obj.get('id')))

        db_exist_id_list = TapdEntryTimeSheet.objects.filter(tapd_time_sheet_created__gte=last_created,
                                                             tapd_workspace_id=workspace_id).values_list(
            'tapd_entry_id', flat=True)

        ready_to_delete_id_list = []
        if new_create_id_list:
            ready_to_delete_id_list = list(set(db_exist_id_list) - set(new_create_id_list))

        now_time = get_now_time()
        batch_number = TapdBatchSrv().get_batch_num(now_time)
        with transaction.atomic():
            TapdEntryTimeSheet.objects.filter(tapd_entry_id__in=ready_to_delete_id_list).exclude(
                entry_status=3).update(entry_status=3,
                                       del_batch_number=batch_number,
                                       del_user='howbuyscm',
                                       del_time=now_time)

        return len(ready_to_delete_id_list)

    def get_time_sheets_by_workspace_id(self, workspace_id, modified, current_page):
        """
        tapd get请求
        """
        page_size = int(TAPD["req_limit"])
        api_params = {
            "workspace_id": workspace_id,
            "modified": ">{}".format(modified),
            "page": current_page,
            "limit": page_size
        }
        module_url = TapdModuleSrv().get_module_url(TapdModuleEnum.TIMESHEET, workspace_id)
        data_list = TapdReqMgt().get_tapd_data_by_url(module_url, api_params)
        count_num = len(data_list) if len(data_list) > 0 else 0
        next_page = current_page if count_num < page_size else current_page + 1
        return data_list, next_page

    @staticmethod
    def write_time_sheets_to_db(data_list, start_time=None):
        ins_objs = None
        upd_rows = None

        if not start_time:
            start_time = get_now_time()
        batch_number = TapdBatchSrv().get_batch_num(start_time)
        time_sheet_map = {}
        if data_list:
            for time_sheet_tmp in data_list:
                time_sheet_obj = time_sheet_tmp.get('Timesheet')
                tapd_time_sheet_id = time_sheet_obj.get('id')
                if not tapd_time_sheet_id:
                    err_msg = ">>>>> tapd工时id为空：{}".format(
                        json.dumps(time_sheet_obj, indent=4, sort_keys=True, ensure_ascii=False))
                    log.error(err_msg)
                    continue
                time_sheet_map[tapd_time_sheet_id] = time_sheet_obj
            if time_sheet_map:
                ins_objs, upd_rows = TapdTimeSheetSrv.__create_or_update_with_map(time_sheet_map, batch_number,
                                                                                  start_time)
        return ins_objs, upd_rows

    @staticmethod
    def __create_or_update_with_map(time_sheet_map, batch_number, opt_time=None):
        if time_sheet_map:

            create_user = TAPD["sync_user"]
            create_time = opt_time
            update_user = TAPD["sync_user"]
            update_time = opt_time
            stamp = 0

            tapd_time_sheet_list = TapdEntryTimeSheet.objects.filter(tapd_entry_id__in=time_sheet_map.keys())
            db_time_sheet_id_map = {time_sheet.tapd_entry_id: time_sheet.id for time_sheet in tapd_time_sheet_list}
            ins_batch_list = []
            ins_json_batch_list = []
            upd_batch_list = []
            upd_json_batch_list = []
            for k, v in time_sheet_map.items():
                k = int(k)
                db_id = None
                if k in db_time_sheet_id_map.keys():
                    db_id = db_time_sheet_id_map.get(k)
                # 属性获取
                tapd_workspace_id = v.get('workspace_id')
                tapd_entry_id = v.get('id')
                tapd_time_sheet_entity_id = v.get('entity_id')

                opt = TapdEntryTimeSheet()
                opt.update_user = update_user
                opt.update_time = update_time
                # 变化的属性
                opt.tapd_time_sheet_entity_id = tapd_time_sheet_entity_id
                opt.tapd_time_sheet_timespent = v.get('timespent')
                opt.tapd_time_sheet_entity_type = v.get('entity_type')
                opt.tapd_time_sheet_spentdate = v.get('spentdate')
                opt.tapd_time_sheet_owner = v.get('owner')
                opt.tapd_time_sheet_created = v.get('created')
                opt.tapd_time_sheet_modified = v.get('modified')
                opt.tapd_time_sheet_memo = v.get('memo')
                opt.tapd_time_sheet_is_delete = v.get('is_delete')

                obj = TapdEntryTimesheetResJson.objects.filter(tapd_entry_id=tapd_entry_id).values('id').first()
                res_json_obj = TapdEntryTimesheetResJson()
                if obj and obj.get('id'):
                    res_json_obj.id = obj.get('id')
                res_json_obj.tapd_entry_id = tapd_entry_id
                res_json_obj.res_json = v

                # 继续填充。。。
                if not db_id:
                    opt.create_user = create_user
                    opt.create_time = create_time
                    opt.stamp = stamp
                    opt.ins_batch_number = batch_number
                    opt.entry_status = 1
                    # 不会变的属性
                    opt.tapd_workspace_id = tapd_workspace_id
                    opt.tapd_entry_id = tapd_entry_id

                    ins_batch_list.append(opt)
                    ins_json_batch_list.append(res_json_obj)
                else:
                    opt.id = db_id
                    opt.upd_batch_number = batch_number
                    opt.entry_status = 2
                    opt.del_time = None
                    opt.del_user = None
                    upd_batch_list.append(opt)
                    upd_json_batch_list.append(res_json_obj)

            if ins_batch_list:
                TapdEntryTimeSheet.objects.bulk_create(ins_batch_list)

            if upd_batch_list:
                upd_fields = [
                    'update_user',
                    'update_time',
                    'entry_status',
                    'del_time',
                    'del_user',
                    'tapd_time_sheet_timespent',
                    'tapd_time_sheet_spentdate',
                    'tapd_time_sheet_modified',
                    'upd_batch_number',
                    'tapd_time_sheet_memo',
                    'tapd_time_sheet_is_delete',
                ]
                TapdEntryTimeSheet.objects.bulk_update(upd_batch_list, upd_fields)
            for obj in ins_json_batch_list + upd_json_batch_list:
                TapdEntryTimesheetResJson.objects.update_or_create(defaults={'res_json': obj.res_json},
                                                                   tapd_entry_id=obj.tapd_entry_id)
                # log.info(">>>>>更新tapd工时：{}".format(upd_rows))

            return len(ins_batch_list), len(upd_batch_list)
