from mantis.settings import TAPD


class TapdExp(Exception):
    def __init__(self, code=0, msg=""):
        self.code = code
        self.msg = msg


class TapdDBDataExp(TapdExp):
    def __init__(self, code=0, msg=""):
        self.code = code
        self.msg = "数据库数据异常，原因：{}".format(msg)


class TapdReqExp(TapdExp):
    def __init__(self, code=0, msg=""):
        self.code = code
        self.msg = msg


class TapdModuleExp(TapdExp):
    def __init__(self, code=0, msg=""):
        self.code = code
        self.msg = msg


class TapdModuleNoneExp(TapdModuleExp):
    def __init__(self,):
        self.code = 0
        self.msg = "Tapd模块未启用"


class TapdModuleUrlNoneExp(TapdModuleExp):
    def __init__(self,):
        self.code = 0
        self.msg = "Tapd模块的「url」未配置"


class TapdBatchExp(TapdExp):
    def __init__(self, code=0, msg=""):
        self.code = code
        self.msg = msg


class TapdBatchRetryMaxExp(TapdBatchExp):
    def __init__(self,):
        self.code = 3001
        self.msg = "批次号获取「递归重试」次数超过最大限制"


class TapdBatchLoopMaxExp(TapdBatchExp):
    def __init__(self,):
        self.code = 3001
        self.msg = "批次号获取「循环重试」次数超过最大限制"


class TapdReqParamExp(TapdReqExp):
    def __init__(self, code=0, msg=""):
        self.code = code
        self.msg = msg


class TapdReqParamNoneExp(TapdReqParamExp):
    code = 1001
    msg = "参数不能为空"

    def __init__(self, msg):
        self.msg = msg


class TapdReqRetryMaxExp(TapdReqExp):
    def __init__(self,):
        self.code = 2001
        self.msg = "请求重试次数超过最大限制"


class TapdReqMgtExp(TapdReqExp):
    def __init__(self, code=0, msg=""):
        self.code = code
        self.msg = msg


class TapdReqLimitExp(TapdReqExp):
    def __init__(self, limit):
        self.msg = "请求Tapd过于频繁，超过Mantis设定({}秒{}次)的并发限定值：{}。".format(
            TAPD["request_limit_seconds"],
            TAPD["request_limit_number"],
            limit,)


class TapdReqMgt429Exp(TapdReqMgtExp):
    code = 429
    msg = "请求过于频繁，超过tapd限制"

    def __init__(self,):
        self.code = 429
        self.msg = "请求Tapd过于频繁，超过腾讯Tapd系统自身的限定。"
