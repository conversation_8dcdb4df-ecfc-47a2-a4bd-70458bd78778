# Create your views here

import datetime
from enum import Enum
from rest_framework import status
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from mantis.settings import ApiResult
from mantis.settings import logger as log
from tapd_gateway.from_tapd.service.tapd_req_strategy import GetIterationFromStrategy, \
    GetStoryFromTapdStrategy, StrategyContext, GetTestPlanFromTapdStrategy, GetTaskFromTapdStrategy, \
    GetLaunchFormFromTapdStrategy, GetTimeSheetFromTapdStrategy, GetBugTapdStrategy
from tapd_gateway.from_tapd.service.tapd_sync_data_success_log import TapdEntryDataSyncLogSer, TapdFunctionLogType


class BizType(Enum):
    ITERATION = "iteration"
    STORY = "story"
    BUG = "bug"
    TEST_PLAN = "test_plan"
    TASK = "task"
    LAUNCH_FORM = "launch_form"
    TIME_SHEET = "timesheet"


class TapdEntryView(ViewSet):
    """
    tapd迭代、需求、任务同步入口
    """
    authentication_classes = []

    def create(self, request):
        """从tapd同步数据"""
        workspace_id = request.data.get("workspace_id")
        modified = request.data.get("modified")
        biz_type = request.data.get("biz_type")
        # 参数检查
        if not workspace_id:
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict(
                                "workspace_id不能为空"))
        current_time = datetime.datetime.now()
        if not modified:
            data_sync_ser = TapdEntryDataSyncLogSer()
            modified = data_sync_ser.get_modified(workspace_id, biz_type, TapdFunctionLogType.DATA_TO_GATEWAY.value,
                                                  current_time)
        tapd_strategy = None
        if biz_type == BizType.ITERATION.value:
            tapd_strategy = GetIterationFromStrategy(workspace_id, modified, None)
        elif biz_type == BizType.STORY.value:
            tapd_strategy = GetStoryFromTapdStrategy(workspace_id, modified, None)
        elif biz_type == BizType.BUG.value:
            tapd_strategy = GetBugTapdStrategy(workspace_id, modified, None)
        elif biz_type == BizType.TEST_PLAN.value:
            tapd_strategy = GetTestPlanFromTapdStrategy(workspace_id, modified, None)
        elif biz_type == BizType.TASK.value:
            tapd_strategy = GetTaskFromTapdStrategy(workspace_id, modified, None)
        elif biz_type == BizType.LAUNCH_FORM.value:
            tapd_strategy = GetLaunchFormFromTapdStrategy(workspace_id, modified, None)
        elif biz_type == BizType.TIME_SHEET.value:
            tapd_strategy = GetTimeSheetFromTapdStrategy(workspace_id, modified, None)
        else:
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict(
                                "不支持的业务类型,workspace_id:{},biz_type:{}".format(workspace_id, biz_type)))
        executor_result = True
        try:
            context = StrategyContext(tapd_strategy)
            ins_batch_size, upd_batch_size, del_rows = context.execute_strategy()
            msg = "同步TAPD成功，插入{}条，更新{}条。删除{} 条".format(ins_batch_size, upd_batch_size, del_rows)
            return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(msg))
        except Exception as e:
            executor_result = False
            log.error("同步tapd信息失败,workspace_id为：{}, 时间为{},biz_type:{}".format(workspace_id, modified, biz_type))
            log.error(str(e))
            # log.error(e.msg)
        finally:
            if executor_result:
                data_sync_ser = TapdEntryDataSyncLogSer()
                data_sync_ser.save_success_log(biz_type, TapdFunctionLogType.DATA_TO_GATEWAY.value, workspace_id,
                                               current_time)
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.failed_dict(
                            "同步tapd信息失败,workspace_id:{},biz_type:{}".format(workspace_id, biz_type)))


