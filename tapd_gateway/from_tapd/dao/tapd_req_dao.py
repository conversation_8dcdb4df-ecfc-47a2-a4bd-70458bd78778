import datetime

from django.db import connection

from mantis.settings import logger as log
from datetime import timedelta


def get_delete_start_date(modify_time):
    start_date = datetime.datetime.strptime(modify_time, '%Y-%m-%d %H:%M:%S').date() - timedelta(
        days=1)
    return start_date.strftime('%Y-%m-%d')


def get_delete_minute(modify_time, delete_minute):
    modify_time = modify_time.strftime('%Y-%m-%d %H:%M:%S')
    new_date = datetime.datetime.strptime(modify_time, '%Y-%m-%d %H:%M:%S') - timedelta(minutes=delete_minute)
    return new_date


def get_tapd_workspace_module(module_name, workspace_id):
    sql = '''
            select ws.tapd_workspace_id,
                   ws.tapd_workspace_name,
                   ws.workspace_short_name,
                   ws.workspace_type,
                   ws.workspace_team_id,
                   ws_m.module_name,
                   ws_m.module_url
            from tapd_workspace ws
                inner join tapd_workspace_module ws_m on ws_m.workspace_id = ws.id
            where ws.tapd_workspace_id = {}
              and ws.workspace_is_active = 1
              and ws_m.module_name = '{}'
              and ws_m.module_is_active = 1
          '''.format(workspace_id, module_name)

    # log.info(">>>>get_tapd_workspace_module sql:{}".format(sql))

    cursor = connection.cursor()
    cursor.execute(sql)

    return cursor


def get_tapd_entity_story_name(workspace_id):
    sql = '''
            select tes.tapd_story_name
            from  tapd_entry_story tes 
            where  tes.tapd_workspace_id = {} 
          '''.format(workspace_id)

    log.info(">>>>get_tapd_entity_story sql:{}".format(sql))

    cursor = connection.cursor()
    cursor.execute(sql)

    return cursor


def get_tapd_entity_iterations(workspace_id, launch_template_id, workitem_type_id):
    sql = '''
            SELECT DISTINCT  tt.tapd_workspace_id, tt.tapd_iteration_name, tt.tapd_entry_id, ts.tapd_story_owner AS signed_by, tm.owner AS archived_by,
                ts.tapd_story_owner AS creator,'{}' AS template_id, tt.entry_status, ts.tapd_story_custom_field_two as team
            FROM tapd_entry_iteration tt
            INNER JOIN tapd_entry_story ts ON tt.tapd_entry_id = ts.tapd_story_iteration_id
            LEFT JOIN tapd_modules tm ON ts.tapd_story_custom_field_two = tm.name
            LEFT JOIN tapd_entry_launch_form lf ON lf.tapd_launch_form_iteration_id = tt.tapd_entry_id
            WHERE ts.tapd_story_status != 'resolved' AND ts.entry_status in(1,2) AND tt.tapd_workspace_id = {} AND lf.id is NULL
                  AND ts.tapd_story_workitem_type_id = '{}'
    '''.format(launch_template_id, workspace_id, workitem_type_id)
    log.info(sql)

    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_tapd_entity_iteration_by_iteration_ids(workspace_id, iteration_ids):
    sql = '''
         SELECT DISTINCT  tt.tapd_workspace_id, tt.tapd_entry_id, tt.tapd_iteration_name, tt.tapd_iteration_status , tt.tapd_iteration_startdate, tt.tapd_iteration_enddate
                FROM tapd_entry_iteration tt
                WHERE tt.tapd_workspace_id = {} and tt.tapd_entry_id IN ({});
        '''.format(workspace_id, iteration_ids)
    log.info(sql)

    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_test_tapd_bug_by_modified(workspace_type, modified):
    now_datetime = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    start_date = get_delete_start_date(modified)
    sql = '''
                select 
                teb.entry_status,
                p.id AS project_id,
                teb.tapd_workspace_id,
                teb.tapd_bug_iteration_id as iteration_id,
                teb.tapd_entry_id,
                teb.tapd_bug_title,
                teb.tapd_bug_severity,
                teb.tapd_bug_created,
                teb.tapd_bug_status,
                teb.tapd_bug_bugtype,
                teb.tapd_bug_fixer, 
                teb.tapd_bug_resolved, 
                teb.tapd_bug_closed, 
                teb.tapd_bug_flows, 
                teb.tapd_bug_created, 
                teb.tapd_bug_reporter,
                teb.tapd_bug_custom_field_10 as tp_app,
                teb.tapd_bug_custom_field_11 as tms_app,
                teb.tapd_bug_custom_field_12 as fp_app,
                teb.tapd_bug_custom_field_13 as crm_app,
                teb.tapd_bug_custom_field_14 as tms_h5,
                teb.tapd_bug_custom_field_15 as fp_h5_app,
                teb.tapd_bug_custom_field_7 as attribution_analysis
                from  tapd_entry_bug teb
                LEFT JOIN tapd_workspace w ON teb.tapd_workspace_id = w.tapd_workspace_id
                LEFT JOIN dev_effic_test_project p ON teb.tapd_bug_iteration_id = p.tapd_iteration_id
                where w.workspace_type = {workspace_type} AND w.workspace_is_active = 1
                AND (teb.update_time between '{modified}' AND '{now_datetime}' 
                OR teb.del_time between '{start_date}' AND '{now_datetime}')
            '''.format(workspace_type=workspace_type, start_date=start_date, modified=modified,
                       now_datetime=now_datetime)

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_tapd_entry_story_by_modified(workspace_type, modified, workitem_type_id=None, is_test_story=None):
    now_datetime = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    start_date = get_delete_start_date(modified)
    if not is_test_story:
        sql = '''
                    SELECT p.id AS project_id, t.id, t.`tapd_workspace_id`,t.`tapd_story_iteration_id`, t.`tapd_entry_id`, t.`tapd_story_description`, 
                    t.`tapd_story_name`, t.tapd_story_begin, t.tapd_story_due, t.tapd_story_created, t.tapd_story_type, t.tapd_story_completed,
                    t.`tapd_story_status`,t.tapd_story_workitem_type_id,t.tapd_story_owner,t.tapd_story_effort, t.entry_status, t.tapd_story_size,
                    t.tapd_story_custom_field_one, t.tapd_story_custom_field_two, t.tapd_story_custom_field_three,
                    t.tapd_story_custom_field_four, t.tapd_story_custom_field_five, t.tapd_story_custom_field_eight,
                    t.tapd_story_custom_field_9, t.tapd_story_custom_field_10, t.tapd_story_custom_field_11, t.tapd_story_custom_field_12, t.tapd_story_custom_field_13,
                    t.tapd_story_custom_field_14, t.tapd_story_custom_field_15, t.tapd_story_custom_field_16, t.tapd_story_custom_field_17, t.tapd_story_custom_field_18,
                    t.tapd_story_custom_field_20, t.`tapd_story_creator` AS create_user, t.tapd_story_custom_field_22 AS test_plan_id, tp.entry_status AS test_plan_status
                    FROM `tapd_entry_story` t 
                    LEFT JOIN tapd_workspace w ON t.tapd_workspace_id = w.tapd_workspace_id
                    LEFT JOIN dev_effic_test_project p ON t.tapd_story_iteration_id = p.tapd_iteration_id
                    LEFT JOIN tapd_entry_test_plan tp ON t.tapd_story_custom_field_22 = tp.tapd_entry_id
                    where w.workspace_type = {workspace_type} AND w.workspace_is_active = 1
                    AND (t.update_time BETWEEN '{modified}' AND '{now_datetime}' 
                    OR t.del_time BETWEEN '{start_date}' AND '{now_datetime}'
                    OR tp.del_time BETWEEN '{start_date}' AND '{now_datetime}'
                    OR tp.update_time BETWEEN '{modified}' AND '{now_datetime}')
                '''.format(workspace_type=workspace_type, start_date=start_date, modified=modified,
                           now_datetime=now_datetime)
    else:
        sql = '''   
                SELECT t.id, t.`tapd_workspace_id`,t.`tapd_story_iteration_id`, t.`tapd_entry_id`, t.`tapd_story_name`, t.tapd_story_begin, t.tapd_story_due,
                    t.`tapd_story_status`,t.tapd_story_workitem_type_id,t.tapd_story_owner,t.tapd_story_effort, t.entry_status, t.tapd_story_size,
                    t.tapd_story_custom_field_one, t.tapd_story_completed,
                    t.tapd_story_custom_field_two, t.tapd_story_custom_field_three, t.tapd_story_custom_field_four, t.tapd_story_custom_field_eight,
                    t.tapd_story_custom_field_9, t.tapd_story_custom_field_10, t.tapd_story_custom_field_11, t.tapd_story_custom_field_12, t.tapd_story_custom_field_13,
                    t.tapd_story_custom_field_14, t.tapd_story_custom_field_15, t.tapd_story_custom_field_16, t.tapd_story_custom_field_17, t.tapd_story_custom_field_18,
                    t.tapd_story_custom_field_20, t.tapd_story_custom_field_24, t.`tapd_story_creator` as create_user
                FROM `tapd_entry_story` t 
                LEFT JOIN tapd_workspace w ON t.tapd_workspace_id = w.tapd_workspace_id
                WHERE w.workspace_type = {workspace_type} AND w.workspace_is_active = 1
                AND (t.update_time BETWEEN '{modified}' AND '{now_datetime}' 
                    OR t.del_time BETWEEN '{start_date}' AND '{now_datetime}')
              '''.format(workspace_type=workspace_type, start_date=start_date, modified=modified,
                         now_datetime=now_datetime)
    if workitem_type_id:
        sql += " AND t.tapd_story_workitem_type_id = {}".format(workitem_type_id)

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_product_tapd_entry_story(workspace_id, start_time, team, workitem_type_id_list):
    end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    workitem_type_id_str = ','.join(workitem_type_id_list)
    sql = '''
            SELECT DISTINCT tapd_entry_id, tapd_story_creator, {team} as team
            FROM tapd_entry_story 
            WHERE tapd_workspace_id = {workspace_id} 
            AND tapd_story_workitem_type_id IN ({workitem_type_id_str})
            AND update_time BETWEEN '{start_time}' AND '{end_time}'
            AND entry_status != 3
            AND {team} != ''
            '''.format(workspace_id=workspace_id, start_time=start_time,
                       end_time=end_time, team=team, workitem_type_id_str=workitem_type_id_str)

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    story_list = []
    for item in cursor:
        story_list.append({'tapd_entry_id': item[0], 'tapd_story_creator': item[1], 'team': item[2]})

    return story_list


def get_work_item_map(work_space_map):
    # work_space_map = [(36243514, 'Feature'), (52223238, 'Feature'), (66914855, 'Feature')]
    if not work_space_map:
        return {}
    placeholders = ', '.join(['(%s, %s)'] * len(work_space_map))
    sql = """
        SELECT DISTINCT tapd_workspace_id, tapd_workitem_type_id
        FROM tapd_workspace_workitem_type 
        WHERE (tapd_workspace_id, tapd_workitem_type_en_name) IN ({placeholders})
    """.format(placeholders=placeholders)

    log.info(sql)
    params = [item for sublist in work_space_map for item in sublist]
    cursor = connection.cursor()
    cursor.execute(sql, params)
    work_item_map = {}
    for item in cursor:
        work_item_map[item[0]] = item[1]

    return work_item_map


def get_tapd_bug_by_iteration_ids(workspace_id, iteration_ids):
    sql = '''
                select 
                teb.tapd_workspace_id, 
                teb.`tapd_bug_iteration_id` as iteration_id,
                teb.tapd_entry_id,
                teb.tapd_bug_title,
                teb.tapd_bug_severity,
                teb.entry_status,
                teb.tapd_bug_status, 
                teb.tapd_bug_bugtype,
                teb.tapd_bug_created,
                teb.tapd_bug_fixer, 
                teb.tapd_bug_reporter, 
                teb.tapd_bug_current_owner, 
                teb.tapd_bug_resolved, 
                teb.tapd_bug_closed, 
                teb.tapd_bug_flows,
                teb.tapd_bug_custom_field_10 as tp_app, 
                teb.tapd_bug_custom_field_11 as tms_app, 
                teb.tapd_bug_custom_field_12 as fp_app,
                teb.tapd_bug_custom_field_13 as crm_app, 
                teb.tapd_bug_custom_field_14 as tms_h5, 
                teb.tapd_bug_custom_field_15 as fp_h5_app,
                teb.tapd_bug_custom_field_7 as attribution_analysis
                from  tapd_entry_bug teb  
                where teb.tapd_workspace_id = {} and teb.tapd_bug_iteration_id in ({})
            '''.format(workspace_id, iteration_ids)

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_tapd_launch_form_by_iteration_ids(workspace_id, iteration_ids):
    sql = '''
            select m.tapd_entry_id,m.entry_status, m.tapd_launch_form_title,m.tapd_launch_form_status,
                   m.tapd_launch_form_release_result, m.entry_status, m.tapd_launch_form_creator, m.tapd_launch_form_archived_by
            from tapd_entry_launch_form m
                where m.tapd_workspace_id = {} and m.tapd_launch_form_iteration_id in ({})
            '''.format(workspace_id, iteration_ids)

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_tapd_task_by_modified(workspace_type, modified):
    now_datetime = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    start_date = get_delete_start_date(modified)
    sql = '''
                SELECT tet.entry_status, p.id AS project_id, ds.id AS dev_story_id, tet.tapd_workspace_id, tet.tapd_task_iteration_id, tet.tapd_task_story_id, tet.tapd_entry_id, tet.tapd_task_name, tet.tapd_task_creator, tet.tapd_task_created,
                tet.tapd_task_status, tet.tapd_task_owner,tet.tapd_task_completed, tet.tapd_task_effort_completed, tet.tapd_task_exceed,  tet.tapd_task_remain, tet.tapd_task_effort,
                tet.tapd_task_custom_field_two, tet.tapd_task_custom_field_one, tet.tapd_task_custom_field_four, tet.tapd_task_custom_field_seven, tet.tapd_task_custom_field_eight, tet.tapd_task_description
                FROM tapd_entry_task tet
                LEFT JOIN tapd_workspace w ON tet.tapd_workspace_id = w.tapd_workspace_id
                LEFT JOIN dev_effic_test_project p ON tet.tapd_task_iteration_id = p.tapd_iteration_id
                LEFT JOIN dev_effic_dev_story ds ON tet.tapd_task_story_id = ds.tapd_story_id
                where w.workspace_type = {workspace_type} 
                AND w.workspace_is_active = 1
                AND (tet.update_time between '{modified}' AND '{now_datetime}' 
                OR tet.del_time between '{start_date}' AND '{now_datetime}')
            '''.format(workspace_type=workspace_type, start_date=start_date, modified=modified,
                       now_datetime=now_datetime)

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_need_etl_tapd_launch_form(workspace_type, modified):
    now_datetime = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    start_date = get_delete_start_date(modified)
    sql = '''
                SELECT p.id AS project_id, m.tapd_entry_id,m.entry_status, m.tapd_launch_form_title,
                       m.tapd_launch_form_status,m.tapd_launch_form_release_result, m.tapd_launch_form_creator, 
                       m.tapd_launch_form_archived_by
                FROM tapd_entry_launch_form m
                INNER JOIN tapd_workspace w ON m.tapd_workspace_id = w.tapd_workspace_id
                INNER JOIN dev_effic_test_project p ON m.tapd_launch_form_iteration_id = p.tapd_iteration_id
                WHERE w.workspace_type = {workspace_type} 
                AND w.workspace_is_active = 1
                AND m.tapd_launch_form_iteration_id != 0
                AND (m.update_time BETWEEN '{modified}' AND '{now_datetime}' 
                OR m.del_time BETWEEN '{start_date}' AND '{now_datetime}')
            '''.format(workspace_type=workspace_type, start_date=start_date, modified=modified,
                       now_datetime=now_datetime)

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_tapd_entity_task_by_iteration_ids(workspace_id, iteration_ids):
    sql = '''
                SELECT id, tapd_workspace_id,tapd_task_iteration_id,tapd_task_story_id, tapd_entry_id,tapd_task_name, tapd_task_creator, tapd_task_created,
                tapd_task_status,tapd_task_owner, tapd_task_completed,tapd_task_effort_completed,tapd_task_exceed,tapd_task_remain, tapd_task_effort,
                tapd_task_custom_field_two, tapd_task_custom_field_one, tapd_task_custom_field_four, tapd_task_custom_field_eight, tapd_task_description,
                entry_status
                FROM tapd_entry_task 
                WHERE tapd_workspace_id = {} AND tapd_task_iteration_id in ({})
            '''.format(workspace_id, iteration_ids)

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_dev_testings():
    sql = '''
            SELECT * FROM (
                SELECT t.tapd_workspace_id, t.tapd_entry_id,
                CONCAT('测试计划_', t.tapd_test_plan_name) AS name,
                t.tapd_test_plan_creator, t.tapd_test_plan_created,
                t.tapd_test_plan_description, t.tapd_test_plan_owner,
                t.tapd_test_plan_status, t.tapd_test_plan_modified,
                t.tapd_test_plan_custom_field_1 AS biz_testing_status,
                t.tapd_test_plan_custom_field_2 AS squad,
                t.tapd_test_plan_custom_field_3 AS team
                FROM tapd_entry_test_plan t
                LEFT JOIN tapd_workspace w ON t.tapd_workspace_id = w.tapd_workspace_id
                LEFT JOIN dev_effic_test_dev_submit tt on tt.tapd_test_plan_id = t.tapd_entry_id 
                WHERE w.workspace_type = 4 AND w.workspace_is_active = 1 and t.entry_status in(1,2) and tt.tapd_story_id IS NULL
                and t.tapd_test_plan_owner IS NOT NULL) vv
            WHERE vv.biz_testing_status LIKE '%提测%';
            '''

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_test_plan_by_iteration_ids(iteration_ids):
    sql = '''
            SELECT t.tapd_workspace_id, t.tapd_entry_id,
            CONCAT('测试计划_', t.tapd_test_plan_name) AS name,
            t.tapd_test_plan_creator, t.tapd_test_plan_created,
            t.tapd_test_plan_description, t.tapd_test_plan_owner,
            t.tapd_test_plan_status,
            t.entry_status
            FROM tapd_entry_test_plan t
            LEFT JOIN tapd_workspace w ON t.tapd_workspace_id = w.tapd_workspace_id
            LEFT JOIN tapd_entry_story s ON t.tapd_entry_id = s.tapd_story_custom_field_22
            WHERE w.workspace_type = 4 AND w.workspace_is_active = 1 AND t.entry_status IN(1,2)
            AND t.tapd_test_plan_owner IS NOT NULL AND s.tapd_story_iteration_id  IN ({});
            '''.format(iteration_ids)

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_dev_submit_id_by_story_id(story_id):
    sql = '''
            select c.id as dev_submit_id
            from tapd_entry_story b
            left join dev_effic_test_dev_submit c on b.tapd_story_custom_field_22 = c.tapd_test_plan_id and c.tapd_story_id = b.tapd_entry_id 
            where b.tapd_entry_id = {};
            '''.format(story_id)

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_tapd_entity_story(workspace_id, workitem_type_id):
    sql = '''
            SELECT t.`tapd_workspace_id`, t.`tapd_entry_id`, t.`tapd_story_name`, t.tapd_story_begin, t.tapd_story_due,
                t.`tapd_story_status`,t.tapd_story_workitem_type_id,t.tapd_story_owner,t.tapd_story_effort,
                t.tapd_story_custom_field_two,t.tapd_story_custom_field_three,
                t.tapd_story_custom_field_10, t.tapd_story_custom_field_11,
                t.tapd_story_custom_field_four, t.tapd_story_custom_field_eight,
                t.tapd_story_custom_field_12, t.tapd_story_custom_field_9,
                t.tapd_story_custom_field_13, t.tapd_story_custom_field_14,
                t.tapd_story_custom_field_15, t.tapd_story_custom_field_16,
                t.tapd_story_custom_field_17, t.tapd_story_custom_field_18,
                t.tapd_story_custom_field_20, t.tapd_story_custom_field_three,
                t.tapd_story_creator, t.tapd_story_custom_field_24
            FROM `tapd_entry_story` t 
            WHERE t.`tapd_story_status` != 'resolved'
            AND t.`tapd_story_owner` != "" AND t.`tapd_story_owner` IS NOT NULL
            AND t.`tapd_story_begin` IS NOT NULL 
            AND t.`tapd_story_due` IS NOT NULL 
            and t.entry_status in(1,2)
            AND t.`tapd_workspace_id` = {}
            AND t.`tapd_story_effort` IS NOT NULL AND t.`tapd_story_effort` != 0
            AND t.`tapd_story_iteration_id` = 0
            AND t.`tapd_story_workitem_type_id` = {};
            '''.format(workspace_id, workitem_type_id)

    log.info(sql)

    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_tapd_entity_story_by_iteration_ids(workspace_id, iteration_ids, workitem_type_id):
    sql = '''
            SELECT t.id, t.`tapd_workspace_id`,t.`tapd_story_iteration_id`, t.`tapd_entry_id`, t.`tapd_story_name`, t.tapd_story_begin, t.tapd_story_due,
                t.`tapd_story_status`,t.tapd_story_workitem_type_id,t.tapd_story_owner,t.tapd_story_effort, t.entry_status, t.tapd_story_size,
                t.tapd_story_custom_field_one,
                t.tapd_story_custom_field_two, t.tapd_story_custom_field_three, t.tapd_story_custom_field_four, t.tapd_story_custom_field_eight,
                t.tapd_story_custom_field_9, t.tapd_story_custom_field_10, t.tapd_story_custom_field_11, t.tapd_story_custom_field_12, t.tapd_story_custom_field_13,
                t.tapd_story_custom_field_14, t.tapd_story_custom_field_15, t.tapd_story_custom_field_16, t.tapd_story_custom_field_17, t.tapd_story_custom_field_18,
                t.tapd_story_custom_field_20, t.tapd_story_custom_field_24, t.`tapd_story_creator` as create_user
            FROM `tapd_entry_story` t 
            WHERE t.`tapd_workspace_id` = {} and t.`tapd_story_iteration_id` IN ({})
            '''.format(workspace_id, iteration_ids)
    if workitem_type_id:
        sql += ''' AND t.tapd_story_workitem_type_id ={} '''.format(workitem_type_id)

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_tapd_entity_story_by_story_id(story_id):
    sql = '''
                SELECT t.`tapd_workspace_id`, t.`tapd_entry_id`, t.`tapd_story_name`, t.`tapd_story_status`, t.`entry_status`,t.tapd_story_iteration_id,
                t.tapd_story_type, t.tapd_story_source,t.tapd_story_module,t.tapd_story_version,t.tapd_story_category_id,t.tapd_story_workitem_type_id,t.tapd_story_parent_id,
                t.tapd_story_children_id,t.tapd_story_ancestor_id,t.tapd_story_level,t.tapd_story_effort,t.tapd_story_effort_completed,t.tapd_story_exceed,
                t.tapd_story_remain, t.tapd_story_release_id,t.tapd_story_bug_id, t.create_user, t.tapd_story_begin, t.tapd_story_due
                FROM `tapd_entry_story` t 
                WHERE  t.`tapd_entry_id` = {} limit 1;
            '''.format(story_id)

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_max_idx_by_time_batch(time_batch):
    sql = '''
            select IFNULL(max(idx_batch), 0) as max_idx_batch
            from tapd_sync_batch
            where time_batch = {}
          '''.format(time_batch)

    cursor = connection.cursor()
    cursor.execute(sql)

    return cursor


def get_product_workspace_map(tapd_workspace_id, workitem_type_en_name, workspace_type):
    workitem_type_id_list = []
    sql = '''
            SELECT DISTINCT t.tapd_workitem_type_id 
            FROM tapd_workspace_story_sync_map m 
            INNER JOIN tapd_workspace_workitem_type t ON m.product_workspace_id = t.tapd_workspace_id
            INNER JOIN tapd_workspace w ON m.product_workspace_id = w.tapd_workspace_id
            WHERE t.tapd_workspace_id = {}
            AND t.tapd_workitem_type_en_name = '{}'
            AND w.workspace_type = {};
          '''.format(tapd_workspace_id, workitem_type_en_name, workspace_type)
    cursor = connection.cursor()
    cursor.execute(sql)
    for item in cursor:
        workitem_type_id_list.append(str(item[0]))

    return workitem_type_id_list


def get_dev_story_dict(workspace_id):
    sql = '''
        SELECT DISTINCT cd.story_id, cd.dst_story_id, cm.type_mapping_type, es.tapd_story_status
        FROM tapd_workspace_story_copy_detail cd 
        INNER JOIN tapd_entry_story es ON cd.dst_workspace_id = es.tapd_workspace_id AND cd.dst_story_id = es.tapd_entry_id
        INNER JOIN tapd_workspace tw ON tw.tapd_workspace_id =  es.tapd_workspace_id
        INNER JOIN tapd_entry_column_mapping cm ON tw.id = cm.workspace_id 
        AND es.tapd_story_status in (SUBSTRING_INDEX(cm.tapd_column_options,',',1))
        WHERE cd.workspace_id = {workspace_id}
        AND cm.tapd_entry_type = 'story'
        AND cm.tapd_column = 'status'
        AND cm.mapping_is_active = 1
            '''.format(workspace_id=workspace_id)

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    story_dict = {}
    for item in cursor:
        if item[0] not in story_dict:
            story_dict[item[0]] = [
                {"dev_story_id": item[1], "type_mapping_type": item[2], "tapd_story_status": item[3]}]
        else:
            story_dict[item[0]].append({"dev_story_id": item[1], "type_mapping_type": item[2], "tapd_story_status": item[3]})

    return story_dict


def get_product_story_mapping_status(workspace_id):
    sql = '''
        SELECT DISTINCT cm.type_mapping_type, cm.tapd_column_options
        FROM tapd_workspace tw
        INNER JOIN tapd_entry_column_mapping cm ON tw.id = cm.workspace_id 
        WHERE tw.tapd_workspace_id = {workspace_id}
        AND cm.tapd_entry_type = 'story'
        AND cm.tapd_column = 'status'
        AND cm.mapping_is_active = 1
            '''.format(workspace_id=workspace_id)

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    story_mapping_status = []
    for item in cursor:
        story_mapping_status.append({"type_mapping_type": item[0], "tapd_column_options": item[1]})
    return story_mapping_status


def get_product_story_status(workspace_id, product_story_id_list):
    if not product_story_id_list:
        return []
    story_ids = ', '.join(map(str, product_story_id_list))
    sql = '''
        SELECT DISTINCT es.tapd_entry_id, es.tapd_story_status
        FROM tapd_entry_story es
        INNER JOIN tapd_workspace_story_copy_detail cd ON cd.workspace_id = es.tapd_workspace_id AND cd.story_id = es.tapd_entry_id
        WHERE es.tapd_workspace_id = {workspace_id}
        AND es.tapd_entry_id in ({story_ids})
    '''.format(workspace_id=workspace_id, story_ids=story_ids)
    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    story_dict = {}
    for item in cursor:
        story_dict[item[0]] = item[1]
    return story_dict



def get_product_custom_field(workspace_id, tapd_name):
    sql = '''
        SELECT DISTINCT wmc.tapd_custom_field
        FROM tapd_workspace w 
        INNER JOIN tapd_workspace_module wm ON w.id = wm.workspace_id
        INNER JOIN tapd_workspace_module_conf wmc ON wmc.module_id = wm.id
        WHERE w.tapd_workspace_id = {}
        AND wmc.tapd_name = '{}';
          '''.format(workspace_id, tapd_name)
    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)

    return cursor.fetchone()[0] if cursor else ''


def get_tapd_timesheet_for_dev_task(workspace_type, modified):
    """根据工作空间获取开发任务时间段内变化的耗时记录。zt@2025-01-07"""
    now_datetime = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    start_date = get_delete_start_date(modified)
    sql = '''
        SELECT tapd_timesheet.entry_status,
               tapd_timesheet.entry_desc,
               tapd_timesheet.tapd_entry_id,
               tapd_timesheet.tapd_workspace_id,
               tapd_timesheet.tapd_time_sheet_entity_id,
               tapd_timesheet.tapd_time_sheet_entity_type,
               tapd_timesheet.tapd_time_sheet_owner,
               tapd_timesheet.tapd_time_sheet_created,
               tapd_timesheet.tapd_time_sheet_modified,
               tapd_timesheet.tapd_time_sheet_spentdate,
               tapd_timesheet.tapd_time_sheet_timespent,
               tapd_timesheet.tapd_time_sheet_timeremain,
               tapd_timesheet.tapd_time_sheet_memo,
               dev_timesheet.id,
               dev_timesheet.dev_workspace_id,
               dev_timesheet.dev_task_id,
               dev_timesheet.dev_task_timesheet_id,
               dev_timesheet.dev_task_timesheet_owner,
               dev_timesheet.dev_task_timesheet_created,
               dev_timesheet.dev_task_timesheet_modified,
               dev_timesheet.dev_task_timesheet_spentdate,
               dev_timesheet.dev_task_timesheet_timespent,
               dev_timesheet.dev_task_timesheet_timeremain,
               dev_timesheet.dev_task_timesheet_memo
        FROM tapd_entry_timesheet tapd_timesheet
                 INNER JOIN tapd_workspace workspace ON workspace.tapd_workspace_id = tapd_timesheet.tapd_workspace_id
                 LEFT JOIN dev_effic_dev_task_timesheet dev_timesheet
                           ON dev_timesheet.dev_task_timesheet_id = tapd_timesheet.tapd_entry_id
        where workspace.workspace_type = {workspace_type} 
          AND workspace.workspace_is_active = 1
          AND (tapd_timesheet.update_time between '{modified}' AND '{now_datetime}'
            OR tapd_timesheet.del_time between '{start_date}' AND '{now_datetime}')
        ;
        '''.format(workspace_type=workspace_type,
                   start_date=start_date,
                   modified=modified,
                   now_datetime=now_datetime)

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_tapd_timesheet_for_test_task(workspace_type, modified):
    """根据工作空间获取测试任务时间段内变化的耗时记录。zt@2025-01-07"""
    now_datetime = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    start_date = get_delete_start_date(modified)
    sql = '''
        SELECT tapd_timesheet.entry_status,
               tapd_timesheet.entry_desc,
               tapd_timesheet.tapd_entry_id,
               tapd_timesheet.tapd_workspace_id,
               tapd_timesheet.tapd_time_sheet_entity_id,
               tapd_timesheet.tapd_time_sheet_entity_type,
               tapd_timesheet.tapd_time_sheet_owner,
               tapd_timesheet.tapd_time_sheet_created,
               tapd_timesheet.tapd_time_sheet_modified,
               tapd_timesheet.tapd_time_sheet_spentdate,
               tapd_timesheet.tapd_time_sheet_timespent,
               tapd_timesheet.tapd_time_sheet_timeremain,
               tapd_timesheet.tapd_time_sheet_memo,
               test_timesheet.id,
               test_timesheet.test_workspace_id,
               test_timesheet.test_task_id,
               test_timesheet.test_task_timesheet_id,
               test_timesheet.test_task_timesheet_owner,
               test_timesheet.test_task_timesheet_created,
               test_timesheet.test_task_timesheet_modified,
               test_timesheet.test_task_timesheet_spentdate,
               test_timesheet.test_task_timesheet_timespent,
               test_timesheet.test_task_timesheet_timeremain,
               test_timesheet.test_task_timesheet_memo
        FROM tapd_entry_timesheet tapd_timesheet
                 INNER JOIN tapd_workspace workspace ON workspace.tapd_workspace_id = tapd_timesheet.tapd_workspace_id
                 LEFT JOIN dev_effic_test_task_timesheet test_timesheet
                           ON test_timesheet.test_task_timesheet_id = tapd_timesheet.tapd_entry_id
        where workspace.workspace_type = {workspace_type} 
          AND workspace.workspace_is_active = 1
          AND (tapd_timesheet.update_time between '{modified}' AND '{now_datetime}'
            OR tapd_timesheet.del_time between '{start_date}' AND '{now_datetime}')
        ;
        '''.format(workspace_type=workspace_type,
                   start_date=start_date,
                   modified=modified,
                   now_datetime=now_datetime)

    log.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor


def get_need_create_story_list():
    sql = '''
            SELECT * FROM (
                SELECT 
                CONCAT('测试计划_', t.tapd_test_plan_name) AS story_name, 
                t.tapd_test_plan_description AS description, t.tapd_test_plan_custom_field_1 AS test_plan_status,
                t.tapd_entry_id AS tapd_test_plan_id,
                t.tapd_test_plan_owner, t.tapd_workspace_id
                FROM tapd_entry_test_plan t
                LEFT JOIN tapd_workspace w ON t.tapd_workspace_id = w.tapd_workspace_id
                LEFT JOIN tapd_entry_test_plan_bind_story bs ON t.tapd_entry_id = bs.tapd_test_plan_id
                WHERE w.workspace_type = 4 AND w.workspace_is_active = 1 AND bs.relative_test_story_id IS NULL
                AND t.tapd_test_plan_owner IS NOT NULL AND t.tapd_test_plan_owner <> '') vv
                WHERE vv.test_plan_status LIKE '%提测%';
          '''
    log.info(">>>>get_need_create_story_list sql:{}".format(sql))

    cursor = connection.cursor()
    cursor.execute(sql)

    return cursor


def get_tapd_id_by_tapd_name(workspace_id, tapd_name, tapd_entry_type=None):
    sql = '''
            SELECT t.tapd_id, t.tapd_custom_field, t.tapd_type, 
                   t.tapd_name, w.tapd_workspace_id, t.conf_column
            FROM tapd_workspace_module_conf t 
            INNER JOIN tapd_workspace_module m ON t.module_id = m.id
            INNER JOIN tapd_workspace w ON w.id = m.workspace_id
            WHERE t.tapd_name = '{}' AND w.tapd_workspace_id = '{}' and t.conf_is_sync = 1
          '''.format(tapd_name, workspace_id)
    if tapd_entry_type:
        sql = sql + " AND t.tapd_entry_type = '{}'".format(tapd_entry_type)

    log.info(">>>>get_tapd_id_by_tapd_name sql:{}".format(sql))

    cursor = connection.cursor()
    cursor.execute(sql)

    return cursor
