from django.db import models


class TapdSyncBatch(models.Model):
    create_user = models.CharField(max_length=100, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=100, verbose_name='修改人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')
    id = models.AutoField(verbose_name='主键ID', primary_key=True)

    time_batch = models.IntegerField(verbose_name='时间批')
    idx_batch = models.IntegerField(verbose_name='序号批')
    sync_mode = models.IntegerField(verbose_name='同步模式')
    sync_workspace_type = models.IntegerField(verbose_name='项目类型')
    sync_module_type = models.CharField(max_length=20, verbose_name='模块类型')
    sync_workspace_id = models.IntegerField(verbose_name='同步项目ID')
    sync_module_name = models.CharField(max_length=20, verbose_name='项目下的模块名称')

    param_start_time = models.DateTimeField(blank=True, null=True, verbose_name='参数起始时间')
    param_end_time = models.DateTimeField(blank=True, null=True, verbose_name='参数截至时间')
    sync_status = models.CharField(max_length=20, verbose_name='同步状态')
    sync_result = models.BooleanField(verbose_name='同步结果')
    req_start_time = models.DateTimeField(blank=True, null=True, verbose_name='请求起始时间')
    req_end_time = models.DateTimeField(blank=True, null=True, verbose_name='请求截至时间')
    req_cost_time = models.FloatField(verbose_name='请求耗时（秒）')
    sync_desc = models.CharField(max_length=255, verbose_name='同步说明')

    class Meta:
        db_table = 'tapd_sync_batch'
        verbose_name = 'tapd同步批次表'


class TapdSyncBatchDetail(models.Model):
    create_user = models.CharField(max_length=100, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=100, verbose_name='修改人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')
    id = models.AutoField(verbose_name='主键ID', primary_key=True)

    batch_number = models.IntegerField(verbose_name='批次号')
    sync_workspace_id = models.IntegerField(verbose_name='同步具体的项目ID')
    sync_module_name = models.CharField(max_length=20, verbose_name='同步具体的模块名')
    sync_detail_status = models.CharField(max_length=20, verbose_name='明细同步状态')
    sync_detail_result = models.BooleanField(verbose_name='明细同步结果')

    req_start_time = models.DateTimeField(blank=True, null=True, verbose_name='请求起始时间')
    req_end_time = models.DateTimeField(blank=True, null=True, verbose_name='请求截至时间')
    req_cost_time = models.FloatField(verbose_name='请求耗时（秒）')
    sync_detail_desc = models.CharField(max_length=255, verbose_name='明细同步说明')

    class Meta:
        db_table = 'tapd_sync_batch_detail'
        verbose_name = 'tapd同步批次明细表'


class TapdHisBug(models.Model):
    create_user = models.CharField(max_length=100, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=100, verbose_name='修改人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')
    id = models.AutoField(verbose_name='主键ID', primary_key=True)

    bug_status = models.IntegerField(verbose_name='缺陷数据状态')
    ins_batch_number = models.IntegerField(verbose_name='插入时批次号')
    upd_batch_number = models.IntegerField(verbose_name='最后一次更新时批次号')
    del_batch_number = models.IntegerField(verbose_name='最后一次删除时批次号')
    del_user = models.CharField(max_length=100, verbose_name='最后一次删除人')
    del_time = models.DateTimeField(blank=True, null=True, verbose_name='最后一次删除时间')
    rec_batch_number = models.IntegerField(verbose_name='最后一次恢复时批次号')
    rec_user = models.CharField(max_length=100, verbose_name='最后一次恢复人')
    rec_time = models.DateTimeField(blank=True, null=True, verbose_name='最后一次恢复时间')

    res_json = models.JSONField(verbose_name='请求返回Json')

    tapd_workspace_id = models.IntegerField(verbose_name='项目ID')
    tapd_bug_id = models.CharField(max_length=20, verbose_name='缺陷ID')
    tapd_bug_title = models.CharField(max_length=200, verbose_name='缺陷标题')
    tapd_bug_description = models.CharField(max_length=255, verbose_name='描述（备用）')
    tapd_bug_priority = models.CharField(max_length=50, verbose_name='优先级')
    tapd_bug_severity = models.CharField(max_length=10, verbose_name='严重程度')

    tapd_bug_module = models.CharField(max_length=100, verbose_name='模块')
    tapd_bug_status = models.CharField(max_length=20, verbose_name='状态')
    tapd_bug_reporter = models.CharField(max_length=100, verbose_name='创建人')
    tapd_bug_created = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    tapd_bug_bugtype = models.CharField(max_length=50, verbose_name='缺陷类型')
    tapd_bug_resolved = models.DateTimeField(blank=True, null=True, verbose_name='修复时间')
    tapd_bug_closed = models.DateTimeField(blank=True, null=True, verbose_name='关闭时间')
    tapd_bug_modified = models.DateTimeField(blank=True, null=True, verbose_name='最后修改时间')
    tapd_bug_lastmodify = models.CharField(max_length=50, verbose_name='最后修改人')
    tapd_bug_de = models.CharField(max_length=50, verbose_name='开发人员')
    tapd_bug_fixer = models.CharField(max_length=50, verbose_name='修复人')
    tapd_bug_version_test = models.CharField(max_length=50, verbose_name='验证版本')
    tapd_bug_version_report = models.CharField(max_length=50, verbose_name='发现版本')
    tapd_bug_version_close = models.CharField(max_length=50, verbose_name='关闭版本')
    tapd_bug_version_fix = models.CharField(max_length=50, verbose_name='合入版本')
    tapd_bug_baseline_find = models.CharField(max_length=50, verbose_name='发现基线')
    tapd_bug_baseline_join = models.CharField(max_length=50, verbose_name='合入基线')
    tapd_bug_baseline_close = models.CharField(max_length=50, verbose_name='关闭版本')
    tapd_bug_baseline_test = models.CharField(max_length=50, verbose_name='验证基线')
    tapd_bug_sourcephase = models.CharField(max_length=50, verbose_name='引入阶段')
    tapd_bug_te = models.CharField(max_length=50, verbose_name='测试人员')
    tapd_bug_current_owner = models.CharField(max_length=50, verbose_name='当前处理人')
    tapd_bug_iteration_id = models.IntegerField(verbose_name='迭代ID')
    tapd_bug_resolution = models.CharField(max_length=50, verbose_name='解决方法')
    tapd_bug_source = models.CharField(max_length=50, verbose_name='缺陷根源')
    tapd_bug_originphase = models.CharField(max_length=50, verbose_name='发现阶段')
    tapd_bug_confirmer = models.CharField(max_length=50, verbose_name='确认人')
    tapd_bug_milestone = models.CharField(max_length=50, verbose_name='里程碑')
    tapd_bug_participator = models.CharField(max_length=50, verbose_name='参与人')
    tapd_bug_closer = models.CharField(max_length=50, verbose_name='关闭人')
    tapd_bug_platform = models.CharField(max_length=50, verbose_name='软件平台')
    tapd_bug_os = models.CharField(max_length=50, verbose_name='操作系统')
    tapd_bug_testtype = models.CharField(max_length=50, verbose_name='测试类型')
    tapd_bug_testphase = models.CharField(max_length=50, verbose_name='测试阶段')
    tapd_bug_frequency = models.CharField(max_length=50, verbose_name='重现规律')
    tapd_bug_cc = models.CharField(max_length=50, verbose_name='抄送人')
    tapd_bug_regression_number = models.CharField(max_length=50, verbose_name='回归数')
    tapd_bug_flows = models.CharField(max_length=500, verbose_name='工作流')
    tapd_bug_feature = models.CharField(max_length=50, verbose_name='功能')
    tapd_bug_testmode = models.CharField(max_length=50, verbose_name='测试方式')
    tapd_bug_estimate = models.IntegerField(verbose_name='估算')
    tapd_bug_issue_id = models.CharField(max_length=50, verbose_name='发行ID')
    tapd_bug_created_from = models.CharField(max_length=50, verbose_name='创建来源')
    tapd_bug_release_id = models.IntegerField(verbose_name='发布计划')
    tapd_bug_verify_time = models.DateTimeField(blank=True, null=True, verbose_name='验证时间')
    tapd_bug_reject_time = models.DateTimeField(blank=True, null=True, verbose_name='拒绝时间')
    tapd_bug_reopen_time = models.DateTimeField(blank=True, null=True, verbose_name='重开时间')
    tapd_bug_audit_time = models.DateTimeField(blank=True, null=True, verbose_name='审核时间')
    tapd_bug_suspend_time = models.DateTimeField(blank=True, null=True, verbose_name='挂起时间')
    tapd_bug_due = models.DateField(blank=True, null=True, verbose_name='预计')
    tapd_bug_begin = models.DateField(blank=True, null=True, verbose_name='预计开始')
    tapd_bug_deadline = models.DateField(blank=True, null=True, verbose_name='解决期限')
    tapd_bug_in_progress_time = models.DateTimeField(blank=True, null=True, verbose_name='接受处理时间')
    tapd_bug_assigned_time = models.DateTimeField(blank=True, null=True, verbose_name='分配时间')
    tapd_bug_template_id = models.CharField(max_length=50, verbose_name='模板ID')
    tapd_bug_story_id = models.CharField(max_length=50, verbose_name='故事ID')
    tapd_bug_label = models.CharField(max_length=50, verbose_name='标签')
    tapd_bug_size = models.CharField(max_length=50, verbose_name='大小')
    tapd_bug_effort = models.IntegerField(verbose_name='预估工时')
    tapd_bug_effort_completed = models.CharField(max_length=50, verbose_name='完成工时')
    tapd_bug_exceed = models.CharField(max_length=50, verbose_name='超出工时')
    tapd_bug_remain = models.CharField(max_length=50, verbose_name='剩余工时')
    tapd_bug_priority_label = models.CharField(max_length=50, verbose_name='优先级标签')

    tapd_bug_custom_field_one = models.CharField(max_length=100, verbose_name='自定义字段001')
    tapd_bug_custom_field_two = models.CharField(max_length=100, verbose_name='自定义字段001')
    tapd_bug_custom_field_three = models.CharField(max_length=100, verbose_name='自定义字段003')
    tapd_bug_custom_field_four = models.CharField(max_length=100, verbose_name='自定义字段004')
    tapd_bug_custom_field_five = models.CharField(max_length=100, verbose_name='自定义字段005')
    tapd_bug_custom_field_6 = models.CharField(max_length=100, verbose_name='自定义字段006')
    tapd_bug_custom_field_7 = models.CharField(max_length=100, verbose_name='自定义字段007')
    tapd_bug_custom_field_8 = models.CharField(max_length=100, verbose_name='自定义字段008')
    tapd_bug_custom_field_9 = models.CharField(max_length=100, verbose_name='自定义字段009')
    tapd_bug_custom_field_10 = models.CharField(max_length=100, verbose_name='自定义字段010')
    tapd_bug_custom_field_11 = models.CharField(max_length=100, verbose_name='自定义字段011')
    tapd_bug_custom_field_12 = models.CharField(max_length=100, verbose_name='自定义字段012')
    tapd_bug_custom_field_13 = models.CharField(max_length=100, verbose_name='自定义字段013')
    tapd_bug_custom_field_14 = models.CharField(max_length=100, verbose_name='自定义字段014')
    tapd_bug_custom_field_15 = models.CharField(max_length=100, verbose_name='自定义字段015')
    tapd_bug_custom_field_16 = models.CharField(max_length=100, verbose_name='自定义字段016')
    tapd_bug_custom_field_17 = models.CharField(max_length=100, verbose_name='自定义字段017')
    tapd_bug_custom_field_18 = models.CharField(max_length=100, verbose_name='自定义字段018')
    tapd_bug_custom_field_19 = models.CharField(max_length=100, verbose_name='自定义字段019')
    tapd_bug_custom_field_20 = models.CharField(max_length=100, verbose_name='自定义字段020')
    tapd_bug_custom_field_21 = models.CharField(max_length=100, verbose_name='自定义字段021')
    tapd_bug_custom_field_22 = models.CharField(max_length=100, verbose_name='自定义字段022')
    tapd_bug_custom_field_23 = models.CharField(max_length=100, verbose_name='自定义字段023')
    tapd_bug_custom_field_24 = models.CharField(max_length=100, verbose_name='自定义字段024')
    tapd_bug_custom_field_25 = models.CharField(max_length=100, verbose_name='自定义字段025')
    tapd_bug_custom_field_26 = models.CharField(max_length=100, verbose_name='自定义字段026')
    tapd_bug_custom_field_27 = models.CharField(max_length=100, verbose_name='自定义字段027')
    tapd_bug_custom_field_28 = models.CharField(max_length=100, verbose_name='自定义字段028')
    tapd_bug_custom_field_29 = models.CharField(max_length=100, verbose_name='自定义字段029')
    tapd_bug_custom_field_30 = models.CharField(max_length=100, verbose_name='自定义字段030')
    tapd_bug_custom_field_31 = models.CharField(max_length=100, verbose_name='自定义字段031')
    tapd_bug_custom_field_32 = models.CharField(max_length=100, verbose_name='自定义字段032')
    tapd_bug_custom_field_33 = models.CharField(max_length=100, verbose_name='自定义字段033')
    tapd_bug_custom_field_34 = models.CharField(max_length=100, verbose_name='自定义字段034')
    tapd_bug_custom_field_35 = models.CharField(max_length=100, verbose_name='自定义字段035')
    tapd_bug_custom_field_36 = models.CharField(max_length=100, verbose_name='自定义字段036')
    tapd_bug_custom_field_37 = models.CharField(max_length=100, verbose_name='自定义字段037')
    tapd_bug_custom_field_38 = models.CharField(max_length=100, verbose_name='自定义字段038')
    tapd_bug_custom_field_39 = models.CharField(max_length=100, verbose_name='自定义字段039')
    tapd_bug_custom_field_40 = models.CharField(max_length=100, verbose_name='自定义字段040')
    tapd_bug_custom_field_41 = models.CharField(max_length=100, verbose_name='自定义字段041')
    tapd_bug_custom_field_42 = models.CharField(max_length=100, verbose_name='自定义字段042')
    tapd_bug_custom_field_43 = models.CharField(max_length=100, verbose_name='自定义字段043')
    tapd_bug_custom_field_44 = models.CharField(max_length=100, verbose_name='自定义字段044')
    tapd_bug_custom_field_45 = models.CharField(max_length=100, verbose_name='自定义字段045')
    tapd_bug_custom_field_46 = models.CharField(max_length=100, verbose_name='自定义字段046')
    tapd_bug_custom_field_47 = models.CharField(max_length=100, verbose_name='自定义字段047')
    tapd_bug_custom_field_48 = models.CharField(max_length=100, verbose_name='自定义字段048')
    tapd_bug_custom_field_49 = models.CharField(max_length=100, verbose_name='自定义字段049')
    tapd_bug_custom_field_50 = models.CharField(max_length=100, verbose_name='自定义字段050')
    tapd_bug_custom_field_51 = models.CharField(max_length=100, verbose_name='自定义字段051')
    tapd_bug_custom_field_52 = models.CharField(max_length=100, verbose_name='自定义字段052')
    tapd_bug_custom_field_53 = models.CharField(max_length=100, verbose_name='自定义字段053')
    tapd_bug_custom_field_54 = models.CharField(max_length=100, verbose_name='自定义字段054')
    tapd_bug_custom_field_55 = models.CharField(max_length=100, verbose_name='自定义字段055')
    tapd_bug_custom_field_56 = models.CharField(max_length=100, verbose_name='自定义字段056')
    tapd_bug_custom_field_57 = models.CharField(max_length=100, verbose_name='自定义字段057')
    tapd_bug_custom_field_58 = models.CharField(max_length=100, verbose_name='自定义字段058')
    tapd_bug_custom_field_59 = models.CharField(max_length=100, verbose_name='自定义字段059')
    tapd_bug_custom_field_60 = models.CharField(max_length=100, verbose_name='自定义字段060')
    tapd_bug_custom_field_61 = models.CharField(max_length=100, verbose_name='自定义字段061')
    tapd_bug_custom_field_62 = models.CharField(max_length=100, verbose_name='自定义字段062')
    tapd_bug_custom_field_63 = models.CharField(max_length=100, verbose_name='自定义字段063')
    tapd_bug_custom_field_64 = models.CharField(max_length=100, verbose_name='自定义字段064')
    tapd_bug_custom_field_65 = models.CharField(max_length=100, verbose_name='自定义字段065')
    tapd_bug_custom_field_66 = models.CharField(max_length=100, verbose_name='自定义字段066')
    tapd_bug_custom_field_67 = models.CharField(max_length=100, verbose_name='自定义字段067')
    tapd_bug_custom_field_68 = models.CharField(max_length=100, verbose_name='自定义字段068')
    tapd_bug_custom_field_69 = models.CharField(max_length=100, verbose_name='自定义字段069')
    tapd_bug_custom_field_70 = models.CharField(max_length=100, verbose_name='自定义字段070')
    tapd_bug_custom_field_71 = models.CharField(max_length=100, verbose_name='自定义字段071')
    tapd_bug_custom_field_72 = models.CharField(max_length=100, verbose_name='自定义字段072')
    tapd_bug_custom_field_73 = models.CharField(max_length=100, verbose_name='自定义字段073')
    tapd_bug_custom_field_74 = models.CharField(max_length=100, verbose_name='自定义字段074')
    tapd_bug_custom_field_75 = models.CharField(max_length=100, verbose_name='自定义字段075')
    tapd_bug_custom_field_76 = models.CharField(max_length=100, verbose_name='自定义字段076')
    tapd_bug_custom_field_77 = models.CharField(max_length=100, verbose_name='自定义字段077')
    tapd_bug_custom_field_78 = models.CharField(max_length=100, verbose_name='自定义字段078')
    tapd_bug_custom_field_79 = models.CharField(max_length=100, verbose_name='自定义字段079')
    tapd_bug_custom_field_80 = models.CharField(max_length=100, verbose_name='自定义字段080')
    tapd_bug_custom_field_81 = models.CharField(max_length=100, verbose_name='自定义字段081')
    tapd_bug_custom_field_82 = models.CharField(max_length=100, verbose_name='自定义字段082')
    tapd_bug_custom_field_83 = models.CharField(max_length=100, verbose_name='自定义字段083')
    tapd_bug_custom_field_84 = models.CharField(max_length=100, verbose_name='自定义字段084')
    tapd_bug_custom_field_85 = models.CharField(max_length=100, verbose_name='自定义字段085')
    tapd_bug_custom_field_86 = models.CharField(max_length=100, verbose_name='自定义字段086')
    tapd_bug_custom_field_87 = models.CharField(max_length=100, verbose_name='自定义字段087')
    tapd_bug_custom_field_88 = models.CharField(max_length=100, verbose_name='自定义字段088')
    tapd_bug_custom_field_89 = models.CharField(max_length=100, verbose_name='自定义字段089')
    tapd_bug_custom_field_90 = models.CharField(max_length=100, verbose_name='自定义字段090')
    tapd_bug_custom_field_91 = models.CharField(max_length=100, verbose_name='自定义字段091')
    tapd_bug_custom_field_92 = models.CharField(max_length=100, verbose_name='自定义字段092')
    tapd_bug_custom_field_93 = models.CharField(max_length=100, verbose_name='自定义字段093')
    tapd_bug_custom_field_94 = models.CharField(max_length=100, verbose_name='自定义字段094')
    tapd_bug_custom_field_95 = models.CharField(max_length=100, verbose_name='自定义字段095')
    tapd_bug_custom_field_96 = models.CharField(max_length=100, verbose_name='自定义字段096')
    tapd_bug_custom_field_97 = models.CharField(max_length=100, verbose_name='自定义字段097')
    tapd_bug_custom_field_98 = models.CharField(max_length=100, verbose_name='自定义字段098')
    tapd_bug_custom_field_99 = models.CharField(max_length=100, verbose_name='自定义字段099')
    tapd_bug_custom_field_100 = models.CharField(max_length=100, verbose_name='自定义字段100')

    tapd_bug_custom_plan_field_1 = models.CharField(max_length=100, verbose_name='自定义计划字段01')
    tapd_bug_custom_plan_field_2 = models.CharField(max_length=100, verbose_name='自定义计划字段02')
    tapd_bug_custom_plan_field_3 = models.CharField(max_length=100, verbose_name='自定义计划字段03')
    tapd_bug_custom_plan_field_4 = models.CharField(max_length=100, verbose_name='自定义计划字段04')
    tapd_bug_custom_plan_field_5 = models.CharField(max_length=100, verbose_name='自定义计划字段05')
    tapd_bug_custom_plan_field_6 = models.CharField(max_length=100, verbose_name='自定义计划字段06')
    tapd_bug_custom_plan_field_7 = models.CharField(max_length=100, verbose_name='自定义计划字段07')
    tapd_bug_custom_plan_field_8 = models.CharField(max_length=100, verbose_name='自定义计划字段08')
    tapd_bug_custom_plan_field_9 = models.CharField(max_length=100, verbose_name='自定义计划字段09')
    tapd_bug_custom_plan_field_10 = models.CharField(max_length=100, verbose_name='自定义计划字段10')

    his_bug_desc = models.CharField(max_length=255, verbose_name='缺陷同步历史说明')

    class Meta:
        db_table = 'tapd_his_bug'
        verbose_name = 'tapd缺陷同步表'


class TapdEntryBug(models.Model):
    create_user = models.CharField(max_length=100, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=100, verbose_name='修改人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')
    id = models.AutoField(verbose_name='主键ID', primary_key=True)

    ins_batch_number = models.IntegerField(verbose_name='插入时批次号')
    upd_batch_number = models.IntegerField(verbose_name='最后一次更新时批次号')
    del_batch_number = models.IntegerField(verbose_name='最后一次删除时批次号')
    del_user = models.CharField(max_length=100, verbose_name='最后一次删除人')
    del_time = models.DateTimeField(blank=True, null=True, verbose_name='最后一次删除时间')
    rec_batch_number = models.IntegerField(verbose_name='最后一次恢复时批次号')
    rec_user = models.CharField(max_length=100, verbose_name='最后一次恢复人')
    rec_time = models.DateTimeField(blank=True, null=True, verbose_name='最后一次恢复时间')
    entry_status = models.IntegerField(verbose_name='缺陷数据状态')
    entry_desc = models.CharField(max_length=255, verbose_name='缺陷实体说明')

    tapd_workspace_id = models.IntegerField(verbose_name='项目ID')

    tapd_entry_id = models.IntegerField(verbose_name='缺陷ID')
    tapd_bug_title = models.CharField(max_length=200, verbose_name='缺陷标题')
    tapd_bug_description = models.CharField(max_length=255, verbose_name='描述（备用）')
    tapd_bug_priority = models.CharField(max_length=50, verbose_name='优先级')
    tapd_bug_severity = models.CharField(max_length=10, verbose_name='严重程度')

    tapd_bug_module = models.CharField(max_length=100, verbose_name='模块')
    tapd_bug_status = models.CharField(max_length=20, verbose_name='状态')
    tapd_bug_reporter = models.CharField(max_length=100, verbose_name='创建人')
    tapd_bug_created = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    tapd_bug_bugtype = models.CharField(max_length=50, verbose_name='缺陷类型')
    tapd_bug_resolved = models.CharField(max_length=20, verbose_name='修复时间')
    tapd_bug_closed = models.CharField(max_length=20, verbose_name='关闭时间')
    tapd_bug_modified = models.CharField(max_length=20, verbose_name='最后修改时间')
    tapd_bug_lastmodify = models.CharField(max_length=50, verbose_name='最后修改人')
    tapd_bug_de = models.CharField(max_length=50, verbose_name='开发人员')
    tapd_bug_fixer = models.CharField(max_length=50, verbose_name='修复人')
    tapd_bug_version_test = models.CharField(max_length=50, verbose_name='验证版本')
    tapd_bug_version_report = models.CharField(max_length=50, verbose_name='发现版本')
    tapd_bug_version_close = models.CharField(max_length=50, verbose_name='关闭版本')
    tapd_bug_version_fix = models.CharField(max_length=50, verbose_name='合入版本')
    tapd_bug_baseline_find = models.CharField(max_length=50, verbose_name='发现基线')
    tapd_bug_baseline_join = models.CharField(max_length=50, verbose_name='合入基线')
    tapd_bug_baseline_close = models.CharField(max_length=50, verbose_name='关闭版本')
    tapd_bug_baseline_test = models.CharField(max_length=50, verbose_name='验证基线')
    tapd_bug_sourcephase = models.CharField(max_length=50, verbose_name='引入阶段')
    tapd_bug_te = models.CharField(max_length=50, verbose_name='测试人员')
    tapd_bug_current_owner = models.CharField(max_length=50, verbose_name='当前处理人')
    tapd_bug_iteration_id = models.IntegerField(verbose_name='迭代ID')
    tapd_bug_resolution = models.CharField(max_length=50, verbose_name='解决方法')
    tapd_bug_source = models.CharField(max_length=50, verbose_name='缺陷根源')
    tapd_bug_originphase = models.CharField(max_length=50, verbose_name='发现阶段')
    tapd_bug_confirmer = models.CharField(max_length=50, verbose_name='确认人')
    tapd_bug_milestone = models.CharField(max_length=50, verbose_name='里程碑')
    tapd_bug_participator = models.CharField(max_length=50, verbose_name='参与人')
    tapd_bug_closer = models.CharField(max_length=50, verbose_name='关闭人')
    tapd_bug_platform = models.CharField(max_length=50, verbose_name='软件平台')
    tapd_bug_os = models.CharField(max_length=50, verbose_name='操作系统')
    tapd_bug_testtype = models.CharField(max_length=50, verbose_name='测试类型')
    tapd_bug_testphase = models.CharField(max_length=50, verbose_name='测试阶段')
    tapd_bug_frequency = models.CharField(max_length=50, verbose_name='重现规律')
    tapd_bug_cc = models.CharField(max_length=50, verbose_name='抄送人')
    tapd_bug_regression_number = models.CharField(max_length=50, verbose_name='回归数')
    tapd_bug_flows = models.CharField(max_length=500, verbose_name='工作流')
    tapd_bug_feature = models.CharField(max_length=50, verbose_name='功能')
    tapd_bug_testmode = models.CharField(max_length=50, verbose_name='测试方式')
    tapd_bug_estimate = models.IntegerField(verbose_name='估算')
    tapd_bug_issue_id = models.IntegerField(verbose_name='发行ID')
    tapd_bug_created_from = models.CharField(max_length=50, verbose_name='创建来源')
    tapd_bug_release_id = models.IntegerField(verbose_name='发布计划')
    tapd_bug_verify_time = models.CharField(max_length=20, verbose_name='验证时间')
    tapd_bug_reject_time = models.CharField(max_length=20, verbose_name='拒绝时间')
    tapd_bug_reopen_time = models.CharField(max_length=20, verbose_name='重开时间')
    tapd_bug_audit_time = models.CharField(max_length=20, verbose_name='审核时间')
    tapd_bug_suspend_time = models.CharField(max_length=20, verbose_name='挂起时间')
    tapd_bug_due = models.CharField(max_length=20, verbose_name='预计')
    tapd_bug_begin = models.CharField(max_length=20, verbose_name='预计开始')
    tapd_bug_deadline = models.CharField(max_length=20, verbose_name='解决期限')
    tapd_bug_in_progress_time = models.CharField(max_length=20, verbose_name='接受处理时间')
    tapd_bug_assigned_time = models.CharField(max_length=20, verbose_name='分配时间')
    tapd_bug_template_id = models.IntegerField(verbose_name='模板ID')
    tapd_bug_story_id = models.IntegerField(verbose_name='故事ID')
    tapd_bug_label = models.CharField(max_length=50, verbose_name='标签')
    tapd_bug_size = models.CharField(max_length=50, verbose_name='大小')
    tapd_bug_effort = models.IntegerField(verbose_name='预估工时')
    tapd_bug_effort_completed = models.CharField(max_length=50, verbose_name='完成工时')
    tapd_bug_exceed = models.CharField(max_length=50, verbose_name='超出工时')
    tapd_bug_remain = models.CharField(max_length=50, verbose_name='剩余工时')
    tapd_bug_priority_label = models.CharField(max_length=50, verbose_name='优先级标签')

    tapd_bug_custom_field_one = models.CharField(max_length=100, verbose_name='自定义字段001')
    tapd_bug_custom_field_two = models.CharField(max_length=100, verbose_name='自定义字段001')
    tapd_bug_custom_field_three = models.CharField(max_length=200, verbose_name='自定义字段003')
    tapd_bug_custom_field_four = models.CharField(max_length=200, verbose_name='自定义字段004')
    tapd_bug_custom_field_five = models.CharField(max_length=100, verbose_name='自定义字段005')
    tapd_bug_custom_field_6 = models.CharField(max_length=100, verbose_name='自定义字段006')
    tapd_bug_custom_field_7 = models.CharField(max_length=100, verbose_name='自定义字段007')
    tapd_bug_custom_field_8 = models.CharField(max_length=100, verbose_name='自定义字段008')
    tapd_bug_custom_field_9 = models.CharField(max_length=100, verbose_name='自定义字段009')
    tapd_bug_custom_field_10 = models.CharField(max_length=100, verbose_name='自定义字段010')
    tapd_bug_custom_field_11 = models.CharField(max_length=100, verbose_name='自定义字段011')
    tapd_bug_custom_field_12 = models.CharField(max_length=100, verbose_name='自定义字段012')
    tapd_bug_custom_field_13 = models.CharField(max_length=100, verbose_name='自定义字段013')
    tapd_bug_custom_field_14 = models.CharField(max_length=100, verbose_name='自定义字段014')
    tapd_bug_custom_field_15 = models.CharField(max_length=100, verbose_name='自定义字段015')
    tapd_bug_custom_field_16 = models.CharField(max_length=100, verbose_name='自定义字段016')
    tapd_bug_custom_field_17 = models.CharField(max_length=100, verbose_name='自定义字段017')
    tapd_bug_custom_field_18 = models.CharField(max_length=100, verbose_name='自定义字段018')
    tapd_bug_custom_field_19 = models.CharField(max_length=100, verbose_name='自定义字段019')
    tapd_bug_custom_field_20 = models.CharField(max_length=100, verbose_name='自定义字段020')
    tapd_bug_custom_field_21 = models.CharField(max_length=100, verbose_name='自定义字段021')
    tapd_bug_custom_field_22 = models.CharField(max_length=100, verbose_name='自定义字段022')
    tapd_bug_custom_field_23 = models.CharField(max_length=100, verbose_name='自定义字段023')
    tapd_bug_custom_field_24 = models.CharField(max_length=100, verbose_name='自定义字段024')
    tapd_bug_custom_field_25 = models.CharField(max_length=100, verbose_name='自定义字段025')
    tapd_bug_custom_field_26 = models.CharField(max_length=100, verbose_name='自定义字段026')
    tapd_bug_custom_field_27 = models.CharField(max_length=100, verbose_name='自定义字段027')
    tapd_bug_custom_field_28 = models.CharField(max_length=100, verbose_name='自定义字段028')
    tapd_bug_custom_field_29 = models.CharField(max_length=100, verbose_name='自定义字段029')
    tapd_bug_custom_field_30 = models.CharField(max_length=100, verbose_name='自定义字段030')
    tapd_bug_custom_field_31 = models.CharField(max_length=100, verbose_name='自定义字段031')
    tapd_bug_custom_field_32 = models.CharField(max_length=100, verbose_name='自定义字段032')
    tapd_bug_custom_field_33 = models.CharField(max_length=100, verbose_name='自定义字段033')
    tapd_bug_custom_field_34 = models.CharField(max_length=100, verbose_name='自定义字段034')
    tapd_bug_custom_field_35 = models.CharField(max_length=100, verbose_name='自定义字段035')
    tapd_bug_custom_field_36 = models.CharField(max_length=100, verbose_name='自定义字段036')
    tapd_bug_custom_field_37 = models.CharField(max_length=100, verbose_name='自定义字段037')
    tapd_bug_custom_field_38 = models.CharField(max_length=100, verbose_name='自定义字段038')
    tapd_bug_custom_field_39 = models.CharField(max_length=100, verbose_name='自定义字段039')
    tapd_bug_custom_field_40 = models.CharField(max_length=100, verbose_name='自定义字段040')
    tapd_bug_custom_field_41 = models.CharField(max_length=100, verbose_name='自定义字段041')
    tapd_bug_custom_field_42 = models.CharField(max_length=100, verbose_name='自定义字段042')
    tapd_bug_custom_field_43 = models.CharField(max_length=100, verbose_name='自定义字段043')
    tapd_bug_custom_field_44 = models.CharField(max_length=100, verbose_name='自定义字段044')
    tapd_bug_custom_field_45 = models.CharField(max_length=100, verbose_name='自定义字段045')
    tapd_bug_custom_field_46 = models.CharField(max_length=100, verbose_name='自定义字段046')
    tapd_bug_custom_field_47 = models.CharField(max_length=100, verbose_name='自定义字段047')
    tapd_bug_custom_field_48 = models.CharField(max_length=100, verbose_name='自定义字段048')
    tapd_bug_custom_field_49 = models.CharField(max_length=100, verbose_name='自定义字段049')
    tapd_bug_custom_field_50 = models.CharField(max_length=100, verbose_name='自定义字段050')
    tapd_bug_custom_field_51 = models.CharField(max_length=100, verbose_name='自定义字段051')
    tapd_bug_custom_field_52 = models.CharField(max_length=100, verbose_name='自定义字段052')
    tapd_bug_custom_field_53 = models.CharField(max_length=100, verbose_name='自定义字段053')
    tapd_bug_custom_field_54 = models.CharField(max_length=100, verbose_name='自定义字段054')
    tapd_bug_custom_field_55 = models.CharField(max_length=100, verbose_name='自定义字段055')
    tapd_bug_custom_field_56 = models.CharField(max_length=100, verbose_name='自定义字段056')
    tapd_bug_custom_field_57 = models.CharField(max_length=100, verbose_name='自定义字段057')
    tapd_bug_custom_field_58 = models.CharField(max_length=100, verbose_name='自定义字段058')
    tapd_bug_custom_field_59 = models.CharField(max_length=100, verbose_name='自定义字段059')
    tapd_bug_custom_field_60 = models.CharField(max_length=100, verbose_name='自定义字段060')
    tapd_bug_custom_field_61 = models.CharField(max_length=100, verbose_name='自定义字段061')
    tapd_bug_custom_field_62 = models.CharField(max_length=100, verbose_name='自定义字段062')
    tapd_bug_custom_field_63 = models.CharField(max_length=100, verbose_name='自定义字段063')
    tapd_bug_custom_field_64 = models.CharField(max_length=100, verbose_name='自定义字段064')
    tapd_bug_custom_field_65 = models.CharField(max_length=100, verbose_name='自定义字段065')
    tapd_bug_custom_field_66 = models.CharField(max_length=100, verbose_name='自定义字段066')
    tapd_bug_custom_field_67 = models.CharField(max_length=100, verbose_name='自定义字段067')
    tapd_bug_custom_field_68 = models.CharField(max_length=100, verbose_name='自定义字段068')
    tapd_bug_custom_field_69 = models.CharField(max_length=100, verbose_name='自定义字段069')
    tapd_bug_custom_field_70 = models.CharField(max_length=100, verbose_name='自定义字段070')
    tapd_bug_custom_field_71 = models.CharField(max_length=100, verbose_name='自定义字段071')
    tapd_bug_custom_field_72 = models.CharField(max_length=100, verbose_name='自定义字段072')
    tapd_bug_custom_field_73 = models.CharField(max_length=100, verbose_name='自定义字段073')
    tapd_bug_custom_field_74 = models.CharField(max_length=100, verbose_name='自定义字段074')
    tapd_bug_custom_field_75 = models.CharField(max_length=100, verbose_name='自定义字段075')
    tapd_bug_custom_field_76 = models.CharField(max_length=100, verbose_name='自定义字段076')
    tapd_bug_custom_field_77 = models.CharField(max_length=100, verbose_name='自定义字段077')
    tapd_bug_custom_field_78 = models.CharField(max_length=100, verbose_name='自定义字段078')
    tapd_bug_custom_field_79 = models.CharField(max_length=100, verbose_name='自定义字段079')
    tapd_bug_custom_field_80 = models.CharField(max_length=100, verbose_name='自定义字段080')
    tapd_bug_custom_field_81 = models.CharField(max_length=100, verbose_name='自定义字段081')
    tapd_bug_custom_field_82 = models.CharField(max_length=100, verbose_name='自定义字段082')
    tapd_bug_custom_field_83 = models.CharField(max_length=100, verbose_name='自定义字段083')
    tapd_bug_custom_field_84 = models.CharField(max_length=100, verbose_name='自定义字段084')
    tapd_bug_custom_field_85 = models.CharField(max_length=100, verbose_name='自定义字段085')
    tapd_bug_custom_field_86 = models.CharField(max_length=100, verbose_name='自定义字段086')
    tapd_bug_custom_field_87 = models.CharField(max_length=100, verbose_name='自定义字段087')
    tapd_bug_custom_field_88 = models.CharField(max_length=100, verbose_name='自定义字段088')
    tapd_bug_custom_field_89 = models.CharField(max_length=100, verbose_name='自定义字段089')
    tapd_bug_custom_field_90 = models.CharField(max_length=100, verbose_name='自定义字段090')
    tapd_bug_custom_field_91 = models.CharField(max_length=100, verbose_name='自定义字段091')
    tapd_bug_custom_field_92 = models.CharField(max_length=100, verbose_name='自定义字段092')
    tapd_bug_custom_field_93 = models.CharField(max_length=100, verbose_name='自定义字段093')
    tapd_bug_custom_field_94 = models.CharField(max_length=100, verbose_name='自定义字段094')
    tapd_bug_custom_field_95 = models.CharField(max_length=100, verbose_name='自定义字段095')
    tapd_bug_custom_field_96 = models.CharField(max_length=100, verbose_name='自定义字段096')
    tapd_bug_custom_field_97 = models.CharField(max_length=100, verbose_name='自定义字段097')
    tapd_bug_custom_field_98 = models.CharField(max_length=100, verbose_name='自定义字段098')
    tapd_bug_custom_field_99 = models.CharField(max_length=100, verbose_name='自定义字段099')
    tapd_bug_custom_field_100 = models.CharField(max_length=100, verbose_name='自定义字段100')

    tapd_bug_custom_plan_field_1 = models.CharField(max_length=100, verbose_name='自定义计划字段01')
    tapd_bug_custom_plan_field_2 = models.CharField(max_length=100, verbose_name='自定义计划字段02')
    tapd_bug_custom_plan_field_3 = models.CharField(max_length=100, verbose_name='自定义计划字段03')
    tapd_bug_custom_plan_field_4 = models.CharField(max_length=100, verbose_name='自定义计划字段04')
    tapd_bug_custom_plan_field_5 = models.CharField(max_length=100, verbose_name='自定义计划字段05')
    tapd_bug_custom_plan_field_6 = models.CharField(max_length=100, verbose_name='自定义计划字段06')
    tapd_bug_custom_plan_field_7 = models.CharField(max_length=100, verbose_name='自定义计划字段07')
    tapd_bug_custom_plan_field_8 = models.CharField(max_length=100, verbose_name='自定义计划字段08')
    tapd_bug_custom_plan_field_9 = models.CharField(max_length=100, verbose_name='自定义计划字段09')
    tapd_bug_custom_plan_field_10 = models.CharField(max_length=100, verbose_name='自定义计划字段10')

    class Meta:
        db_table = 'tapd_entry_bug'
        verbose_name = 'tapd缺陷实体表'


class TapdEntryBugResJson(models.Model):
    tapd_entry_id = models.IntegerField(verbose_name='缺陷ID')
    res_json = models.JSONField(verbose_name='请求返回Json')

    class Meta:
        db_table = 'tapd_entry_bug_res_json'
        verbose_name = 'tapd缺陷实体返回消息表'


class TapdEntryIteration(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    tapd_workspace_id = models.IntegerField(verbose_name='tapd工作空间ID')
    tapd_entry_id = models.IntegerField(verbose_name='tapd迭代Id')
    tapd_iteration_name = models.CharField(max_length=200, verbose_name='tapd迭代名称')
    tapd_iteration_startdate = models.CharField(max_length=20, verbose_name='tapd开始时间')
    tapd_iteration_enddate = models.CharField(max_length=20, verbose_name='tapd结束时间')
    tapd_iteration_status = models.CharField(max_length=20, verbose_name='tapd迭代状态')

    entry_status = models.IntegerField(verbose_name='迭代数据状态')
    ins_batch_number = models.IntegerField(verbose_name='插入时批次号')
    upd_batch_number = models.IntegerField(verbose_name='最后一次更新时批次号')
    del_batch_number = models.IntegerField(verbose_name='最后一次删除时批次号')
    del_user = models.CharField(max_length=100, verbose_name='最后一次删除人')
    del_time = models.DateTimeField(blank=True, null=True, verbose_name='最后一次删除时间')
    rec_batch_number = models.IntegerField(verbose_name='最后一次恢复时批次号')
    rec_user = models.CharField(max_length=100, verbose_name='最后一次恢复人')
    rec_time = models.DateTimeField(blank=True, null=True, verbose_name='最后一次恢复时间')
    tapd_iteration_release_id = models.IntegerField(verbose_name='发布ID')
    tapd_iteration_description = models.CharField(max_length=255, verbose_name='迭代描述')
    tapd_iteration_creator = models.CharField(max_length=100, verbose_name='创建人')
    tapd_iteration_created = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    tapd_iteration_modified = models.CharField(max_length=20, verbose_name='修改时间')
    tapd_iteration_completed = models.CharField(max_length=20, verbose_name='完成时间')
    tapd_iteration_entity_type = models.CharField(max_length=50, verbose_name='所属实体对象')
    tapd_iteration_parent_id = models.IntegerField(verbose_name="父ID")
    tapd_iteration_ancestor_id = models.IntegerField(verbose_name="组父ID")
    tapd_iteration_path = models.CharField(max_length=500, verbose_name='迭代路径')
    tapd_iteration_workitem_type_id = models.IntegerField(verbose_name='迭代类别')
    tapd_iteration_templated_id = models.IntegerField(verbose_name='模板ID')
    tapd_iteration_plan_app_id = models.IntegerField(verbose_name='计划应用ID')
    tapd_iteration_crucial_moment = models.CharField(max_length=20, verbose_name='关键时刻')
    tapd_iteration_custom_field_1 = models.CharField(max_length=100, verbose_name='自定义字段01')
    tapd_iteration_custom_field_2 = models.CharField(max_length=100, verbose_name='自定义字段02')
    tapd_iteration_custom_field_3 = models.CharField(max_length=100, verbose_name='自定义字段03')
    tapd_iteration_custom_field_4 = models.CharField(max_length=100, verbose_name='自定义字段04')
    tapd_iteration_custom_field_5 = models.CharField(max_length=100, verbose_name='自定义字段05')
    tapd_iteration_custom_field_6 = models.CharField(max_length=100, verbose_name='自定义字段06')
    tapd_iteration_custom_field_7 = models.CharField(max_length=100, verbose_name='自定义字段07')
    tapd_iteration_custom_field_8 = models.CharField(max_length=100, verbose_name='自定义字段08')
    tapd_iteration_custom_field_9 = models.CharField(max_length=100, verbose_name='自定义字段09')
    tapd_iteration_custom_field_10 = models.CharField(max_length=100, verbose_name='自定义字段10')
    tapd_iteration_custom_field_11 = models.CharField(max_length=100, verbose_name='自定义字段11')
    tapd_iteration_custom_field_12 = models.CharField(max_length=100, verbose_name='自定义字段12')
    tapd_iteration_custom_field_13 = models.CharField(max_length=100, verbose_name='自定义字段13')
    tapd_iteration_custom_field_14 = models.CharField(max_length=100, verbose_name='自定义字段14')
    tapd_iteration_custom_field_15 = models.CharField(max_length=100, verbose_name='自定义字段15')
    tapd_iteration_custom_field_16 = models.CharField(max_length=100, verbose_name='自定义字段16')
    tapd_iteration_custom_field_17 = models.CharField(max_length=100, verbose_name='自定义字段17')
    tapd_iteration_custom_field_18 = models.CharField(max_length=100, verbose_name='自定义字段18')
    tapd_iteration_custom_field_19 = models.CharField(max_length=100, verbose_name='自定义字段19')
    tapd_iteration_custom_field_20 = models.CharField(max_length=100, verbose_name='自定义字段20')
    tapd_iteration_custom_field_21 = models.CharField(max_length=100, verbose_name='自定义字段21')
    tapd_iteration_custom_field_22 = models.CharField(max_length=100, verbose_name='自定义字段22')
    tapd_iteration_custom_field_23 = models.CharField(max_length=100, verbose_name='自定义字段23')
    tapd_iteration_custom_field_24 = models.CharField(max_length=100, verbose_name='自定义字段24')
    tapd_iteration_custom_field_25 = models.CharField(max_length=100, verbose_name='自定义字段25')
    tapd_iteration_custom_field_26 = models.CharField(max_length=100, verbose_name='自定义字段26')
    tapd_iteration_custom_field_27 = models.CharField(max_length=100, verbose_name='自定义字段27')
    tapd_iteration_custom_field_28 = models.CharField(max_length=100, verbose_name='自定义字段28')
    tapd_iteration_custom_field_29 = models.CharField(max_length=100, verbose_name='自定义字段29')
    tapd_iteration_custom_field_30 = models.CharField(max_length=100, verbose_name='自定义字段30')
    tapd_iteration_custom_field_31 = models.CharField(max_length=100, verbose_name='自定义字段31')
    tapd_iteration_custom_field_32 = models.CharField(max_length=100, verbose_name='自定义字段32')
    tapd_iteration_custom_field_33 = models.CharField(max_length=100, verbose_name='自定义字段33')
    tapd_iteration_custom_field_34 = models.CharField(max_length=100, verbose_name='自定义字段34')
    tapd_iteration_custom_field_35 = models.CharField(max_length=100, verbose_name='自定义字段35')
    tapd_iteration_custom_field_36 = models.CharField(max_length=100, verbose_name='自定义字段36')
    tapd_iteration_custom_field_37 = models.CharField(max_length=100, verbose_name='自定义字段37')
    tapd_iteration_custom_field_38 = models.CharField(max_length=100, verbose_name='自定义字段38')
    tapd_iteration_custom_field_39 = models.CharField(max_length=100, verbose_name='自定义字段39')
    tapd_iteration_custom_field_40 = models.CharField(max_length=100, verbose_name='自定义字段40')
    tapd_iteration_custom_field_41 = models.CharField(max_length=100, verbose_name='自定义字段41')
    tapd_iteration_custom_field_42 = models.CharField(max_length=100, verbose_name='自定义字段42')
    tapd_iteration_custom_field_43 = models.CharField(max_length=100, verbose_name='自定义字段43')
    tapd_iteration_custom_field_44 = models.CharField(max_length=100, verbose_name='自定义字段44')
    tapd_iteration_custom_field_45 = models.CharField(max_length=100, verbose_name='自定义字段45')
    tapd_iteration_custom_field_46 = models.CharField(max_length=100, verbose_name='自定义字段46')
    tapd_iteration_custom_field_47 = models.CharField(max_length=100, verbose_name='自定义字段47')
    tapd_iteration_custom_field_48 = models.CharField(max_length=100, verbose_name='自定义字段48')
    tapd_iteration_custom_field_49 = models.CharField(max_length=100, verbose_name='自定义字段49')
    tapd_iteration_custom_field_50 = models.CharField(max_length=100, verbose_name='自定义字段50')
    entry_desc = models.CharField(max_length=255, verbose_name='迭代描述')

    create_user = models.CharField(max_length=20, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=20, verbose_name='修改人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')

    class Meta:
        db_table = 'tapd_entry_iteration'
        verbose_name = 'tapd迭代实体表'


class TapdEntryIterationResJson(models.Model):
    tapd_entry_id = models.IntegerField(verbose_name='缺陷ID')
    res_json = models.JSONField(verbose_name='请求返回Json')

    class Meta:
        db_table = 'tapd_entry_iteration_res_json'
        verbose_name = 'tapd迭代实体返回消息表'


class TapdEntryStory(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    create_user = models.CharField(max_length=20, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=20, verbose_name='修改人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')

    tapd_workspace_id = models.IntegerField(verbose_name='tapd工作空间ID')
    tapd_entry_id = models.IntegerField(verbose_name='tapd故事Id')
    tapd_story_name = models.CharField(max_length=500, verbose_name='tapd需求名称')
    tapd_story_status = models.CharField(max_length=20, verbose_name='tapd需求状态')
    res_json = models.JSONField(verbose_name='tapd请求返回Json')

    entry_status = models.IntegerField(verbose_name='tapd需求状态')
    ins_batch_number = models.IntegerField(verbose_name='插入时批次号')
    upd_batch_number = models.IntegerField(verbose_name='最后一次更新时批次号')
    del_batch_number = models.IntegerField(verbose_name='最后一次删除时批次号')
    del_user = models.CharField(max_length=100, verbose_name='最后一次删除人')
    del_time = models.DateTimeField(blank=True, null=True, verbose_name='最后一次删除时间')
    rec_batch_number = models.IntegerField(verbose_name='最后一次恢复时批次号')
    rec_user = models.CharField(max_length=100, verbose_name='最后一次恢复人')
    rec_time = models.DateTimeField(blank=True, null=True, verbose_name='最后一次恢复时间')
    tapd_story_workitem_type_id = models.BigIntegerField(verbose_name='需求类别ID')

    tapd_story_description = models.CharField(max_length=255, verbose_name='迭代描述')
    tapd_story_creator = models.CharField(max_length=100, verbose_name='创建人')
    tapd_story_created = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    tapd_story_modified = models.CharField(max_length=20, verbose_name='修改时间')
    tapd_story_owner = models.CharField(max_length=100, verbose_name='处理人')
    tapd_story_cc = models.CharField(max_length=100, verbose_name='抄送人')
    tapd_story_begin = models.CharField(max_length=20, verbose_name='预计开始')
    tapd_story_due = models.CharField(max_length=20, verbose_name='预计结束')
    tapd_story_size = models.IntegerField(verbose_name='规模')
    tapd_story_priority = models.CharField(max_length=50, verbose_name='故事优先级')
    tapd_story_iteration_id = models.IntegerField(verbose_name='迭代ID')
    tapd_story_workitem_type_id = models.IntegerField(verbose_name='类别')
    tapd_story_developer = models.CharField(max_length=100, verbose_name='开发人员')
    tapd_story_test_focus = models.CharField(max_length=100, verbose_name='测试重点')
    tapd_story_type = models.CharField(max_length=100, verbose_name='需求类型')
    tapd_story_source = models.CharField(max_length=100, verbose_name='需求来源')
    tapd_story_module = models.CharField(max_length=100, verbose_name='模块')
    tapd_story_version = models.CharField(max_length=100, verbose_name='版本')
    tapd_story_completed = models.CharField(max_length=20, verbose_name='完成时间')
    tapd_story_category_id = models.IntegerField(verbose_name='需求分类')
    tapd_story_path = models.CharField(max_length=500, verbose_name='路径')
    tapd_story_parent_id = models.IntegerField(verbose_name='父ID')
    tapd_story_children_id = models.CharField(max_length=500, verbose_name='子ID')
    tapd_story_ancestor_id = models.IntegerField(verbose_name='祖父ID')
    tapd_story_level = models.CharField(max_length=20, verbose_name='层级')
    tapd_story_business_value = models.IntegerField(verbose_name='业务价值')
    tapd_story_effort = models.CharField(max_length=20, verbose_name='预估工时')
    tapd_story_effort_completed = models.CharField(max_length=20, verbose_name='完成工时')
    tapd_story_exceed = models.FloatField(verbose_name='超出工时')
    tapd_story_remain = models.FloatField(verbose_name='剩余工时')
    tapd_story_release_id = models.IntegerField(verbose_name='发布ID')
    tapd_story_bug_id = models.IntegerField(verbose_name='缺陷ID')
    tapd_story_templated_id = models.IntegerField(verbose_name='模板ID')
    tapd_story_created_from = models.CharField(max_length=20, verbose_name='创建来源')
    tapd_story_feature = models.CharField(max_length=50, verbose_name='功能')
    tapd_story_label = models.CharField(max_length=20, verbose_name='标签查询')
    tapd_story_progress = models.CharField(max_length=20, verbose_name='进步')
    tapd_story_creator = models.CharField(max_length=20, verbose_name='创建人')
    tapd_story_is_archived = models.IntegerField(verbose_name='是否归档')
    tapd_story_priority_label = models.CharField(max_length=50, verbose_name='优先级标签')

    tapd_story_custom_field_one = models.TextField(verbose_name='自定义字段01')
    tapd_story_custom_field_two = models.CharField(max_length=100, verbose_name='自定义字段02')
    tapd_story_custom_field_three = models.CharField(max_length=100, verbose_name='自定义字段03')
    tapd_story_custom_field_four = models.CharField(max_length=100, verbose_name='自定义字段04')
    tapd_story_custom_field_five = models.CharField(max_length=100, verbose_name='自定义字段05')
    tapd_story_custom_field_six = models.CharField(max_length=100, verbose_name='自定义字段06')
    tapd_story_custom_field_seven = models.CharField(max_length=100, verbose_name='自定义字段07')
    tapd_story_custom_field_eight = models.TextField(verbose_name='自定义字段08')
    tapd_story_custom_field_9 = models.TextField(verbose_name='自定义字段09')
    tapd_story_custom_field_10 = models.CharField(max_length=100, verbose_name='自定义字段10')
    tapd_story_custom_field_11 = models.CharField(max_length=100, verbose_name='自定义字段11')
    tapd_story_custom_field_12 = models.CharField(max_length=100, verbose_name='自定义字段12')
    tapd_story_custom_field_13 = models.CharField(max_length=100, verbose_name='自定义字段13')
    tapd_story_custom_field_14 = models.CharField(max_length=100, verbose_name='自定义字段14')
    tapd_story_custom_field_15 = models.CharField(max_length=100, verbose_name='自定义字段15')
    tapd_story_custom_field_16 = models.CharField(max_length=100, verbose_name='自定义字段16')
    tapd_story_custom_field_17 = models.CharField(max_length=100, verbose_name='自定义字段17')
    tapd_story_custom_field_18 = models.CharField(max_length=100, verbose_name='自定义字段18')
    tapd_story_custom_field_19 = models.CharField(max_length=100, verbose_name='自定义字段19')
    tapd_story_custom_field_20 = models.CharField(max_length=100, verbose_name='自定义字段20')
    tapd_story_custom_field_21 = models.CharField(max_length=100, verbose_name='自定义字段21')
    tapd_story_custom_field_22 = models.CharField(max_length=100, verbose_name='自定义字段22')
    tapd_story_custom_field_23 = models.CharField(max_length=100, verbose_name='自定义字段23')
    tapd_story_custom_field_24 = models.TextField(verbose_name='自定义字段24')
    tapd_story_custom_field_25 = models.CharField(max_length=100, verbose_name='自定义字段25')
    tapd_story_custom_field_26 = models.CharField(max_length=100, verbose_name='自定义字段26')
    tapd_story_custom_field_27 = models.CharField(max_length=100, verbose_name='自定义字段27')
    tapd_story_custom_field_28 = models.CharField(max_length=100, verbose_name='自定义字段28')
    tapd_story_custom_field_29 = models.CharField(max_length=100, verbose_name='自定义字段29')
    tapd_story_custom_field_30 = models.CharField(max_length=100, verbose_name='自定义字段30')
    tapd_story_custom_field_31 = models.CharField(max_length=100, verbose_name='自定义字段31')
    tapd_story_custom_field_32 = models.CharField(max_length=100, verbose_name='自定义字段32')
    tapd_story_custom_field_33 = models.CharField(max_length=100, verbose_name='自定义字段33')
    tapd_story_custom_field_34 = models.CharField(max_length=100, verbose_name='自定义字段34')
    tapd_story_custom_field_35 = models.CharField(max_length=100, verbose_name='自定义字段35')
    tapd_story_custom_field_36 = models.CharField(max_length=100, verbose_name='自定义字段36')
    tapd_story_custom_field_37 = models.CharField(max_length=100, verbose_name='自定义字段37')
    tapd_story_custom_field_38 = models.CharField(max_length=100, verbose_name='自定义字段38')
    tapd_story_custom_field_39 = models.CharField(max_length=100, verbose_name='自定义字段39')
    tapd_story_custom_field_40 = models.CharField(max_length=100, verbose_name='自定义字段40')
    tapd_story_custom_field_41 = models.CharField(max_length=100, verbose_name='自定义字段41')
    tapd_story_custom_field_42 = models.CharField(max_length=100, verbose_name='自定义字段42')
    tapd_story_custom_field_43 = models.CharField(max_length=100, verbose_name='自定义字段43')
    tapd_story_custom_field_44 = models.CharField(max_length=100, verbose_name='自定义字段44')
    tapd_story_custom_field_45 = models.CharField(max_length=100, verbose_name='自定义字段45')
    tapd_story_custom_field_46 = models.CharField(max_length=100, verbose_name='自定义字段46')
    tapd_story_custom_field_47 = models.CharField(max_length=100, verbose_name='自定义字段47')
    tapd_story_custom_field_48 = models.CharField(max_length=100, verbose_name='自定义字段48')
    tapd_story_custom_field_49 = models.CharField(max_length=100, verbose_name='自定义字段49')
    tapd_story_custom_field_50 = models.CharField(max_length=100, verbose_name='自定义字段50')
    tapd_story_custom_field_51 = models.CharField(max_length=100, verbose_name='自定义字段51')
    tapd_story_custom_field_52 = models.CharField(max_length=100, verbose_name='自定义字段52')
    tapd_story_custom_field_53 = models.CharField(max_length=100, verbose_name='自定义字段53')
    tapd_story_custom_field_54 = models.CharField(max_length=100, verbose_name='自定义字段54')
    tapd_story_custom_field_55 = models.CharField(max_length=100, verbose_name='自定义字段55')
    tapd_story_custom_field_56 = models.CharField(max_length=100, verbose_name='自定义字段56')
    tapd_story_custom_field_57 = models.CharField(max_length=100, verbose_name='自定义字段57')
    tapd_story_custom_field_58 = models.CharField(max_length=100, verbose_name='自定义字段58')
    tapd_story_custom_field_59 = models.CharField(max_length=100, verbose_name='自定义字段59')
    tapd_story_custom_field_60 = models.CharField(max_length=100, verbose_name='自定义字段60')
    tapd_story_custom_field_61 = models.CharField(max_length=100, verbose_name='自定义字段61')
    tapd_story_custom_field_62 = models.CharField(max_length=100, verbose_name='自定义字段62')
    tapd_story_custom_field_63 = models.CharField(max_length=100, verbose_name='自定义字段63')
    tapd_story_custom_field_64 = models.CharField(max_length=100, verbose_name='自定义字段64')
    tapd_story_custom_field_65 = models.CharField(max_length=100, verbose_name='自定义字段65')
    tapd_story_custom_field_66 = models.CharField(max_length=100, verbose_name='自定义字段66')
    tapd_story_custom_field_67 = models.CharField(max_length=100, verbose_name='自定义字段67')
    tapd_story_custom_field_68 = models.CharField(max_length=100, verbose_name='自定义字段68')
    tapd_story_custom_field_69 = models.CharField(max_length=100, verbose_name='自定义字段69')
    tapd_story_custom_field_70 = models.CharField(max_length=100, verbose_name='自定义字段70')
    tapd_story_custom_field_71 = models.CharField(max_length=100, verbose_name='自定义字段71')
    tapd_story_custom_field_72 = models.CharField(max_length=100, verbose_name='自定义字段72')
    tapd_story_custom_field_73 = models.CharField(max_length=100, verbose_name='自定义字段73')
    tapd_story_custom_field_74 = models.CharField(max_length=100, verbose_name='自定义字段74')
    tapd_story_custom_field_75 = models.CharField(max_length=100, verbose_name='自定义字段75')
    tapd_story_custom_field_76 = models.CharField(max_length=100, verbose_name='自定义字段76')
    tapd_story_custom_field_77 = models.CharField(max_length=100, verbose_name='自定义字段77')
    tapd_story_custom_field_78 = models.CharField(max_length=100, verbose_name='自定义字段78')
    tapd_story_custom_field_79 = models.CharField(max_length=100, verbose_name='自定义字段79')
    tapd_story_custom_field_80 = models.CharField(max_length=100, verbose_name='自定义字段80')
    tapd_story_custom_field_81 = models.CharField(max_length=100, verbose_name='自定义字段81')
    tapd_story_custom_field_82 = models.CharField(max_length=100, verbose_name='自定义字段82')
    tapd_story_custom_field_83 = models.CharField(max_length=100, verbose_name='自定义字段83')
    tapd_story_custom_field_84 = models.CharField(max_length=100, verbose_name='自定义字段84')
    tapd_story_custom_field_85 = models.CharField(max_length=100, verbose_name='自定义字段85')
    tapd_story_custom_field_86 = models.CharField(max_length=100, verbose_name='自定义字段86')
    tapd_story_custom_field_87 = models.CharField(max_length=100, verbose_name='自定义字段87')
    tapd_story_custom_field_88 = models.CharField(max_length=100, verbose_name='自定义字段88')
    tapd_story_custom_field_89 = models.CharField(max_length=100, verbose_name='自定义字段89')
    tapd_story_custom_field_90 = models.CharField(max_length=100, verbose_name='自定义字段90')
    tapd_story_custom_field_91 = models.CharField(max_length=100, verbose_name='自定义字段91')
    tapd_story_custom_field_92 = models.CharField(max_length=100, verbose_name='自定义字段92')
    tapd_story_custom_field_93 = models.CharField(max_length=100, verbose_name='自定义字段93')
    tapd_story_custom_field_94 = models.CharField(max_length=100, verbose_name='自定义字段94')
    tapd_story_custom_field_95 = models.CharField(max_length=100, verbose_name='自定义字段95')
    tapd_story_custom_field_96 = models.CharField(max_length=100, verbose_name='自定义字段96')
    tapd_story_custom_field_97 = models.CharField(max_length=100, verbose_name='自定义字段97')
    tapd_story_custom_field_98 = models.CharField(max_length=100, verbose_name='自定义字段98')
    tapd_story_custom_field_99 = models.CharField(max_length=100, verbose_name='自定义字段99')
    tapd_story_custom_field_100 = models.CharField(max_length=100, verbose_name='自定义字段100')
    tapd_story_custom_plan_field_1 = models.CharField(max_length=100, verbose_name='自定义计划字段01')
    tapd_story_custom_plan_field_2 = models.CharField(max_length=100, verbose_name='自定义计划字段02')
    tapd_story_custom_plan_field_3 = models.CharField(max_length=100, verbose_name='自定义计划字段03')
    tapd_story_custom_plan_field_4 = models.CharField(max_length=100, verbose_name='自定义计划字段04')
    tapd_story_custom_plan_field_5 = models.CharField(max_length=100, verbose_name='自定义计划字段05')
    tapd_story_custom_plan_field_6 = models.CharField(max_length=100, verbose_name='自定义计划字段06')
    tapd_story_custom_plan_field_7 = models.CharField(max_length=100, verbose_name='自定义计划字段07')
    tapd_story_custom_plan_field_8 = models.CharField(max_length=100, verbose_name='自定义计划字段08')
    tapd_story_custom_plan_field_9 = models.CharField(max_length=100, verbose_name='自定义计划字段09')
    tapd_story_custom_plan_field_10 = models.CharField(max_length=100, verbose_name='自定义计划字段10')

    entry_desc = models.CharField(max_length=255, verbose_name='迭代描述')
    create_user = models.CharField(max_length=20, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=20, verbose_name='修改人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')

    class Meta:
        db_table = 'tapd_entry_story'
        verbose_name = 'tapd故事实体表'


class TapdEntryStoryResJson(models.Model):
    tapd_entry_id = models.IntegerField(verbose_name='缺陷ID')
    res_json = models.JSONField(verbose_name='请求返回Json')

    class Meta:
        db_table = 'tapd_entry_story_res_json'
        verbose_name = 'tapd故事实体返回消息表'


class TapdEntryTestPlan(models.Model):
    create_user = models.CharField(max_length=100, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=100, verbose_name='修改人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')
    id = models.AutoField(verbose_name='主键ID', primary_key=True)

    ins_batch_number = models.IntegerField(verbose_name='插入时批次号')
    upd_batch_number = models.IntegerField(verbose_name='最后一次更新时批次号')
    del_batch_number = models.IntegerField(verbose_name='最后一次删除时批次号')
    del_user = models.CharField(max_length=100, verbose_name='最后一次删除人')
    del_time = models.DateTimeField(blank=True, null=True, verbose_name='最后一次删除时间')
    rec_batch_number = models.IntegerField(verbose_name='最后一次恢复时批次号')
    rec_user = models.CharField(max_length=100, verbose_name='最后一次恢复人')
    rec_time = models.DateTimeField(blank=True, null=True, verbose_name='最后一次恢复时间')
    entry_status = models.IntegerField(verbose_name='测试计划数据状态')
    entry_desc = models.CharField(max_length=255, verbose_name='缺陷实体说明')

    res_json = models.JSONField(verbose_name='请求返回Json')

    tapd_workspace_id = models.IntegerField(verbose_name='项目ID')

    tapd_entry_id = models.IntegerField(verbose_name='测试计划ID')
    tapd_test_plan_name = models.CharField(max_length=200, verbose_name='测试计划标题')
    tapd_test_plan_description = models.CharField(max_length=255, verbose_name='描述（备用）')
    tapd_test_plan_iteration_id = models.BigIntegerField(verbose_name='迭代ID')
    tapd_test_plan_version = models.CharField(max_length=100, verbose_name='版本')

    tapd_test_plan_owner = models.CharField(max_length=100, verbose_name='测试计划负责人')
    tapd_test_plan_status = models.CharField(max_length=20, verbose_name='测试计划状态')
    tapd_test_plan_type = models.CharField(max_length=100, verbose_name='测试计划类型')
    tapd_test_plan_start_date = models.CharField(max_length=20, verbose_name='预计开始')
    tapd_test_plan_end_date = models.CharField(max_length=20, verbose_name='预计结束')
    tapd_test_plan_creator = models.CharField(verbose_name='创建人', max_length=100)
    tapd_test_plan_created = models.DateTimeField(verbose_name='创建时间')
    tapd_test_plan_modifier = models.CharField(verbose_name='修改人', max_length=100)
    tapd_test_plan_modified = models.CharField(max_length=20, verbose_name='最后修改时间')
    tapd_test_plan_created_from = models.CharField(max_length=100, verbose_name='测试计划创建来源')

    tapd_test_plan_custom_field_1 = models.CharField(max_length=100, verbose_name='自定义字段001')
    tapd_test_plan_custom_field_2 = models.CharField(max_length=100, verbose_name='自定义字段001')
    tapd_test_plan_custom_field_3 = models.CharField(max_length=100, verbose_name='自定义字段003')
    tapd_test_plan_custom_field_4 = models.CharField(max_length=100, verbose_name='自定义字段004')
    tapd_test_plan_custom_field_5 = models.CharField(max_length=100, verbose_name='自定义字段005')
    tapd_test_plan_custom_field_6 = models.CharField(max_length=100, verbose_name='自定义字段006')
    tapd_test_plan_custom_field_7 = models.CharField(max_length=100, verbose_name='自定义字段007')
    tapd_test_plan_custom_field_8 = models.CharField(max_length=100, verbose_name='自定义字段008')
    tapd_test_plan_custom_field_9 = models.CharField(max_length=100, verbose_name='自定义字段009')
    tapd_test_plan_custom_field_10 = models.CharField(max_length=100, verbose_name='自定义字段010')
    tapd_test_plan_custom_field_11 = models.CharField(max_length=100, verbose_name='自定义字段011')
    tapd_test_plan_custom_field_12 = models.CharField(max_length=100, verbose_name='自定义字段012')
    tapd_test_plan_custom_field_13 = models.CharField(max_length=100, verbose_name='自定义字段013')
    tapd_test_plan_custom_field_14 = models.CharField(max_length=100, verbose_name='自定义字段014')
    tapd_test_plan_custom_field_15 = models.CharField(max_length=100, verbose_name='自定义字段015')
    tapd_test_plan_custom_field_16 = models.CharField(max_length=100, verbose_name='自定义字段016')
    tapd_test_plan_custom_field_17 = models.CharField(max_length=100, verbose_name='自定义字段017')
    tapd_test_plan_custom_field_18 = models.CharField(max_length=100, verbose_name='自定义字段018')
    tapd_test_plan_custom_field_19 = models.CharField(max_length=100, verbose_name='自定义字段019')
    tapd_test_plan_custom_field_20 = models.CharField(max_length=100, verbose_name='自定义字段020')
    tapd_test_plan_custom_field_21 = models.CharField(max_length=100, verbose_name='自定义字段021')
    tapd_test_plan_custom_field_22 = models.CharField(max_length=100, verbose_name='自定义字段022')
    tapd_test_plan_custom_field_23 = models.CharField(max_length=100, verbose_name='自定义字段023')
    tapd_test_plan_custom_field_24 = models.CharField(max_length=100, verbose_name='自定义字段024')
    tapd_test_plan_custom_field_25 = models.CharField(max_length=100, verbose_name='自定义字段025')
    tapd_test_plan_custom_field_26 = models.CharField(max_length=100, verbose_name='自定义字段026')
    tapd_test_plan_custom_field_27 = models.CharField(max_length=100, verbose_name='自定义字段027')
    tapd_test_plan_custom_field_28 = models.CharField(max_length=100, verbose_name='自定义字段028')
    tapd_test_plan_custom_field_29 = models.CharField(max_length=100, verbose_name='自定义字段029')
    tapd_test_plan_custom_field_30 = models.CharField(max_length=100, verbose_name='自定义字段030')
    tapd_test_plan_custom_field_31 = models.CharField(max_length=100, verbose_name='自定义字段031')
    tapd_test_plan_custom_field_32 = models.CharField(max_length=100, verbose_name='自定义字段032')
    tapd_test_plan_custom_field_33 = models.CharField(max_length=100, verbose_name='自定义字段033')
    tapd_test_plan_custom_field_34 = models.CharField(max_length=100, verbose_name='自定义字段034')
    tapd_test_plan_custom_field_35 = models.CharField(max_length=100, verbose_name='自定义字段035')
    tapd_test_plan_custom_field_36 = models.CharField(max_length=100, verbose_name='自定义字段036')
    tapd_test_plan_custom_field_37 = models.CharField(max_length=100, verbose_name='自定义字段037')
    tapd_test_plan_custom_field_38 = models.CharField(max_length=100, verbose_name='自定义字段038')
    tapd_test_plan_custom_field_39 = models.CharField(max_length=100, verbose_name='自定义字段039')
    tapd_test_plan_custom_field_40 = models.CharField(max_length=100, verbose_name='自定义字段040')
    tapd_test_plan_custom_field_41 = models.CharField(max_length=100, verbose_name='自定义字段041')
    tapd_test_plan_custom_field_42 = models.CharField(max_length=100, verbose_name='自定义字段042')
    tapd_test_plan_custom_field_43 = models.CharField(max_length=100, verbose_name='自定义字段043')
    tapd_test_plan_custom_field_44 = models.CharField(max_length=100, verbose_name='自定义字段044')
    tapd_test_plan_custom_field_45 = models.CharField(max_length=100, verbose_name='自定义字段045')
    tapd_test_plan_custom_field_46 = models.CharField(max_length=100, verbose_name='自定义字段046')
    tapd_test_plan_custom_field_47 = models.CharField(max_length=100, verbose_name='自定义字段047')
    tapd_test_plan_custom_field_48 = models.CharField(max_length=100, verbose_name='自定义字段048')
    tapd_test_plan_custom_field_49 = models.CharField(max_length=100, verbose_name='自定义字段049')
    tapd_test_plan_custom_field_50 = models.CharField(max_length=100, verbose_name='自定义字段050')

    class Meta:
        db_table = 'tapd_entry_test_plan'
        verbose_name = 'tapd测试计划实体表'


class TapdEntryTestPlanResJson(models.Model):
    tapd_entry_id = models.IntegerField(verbose_name='缺陷ID')
    res_json = models.JSONField(verbose_name='请求返回Json')

    class Meta:
        db_table = 'tapd_entry_test_plan_res_json'
        verbose_name = 'tapd测试计划实体返回消息表'


class TapdEntryTask(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    tapd_workspace_id = models.IntegerField(verbose_name='tapd工作空间ID')
    tapd_entry_id = models.IntegerField(verbose_name='tapd迭代Id')
    tapd_task_name = models.CharField(max_length=500, verbose_name='tapd任务名称')
    tapd_task_status = models.CharField(max_length=20, verbose_name='tapd任务状态')
    res_json = models.JSONField(verbose_name='tapd请求返回Json')

    entry_status = models.IntegerField(verbose_name='迭代数据状态')
    ins_batch_number = models.IntegerField(verbose_name='插入时批次号')
    upd_batch_number = models.IntegerField(verbose_name='最后一次更新时批次号')
    del_batch_number = models.IntegerField(verbose_name='最后一次删除时批次号')
    del_user = models.CharField(max_length=100, verbose_name='最后一次删除人')
    del_time = models.DateTimeField(blank=True, null=True, verbose_name='最后一次删除时间')
    rec_batch_number = models.IntegerField(verbose_name='最后一次恢复时批次号')
    rec_user = models.CharField(max_length=100, verbose_name='最后一次恢复人')
    rec_time = models.DateTimeField(blank=True, null=True, verbose_name='最后一次恢复时间')

    tapd_task_description = models.CharField(max_length=255, verbose_name='迭代描述')
    tapd_task_creator = models.CharField(max_length=100, verbose_name='创建人')
    tapd_task_created = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    tapd_task_modified = models.CharField(max_length=20, verbose_name='修改时间')
    tapd_task_owner = models.CharField(max_length=100, verbose_name='处理人')
    tapd_task_cc = models.CharField(max_length=100, verbose_name='抄送人')
    tapd_task_begin = models.CharField(max_length=20, verbose_name='预计开始')
    tapd_task_due = models.CharField(max_length=20, verbose_name='预计结束')
    tapd_task_story_id = models.IntegerField(verbose_name='故事ID')
    tapd_task_iteration_id = models.IntegerField(verbose_name='迭代ID')
    tapd_task_priority = models.CharField(max_length=50, verbose_name='故事优先级')
    tapd_task_progress = models.CharField(max_length=20, verbose_name='进步')
    tapd_task_completed = models.CharField(max_length=20, verbose_name='完成时间')
    tapd_task_effort_completed = models.CharField(max_length=20, verbose_name='完成工时')
    tapd_task_exceed = models.FloatField(verbose_name='超出工时')
    tapd_task_remain = models.FloatField(verbose_name='剩余工时')
    tapd_task_effort = models.CharField(max_length=20, verbose_name='预估工时')
    tapd_task_has_attachment = models.CharField(max_length=20, verbose_name='是否有附件')
    tapd_task_release_id = models.IntegerField(verbose_name='发布ID')
    tapd_task_label = models.CharField(max_length=20, verbose_name='标签查询')
    tapd_task_priority_label = models.CharField(max_length=50, verbose_name='优先级标签')

    tapd_task_custom_field_one = models.CharField(max_length=100, verbose_name='自定义字段01')
    tapd_task_custom_field_two = models.CharField(max_length=100, verbose_name='自定义字段02')
    tapd_task_custom_field_three = models.CharField(max_length=100, verbose_name='自定义字段03')
    tapd_task_custom_field_four = models.CharField(max_length=100, verbose_name='自定义字段04')
    tapd_task_custom_field_five = models.CharField(max_length=100, verbose_name='自定义字段05')
    tapd_task_custom_field_six = models.CharField(max_length=100, verbose_name='自定义字段06')
    tapd_task_custom_field_seven = models.CharField(max_length=100, verbose_name='自定义字段07')
    tapd_task_custom_field_eight = models.CharField(max_length=100, verbose_name='自定义字段08')
    tapd_task_custom_field_9 = models.CharField(max_length=100, verbose_name='自定义字段09')
    tapd_task_custom_field_10 = models.CharField(max_length=100, verbose_name='自定义字段10')
    tapd_task_custom_field_11 = models.CharField(max_length=100, verbose_name='自定义字段11')
    tapd_task_custom_field_12 = models.CharField(max_length=100, verbose_name='自定义字段12')
    tapd_task_custom_field_13 = models.CharField(max_length=100, verbose_name='自定义字段13')
    tapd_task_custom_field_14 = models.CharField(max_length=100, verbose_name='自定义字段14')
    tapd_task_custom_field_15 = models.CharField(max_length=100, verbose_name='自定义字段15')
    tapd_task_custom_field_16 = models.CharField(max_length=100, verbose_name='自定义字段16')
    tapd_task_custom_field_17 = models.CharField(max_length=100, verbose_name='自定义字段17')
    tapd_task_custom_field_18 = models.CharField(max_length=100, verbose_name='自定义字段18')
    tapd_task_custom_field_19 = models.CharField(max_length=100, verbose_name='自定义字段19')
    tapd_task_custom_field_20 = models.CharField(max_length=100, verbose_name='自定义字段20')
    tapd_task_custom_field_21 = models.CharField(max_length=100, verbose_name='自定义字段21')
    tapd_task_custom_field_22 = models.CharField(max_length=100, verbose_name='自定义字段22')
    tapd_task_custom_field_23 = models.CharField(max_length=100, verbose_name='自定义字段23')
    tapd_task_custom_field_24 = models.CharField(max_length=100, verbose_name='自定义字段24')
    tapd_task_custom_field_25 = models.CharField(max_length=100, verbose_name='自定义字段25')
    tapd_task_custom_field_26 = models.CharField(max_length=100, verbose_name='自定义字段26')
    tapd_task_custom_field_27 = models.CharField(max_length=100, verbose_name='自定义字段27')
    tapd_task_custom_field_28 = models.CharField(max_length=100, verbose_name='自定义字段28')
    tapd_task_custom_field_29 = models.CharField(max_length=100, verbose_name='自定义字段29')
    tapd_task_custom_field_30 = models.CharField(max_length=100, verbose_name='自定义字段30')
    tapd_task_custom_field_31 = models.CharField(max_length=100, verbose_name='自定义字段31')
    tapd_task_custom_field_32 = models.CharField(max_length=100, verbose_name='自定义字段32')
    tapd_task_custom_field_33 = models.CharField(max_length=100, verbose_name='自定义字段33')
    tapd_task_custom_field_34 = models.CharField(max_length=100, verbose_name='自定义字段34')
    tapd_task_custom_field_35 = models.CharField(max_length=100, verbose_name='自定义字段35')
    tapd_task_custom_field_36 = models.CharField(max_length=100, verbose_name='自定义字段36')
    tapd_task_custom_field_37 = models.CharField(max_length=100, verbose_name='自定义字段37')
    tapd_task_custom_field_38 = models.CharField(max_length=100, verbose_name='自定义字段38')
    tapd_task_custom_field_39 = models.CharField(max_length=100, verbose_name='自定义字段39')
    tapd_task_custom_field_40 = models.CharField(max_length=100, verbose_name='自定义字段40')
    tapd_task_custom_field_41 = models.CharField(max_length=100, verbose_name='自定义字段41')
    tapd_task_custom_field_42 = models.CharField(max_length=100, verbose_name='自定义字段42')
    tapd_task_custom_field_43 = models.CharField(max_length=100, verbose_name='自定义字段43')
    tapd_task_custom_field_44 = models.CharField(max_length=100, verbose_name='自定义字段44')
    tapd_task_custom_field_45 = models.CharField(max_length=100, verbose_name='自定义字段45')
    tapd_task_custom_field_46 = models.CharField(max_length=100, verbose_name='自定义字段46')
    tapd_task_custom_field_47 = models.CharField(max_length=100, verbose_name='自定义字段47')
    tapd_task_custom_field_48 = models.CharField(max_length=100, verbose_name='自定义字段48')
    tapd_task_custom_field_49 = models.CharField(max_length=100, verbose_name='自定义字段49')
    tapd_task_custom_field_50 = models.CharField(max_length=100, verbose_name='自定义字段50')
    tapd_task_custom_plan_field_1 = models.CharField(max_length=100, verbose_name='自定义计划字段01')
    tapd_task_custom_plan_field_2 = models.CharField(max_length=100, verbose_name='自定义计划字段02')
    tapd_task_custom_plan_field_3 = models.CharField(max_length=100, verbose_name='自定义计划字段03')
    tapd_task_custom_plan_field_4 = models.CharField(max_length=100, verbose_name='自定义计划字段04')
    tapd_task_custom_plan_field_5 = models.CharField(max_length=100, verbose_name='自定义计划字段05')
    tapd_task_custom_plan_field_6 = models.CharField(max_length=100, verbose_name='自定义计划字段06')
    tapd_task_custom_plan_field_7 = models.CharField(max_length=100, verbose_name='自定义计划字段07')
    tapd_task_custom_plan_field_8 = models.CharField(max_length=100, verbose_name='自定义计划字段08')
    tapd_task_custom_plan_field_9 = models.CharField(max_length=100, verbose_name='自定义计划字段09')
    tapd_task_custom_plan_field_10 = models.CharField(max_length=100, verbose_name='自定义计划字段10')

    entry_desc = models.CharField(max_length=255, verbose_name='迭代描述')
    create_user = models.CharField(max_length=20, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=20, verbose_name='修改人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')

    class Meta:
        db_table = 'tapd_entry_task'
        verbose_name = 'tapd任务实体表'


class TapdEntryTaskResJson(models.Model):
    tapd_entry_id = models.IntegerField(verbose_name='缺陷ID')
    res_json = models.JSONField(verbose_name='请求返回Json')

    class Meta:
        db_table = 'tapd_entry_task_res_json'
        verbose_name = 'tapd任务实体返回消息表'


class TapdEntryLaunchForm(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    tapd_workspace_id = models.IntegerField(verbose_name='tapd工作空间ID')
    tapd_entry_id = models.IntegerField(verbose_name='tapd迭代Id')
    tapd_launch_form_title = models.CharField(max_length=200, verbose_name='tapd发布评审名称')
    tapd_launch_form_status = models.CharField(max_length=20, verbose_name='tapd发布评审状态')

    ins_batch_number = models.IntegerField(verbose_name='插入时批次号')
    upd_batch_number = models.IntegerField(verbose_name='最后一次更新时批次号')
    del_batch_number = models.IntegerField(verbose_name='最后一次删除时批次号')
    del_user = models.CharField(max_length=100, verbose_name='最后一次删除人')
    del_time = models.DateTimeField(blank=True, null=True, verbose_name='最后一次删除时间')
    rec_batch_number = models.IntegerField(verbose_name='最后一次恢复时批次号')
    rec_user = models.CharField(max_length=100, verbose_name='最后一次恢复人')
    rec_time = models.DateTimeField(blank=True, null=True, verbose_name='最后一次恢复时间')
    entry_status = models.IntegerField(verbose_name='迭代数据状态')
    entry_desc = models.CharField(max_length=255, verbose_name='迭代描述')
    res_json = models.JSONField(verbose_name='tapd请求返回Json')

    tapd_launch_form_description = models.CharField(max_length=255, verbose_name='描述')
    tapd_launch_form_creator = models.CharField(max_length=100, verbose_name='创建人')
    tapd_launch_form_created = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    tapd_launch_form_version_type = models.CharField(max_length=100, verbose_name="发布评审版本类型")
    tapd_launch_form_baseline = models.CharField(max_length=100, verbose_name="基线")
    tapd_launch_form_release_model = models.CharField(max_length=100, verbose_name="发布模块")
    tapd_launch_form_roadmap_version = models.CharField(max_length=100, verbose_name="测试计划状态")
    tapd_launch_form_release_type = models.CharField(max_length=100, verbose_name="路标版本")
    tapd_launch_form_change_type = models.CharField(max_length=100, verbose_name='变更类型')
    tapd_launch_form_signed_by = models.CharField(max_length=20, verbose_name='签发人')
    tapd_launch_form_archived_by = models.CharField(max_length=20, verbose_name='发布确认人')
    tapd_launch_form_cc = models.CharField(max_length=100, verbose_name='抄送人')
    tapd_launch_form_change_notifier = models.CharField(max_length=100, verbose_name='变更通知人')
    tapd_launch_form_signed = models.CharField(max_length=20, verbose_name='签发时间')
    tapd_launch_form_archived = models.CharField(max_length=20, verbose_name='归档时间')
    tapd_launch_form_signer_result = models.CharField(max_length=255, verbose_name='签发结论')
    tapd_launch_form_signer_comment = models.CharField(max_length=255, verbose_name='签发意见')
    tapd_launch_form_release_result = models.CharField(max_length=255, verbose_name='发布结果')
    tapd_launch_form_release_comment = models.CharField(max_length=255, verbose_name='发布意见')
    tapd_launch_form_test_path = models.CharField(max_length=255, verbose_name='测试路径')
    tapd_launch_form_created_path = models.CharField(max_length=255, verbose_name='归档路径')
    tapd_launch_form_remark = models.CharField(max_length=255, verbose_name='备注')
    tapd_launch_form_participator = models.CharField(max_length=100, verbose_name='参与人')
    tapd_launch_form_template_id = models.IntegerField(verbose_name='模板ID')
    tapd_launch_form_iteration_id = models.IntegerField(verbose_name='迭代ID')
    tapd_launch_form_release_id = models.IntegerField(verbose_name='发布计划ID')
    tapd_launch_form_flows = models.CharField(max_length=500, verbose_name='工作流')
    tapd_launch_form_modified = models.CharField(max_length=20, verbose_name='修改时间')

    tapd_launch_form_custom_field_one = models.CharField(max_length=100, verbose_name='自定义字段01')
    tapd_launch_form_custom_field_two = models.CharField(max_length=100, verbose_name='自定义字段02')
    tapd_launch_form_custom_field_three = models.CharField(max_length=100, verbose_name='自定义字段03')
    tapd_launch_form_custom_field_four = models.CharField(max_length=100, verbose_name='自定义字段04')
    tapd_launch_form_custom_field_five = models.CharField(max_length=100, verbose_name='自定义字段05')
    tapd_launch_form_custom_field_six = models.CharField(max_length=100, verbose_name='自定义字段06')
    tapd_launch_form_custom_field_seven = models.CharField(max_length=100, verbose_name='自定义字段07')
    tapd_launch_form_custom_field_eight = models.CharField(max_length=100, verbose_name='自定义字段08')
    tapd_launch_form_custom_field_9 = models.CharField(max_length=100, verbose_name='自定义字段09')
    tapd_launch_form_custom_field_10 = models.CharField(max_length=100, verbose_name='自定义字段10')
    tapd_launch_form_custom_field_11 = models.CharField(max_length=100, verbose_name='自定义字段11')
    tapd_launch_form_custom_field_12 = models.CharField(max_length=100, verbose_name='自定义字段12')
    tapd_launch_form_custom_field_13 = models.CharField(max_length=100, verbose_name='自定义字段13')
    tapd_launch_form_custom_field_14 = models.CharField(max_length=100, verbose_name='自定义字段14')
    tapd_launch_form_custom_field_15 = models.CharField(max_length=100, verbose_name='自定义字段15')
    tapd_launch_form_custom_field_16 = models.CharField(max_length=100, verbose_name='自定义字段16')
    tapd_launch_form_custom_field_17 = models.CharField(max_length=100, verbose_name='自定义字段17')
    tapd_launch_form_custom_field_18 = models.CharField(max_length=100, verbose_name='自定义字段18')
    tapd_launch_form_custom_field_19 = models.CharField(max_length=100, verbose_name='自定义字段19')
    tapd_launch_form_custom_field_20 = models.CharField(max_length=100, verbose_name='自定义字段20')
    tapd_launch_form_custom_field_21 = models.CharField(max_length=100, verbose_name='自定义字段21')
    tapd_launch_form_custom_field_22 = models.CharField(max_length=100, verbose_name='自定义字段22')
    tapd_launch_form_custom_field_23 = models.CharField(max_length=100, verbose_name='自定义字段23')
    tapd_launch_form_custom_field_24 = models.CharField(max_length=100, verbose_name='自定义字段24')
    tapd_launch_form_custom_field_25 = models.CharField(max_length=100, verbose_name='自定义字段25')
    tapd_launch_form_custom_field_26 = models.CharField(max_length=100, verbose_name='自定义字段26')
    tapd_launch_form_custom_field_27 = models.CharField(max_length=100, verbose_name='自定义字段27')
    tapd_launch_form_custom_field_28 = models.CharField(max_length=100, verbose_name='自定义字段28')
    tapd_launch_form_custom_field_29 = models.CharField(max_length=100, verbose_name='自定义字段29')
    tapd_launch_form_custom_field_30 = models.CharField(max_length=100, verbose_name='自定义字段30')
    tapd_launch_form_custom_field_31 = models.CharField(max_length=100, verbose_name='自定义字段31')
    tapd_launch_form_custom_field_32 = models.CharField(max_length=100, verbose_name='自定义字段32')
    tapd_launch_form_custom_field_33 = models.CharField(max_length=100, verbose_name='自定义字段33')
    tapd_launch_form_custom_field_34 = models.CharField(max_length=100, verbose_name='自定义字段34')
    tapd_launch_form_custom_field_35 = models.CharField(max_length=100, verbose_name='自定义字段35')
    tapd_launch_form_custom_field_36 = models.CharField(max_length=100, verbose_name='自定义字段36')
    tapd_launch_form_custom_field_37 = models.CharField(max_length=100, verbose_name='自定义字段37')
    tapd_launch_form_custom_field_38 = models.CharField(max_length=100, verbose_name='自定义字段38')
    tapd_launch_form_custom_field_39 = models.CharField(max_length=100, verbose_name='自定义字段39')
    tapd_launch_form_custom_field_40 = models.CharField(max_length=100, verbose_name='自定义字段40')
    tapd_launch_form_custom_field_41 = models.CharField(max_length=100, verbose_name='自定义字段41')
    tapd_launch_form_custom_field_42 = models.CharField(max_length=100, verbose_name='自定义字段42')
    tapd_launch_form_custom_field_43 = models.CharField(max_length=100, verbose_name='自定义字段43')
    tapd_launch_form_custom_field_44 = models.CharField(max_length=100, verbose_name='自定义字段44')
    tapd_launch_form_custom_field_45 = models.CharField(max_length=100, verbose_name='自定义字段45')
    tapd_launch_form_custom_field_46 = models.CharField(max_length=100, verbose_name='自定义字段46')
    tapd_launch_form_custom_field_47 = models.CharField(max_length=100, verbose_name='自定义字段47')
    tapd_launch_form_custom_field_48 = models.CharField(max_length=100, verbose_name='自定义字段48')
    tapd_launch_form_custom_field_49 = models.CharField(max_length=100, verbose_name='自定义字段49')
    tapd_launch_form_custom_field_50 = models.CharField(max_length=100, verbose_name='自定义字段50')

    create_user = models.CharField(max_length=20, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=20, verbose_name='修改人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')

    class Meta:
        db_table = 'tapd_entry_launch_form'
        verbose_name = 'tapd发布评审实体表'


class TapdEntryLaunchFormResJson(models.Model):
    tapd_entry_id = models.IntegerField(verbose_name='缺陷ID')
    res_json = models.JSONField(verbose_name='请求返回Json')

    class Meta:
        db_table = 'tapd_entry_launch_form_res_json'
        verbose_name = 'tapd评审计划实体返回消息表'


class TapdEntryTimeSheet(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    tapd_workspace_id = models.IntegerField(verbose_name='tapd工作空间ID')
    tapd_entry_id = models.IntegerField(verbose_name='tapd迭代Id')
    entry_status = models.IntegerField(verbose_name='状态')
    entry_desc = models.CharField(max_length=255, verbose_name='描述')

    ins_batch_number = models.IntegerField(verbose_name='插入时批次号')
    upd_batch_number = models.IntegerField(verbose_name='最后一次更新时批次号')
    del_batch_number = models.IntegerField(verbose_name='最后一次删除时批次号')
    del_user = models.CharField(max_length=100, verbose_name='最后一次删除人')
    del_time = models.DateTimeField(blank=True, null=True, verbose_name='最后一次删除时间')
    rec_batch_number = models.IntegerField(verbose_name='最后一次恢复时批次号')
    rec_user = models.CharField(max_length=100, verbose_name='最后一次恢复人')
    rec_time = models.DateTimeField(blank=True, null=True, verbose_name='最后一次恢复时间')
    res_json = models.JSONField(verbose_name='tapd请求返回Json')

    tapd_time_sheet_entity_id = models.IntegerField(verbose_name="对象ID")
    tapd_time_sheet_entity_type = models.CharField(max_length=50, verbose_name='对象类型，如story、task等')
    tapd_time_sheet_owner = models.CharField(max_length=50, verbose_name="创建人")
    tapd_time_sheet_created = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    tapd_time_sheet_modified = models.CharField(max_length=20, verbose_name='修改时间')
    tapd_time_sheet_timespent = models.CharField(max_length=20, verbose_name="花费工时")
    tapd_time_sheet_spentdate = models.CharField(max_length=20, verbose_name="花费日期")
    tapd_time_sheet_timeremain = models.CharField(max_length=20, verbose_name="剩余工时")
    tapd_time_sheet_memo = models.CharField(max_length=255, verbose_name="花费描述")
    tapd_time_sheet_is_delete = models.IntegerField(verbose_name="是否删除")

    create_user = models.CharField(max_length=20, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=20, verbose_name='修改人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='修改时间')
    stamp = models.IntegerField(verbose_name='版本')

    class Meta:
        db_table = 'tapd_entry_timesheet'
        verbose_name = 'tapd工时实体表'


class TapdEntryTimesheetResJson(models.Model):
    tapd_entry_id = models.IntegerField(verbose_name='缺陷ID')
    res_json = models.JSONField(verbose_name='请求返回Json')

    class Meta:
        db_table = 'tapd_entry_timesheet_res_json'
        verbose_name = 'tapd时间花费实体返回消息表'


class TapdEntryTestPlanBindStory(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    tapd_test_plan_id = models.BigIntegerField(verbose_name='测试计划ID')
    relative_dev_story_ids = models.CharField(verbose_name='关联研发需求ids', max_length=500)
    relative_test_story_id = models.BigIntegerField(verbose_name='关联测试需求id')
    update_time = models.DateTimeField(verbose_name='更新时间')

    class Meta:
        db_table = 'tapd_entry_test_plan_bind_story'
        verbose_name = 'tapd测试计划关联故事表'


class TapdEntryWorkspace(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    tapd_workspace_id = models.BigIntegerField(verbose_name='项目ID')
    tapd_workspace_parent_id = models.BigIntegerField(verbose_name='项目父ID')
    tapd_workspace_company_id = models.BigIntegerField(verbose_name='项目所属公司ID')
    tapd_workspace_name = models.CharField(verbose_name='项目名', max_length=100)
    tapd_workspace_status = models.CharField(verbose_name='项目状态', max_length=50)
    tapd_workspace_creator_id = models.BigIntegerField(verbose_name='项目创建者ID')
    tapd_workspace_creator = models.CharField(verbose_name='项目创建者', max_length=100)
    tapd_workspace_description = models.CharField(verbose_name='tapd项目说明', max_length=999)
    workspace_short_name = models.CharField(verbose_name='项目简称', max_length=100)
    workspace_type = models.IntegerField(verbose_name='项目类型')
    workspace_team_id = models.BigIntegerField(verbose_name='项目对应的小团队ID')
    workspace_is_active = models.IntegerField(verbose_name='项目是否可用')
    workspace_desc = models.CharField(verbose_name='项目说明', max_length=255)

    class Meta:
        db_table = 'tapd_workspace'
        verbose_name = 'tapd项目表'


class TapdEntryDataSyncLog(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    req_module = models.CharField(verbose_name='请求模块', max_length=100)
    req_type = models.CharField(verbose_name='请求类型', max_length=50)
    req_desc = models.CharField(verbose_name='请求说明', max_length=255)
    workspace_id = models.BigIntegerField(verbose_name='工作空间')
    success_time = models.DateTimeField(verbose_name='执行成功时间')
    create_user = models.CharField(verbose_name='创建人', max_length=20)
    update_user = models.CharField(verbose_name='修改人', max_length=20)
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_time = models.DateTimeField(verbose_name='更新时间')

    class Meta:
        db_table = 'tapd_data_sync_log'
        verbose_name = 'tapd数据同步成功记录表'


class TapdWorkspaceStorySyncDetail(models.Model):
    create_user = models.CharField(max_length=20, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=20, verbose_name='更新人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='更新时间')
    stamp = models.BigIntegerField(verbose_name='stamp')
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    workspace_id = models.BigIntegerField(verbose_name='同步具体的项目ID(来源)')
    story_id = models.BigIntegerField(verbose_name='需求ID(来源)')
    dst_workspace_id = models.BigIntegerField(verbose_name='目标项目ID')
    dst_story_id = models.BigIntegerField(verbose_name='目标需求ID')

    class Meta:
        db_table = 'tapd_workspace_story_copy_detail'
        verbose_name = 'tapd产品需求同步研发明细表'


class TapdWorkspaceModule(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    workspace_id = models.BigIntegerField(verbose_name='工作空间')
    module_name = models.CharField(verbose_name='项目模块名', max_length=50)
    module_url = models.CharField(verbose_name='项目url路径', max_length=100)
    module_is_active = models.IntegerField(verbose_name='项目是否可用')
    module_desc = models.CharField(verbose_name='项目说明', max_length=255)
    create_user = models.CharField(verbose_name='创建人', max_length=20)
    update_user = models.CharField(verbose_name='修改人', max_length=20)
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_time = models.DateTimeField(verbose_name='更新时间')
    stamp = models.BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'tapd_workspace_module'
        verbose_name = 'tapd_workspace_module表'


class TapdWorkspaceModuleConf(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    module_id = models.BigIntegerField(verbose_name='模块ID')
    tapd_id = models.BigIntegerField(verbose_name='tapd实体ID')
    tapd_entry_type = models.CharField(verbose_name='tapd实体类型', max_length=50)
    tapd_custom_field = models.CharField(verbose_name='自定义字段', max_length=100)
    tapd_type = models.CharField(verbose_name='自定义字段类型', max_length=50)
    tapd_name = models.CharField(verbose_name='自定义字段名称', max_length=50)
    tapd_options = models.CharField(verbose_name='自定义字段选项', max_length=999)
    tapd_sort = models.IntegerField(verbose_name='自定义字段排序')
    conf_is_sync = models.IntegerField(verbose_name='是否同步')
    conf_table = models.CharField(verbose_name='映射表', max_length=50)
    conf_column = models.CharField(verbose_name='映射字段', max_length=50)
    conf_name = models.CharField(verbose_name='映射名称', max_length=50)
    conf_type = models.CharField(verbose_name='映射类型', max_length=100)
    conf_is_active = models.IntegerField(verbose_name='项目是否可用')
    conf_desc = models.CharField(verbose_name='项目说明', max_length=255)
    workspace_id = models.BigIntegerField(verbose_name='工作空间')
    module_name = models.CharField(verbose_name='项目模块名', max_length=50)
    module_url = models.CharField(verbose_name='项目url路径', max_length=100)
    module_is_active = models.IntegerField(verbose_name='项目是否可用')
    create_user = models.CharField(verbose_name='创建人', max_length=20)
    update_user = models.CharField(verbose_name='修改人', max_length=20)
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_time = models.DateTimeField(verbose_name='更新时间')
    stamp = models.BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'tapd_workspace_module_conf'
        verbose_name = 'tapd_workspace_module配置表'
