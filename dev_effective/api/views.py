import datetime

from django.db import transaction
from rest_framework import status
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from dev_effective.model.models import DevEfficTestDevSubmit, DevEfficTestDevSubmitBindStory
from dev_effective.service.biz_iteration_service import BizIterationSer
from dev_effective.service.biz_launch_form_service import BizLaunchFormSer
from dev_effective.service.biz_story_service import BizStorySer
from dev_effective.dao.dev_effective_dao import BizStoryDao
from dev_effective.service.biz_tapd_data_write_strategy import CheckTapdDataSer, TapdDataSyncToBizSer
from dev_effective.service.biz_test_plan_service import BizTestPlanSer
from mantis.settings import ApiResult
from mantis.settings import logger as log
from tapd_gateway.from_tapd.model.models import TapdEntryBug, TapdEntryTestPlanBindStory
from tapd_gateway.from_tapd.service.tapd_entry_bug_ser import TapdEntityBugSer
from tapd_gateway.from_tapd.service.tapd_sync_data_success_log import TapdEntryDataSyncLogSer, TapdFunctionLogType
from tapd_gateway.to_tapd.model.bug_model import TapdServiceResults


class CheckTapdDataBizView(ViewSet):
    """
    tapd数据问题情况收集
    """
    authentication_classes = []

    def list(self, request):
        biz_tapd = CheckTapdDataSer()

        try:
            biz_tapd.start_check()
            return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(
                "执行成功"))
        except Exception as e:
            log.error(e)
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict(
                                "执行失败"))


class BizETLView(ViewSet):
    authentication_classes = []
    """"
    测试任务、测试缺陷、测试发布评审、研发需求和研发任务ETL
    加 测试提测类需求ETL
    """

    def list(self, request):
        env_type = request.query_params.get("env_type")
        entry_type = request.query_params.get("entry_type")
        log.info('测试任务、测试缺陷、测试发布评审ETL:{}, entry_type:{}'.format(env_type, entry_type))
        if not env_type:
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict(
                                "env_type不能为空"))
        if not entry_type:
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict(
                                "entry_type不能为空"))
        try:
            sync_ser = TapdDataSyncToBizSer(env_type, entry_type)
            sync_ser.sync_data_to_biz()
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.success_dict(
                                "数据ETL成功"))
        except Exception as e:
            log.error(e)
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict(
                                "数据ETL失败"))


class BizIterationView(ViewSet):
    authentication_classes = []
    """
    测试需求创建测试迭代，迭代关联需求
    """

    def list(self, request):
        workspace_id = request.query_params.get("workspace_id")
        if not workspace_id:
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict(
                                "workspace_id不能为空"))

        story_ser = BizIterationSer()
        ins_num = story_ser.create_iteration(workspace_id)
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict("需求创建迭代success: {} 条".format(ins_num)))


class CreateLaunchFormFromIterationView(ViewSet):
    authentication_classes = []
    """
    迭代创建发布评审
    """

    def list(self, request):
        workspace_id = request.query_params.get("workspace_id")
        if not workspace_id:
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict(
                                "workspace_id不能为空"))
        try:
            launch_form_ser = BizLaunchFormSer()
            success_num = launch_form_ser.create_launch_form(workspace_id)
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.success_dict("创建发布评审成功 {} 条".format(success_num)))
        except Exception as e:
            log.error('创建发布评审失败，workspace_id:{}'.format(workspace_id))
            log.error(str(e))
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict("创建发布评审失败，原因： {} ".format(e)))


class BizBugCreateView(ViewSet):
    authentication_classes = []
    """
    自动创建bug
    """

    def create(self, request):
        log.info(request.data)
        uuid = request.data.get('uuid')
        if not uuid:
            return Response(data=ApiResult.failed_dict(msg='没有uuid请确认'))

        uuid_obj = TapdServiceResults.objects.filter(request_uuid=uuid)
        if uuid_obj:
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict('bug已创建'))

        bug_ser = TapdEntityBugSer()
        bug_url = bug_ser.create_bug(request.data)
        if not bug_url:
            return Response(status=status.HTTP_200_OK, data=ApiResult.failed_dict('bug创建失败'))
        bug_obj = {'bug_url': bug_url}
        return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(data=bug_obj, msg="bug创建成功"))


class BizBugStatus(ViewSet):
    authentication_classes = []

    def create(self, request):
        bug_id_info = {}
        entry_id_list = []
        statu_info = {}
        log.info(request.data)
        uuid_list = request.data['uuid_list']
        uuid_list = uuid_list.split(',')
        objs = TapdServiceResults.objects.filter(request_uuid__in=uuid_list)
        for obj in objs:
            bug_id_info[obj.tapd_entry_id] = {'bug_create_uuid': obj.request_uuid, 'tapd_bug_id': obj.tapd_entry_id}
            entry_id_list.append(obj.tapd_entry_id)
        entry_bug_objs = TapdEntryBug.objects.filter(tapd_entry_id__in=entry_id_list)
        for entry_bug_obj in entry_bug_objs:
            bug_id_info[entry_bug_obj.tapd_entry_id]['tapd_bug_status'] = entry_bug_obj.tapd_bug_status
            tapd_entry_status = 'exist'
            if entry_bug_obj.entry_status and int(entry_bug_obj.entry_status) == 3:
                tapd_entry_status = 'delete'
            bug_id_info[entry_bug_obj.tapd_entry_id]['tapd_entry_status'] = tapd_entry_status
            statu_info[bug_id_info[entry_bug_obj.tapd_entry_id]['bug_create_uuid']] = bug_id_info[
                entry_bug_obj.tapd_entry_id]
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(data=statu_info, msg="获取tapd bug状态成功"))


class BizTestPlanView(ViewSet):
    authentication_classes = []
    """
        测试计划
        """

    def list(self, request):
        try:
            test_plan_ser = BizTestPlanSer()
            ins_rows = test_plan_ser.sync_test_plan_data()
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.success_dict(
                                "测试计划创建需求成功，成功创建{}条数据".format(ins_rows)))
        except Exception as e:
            log.info('研发端测试计划创建需求失败')
            log.error(e)
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict(
                                "同步执行计划业务数据失败"))


class RefreshTestDevSubmitRelativeStories(ViewSet):
    authentication_classes = []

    def list(self, request):
        # 根据test_dev_sumbit_id，刷新关联开发叶子需求的关联关系
        tapd_test_plan_id = request.query_params.get("tapd_test_plan_id")
        if tapd_test_plan_id:
            dev_submit_list = DevEfficTestDevSubmit.objects.filter(tapd_test_plan_id=tapd_test_plan_id)
        else:
            dev_submit_list = DevEfficTestDevSubmit.objects.filter()
        for dev_submit in dev_submit_list:
            dev_submit_id = dev_submit.id
            try:
                with transaction.atomic():
                    DevEfficTestDevSubmitBindStory.objects.filter(dev_submit_id=dev_submit_id).delete()
                    bind_story = TapdEntryTestPlanBindStory.objects.filter(
                        tapd_test_plan_id=dev_submit.tapd_test_plan_id)
                    dev_story_ids = []
                    if bind_story and bind_story[0].relative_dev_story_ids:
                        relative_dev_story_ids = bind_story[0].relative_dev_story_ids.split(',')
                        dev_story_ids = BizStoryDao.get_dev_leaf_story_id_for_dev_submit(relative_dev_story_ids)
                    if dev_story_ids:
                        ins_bind_list = []
                        for story_id in dev_story_ids:
                            bind = DevEfficTestDevSubmitBindStory()
                            bind.dev_submit_id = dev_submit_id
                            bind.dev_story_id = story_id
                            ins_bind_list.append(bind)
                        DevEfficTestDevSubmitBindStory.objects.bulk_create(ins_bind_list)
            except Exception as e:
                log.error(e)
                Response(status=status.HTTP_200_OK,
                         data=ApiResult.failed_dict("刷新dev_effic_test_dev_submit_story失败"))
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict("刷新dev_effic_test_dev_submit_story成功"))


class CreateDevStoryFromProductStoryView(ViewSet):
    authentication_classes = []
    """
    从产品需求创建研发需求
    """

    def create(self, request):
        workspace_id_list = request.data.get('workspace_id_list')
        modified = request.data.get("modified")
        biz_type = request.data.get("biz_type")
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if not workspace_id_list:
            return Response(status=status.HTTP_200_OK, data=ApiResult.failed_dict('workspace_id_list不能为空'))

        story_ser = BizStorySer()
        data_sync_ser = TapdEntryDataSyncLogSer()

        success_time = None
        for workspace_id in workspace_id_list:
            result = True
            try:
                if not modified:
                    modified = data_sync_ser.get_modified(workspace_id, biz_type,
                                                          TapdFunctionLogType.DATA_FROM_GATEWAY_PRODUCT_STORY.value,
                                                          current_time)
                success_time = story_ser.create_story_from_product_story(workspace_id, modified)
            except Exception as e:
                result = False
                log.error(e)
                return Response(status=status.HTTP_200_OK,
                                data=ApiResult.failed_dict("创建研发需求失败！"))
            finally:
                if result and success_time:
                    data_sync_ser.save_success_log(biz_type, TapdFunctionLogType.DATA_FROM_GATEWAY_PRODUCT_STORY.value,
                                                   workspace_id, success_time)
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict("创建研发需求成功！"))


class SyncDevStoryStatusToProductStoryView(ViewSet):
    authentication_classes = []
    """
    研发需求状态同步到产品需求
    """
    def create(self, request):
        # 业务workspace_id
        workspace_id = request.data.get('workspace_id')
        story_ser = BizStorySer()
        try:
            story_ser.sync_product_story_status_from_dev_story(workspace_id)
        except Exception as e:
            log.error(e)
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict("研发需求状态同步业务失败！"))
        return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict("研发需求状态同步业务成功！"))