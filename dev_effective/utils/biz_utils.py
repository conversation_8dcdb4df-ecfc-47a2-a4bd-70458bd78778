import datetime
from mantis.settings import logger as log


class TapdUtils:

    @staticmethod
    def date_str_to_date(date_str):
        try:
            if date_str and date_str != '':
                if type(date_str) is datetime.date or type(date_str) is datetime.datetime:
                    return date_str
                new_date = datetime.datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                return new_date
        except Exception as e:
            log.info('日期转换异常1：date_str：{}, error:{}'.format(date_str, str(e)))
            try:
                new_date = datetime.datetime.strptime(date_str, '%Y-%m-%d').date()
                return new_date
            except Exception as e:
                log.error('日期转换异常2：date_str：{}, error:{}'.format(date_str, str(e)))
        return None

    @staticmethod
    def date_to_date_str(date_obj):
        try:
            if date_obj:
                new_date = str(date_obj)
                return new_date
        except Exception as e:
            log.error('日期转换异常：date：{}, error:{}'.format(date_obj, str(e)))
        return None

    @staticmethod
    def str_split(data_str, split_rule):
        try:
            if data_str and split_rule and split_rule in data_str:
                data_list = data_str.split(split_rule)
                return data_list[0]
        except Exception as e:
            log.error('字符串切割异常：date_str：{}, error:{}'.format(data_str, str(e)))
        return data_str


if __name__ == '__main__':
    date_str = TapdUtils.date_str_to_date('2024-04-29 18:27:21')
    print(date_str)