from django.db import models


class DevEfficTestProject(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    tapd_iteration_id = models.BigIntegerField(verbose_name='迭代ID')
    tapd_story_id = models.BigIntegerField(verbose_name='故事ID')
    name = models.CharField(max_length=200, verbose_name='需求名称')
    owner = models.CharField(max_length=20, verbose_name='当前处理人')
    expected_start = models.DateTimeField(blank=True, null=True, verbose_name='预计开始时间')
    expected_end = models.DateTimeField(blank=True, null=True, verbose_name='预计结束时间')
    actual_start = models.DateTimeField(blank=True, null=True, verbose_name='实际开始时间')
    actual_end = models.DateTimeField(blank=True, null=True, verbose_name='实际结束时间')
    effort = models.Char<PERSON>ield(max_length=5, verbose_name='预估工时')
    work_hours = models.CharField(max_length=5, verbose_name='工时')
    status = models.CharField(max_length=20, verbose_name='需求数据状态')
    delay_days = models.CharField(max_length=10, verbose_name='延期天数')
    delay_description = models.TextField(verbose_name='延期描述')
    compatibility_description = models.CharField(max_length=500, verbose_name='兼容性描述')
    test_summary = models.TextField(verbose_name='测试总结')
    tp_iters = models.CharField(max_length=200, verbose_name='藏经阁迭代')
    tms_iters = models.CharField(max_length=200, verbose_name='达摩院服务端迭代')
    tms_h5_iters = models.CharField(max_length=200, verbose_name='达摩院h5迭代')
    fp_iters = models.CharField(max_length=200, verbose_name='爱码仕服务端迭代')
    fp_h5_app_iters = models.CharField(max_length=200, verbose_name='爱码仕h5&客户端迭代')
    crm_iters = models.CharField(max_length=200, verbose_name='六扇门迭代')
    team = models.CharField(max_length=10, verbose_name='团队')
    dev2test_date = models.CharField(max_length=50, verbose_name='开发提测日期')
    task_division_ratio = models.CharField(max_length=100, verbose_name='任务分配比例')
    business_system = models.CharField(max_length=50, verbose_name='业务系统')
    data_source = models.CharField(max_length=10, verbose_name='数据来源')
    create_user = models.CharField(max_length=20, verbose_name='创建人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=20, verbose_name='修改人')
    update_time = models.DateTimeField(auto_now_add=True, verbose_name='修改时间')

    class Meta:
        db_table = 'dev_effic_test_project'
        verbose_name = '业务项目实体表'


class BizEntrySyncMsgInfo(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    title = models.CharField(max_length=128, verbose_name='标题')
    content = models.CharField(max_length=999, verbose_name='内容')
    source = models.CharField(max_length=128, verbose_name='来源')
    creator = models.CharField(max_length=20, verbose_name='tapd创建人')
    owner = models.CharField(max_length=100, verbose_name='tapd处理人')
    status = models.IntegerField(verbose_name='状态')
    complate_time = models.DateTimeField(blank=True, null=True, verbose_name='通知时间')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        db_table = 'biz_entry_sync_msg_info'
        verbose_name = '业务信件表'


class DevEfficTestReport(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    project_id = models.IntegerField(verbose_name='测试项目ID')
    tapd_launch_form_id = models.IntegerField(verbose_name='tapd发布评审ID')
    title = models.CharField(max_length=200, verbose_name='评审标题')
    status = models.CharField(max_length=20, verbose_name='评审状态:success、fail、approving审批中')
    creator = models.CharField(max_length=100, verbose_name='报告创建人')
    approver = models.CharField(max_length=100, verbose_name='报告审批人')
    data_source = models.CharField(max_length=10, verbose_name='来源')
    create_user = models.CharField(max_length=20, verbose_name='创建人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=20, verbose_name='修改人')
    update_time = models.DateTimeField(auto_now_add=True, verbose_name='修改时间')

    class Meta:
        db_table = 'dev_effic_test_report'
        verbose_name = '业务测试报告表'


class DevEfficTestReportLibInfoDjango(models.Model):
    report_id = models.IntegerField(null=True, verbose_name='报告ID')
    report_url = models.CharField(null=True, verbose_name='测试报告地址', max_length=500)
    create_time = models.DateTimeField(null=True, verbose_name='创建时间')
    create_user = models.CharField(null=True, verbose_name='创建人', max_length=50)
    update_time = models.DateTimeField(null=True, verbose_name='更新时间')
    update_user = models.CharField(null=True, verbose_name='更新人', max_length=50)

    class Meta:
        db_table = 'dev_effic_test_report_lib_info'
        verbose_name = '测试报告库信息表'


class DevEfficTestDevSubmit(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    project_id = models.IntegerField(verbose_name='测试项目ID')
    tapd_test_plan_id = models.IntegerField(verbose_name='tapd开发测试计划ID')
    tapd_story_id = models.IntegerField(verbose_name='tapd测试需求（提测计划）ID')
    tapd_test_plan_status = models.IntegerField(verbose_name='tapd测试计划状态：0,已删除，1，正常')
    name = models.CharField(max_length=500, verbose_name='提测单名称')
    description = models.TextField(verbose_name='提测单描述')
    status = models.CharField(max_length=20, verbose_name='提测单状态')
    owner = models.CharField(max_length=50, verbose_name='处理人')
    creator = models.CharField(max_length=50, verbose_name='提测单创建人')
    created = models.DateTimeField(blank=True, null=True, verbose_name='提测单创建时间')
    team = models.CharField(max_length=20, verbose_name='团队')
    data_source = models.CharField(max_length=10, verbose_name='来源')
    create_user = models.CharField(max_length=20, verbose_name='创建人')
    update_user = models.CharField(max_length=20, verbose_name='最后修改人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now_add=True, verbose_name='修改时间')

    class Meta:
        db_table = 'dev_effic_test_dev_submit'
        verbose_name = '业务开发提测实体表'


class DevEfficTestDevSubmitBindStory(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    dev_submit_id = models.IntegerField(verbose_name='提测单ID')
    dev_story_id = models.IntegerField(verbose_name='研发测试子需求ID')

    class Meta:
        db_table = 'dev_effic_test_dev_submit_story'
        verbose_name = '业务开发提测关联需求id关系表'


class DevEfficTestBug(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    project_id = models.BigIntegerField(verbose_name='测试项目ID')
    tapd_bug_id = models.IntegerField(verbose_name='tapd缺陷ID')
    title = models.CharField(max_length=200, verbose_name='缺陷名称')
    severity = models.CharField(max_length=10, verbose_name='严重程度')
    status = models.CharField(max_length=20, verbose_name='状态')
    bug_type = models.CharField(max_length=50, verbose_name='缺陷类型')
    fixer = models.CharField(max_length=50, verbose_name='缺陷修复人')
    reporter = models.CharField(max_length=100, verbose_name='缺陷创建人')
    resolved = models.DateTimeField(blank=True, null=True, verbose_name='修复时间')
    created = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    closed = models.DateTimeField(blank=True, null=True, verbose_name='关闭时间')
    flows = models.CharField(max_length=500, verbose_name='工作流')
    tp_app = models.CharField(max_length=100, verbose_name='藏经阁全应用')
    tms_app = models.CharField(max_length=100, verbose_name='达摩院服务端应用')
    fp_app = models.CharField(max_length=100, verbose_name='爱码仕服务端应用')
    crm_app = models.CharField(max_length=100, verbose_name='六扇门全应用')
    tms_h5 = models.CharField(max_length=100, verbose_name='达摩院h5应用')
    fp_h5_app = models.CharField(max_length=100, verbose_name='爱码仕H5&客户端应用')
    attribution_analysis = models.CharField(max_length=100, verbose_name='根因分析')
    data_source = models.CharField(max_length=100, verbose_name='数据来源')
    create_user = models.CharField(max_length=20, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=20, verbose_name='更新人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'dev_effic_test_bug'
        verbose_name = '业务缺陷表'


class DevEfficTestTask(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    project_id = models.IntegerField(verbose_name='测试项目ID')
    tapd_task_id = models.IntegerField(verbose_name='tapd任务ID')
    dev_submit_id = models.IntegerField(verbose_name='提测单ID')
    name = models.CharField(max_length=500, verbose_name='任务名称')
    description = models.CharField(max_length=1000, verbose_name='任务描述')
    status = models.CharField(max_length=50, verbose_name='状态')
    owner = models.CharField(max_length=20, verbose_name='任务处理人')
    creator = models.CharField(max_length=20, verbose_name='任务创建人')
    task_type = models.CharField(max_length=50, verbose_name='任务类型')
    created = models.DateTimeField(blank=True, null=True, verbose_name='任务创建时间')
    completed = models.DateTimeField(blank=True, null=True, verbose_name='任务完成时间')
    effort_completed = models.CharField(max_length=20, verbose_name='完成工时')
    exceed = models.CharField(max_length=50, verbose_name='超出工时')
    remain = models.FloatField(verbose_name='剩余工时')
    effort = models.CharField(max_length=20, verbose_name='预估工时')
    data_source = models.CharField(max_length=100, verbose_name='数据来源')
    create_user = models.CharField(max_length=20, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=20, verbose_name='更新人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'dev_effic_test_task'
        verbose_name = '测试任务表'


class DevEfficDevStory(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    name = models.CharField(max_length=500, verbose_name='需求名称')
    owner = models.CharField(max_length=20, verbose_name='当前处理人')
    expected_start = models.DateField(blank=True, null=True, verbose_name='预计开始日期')
    expected_end = models.DateField(blank=True, null=True, verbose_name='预计结束日期')
    actual_start = models.DateField(blank=True, null=True, verbose_name='实际开始日期')
    actual_end = models.DateField(blank=True, null=True, verbose_name='实际结束日期')
    effort = models.CharField(max_length=5, verbose_name='预计工时')
    work_hours = models.CharField(max_length=5, verbose_name='工时')
    size = models.IntegerField(verbose_name='规模')
    status = models.CharField(max_length=20, verbose_name='状态')
    type = models.CharField(max_length=100, verbose_name='需求类型')
    delay_days = models.CharField(max_length=10, verbose_name='延期天数')
    delay_description = models.TextField(verbose_name='延期说明')
    team = models.CharField(max_length=10, verbose_name='团队')
    howbuy_project = models.CharField(max_length=100, verbose_name='公司战略项目')
    tapd_story_id = models.CharField(max_length=10, verbose_name='故事ID')
    data_source = models.CharField(max_length=10, verbose_name='数据来源')
    create_user = models.CharField(max_length=20, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=20, verbose_name='更新人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'dev_effic_dev_story'
        verbose_name = '研发需求表'


class DevEfficDevTask(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    name = models.CharField(max_length=500, verbose_name='任务名称')
    description = models.CharField(max_length=1000, verbose_name='任务描述')
    status = models.CharField(max_length=50, verbose_name='状态')
    task_type = models.CharField(max_length=10, verbose_name='任务类型')
    owner = models.CharField(max_length=100, verbose_name='任务处理人')
    creator = models.CharField(max_length=50, verbose_name='任务创建人')
    created = models.DateTimeField(blank=True, null=True, verbose_name='任务创建时间')
    completed = models.DateField(blank=True, null=True, verbose_name='任务完成时间')
    effort_completed = models.CharField(max_length=100, verbose_name='任务完成工时')
    exceed = models.CharField(max_length=50, verbose_name='任务超出工时')
    remain = models.FloatField(verbose_name='任务剩余工时')
    effort = models.CharField(max_length=20, verbose_name='任务预估工时')
    dev_story_id = models.BigIntegerField(verbose_name='需求ID')
    tapd_task_id = models.BigIntegerField(verbose_name='tapd任务ID')
    data_source = models.CharField(max_length=10, verbose_name='数据来源')
    create_user = models.CharField(max_length=20, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=20, verbose_name='更新人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'dev_effic_dev_task'
        verbose_name = '研发任务表'


class DevEfficDevTaskTimeSheet(models.Model):
    create_user = models.CharField(max_length=20, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=20, verbose_name='更新人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='更新时间')
    stamp = models.BigIntegerField(verbose_name='stamp')
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    dev_task_timesheet_id = models.BigIntegerField(verbose_name='tapd耗时ID')
    dev_workspace_id = models.BigIntegerField(verbose_name='tapd项目ID')
    dev_task_id = models.BigIntegerField(verbose_name='tapd任务ID')
    dev_task_timesheet_owner = models.CharField(max_length=50, blank=True, null=True, verbose_name='所有者')
    dev_task_timesheet_created = models.CharField(max_length=20, blank=True, null=True, verbose_name='创建时间')
    dev_task_timesheet_modified = models.CharField(max_length=20, blank=True, null=True, verbose_name='修改时间')
    dev_task_timesheet_spentdate = models.CharField(max_length=20, blank=True, null=True, verbose_name='花费日期')
    dev_task_timesheet_timespent = models.CharField(max_length=20, blank=True, null=True, verbose_name='花费工时')
    dev_task_timesheet_timeremain = models.CharField(max_length=20, blank=True, null=True, verbose_name='剩余工时')
    dev_task_timesheet_memo = models.TextField(blank=True, null=True, verbose_name='花费描述')
    dev_task_timesheet_desc = models.CharField(max_length=255, blank=True, null=True, verbose_name='研发任务工时说明')

    class Meta:
        db_table = 'dev_effic_dev_task_timesheet'
        verbose_name = '研效开发任务工时'


class DevEfficTestTaskTimeSheet(models.Model):
    create_user = models.CharField(max_length=20, verbose_name='创建人')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_user = models.CharField(max_length=20, verbose_name='更新人')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='更新时间')
    stamp = models.BigIntegerField(verbose_name='stamp')
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    test_task_timesheet_id = models.BigIntegerField(verbose_name='tapd耗时ID')
    test_workspace_id = models.BigIntegerField(verbose_name='tapd项目ID')
    test_task_id = models.BigIntegerField(verbose_name='tapd任务ID')
    test_task_timesheet_owner = models.CharField(max_length=50, blank=True, null=True, verbose_name='所有者')
    test_task_timesheet_created = models.CharField(max_length=20, blank=True, null=True, verbose_name='创建时间')
    test_task_timesheet_modified = models.CharField(max_length=20, blank=True, null=True, verbose_name='修改时间')
    test_task_timesheet_spentdate = models.CharField(max_length=20, blank=True, null=True, verbose_name='花费日期')
    test_task_timesheet_timespent = models.CharField(max_length=20, blank=True, null=True, verbose_name='花费工时')
    test_task_timesheet_timeremain = models.CharField(max_length=20, blank=True, null=True, verbose_name='剩余工时')
    test_task_timesheet_memo = models.TextField(blank=True, null=True, verbose_name='花费描述')
    test_task_timesheet_desc = models.CharField(max_length=255, blank=True, null=True, verbose_name='研发任务工时说明')

    class Meta:
        db_table = 'dev_effic_test_task_timesheet'
        verbose_name = '研效测试任务工时'
