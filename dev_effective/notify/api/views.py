from rest_framework import status
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from dev_effective.notify.service.notify_msg_service import NotifyMsgSer
from mantis.settings import ApiResult
from mantis.settings import logger as log


class NotifyBizView(ViewSet):
    """
    通知业务
    """
    authentication_classes = []

    def list(self, request):
        notify_msg = NotifyMsgSer()
        try:
            notify_msg.notify_msg()
            return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(
                "通知执行成功"))
        except Exception as e:
            log.error(e)
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict(
                                "通知执行失败"))