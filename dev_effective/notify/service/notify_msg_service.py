import datetime
import requests

from itertools import groupby
from abc import ABC, abstractmethod
from dev_effective.model.models import BizEntrySyncMsgInfo
from dev_effective.notify.service.email_template import notify_table
from dev_effective.notify.service.send_email import SendMail
from dev_effective.service.biz_iteration_service import OperationFailModuleEnum
from dev_effective.service.biz_tapd_data_write_strategy import NotifyStatusEnum
from mantis.settings import logger as log, SPIDER
from mantis.settings import EMAIL_BASES


class UserNameInfo:

    def __init__(self, user_cn_name=None, user_en_name=None):
        self.__user_cn_name = user_cn_name
        self.__user_en_name = user_en_name

    def get_user_cn_name(self):
        try:
            url = '{}user/get_cn_name/?en_name={}'.format(SPIDER["url"], self.__user_en_name)
            response = requests.get(url)
            names = response.json()

            for name in names:
                return name.get("cn_name")
        except Exception as e:
            log.error("调用spider接口异常： {}".format(str(e)))
        return None

    def get_user_en_name(self):
        try:
            url = '{}user/get_cn_name/?cn_name={}'.format(SPIDER["url"], self.__user_cn_name)
            response = requests.get(url)
            names = response.json()

            for name in names:
                return name.get("username")
        except Exception as e:
            log.error("调用spider接口异常： {}".format(str(e)))
        return None


class SendNotifyEmail:
    def send_notify_email(self, notify_list, subject, entry_name, to_who='creator'):
        story_duplication_name_cc = EMAIL_BASES["story_duplication_name_cc"]
        if notify_list:
            notify_dict = {key: list(group) for key, group in
                           groupby(notify_list, key=lambda x: x[to_who])}
            for to_who, notify_list in notify_dict.items():
                if to_who:
                    send_mail = SendMail()
                    send_mail.set_subject(subject)
                    td_content = ''
                    for notify in notify_list:
                        title = notify.get("title")
                        content = notify.get("content")
                        td_content += '<tr><td>{}</td><td>{}</td></tr>'.format(title, content)
                    mail_info_str = notify_table.format(entry_name=entry_name, td_content=td_content)
                    send_mail.set_content(mail_info_str)
                    if to_who == 'm?WYBPmq':
                        to_who = 'pa'
                    else:
                        uni = UserNameInfo(user_cn_name=to_who.split(";")[0].split("_")[0])
                        to_who = uni.get_user_en_name()
                    if to_who:
                        send_mail.set_to(to_who + '@howbuy.com')
                        send_mail.set_cc(story_duplication_name_cc)
                        send_mail.send()


class NotifyMsgStrategy(ABC):

    @abstractmethod
    def execute_notify(self, notify_list):
        pass


class StoryDuplicationNameStrategy(NotifyMsgStrategy):
    def execute_notify(self, notify_list):
        sne = SendNotifyEmail()
        sne.send_notify_email(notify_list, "【TAPD】需求名称重名提醒", "需求")


class LaunchFormDuplicationNameStrategy(NotifyMsgStrategy):
    def execute_notify(self, notify_list):
        sne = SendNotifyEmail()
        sne.send_notify_email(notify_list, "【TAPD】发布评审名称重名提醒", "发布评审")


class StoryInfoIncompleteStrategy(NotifyMsgStrategy):
    def execute_notify(self, notify_list):
        sne = SendNotifyEmail()
        sne.send_notify_email(notify_list, "【TAPD】需求信息不完整提醒", "需求")


class TaskInfoIncompleteStrategy(NotifyMsgStrategy):
    def execute_notify(self, notify_list):
        sne = SendNotifyEmail()
        sne.send_notify_email(notify_list, "【TAPD】任务信息不完整提醒", "任务", to_who='owner')


class TestPlanRelativeDuplicationStoryStrategy(NotifyMsgStrategy):
    def execute_notify(self, notify_list):
        sne = SendNotifyEmail()
        sne.send_notify_email(notify_list, "【TAPD】提测计划关联需求重复（父子需求）提醒", "需求")


class StoryHaveChildrenStrategy(NotifyMsgStrategy):
    def execute_notify(self, notify_list):
        sne = SendNotifyEmail()
        sne.send_notify_email(notify_list, "【TAPD】测试需求不允许有层级关系提醒", "需求")


class BugAppSelectWrongStrategy(NotifyMsgStrategy):
    def execute_notify(self, notify_list):
        sne = SendNotifyEmail()
        sne.send_notify_email(notify_list, "【TAPD】迭代中的缺陷绑定的应用与需求绑定的研发迭代不一致提醒", "迭代")


class StrategyContext:
    def __init__(self, strategy):
        self.strategy = strategy

    def set_strategy(self, strategy):
        self.strategy = strategy

    def execute_strategy(self, notify_list):
        return self.strategy.execute_notify(notify_list)


class NotifyMsgSer:

    def get_strategy(self, source):
        if source == OperationFailModuleEnum.STORY_DUPLICATION_NAME.module_name:
            return StoryDuplicationNameStrategy()
        elif source == OperationFailModuleEnum.LAUNCH_FORM_DUPLICATION_NAME.module_name:
            return LaunchFormDuplicationNameStrategy()
        elif source == OperationFailModuleEnum.STORY_INFO_INCOMPLETE.module_name:
            return StoryInfoIncompleteStrategy()
        elif source == OperationFailModuleEnum.TASK_INFO_INCOMPLETE.module_name:
            return TaskInfoIncompleteStrategy()
        elif source == OperationFailModuleEnum.TEST_PLAN_RELATIVE_DUPLICATION_STORY.module_name:
            return TestPlanRelativeDuplicationStoryStrategy()
        elif source == OperationFailModuleEnum.STORY_HAVE_CHILDREN.module_name:
            return StoryHaveChildrenStrategy()
        elif source == OperationFailModuleEnum.BUG_APP_SELECT_WRONG.module_name:
            return BugAppSelectWrongStrategy()

    def notify_msg(self):
        notify_list = BizEntrySyncMsgInfo.objects.filter(status=NotifyStatusEnum.NOT_NOTIFY.status).all()
        notify_data_list = []
        update_list = []
        for notify in notify_list:
            msg_info = BizEntrySyncMsgInfo()
            msg_info.id = notify.id
            msg_info.title = notify.title
            msg_info.content = notify.content
            msg_info.source = notify.source
            msg_info.status = NotifyStatusEnum.HAS_NOTIFY.status
            msg_info.complate_time = datetime.datetime.now()
            update_list.append(msg_info)
            notify_data_list.append(
                {"id": notify.id, "title": notify.title, "content": notify.content, "source": notify.source,
                 "creator": notify.creator, "owner": notify.owner, "status": NotifyStatusEnum.HAS_NOTIFY.status,
                 "complate_time": datetime.datetime.now()})
        if notify_data_list:
            grouped_dict = {key: list(group) for key, group in
                            groupby(notify_data_list, key=lambda x: x["source"])}
            for key, value in grouped_dict.items():
                log.info("通知业务类型:{}".format(key))
                notify_strategy = self.get_strategy(key)
                context = StrategyContext(notify_strategy)
                context.execute_strategy(value)
            upd_fields = [
                'status',
                'complate_time',
            ]
            upd_rows = BizEntrySyncMsgInfo.objects.bulk_update(update_list, upd_fields)
            return upd_rows
        return 0
