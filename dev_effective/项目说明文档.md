# Dev Effective 模块项目说明文档

## 项目概述

`dev_effective` 模块是 Mantis 系统中的研发效能管理核心模块，负责处理研发过程中的需求管理、迭代管理、测试计划、缺陷管理等业务流程。该模块与 TAPD Gateway 深度集成，实现从 TAPD 系统到业务系统的数据同步和流程自动化。

### 核心功能

- **需求生命周期管理**：从产品需求到研发需求的转换和状态同步
- **迭代管理**：自动创建测试迭代，关联需求和任务
- **测试计划管理**：测试计划创建和需求关联
- **发布评审管理**：基于迭代自动创建发布评审流程
- **缺陷管理**：自动化缺陷创建和状态跟踪
- **数据ETL处理**：TAPD数据到业务数据的转换和同步

## 技术架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Dev Effective 模块                      │
├─────────────────────────────────────────────────────────────┤
│  API 接口层 (api/views.py)                                 │
│  ├── CheckTapdDataBizView      # TAPD数据问题检查          │
│  ├── BizETLView                # 业务数据ETL处理           │
│  ├── BizIterationView          # 迭代管理                  │
│  ├── CreateLaunchFormFromIterationView # 发布评审创建      │
│  ├── BizBugCreateView          # 缺陷自动创建              │
│  ├── BizTestPlanView           # 测试计划管理              │
│  └── RefreshTestDevSubmitRelativeStories # 提测关联刷新   │
├─────────────────────────────────────────────────────────────┤
│  服务层 (service/)                                         │
│  ├── biz_story_service         # 需求业务服务              │
│  ├── biz_iteration_service     # 迭代业务服务              │
│  ├── biz_launch_form_service   # 发布评审服务              │
│  ├── biz_test_plan_service     # 测试计划服务              │
│  ├── biz_bug_service           # 缺陷业务服务              │
│  └── biz_tapd_data_write_strategy # TAPD数据写入策略       │
├─────────────────────────────────────────────────────────────┤
│  数据访问层 (dao/)                                         │
│  └── dev_effective_dao         # 业务数据访问对象          │
├─────────────────────────────────────────────────────────────┤
│  数据模型层 (model/models.py)                              │
│  ├── DevEfficTestProject       # 业务测试项目模型          │
│  ├── DevEfficTestReport        # 业务测试报告模型          │
│  ├── DevEfficTestDevSubmit     # 开发提测模型              │
│  ├── DevEfficTestBug           # 业务缺陷模型              │
│  ├── DevEfficTestTask          # 测试任务模型              │
│  ├── DevEfficDevStory          # 研发需求模型              │
│  └── BizEntrySyncMsgInfo       # 业务同步消息模型          │
├─────────────────────────────────────────────────────────────┤
│  通知模块 (notify/)                                        │
│  ├── api/views                 # 通知API接口               │
│  ├── dao/                      # 通知数据访问              │
│  └── service/                  # 通知服务                  │
└─────────────────────────────────────────────────────────────┘
```

## 核心模块

### 1. API 接口层

#### 主要视图类

- **CheckTapdDataBizView**: TAPD数据问题情况收集和检查
- **BizETLView**: 测试任务、测试缺陷、测试发布评审、研发需求和研发任务ETL
- **BizIterationView**: 测试需求创建测试迭代，迭代关联需求
- **CreateLaunchFormFromIterationView**: 迭代创建发布评审
- **BizBugCreateView**: 自动创建缺陷
- **BizBugStatus**: 获取缺陷状态信息
- **BizTestPlanView**: 测试计划管理
- **RefreshTestDevSubmitRelativeStories**: 刷新提测单关联的开发需求关系

### 2. 服务层

#### 核心服务类

- **BizStorySer**: 需求业务服务
  - 同步研发需求到业务系统
  - 同步测试提测需求
  - 同步测试需求到业务系统
  - 需求状态管理和转换

- **BizIterationSer**: 迭代业务服务
  - 基于需求创建迭代
  - 迭代与需求的关联管理
  - 使用责任链模式处理迭代创建流程

- **BizLaunchFormSer**: 发布评审服务
  - 基于迭代创建发布评审
  - 发布评审流程管理

- **BizTestPlanSer**: 测试计划服务
  - 测试计划数据同步
  - 测试计划与需求关联

- **CheckTapdDataSer**: TAPD数据检查服务
  - 数据完整性检查
  - 数据质量问题收集

- **TapdDataSyncToBizSer**: TAPD数据同步服务
  - 数据ETL处理
  - 多种实体类型的数据同步

### 3. 数据模型层

#### 核心数据模型

- **DevEfficTestProject**: 业务项目实体表
  - 存储测试项目的基本信息、时间计划、团队信息等
  - 包含各系统迭代信息（藏经阁、达摩院、爱码仕、六扇门）

- **DevEfficTestReport**: 业务测试报告表
  - 存储测试报告的状态、创建者、审批者等信息

- **DevEfficTestDevSubmit**: 业务开发提测实体表
  - 管理开发提测单的生命周期
  - 关联测试计划和测试需求

- **DevEfficTestBug**: 业务缺陷表
  - 存储缺陷的详细信息、修复状态、归因分析等
  - 支持多应用系统的缺陷分类

- **DevEfficTestTask**: 测试任务表
  - 管理测试任务的执行状态和工时信息

- **DevEfficDevStory**: 研发需求模型
  - 存储研发需求的详细信息和状态

## 数据流程

### 需求管理流程
```
1. TAPD产品需求 → 研发需求转换
2. 研发需求 → 测试需求创建
3. 测试需求 → 迭代创建
4. 迭代 → 发布评审创建
5. 需求状态同步 → TAPD系统
```

### 数据ETL流程
```
1. 从TAPD Gateway获取原始数据
2. 数据清洗和转换
3. 业务规则应用
4. 写入业务数据表
5. 状态同步和通知
```

### 缺陷管理流程
```
1. 自动缺陷创建请求
2. 缺陷信息验证
3. TAPD缺陷创建
4. 缺陷状态跟踪
5. 缺陷归因分析
```

## API 接口

### URL 路由配置

```python
# 主要API端点
router.register(r'create_story_from_dev_test_plan', BizTestPlanView)
router.register(r'create_iteration_from_story', BizIterationView)
router.register(r'create_launch_form_from_iteration', CreateLaunchFormFromIterationView)
router.register(r'auto_bug_create', BizBugCreateView)
router.register(r'check_tapd_data', CheckTapdDataBizView)
router.register(r'sync_data_from_gateway', BizETLView)
router.register(r'get_bug_status', BizBugStatus)
router.register(r'send_msg', NotifyBizView)
```

### 主要接口说明

#### 1. 数据ETL同步
- **URL**: `/dev_effective/sync_data_from_gateway/`
- **方法**: GET
- **功能**: 从TAPD Gateway同步数据到业务系统
- **参数**: `env_type`, `entry_type`

#### 2. 迭代创建
- **URL**: `/dev_effective/create_iteration_from_story/`
- **方法**: GET
- **功能**: 基于测试需求创建迭代
- **参数**: `workspace_id`

#### 3. 发布评审创建
- **URL**: `/dev_effective/create_launch_form_from_iteration/`
- **方法**: GET
- **功能**: 基于迭代创建发布评审
- **参数**: `workspace_id`

#### 4. 自动缺陷创建
- **URL**: `/dev_effective/auto_bug_create/`
- **方法**: POST
- **功能**: 自动创建TAPD缺陷
- **参数**: 缺陷详细信息JSON

#### 5. 缺陷状态查询
- **URL**: `/dev_effective/get_bug_status/`
- **方法**: POST
- **功能**: 批量查询缺陷状态
- **参数**: `uuid_list`

## 配置说明

### 数据库配置
```ini
[DATABASE]
host = localhost
port = 3306
user = dev_user
password = dev_password
database = mantis
```

### TAPD集成配置
```ini
[TAPD]
api_url = https://api.tapd.cn
api_user = your_api_user
api_password = your_api_password
workspace_id = your_workspace_id
```

### 业务配置
```ini
[BUSINESS]
test_submit_story_workitem_type_id = 123
workitem_type_id = 456
default_team = 研发团队
```

## 部署指南

### 环境要求
- Python 3.8+
- Django 3.2+
- MySQL 5.7+
- Redis (用于缓存)

### 安装步骤
1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **数据库迁移**
   ```bash
   python manage.py makemigrations dev_effective
   python manage.py migrate
   ```

3. **配置文件**
   - 复制 `settings.ini.example` 为 `settings.ini`
   - 根据环境修改配置参数

4. **启动服务**
   ```bash
   python manage.py runserver 0.0.0.0:8088
   ```

### 定时任务配置
建议配置以下定时任务：
```bash
# 每小时同步TAPD数据
0 * * * * curl -X GET "http://localhost:8088/dev_effective/sync_data_from_gateway/?env_type=prod&entry_type=story"

# 每天检查TAPD数据质量
0 2 * * * curl -X GET "http://localhost:8088/dev_effective/check_tapd_data/"

# 每天刷新提测关联关系
0 3 * * * curl -X GET "http://localhost:8088/dev_effective/refresh_test_dev_submit/"
```

## 业务特性

### 1. 责任链模式
模块使用责任链模式处理复杂的业务流程：
- **CreateIterationHandler**: 处理迭代创建
- **UpdateStoryHandler**: 处理需求更新
- 支持流程的灵活扩展和组合

### 2. 数据映射和转换
- **StoryMappingEnum**: 需求状态映射枚举
- **TapdUtils**: TAPD数据格式转换工具
- 支持多种数据格式的转换和验证

### 3. 批量数据处理
- 支持批量创建、更新、删除操作
- 使用Django ORM的bulk操作提高性能
- 事务管理确保数据一致性

### 4. 多系统集成
- 支持多个业务系统的迭代管理（藏经阁、达摩院、爱码仕、六扇门）
- 统一的数据模型和接口设计
- 灵活的配置管理

## 监控和日志

### 日志配置
系统使用Django标准日志配置：
- **INFO**: 正常业务流程日志
- **ERROR**: 错误和异常日志
- **DEBUG**: 调试信息（开发环境）

### 关键监控指标
- **数据同步成功率**: 监控TAPD数据同步的成功率
- **API响应时间**: 监控接口响应性能
- **业务流程完成率**: 监控迭代创建、发布评审等流程的完成情况
- **缺陷创建成功率**: 监控自动缺陷创建的成功率

## 常见问题排查

### 1. 数据同步失败
**现象**: ETL接口返回失败状态
**排查步骤**:
1. 检查TAPD Gateway连接状态
2. 查看错误日志确定具体错误原因
3. 检查数据库连接和权限
4. 验证TAPD API认证信息

### 2. 迭代创建失败
**现象**: 迭代创建接口返回异常
**排查步骤**:
1. 检查需求数据完整性
2. 验证TAPD工作空间权限
3. 检查迭代名称是否重复
4. 确认需求状态是否符合创建条件

### 3. 缺陷创建异常
**现象**: 自动缺陷创建失败
**排查步骤**:
1. 检查缺陷信息完整性
2. 验证TAPD缺陷创建权限
3. 检查UUID是否重复
4. 确认缺陷模板配置

## 与其他模块的集成

### 1. 与 TAPD Gateway 模块集成

**数据流向**:
```
TAPD系统 → tapd_gateway (数据采集) → dev_effective (业务处理)
```

**集成点**:
- **数据模型**: 使用 `tapd_gateway.models` 中的数据模型
- **数据同步**: 通过ETL接口同步TAPD数据
- **业务处理**: 基于TAPD数据进行业务逻辑处理

**主要依赖**:
```python
from tapd_gateway.models import (
    TapdEntryStory,      # 需求数据
    TapdEntryIteration,  # 迭代数据
    TapdEntryBug,        # 缺陷数据
    BizBugCreate         # 缺陷创建业务
)
```

### 2. 与 User 模块集成

**认证依赖**:
```
user (认证服务) → dev_effective (业务处理)
```

**集成方式**:
- **API认证**: 所有API接口都依赖user模块的JWT或会话认证
- **操作人记录**: 记录当前操作用户信息
- **权限控制**: 基于用户角色的操作权限验证

**使用示例**:
```python
class DemandView(ViewSet):
    authentication_classes = [JWTAuthentication, LoginAuthentication]
    permission_classes = [IsAuthenticated]
    
    def create(self, request):
        # 获取当前操作用户
        operator = request.user.username
        
        # 记录操作人信息
        demand_data = {
            'creator': operator,
            'modifier': operator,
            # 其他业务数据
        }
```

### 3. 与 Task Management 模块集成

**任务调用关系**:
```
dev_effective (业务触发) → task_mgt (任务执行) → qa_scripts (脚本处理)
```

**集成场景**:
- **数据分析任务**: 触发单元测试数据分析
- **报告生成任务**: 创建业务测试报告
- **批量处理任务**: 大批量数据处理

**调用示例**:
```python
from task_mgt.external_service import ExternalService

class DemandSer:
    def trigger_analysis_task(self, demand_id, operator):
        # 创建外部服务任务
        service = ExternalService(
            srv_name='analyze_demand_data',
            operator=operator,
            params={'demand_id': demand_id}
        )
        
        # 执行任务
        result = service.call_local_service()
        return result
```

### 4. 与 Quality 模块集成

**历史集成关系**:
```
quality (已废弃) → dev_effective (功能迁移)
```

**功能迁移**:
- **缺陷状态查询**: 从quality模块迁移到dev_effective
- **TAPD集成**: 统一使用tapd_gateway模块
- **业务逻辑**: 整合到dev_effective的业务流程中

**迁移说明**:
- quality模块的`GetBugCreateStatus`功能已迁移
- 建议使用dev_effective的`get_bug_status`接口
- 保持API兼容性，逐步迁移客户端调用

## 扩展开发

### 添加新的业务流程
1. **创建新的Handler类**
   ```python
   class NewProcessHandler(Handler):
       def handle(self, request):
           # 实现具体的业务逻辑
           pass
   ```

2. **扩展服务类**
   ```python
   class NewBusinessSer:
       def process_new_business(self, params):
           # 实现新的业务处理逻辑
           pass
   ```

3. **添加API接口**
   ```python
   class NewBusinessView(ViewSet):
       def list(self, request):
           # 实现API接口逻辑
           pass
   ```

### 添加新的数据模型
1. **创建数据模型**
   ```python
   class NewBusinessModel(models.Model):
       # 定义字段
       pass
   ```

2. **创建数据访问对象**
   ```python
   class NewBusinessDao:
       def get_data(self, params):
           # 实现数据访问逻辑
           pass
   ```

## 版本历史

### v1.0.0 (当前版本)
- 基础需求管理功能
- 迭代和发布评审管理
- TAPD数据同步和ETL
- 自动缺陷创建
- 通知和消息管理

### 后续规划
- 增加更多业务流程自动化
- 优化数据同步性能
- 增强监控和告警功能
- 支持更多外部系统集成
- 增加数据分析和报表功能

## 联系信息

- **项目负责人**: 研发效能团队
- **技术支持**: 通过内部工单系统提交
- **文档更新**: 2024年12月

---

*本文档随项目版本更新，请以最新版本为准。*