from django.urls import path, include

from dev_effective.api import views
from rest_framework.routers import DefaultRouter

from dev_effective.api.views import RefreshTestDevSubmitRelativeStories
from dev_effective.notify.api.views import NotifyBizView

router = DefaultRouter()

router.register(r'create_story_from_dev_test_plan', views.BizTestPlanView, basename="create_story_from_dev_test_plan/")
router.register(r'create_iteration_from_story', views.BizIterationView, basename="create_iteration_from_story/")
router.register(r'create_launch_form_from_iteration', views.CreateLaunchFormFromIterationView,
                basename="create_launch_form_from_iteration/")
router.register(r'auto_bug_create', views.BizBugCreateView, basename="auto_bug_create/")
router.register(r'create_story_from_product_story', views.CreateDevStoryFromProductStoryView, basename="create_story_from_product_story/")

router.register(r'check_tapd_data', views.CheckTapdDataBizView, basename="check_tapd_data/")
router.register(r'sync_data_from_gateway', views.BizETLView, basename="sync_data_from_gateway/")
router.register(r'get_bug_status', views.BizBugStatus, basename="get_bug_status/")
router.register(r'send_msg', NotifyBizView, basename="send_msg/")
router.register(r'sync_dev_story_status_to_product_story', views.SyncDevStoryStatusToProductStoryView, basename="sync_dev_story_status_to_product_story/")

router.register(r'refresh_test_dev_submit', RefreshTestDevSubmitRelativeStories, basename="refresh_test_dev_submit")

urlpatterns = [
    path("", include(router.urls))
]
