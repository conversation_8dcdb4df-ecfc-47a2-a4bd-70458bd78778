from django.db import connection
from mantis.pool import instance_db_session
from sqlalchemy import text


class TapdWorkspaceModuleConfDao:
    __query_tapd_custom_field_sql = '''
                            SELECT c.conf_column 
                            FROM tapd_workspace_module_conf c
                            JOIN tapd_workspace_module m ON c.module_id = m.id
                            JOIN tapd_workspace w ON m.workspace_id = w.id
                            WHERE c.tapd_name = :tapd_name AND m.module_name = :module_name 
                            AND w.tapd_workspace_id = :workspace_id
                            '''

    def __init__(self, tapd_name=None, workspace_id=None, module_name=None):
        self.__tapd_name = tapd_name
        self.__workspace_id = workspace_id
        self.__module_name = module_name

    def get_custom_field_by_tapd_name(self):
        db_session = instance_db_session()
        result = db_session.execute(
            text(self.__query_tapd_custom_field_sql),
            {'tapd_name': self.__tapd_name, 'module_name': self.__module_name, 'workspace_id': self.__workspace_id}
        ).scalar()
        db_session.close()
        return result


class BizStoryDao:

    @classmethod
    def get_dev_leaf_story_id_for_dev_submit(cls, tapd_story_id_list):
        if not tapd_story_id_list:
            return []
        tapd_story_id_list = list(set(tapd_story_id_list))
        sql = '''
                        SELECT vv.tapd_entry_id_1, vv.tapd_entry_id_leaf, deds.id as dev_story_id FROM dev_effic_dev_story deds 
                            INNER JOIN (
                            SELECT
                            DISTINCT
                            tapd_entry_id_1, 
                            CASE WHEN tapd_entry_id_6 IS NOT NULL THEN tapd_entry_id_6 ELSE
                            (
                            -- 层1*****
                            CASE WHEN tapd_entry_id_5 IS NOT NULL THEN tapd_entry_id_5 ELSE
                            (
                            -- 层2*****
                            CASE WHEN tapd_entry_id_4 IS NOT NULL THEN tapd_entry_id_4 ELSE
                            (
                            -- 层3*****
                            CASE WHEN tapd_entry_id_3 IS NOT NULL THEN tapd_entry_id_3 ELSE
                            (
                            -- 层4*****
                            CASE WHEN tapd_entry_id_2 IS NOT NULL THEN tapd_entry_id_2 ELSE
                            (
                            tapd_entry_id_1
                            ) END
                            -- *****层4
                            ) END
                            -- *****层3
                            ) END
                            -- *****层2
                            ) END 
                            -- *****层1
                            ) END 
                            AS tapd_entry_id_leaf
                            FROM 
                            (
                            SELECT
                                    t_ds_1.tapd_entry_id AS tapd_entry_id_1, t_ds_1.tapd_story_name AS tapd_story_name_1, t_ds_1.tapd_story_size AS tapd_story_size_1, t_ds_1.tapd_story_owner AS tapd_story_owner_1
                                    , t_ds_2.tapd_entry_id AS tapd_entry_id_2, t_ds_2.tapd_story_name AS tapd_story_name_2, t_ds_2.tapd_story_size AS tapd_story_size_2, t_ds_2.tapd_story_owner AS tapd_story_owner_2
                                    , t_ds_3.tapd_entry_id AS tapd_entry_id_3, t_ds_3.tapd_story_name AS tapd_story_name_3, t_ds_3.tapd_story_size AS tapd_story_size_3, t_ds_3.tapd_story_owner AS tapd_story_owner_3
                                    , t_ds_4.tapd_entry_id AS tapd_entry_id_4, t_ds_4.tapd_story_name AS tapd_story_name_4, t_ds_4.tapd_story_size AS tapd_story_size_4, t_ds_4.tapd_story_owner AS tapd_story_owner_4
                                    , t_ds_5.tapd_entry_id AS tapd_entry_id_5, t_ds_5.tapd_story_name AS tapd_story_name_5, t_ds_5.tapd_story_size AS tapd_story_size_5, t_ds_5.tapd_story_owner AS tapd_story_owner_5
                                    , t_ds_6.tapd_entry_id AS tapd_entry_id_6, t_ds_6.tapd_story_name AS tapd_story_name_6, t_ds_6.tapd_story_size AS tapd_story_size_6, t_ds_6.tapd_story_owner AS tapd_story_owner_6, t_ds_6.tapd_story_children_id
                            FROM
                                    tapd_entry_story t_ds_1
                            LEFT OUTER JOIN
                                    tapd_entry_story t_ds_2
                            ON t_ds_2.tapd_story_parent_id = t_ds_1.tapd_entry_id
                            LEFT OUTER JOIN
                                    tapd_entry_story t_ds_3
                            ON t_ds_3.tapd_story_parent_id = t_ds_2.tapd_entry_id
                            LEFT OUTER JOIN
                                    tapd_entry_story t_ds_4
                            ON t_ds_4.tapd_story_parent_id = t_ds_3.tapd_entry_id
                            LEFT OUTER JOIN
                                    tapd_entry_story t_ds_5
                            ON t_ds_5.tapd_story_parent_id = t_ds_4.tapd_entry_id
                            LEFT OUTER JOIN
                                    tapd_entry_story t_ds_6
                            ON t_ds_6.tapd_story_parent_id = t_ds_5.tapd_entry_id
                            WHERE t_ds_1.tapd_entry_id IN ('{}')
                            ) d
                            ) vv ON vv.tapd_entry_id_leaf = deds.tapd_story_id
                    '''.format("', '".join(tapd_story_id_list))

        cursor = connection.cursor()
        cursor.execute(sql)
        dev_story_id_list = []
        for row in cursor.fetchall():
            dev_story_id_list.append(row[2])

        return list(set(dev_story_id_list))