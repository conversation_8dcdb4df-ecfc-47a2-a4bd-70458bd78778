import datetime

from django.db import transaction

from dev_effective.model.models import DevEfficTestDevSubmit, DevEfficTestDevSubmitBindStory, DevEfficDevStory
from dev_effective.dao.dev_effective_dao import BizStoryDao
from dev_effective.utils.biz_utils import TapdUtils
from mantis.settings import logger as log, TEST_REPORT, TAPD
from tapd_gateway.from_tapd.model.models import TapdEntryTestPlanBindStory
from tapd_gateway.from_tapd.service.tapd_entry_story_ser import TapdEntityStorySer
from tapd_gateway.from_tapd.service.tapd_entry_test_plan_ser import TapdEntityTestPlanSer
from tapd_gateway.from_tapd.service.tapd_req_srv import TapdStorySrv


class BizTestPlanSer:

    def sync_test_plan_data(self):
        test_plan_srv = TapdEntityTestPlanSer()
        dev_testing_list = test_plan_srv.get_biz_dev_testing()
        log.info('研发端测试计划创建需求数据成功，共获取{}条数据'.format(len(dev_testing_list)))
        ins_rows = self.update_or_create_test_plan(dev_testing_list)
        log.info('研发端测试计划创建需求成功，成功创建{}条数据'.format(ins_rows))
        return ins_rows

    def update_or_create_test_plan(self, test_plan_list, project_id=None):
        test_plan_map = {}
        ins_rows = 0
        if not test_plan_list:
            return ins_rows
        delete_test_plan_list = []
        for test_plan in test_plan_list:
            if test_plan.get('entry_status') and test_plan.get('entry_status') == 3:
                delete_test_plan_list.append(test_plan.get('tapd_entry_id'))
            else:
                test_plan_map[test_plan.get('tapd_entry_id')] = test_plan
        test_dev_submit_list = DevEfficTestDevSubmit.objects.filter(tapd_test_plan_id__in=test_plan_map.keys())
        db_test_plan_id_map = {test_dev_submit.tapd_test_plan_id: test_dev_submit.tapd_story_id for test_dev_submit in
                               test_dev_submit_list}
        cur_time = datetime.datetime.now()
        for k, v in test_plan_map.items():
            k = int(k)
            test_plan_id = v.get('tapd_entry_id')
            workspace_id = v.get('tapd_workspace_id')
            self.update_dev_submit(cur_time, project_id, test_plan_id, v)
            tapd_story_id = None
            if k in db_test_plan_id_map.keys():
                tapd_story_id = db_test_plan_id_map.get(k)
            if not tapd_story_id:
                ins_rows += 1
                param_json = {
                    "workspace_id": TEST_REPORT.get("workspace_id"),
                    "workitem_type_id": TEST_REPORT.get("story_workitem_type_id"),
                    "name": v.get('name'),
                    "description": v.get('tapd_test_plan_description'),
                    "owner": v.get('tapd_test_plan_owner'),
                    "developer": v.get('tapd_test_plan_creator'),
                    "custom_field_two": v.get('team'),
                    "custom_field_three": v.get('squad'),
                    "custom_field_20": v.get('tapd_test_plan_modified'),
                    "custom_field_22": test_plan_id,
                    "custom_field_23": 'https://www.tapd.cn/' + str(
                        workspace_id) + "/sparrow/test_plan/view/" + str(
                        test_plan_id)
                }

                story_ser = TapdStorySrv()
                log.info('开始创建故事，请求参数{}'.format(param_json))
                story_result = story_ser.create_story_by_workspace_id(workspace_id, param_json)
                try:
                    if story_result:
                        tapd_story_id = story_result.get('Story').get('id')
                        DevEfficTestDevSubmit.objects.update_or_create(
                            defaults={"update_user": 'howbuyscm', "update_time": cur_time,
                                      "tapd_story_id": tapd_story_id},
                            tapd_test_plan_id=test_plan_id
                        )
                        story_ser = TapdStorySrv()
                        story_ser.write_stories_to_db([story_result])
                except Exception as e:
                    log.error('创建故事回填数据失败--------->workspace_id:{}, 测试计划ID：{}'.format(workspace_id,
                                                                                                    test_plan_id))
                    log.error(e)
            log.info('开始处理关系表')
            try:
                dev_submit = DevEfficTestDevSubmit.objects.filter(tapd_test_plan_id=test_plan_id)
                dev_submit_id = dev_submit[0].id
                with transaction.atomic():
                    DevEfficTestDevSubmitBindStory.objects.filter(dev_submit_id=dev_submit_id).delete()
                    bind_story = TapdEntryTestPlanBindStory.objects.filter(tapd_test_plan_id=test_plan_id)
                    dev_story_ids = []
                    if bind_story and bind_story[0].relative_dev_story_ids:
                        relative_dev_story_ids = bind_story[0].relative_dev_story_ids.split(',')
                        dev_story_ids = BizStoryDao.get_dev_leaf_story_id_for_dev_submit(relative_dev_story_ids)
                    #     if bind_story[0].relative_dev_story_ids:
                    #         for storyId in bind_story[0].relative_dev_story_ids.split(','):
                    #             recursion_get_story_by_story_id(storyId, story_ids)
                    # story_id_list = list(set(story_ids))
                    if dev_story_ids:
                        ins_bind_list = []
                        for story_id in dev_story_ids:
                            bind = DevEfficTestDevSubmitBindStory()
                            bind.dev_submit_id = dev_submit_id
                            bind.dev_story_id = story_id
                            ins_bind_list.append(bind)
                        DevEfficTestDevSubmitBindStory.objects.bulk_create(ins_bind_list)
            except Exception as e:
                log.error(
                    '写入关系表数据失败--------->workspace_id:{}, 测试计划ID：{}'.format(workspace_id, test_plan_id))
                log.error(e)

        if delete_test_plan_list:
            DevEfficTestDevSubmit.objects.filter(tapd_test_plan_id__in=delete_test_plan_list).delete()
        log.info('写入、删除 发布评审表 分别是{}，{}'.format(ins_rows, len(delete_test_plan_list)))
        return ins_rows

    def update_dev_submit(self, cur_time, project_id, test_plan_id, v):
        if project_id:
            DevEfficTestDevSubmit.objects.update_or_create(
                defaults={"create_user": 'howbuyscm',
                          "create_time": cur_time,
                          "name": v.get('name'),
                          "description": v.get('tapd_test_plan_description'),
                          "owner": v.get('tapd_test_plan_owner'),
                          "creator": v.get('tapd_test_plan_creator'),
                          "tapd_test_plan_status": 1,
                          "created": TapdUtils.date_to_date_str(v.get('tapd_test_plan_created')),
                          "team": v.get('tapd_test_plan_owner'),
                          "project_id": project_id,
                          "data_source": "TAPD",
                          "status": v.get('tapd_test_plan_status')},
                tapd_test_plan_id=test_plan_id
            )
        else:
            DevEfficTestDevSubmit.objects.update_or_create(
                defaults={"create_user": 'howbuyscm',
                          "create_time": cur_time,
                          "name": v.get('name'),
                          "description": v.get('tapd_test_plan_description'),
                          "owner": v.get('tapd_test_plan_owner'),
                          "creator": v.get('tapd_test_plan_creator'),
                          "tapd_test_plan_status": 1,
                          "created": TapdUtils.date_to_date_str(v.get('tapd_test_plan_created')),
                          "team": v.get('tapd_test_plan_owner'),
                          "data_source": "TAPD",
                          "status": v.get('tapd_test_plan_status')},
                tapd_test_plan_id=test_plan_id
            )


"""
递归查询故事及其子需求
"""


def recursion_get_story_by_story_id(story_id, story_ids):
    if story_id:
        story_ser = TapdEntityStorySer()
        story_obj = story_ser.get_story_by_story_id(story_id)
        if story_obj:
            story_children_id = story_obj[0].get('tapd_story_children_id')
            if story_children_id:
                if '|' != story_children_id:
                    story_children_ids = story_children_id.split('|')
                    children_ids = list(set(story_children_ids))
                    for children_id in children_ids:
                        if children_id:
                            recursion_get_story_by_story_id(children_id, story_ids)
                else:
                    story_ids.append(story_id)

    return story_ids
