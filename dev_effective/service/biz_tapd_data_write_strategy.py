"""
抽象基类，定义了一个抽象方法
"""
import datetime
import enum
import json
from abc import ABC, abstractmethod

from django.db.models import Q

from dev_effective.model.models import BizEntrySyncMsgInfo
from dev_effective.service.biz_bug_service import BizBugSer
from dev_effective.service.biz_iteration_service import OperationFailModuleEnum
from dev_effective.service.biz_launch_form_service import BizLaunchFormSer
from dev_effective.service.biz_story_service import BizStorySer
from dev_effective.service.biz_task_service import BizTaskSer
from dev_effective.dao.dev_effective_dao import TapdWorkspaceModuleConfDao
from dev_effective.service.biz_timesheet_service import BizTimeSheetSer
from mantis.settings import TAPD
from mantis.settings import logger as log
from tapd_gateway.from_tapd.api.views import BizType
from tapd_gateway.from_tapd.dao.tapd_req_dao import get_delete_minute
from tapd_gateway.from_tapd.model.models import TapdEntryWorkspace, TapdEntryStory, TapdEntryTask, TapdEntryTestPlan, \
    TapdEntryTestPlanBindStory, TapdEntryBug, TapdEntryIteration, TapdEntryLaunchForm
from tapd_gateway.from_tapd.service.tapd_sync_data_success_log import TapdEntryDataSyncLogSer, TapdFunctionLogType
from test_report.tapd.tapd_bugs import TAPDBugs


@enum.unique
class NotifyStatusEnum(enum.Enum):
    NOT_NOTIFY = (0, "未通知")
    HAS_NOTIFY = (1, "已通知")

    def __init__(self, status, module_desc):
        self.status = status
        self.module_desc = module_desc


class BizEntryDeliverMsgInfoSer:
    def save_msg(self, source, title, content, creator, owner):
        ins_list = []
        sync_msg = BizEntrySyncMsgInfo()
        sync_msg.title = title
        sync_msg.content = content
        sync_msg.source = source
        sync_msg.status = NotifyStatusEnum.NOT_NOTIFY.status
        sync_msg.create_time = datetime.datetime.now()
        sync_msg.creator = creator
        sync_msg.owner = owner
        ins_list.append(sync_msg)
        try:
            BizEntrySyncMsgInfo.objects.bulk_create(ins_list)
        except Exception as e:
            log.info(str(e))


class TapdCheckStrategyExecutor(ABC):

    @abstractmethod
    def deliver_msg(self, workspace_id, modified):
        pass


class TapdCheckStrategy:
    def __init__(self, strategy):
        self.strategy = strategy

    def set_strategy(self, strategy):
        self.strategy = strategy

    def execute_strategy(self, config_list, workspace_list):
        return self.strategy.deliver_msg(config_list, workspace_list)


class RepeatChecking(TapdCheckStrategy):
    def __init__(self):
        pass

    def deliver_msg(self, repeat_check_list, workspace_list):
        log.info('重复性检查')
        dev_workspace_id_list = []
        test_workspace_id_list = []
        workspace_type_map = {}
        for workspace in workspace_list:
            workspace_id = workspace.get('tapd_workspace_id')
            workspace_type = workspace.get('workspace_type')
            if workspace_type == WorkspaceType.TEST.status:
                test_workspace_id_list.append(workspace_id)
            elif workspace_type == WorkspaceType.DEV.status:
                dev_workspace_id_list.append(workspace_id)
            else:
                log.error('重复性检验,暂不支持的workspace_type：{}'.format(workspace_type))
        workspace_type_map[WorkspaceType.TEST.status] = test_workspace_id_list
        workspace_type_map[WorkspaceType.DEV.status] = dev_workspace_id_list
        if repeat_check_list:
            for repeat in repeat_check_list:
                workspace_type = repeat.get('workspace_type')
                modules = repeat.get('modules')
                workspace_id_list = workspace_type_map.get(workspace_type)
                if workspace_type == WorkspaceType.TEST.status:
                    self.test_repeat_check(modules, workspace_id_list)
                elif workspace_type == WorkspaceType.DEV.status:
                    self.dev_repeat_check(modules, workspace_id_list)

    def dev_repeat_check(self, modules, workspace_id_list):
        if modules == BizType.TEST_PLAN.value:
            for workspace_id in workspace_id_list:
                test_plan_info = TapdEntryTestPlan.objects.filter(tapd_workspace_id=workspace_id).exclude(
                    entry_status=3).values(
                    "tapd_test_plan_name", "tapd_entry_id", "tapd_test_plan_creator", "tapd_test_plan_owner")
                for test_plan in test_plan_info:
                    for relative_dev_story in TapdEntryTestPlanBindStory.objects.filter(
                            tapd_test_plan_id=test_plan.get("tapd_entry_id")).values("relative_dev_story_ids"):
                        relative_dev_story_list = relative_dev_story.get("relative_dev_story_ids").split(
                            ",") if relative_dev_story.get("relative_dev_story_ids") else []
                        for dev_story_id_order_1 in relative_dev_story_list:
                            for dev_story_id_order_2 in relative_dev_story_list:
                                if dev_story_id_order_1 == dev_story_id_order_2:
                                    continue
                                if TapdEntryStory.objects.filter(tapd_entry_id=dev_story_id_order_1,
                                                                 tapd_story_parent_id=dev_story_id_order_2):
                                    sync_msg = BizEntryDeliverMsgInfoSer()
                                    sync_msg.save_msg(
                                        OperationFailModuleEnum.TEST_PLAN_RELATIVE_DUPLICATION_STORY.module_name,
                                        test_plan.get("tapd_test_plan_name"),
                                        '测试计划关联需求重复了（父子需求）',
                                        test_plan.get('tapd_test_plan_creator'),
                                        test_plan.get('tapd_test_plan_owner'))
                                    # 测试计划检测到一次就跳出，不需要重复检测
                                    break
                            break
                        break

    def test_repeat_check(self, modules, workspace_id_list):
        if modules == BizType.STORY.value:
            for workspace_id in workspace_id_list:
                story_name_list = TapdEntryStory.objects.filter(tapd_workspace_id=workspace_id,
                                                                tapd_story_workitem_type_id=TAPD.get(
                                                                    "workitem_type_id")).exclude(
                    entry_status=3).values(
                    'tapd_story_name', 'tapd_story_creator', 'tapd_story_owner')
                names = set()
                for story in story_name_list:
                    name_tuple = story.get('tapd_story_name')
                    if name_tuple in names:
                        sync_msg = BizEntryDeliverMsgInfoSer()
                        sync_msg.save_msg(OperationFailModuleEnum.STORY_DUPLICATION_NAME.module_name,
                                          name_tuple,
                                          '需求名称重名', story.get('tapd_story_creator'),
                                          story.get('tapd_story_owner'))
                    else:
                        names.add(name_tuple)
        elif modules == BizType.LAUNCH_FORM.value:
            for workspace_id in workspace_id_list:
                launch_form_list = TapdEntryLaunchForm.objects.filter(tapd_workspace_id=workspace_id).exclude(
                    entry_status=3).values(
                    'tapd_entry_id', 'tapd_launch_form_title', 'tapd_launch_form_creator')
                names = set()
                for launch_form in launch_form_list:
                    name_tuple = launch_form.get('tapd_launch_form_title')
                    if name_tuple in names:
                        sync_msg = BizEntryDeliverMsgInfoSer()
                        sync_msg.save_msg(OperationFailModuleEnum.LAUNCH_FORM_DUPLICATION_NAME.module_name,
                                          name_tuple,
                                          '发布评审名称重名', launch_form.get('tapd_launch_form_creator'),
                                          None)
                    else:
                        names.add(name_tuple)


class NotNullChecking(TapdCheckStrategy):
    def __init__(self):
        pass

    def deliver_msg(self, not_null_list, workspace_list):
        log.info('非空检验')
        dev_workspace_id_list = []
        test_workspace_id_list = []
        workspace_type_map = {}
        for workspace in workspace_list:
            workspace_id = workspace.get('tapd_workspace_id')
            workspace_type = workspace.get('workspace_type')
            if workspace_type == WorkspaceType.TEST.status:
                test_workspace_id_list.append(workspace_id)
            elif workspace_type == WorkspaceType.DEV.status:
                dev_workspace_id_list.append(workspace_id)
            else:
                log.error('非空检验,暂不支持的workspace_type：{}'.format(workspace_type))
        workspace_type_map[WorkspaceType.TEST.status] = test_workspace_id_list
        workspace_type_map[WorkspaceType.DEV.status] = dev_workspace_id_list
        if not_null_list:
            for not_null in not_null_list:
                workspace_type = not_null.get('workspace_type')
                modules = not_null.get('modules')
                workspace_id_list = workspace_type_map.get(workspace_type)
                if workspace_type == WorkspaceType.TEST.status and modules == BizType.STORY.value:
                    for workspace_id in workspace_id_list:
                        self.__story_not_null_check(workspace_id)
                elif modules == BizType.TASK.value:
                    for workspace_id in workspace_id_list:
                        self.__task_not_null_check(workspace_id)

    def __story_not_null_check(self, workspace_id):

        story_list = TapdEntryStory.objects.filter(tapd_workspace_id=workspace_id,
                                                   tapd_story_workitem_type_id=TAPD.get(
                                                       "workitem_type_id")).exclude(
            tapd_story_status='resolved').exclude(
            entry_status=3)

        self.process_story_info(story_list)

    def process_story_info(self, story_list):
        # max_threads = 5
        # semaphore = threading.Semaphore(max_threads)
        # for story in story_list:
        #     threads = []
        #     with semaphore:
        #         thread = threading.Thread(target=self.check_story_info, args=(story,))
        #         threads.append(thread)
        #         thread.start()
        # for thread in threads:
        #     thread.join()
        for story in story_list:
            self.check_story_info(story)

    def check_story_info(self, story):
        content = ''
        is_add = False
        story = story.__dict__
        if not story.get('tapd_story_owner'):
            content += '需求【处理人】为空;'
            is_add = True
        if not story.get('tapd_story_begin'):
            content += '需求【预计开始时间】为空;'
            is_add = True
        if not story.get('tapd_story_due'):
            content += '需求【预计结束时间】为空;'
            is_add = True
        if not story.get('tapd_story_effort'):
            content += '需求【预计工时】为空;'
            is_add = True
        if not self.check_task_division_ratio_is_null(story):
            content += '需求【任务分工比例】为空;'
            is_add = True
        if is_add:
            sync_msg = BizEntryDeliverMsgInfoSer()
            sync_msg.save_msg(OperationFailModuleEnum.STORY_INFO_INCOMPLETE.module_name,
                              story.get('tapd_story_name'),
                              content,
                              story.get('tapd_story_creator'),
                              story.get('tapd_story_owner'))

    def check_task_division_ratio_is_null(self, story):
        if story.get('tapd_story_iteration_id'):
            task_owner_list = TapdEntryTask.objects.filter(
                tapd_task_iteration_id=story.get('tapd_story_iteration_id')).exclude(
                entry_status=3).values('tapd_task_owner').distinct()
            name_list = []
            for task_owner in task_owner_list:
                name_list.append(task_owner.get('tapd_task_owner'))
            if len(set(name_list)) > 1:
                dao = TapdWorkspaceModuleConfDao('任务分工比例', story.get('tapd_workspace_id'), 'story')
                task_division_ratio_custom_field = dao.get_custom_field_by_tapd_name()
                if not story.get(task_division_ratio_custom_field):
                    return False
        return True

    def __task_not_null_check(self, workspace_id):
        start_datetime = '2024-03-01 00:00:00'
        task_list = (TapdEntryTask.objects.filter(tapd_workspace_id=workspace_id).
                     exclude(Q(tapd_task_owner__isnull=True) | Q(tapd_task_owner='')).exclude(
            entry_status=3).filter(tapd_task_modified__gt=start_datetime))
        self.process_tasks(task_list, workspace_id)

    def process_tasks(self, task_list, workspace_id):
        # max_threads = 5
        # semaphore = threading.Semaphore(max_threads)
        # for task in task_list:
        #     threads = []
        #
        #     with semaphore:
        #         thread = threading.Thread(target=self.process_task, args=(task, workspace_id,))
        #         threads.append(thread)
        #         thread.start()
        #
        # for thread in threads:
        #     thread.join()
        for task in task_list:
            self.process_task(task, workspace_id)

    def process_task(self, task, workspace_id):
        content = ''
        is_add = False
        dao = TapdWorkspaceModuleConfDao('任务类型', workspace_id, 'task')
        task_type = dao.get_custom_field_by_tapd_name()
        task = task.__dict__
        if not task.get(task_type):
            content += "【任务类型】为空;"
            is_add = True
        if task.get("tapd_task_status") == 'done' and not task.get("tapd_task_effort_completed"):
            content += "已完成的任务，工时为空;"
            is_add = True
        if is_add:
            sync_msg = BizEntryDeliverMsgInfoSer()
            sync_msg.save_msg(OperationFailModuleEnum.TASK_INFO_INCOMPLETE.module_name,
                              task.get('tapd_task_name'),
                              content,
                              task.get('tapd_task_creator'),
                              task.get('tapd_task_owner'))


class StoryLevelChecking(TapdCheckStrategy):
    def __init__(self):
        pass

    def deliver_msg(self, story_level_list, workspace_list):
        log.info('测试【测试计划】中需求层级检验')
        dev_workspace_id_list = []
        test_workspace_id_list = []
        workspace_type_map = {}
        for workspace in workspace_list:
            workspace_id = workspace.get('tapd_workspace_id')
            workspace_type = workspace.get('workspace_type')
            if workspace_type == WorkspaceType.TEST.status:
                test_workspace_id_list.append(workspace_id)
            elif workspace_type == WorkspaceType.DEV.status:
                dev_workspace_id_list.append(workspace_id)
            else:
                log.error('需求层级检验,暂不支持的workspace_type：{}'.format(workspace_type))
        workspace_type_map[WorkspaceType.TEST.status] = test_workspace_id_list
        workspace_type_map[WorkspaceType.DEV.status] = dev_workspace_id_list
        if story_level_list:
            for story_level in story_level_list:
                workspace_type = story_level.get('workspace_type')
                modules = story_level.get('modules')
                workspace_id_list = workspace_type_map.get(workspace_type)
                if workspace_type == WorkspaceType.TEST.status and modules == BizType.STORY.value:
                    for workspace_id in workspace_id_list:
                        story_list = TapdEntryStory.objects.filter(tapd_workspace_id=workspace_id,
                                                                   tapd_story_workitem_type_id=TAPD.get(
                                                                       "workitem_type_id")).exclude(
                            tapd_story_status='resolved').exclude(
                            entry_status=3).values(
                            'tapd_story_name', 'tapd_story_owner', 'tapd_story_creator', 'tapd_story_children_id')
                        self.__story_level_check(story_list)

    def __story_level_check(self, story_list):
        for story in story_list:
            if story.get("tapd_story_children_id") != "|":
                sync_msg = BizEntryDeliverMsgInfoSer()
                sync_msg.save_msg(OperationFailModuleEnum.STORY_HAVE_CHILDREN.module_name,
                                  story.get('tapd_story_name'),
                                  OperationFailModuleEnum.STORY_HAVE_CHILDREN.module_desc,
                                  story.get('tapd_task_creator'),
                                  story.get('tapd_task_owner'))


class AppBindChecking(TapdCheckStrategy):
    def __init__(self):
        pass

    def deliver_msg(self, app_bind_list, workspace_list):
        log.info('缺陷绑定的应用是否存在研发迭代中检测')
        dev_workspace_id_list = []
        test_workspace_id_list = []
        workspace_type_map = {}
        for workspace in workspace_list:
            workspace_id = workspace.get('tapd_workspace_id')
            workspace_type = workspace.get('workspace_type')
            if workspace_type == WorkspaceType.TEST.status:
                test_workspace_id_list.append(workspace_id)
            elif workspace_type == WorkspaceType.DEV.status:
                dev_workspace_id_list.append(workspace_id)
            else:
                log.error('缺陷绑定的应用是否存在研发迭代中检测,暂不支持的workspace_type：{}'.format(workspace_type))
        workspace_type_map[WorkspaceType.TEST.status] = test_workspace_id_list
        workspace_type_map[WorkspaceType.DEV.status] = dev_workspace_id_list
        if app_bind_list:
            for app_bind in app_bind_list:
                workspace_type = app_bind.get('workspace_type')
                modules = app_bind.get('modules')
                workspace_id_list = workspace_type_map.get(workspace_type)
                if workspace_type == WorkspaceType.TEST.status and modules == BizType.BUG.value:
                    for workspace_id in workspace_id_list:
                        self.__app_bind_check(workspace_id)

    def __app_bind_check(self, workspace_id):
        tapd_bug_iteration_id_list = TapdEntryBug.objects.filter(tapd_workspace_id=workspace_id,
                                                                 tapd_bug_status__in=['resolved', 'closed']).exclude(
            entry_status=3).values(
            'tapd_bug_iteration_id').distinct()
        for iteration_info in tapd_bug_iteration_id_list:
            iteration_id = iteration_info.get("tapd_bug_iteration_id")
            tb = TAPDBugs([iteration_id], workspace_id)
            check_result, bug_list = tb.check_bug_iter_app_info(iteration_id)
            tei = TapdEntryIteration.objects.filter(tapd_entry_id=iteration_id).values('tapd_iteration_creator',
                                                                                       'tapd_iteration_name')
            if not check_result:
                sync_msg = BizEntryDeliverMsgInfoSer()
                sync_msg.save_msg(OperationFailModuleEnum.BUG_APP_SELECT_WRONG.module_name,
                                  tei[0].get('tapd_iteration_name'),
                                  OperationFailModuleEnum.BUG_APP_SELECT_WRONG.module_desc + "，缺陷id列表：{}".format(
                                      bug_list),
                                  tei[0].get('tapd_iteration_creator'),
                                  tei[0].get('tapd_iteration_creator'))


@enum.unique
class WorkspaceType(enum.Enum):
    PRO_BUG = (1, "产线bug")
    PLATFORM = (2, "平台")
    TEST = (3, "test")
    DEV = (4, "dev")
    PRD = (5, "product")

    def __init__(self, status, module_desc):
        self.status = status
        self.module_desc = module_desc


@enum.unique
class TapdCheckTypeEnum(enum.Enum):
    NOT_NULL = ("notNull", "非空检验")
    REPEAT_CHECK = ("repeatCheck", "重复性检验")
    STORY_LEVEL = ("storyLevel", "研发【测试计划】中需求层级检验")
    APP_BIND = ("appBind", "缺陷绑定的应用是否在迭代中")

    def __init__(self, module, module_desc):
        self.module = module
        self.module_desc = module_desc


class CheckTapdDataSer:

    def get_json_data(self):
        tapd_check_content = TAPD['tapd_check_content']
        # tapd_check_content = '[{"checkType":"repeatCheck","workspace_type":3,"modules":"launch_form","status":"open"}]'
        json_array = json.loads(tapd_check_content)
        return json_array

    def start_check(self):
        workspace_type_list = []
        check_content_map = {}
        not_null_list = []
        repeat_check_list = []
        story_level_list = []
        app_bind_list = []
        try:
            json_array = self.get_json_data()
            for json in json_array:
                workspace_type_list.append(json.get('workspace_type'))
                checkType = json.get('checkType')
                status = json.get('status')
                if status == 'open':
                    if TapdCheckTypeEnum.NOT_NULL.module == checkType:
                        not_null_list.append(json)
                    elif TapdCheckTypeEnum.REPEAT_CHECK.module == checkType:
                        repeat_check_list.append(json)
                    elif TapdCheckTypeEnum.STORY_LEVEL.module == checkType:
                        story_level_list.append(json)
                    elif TapdCheckTypeEnum.APP_BIND.module == checkType:
                        app_bind_list.append(json)
                    else:
                        log.error("不支持的检查类型")
        except Exception as err:
            log.error(err)
            raise Exception("获取tapd检查配置失败")
        check_content_map[TapdCheckTypeEnum.NOT_NULL.module] = not_null_list
        check_content_map[TapdCheckTypeEnum.REPEAT_CHECK.module] = repeat_check_list
        check_content_map[TapdCheckTypeEnum.STORY_LEVEL.module] = story_level_list
        check_content_map[TapdCheckTypeEnum.APP_BIND.module] = app_bind_list
        workspace_type_list = list(set(workspace_type_list))
        workspace_list = TapdEntryWorkspace.objects.filter(workspace_type__in=workspace_type_list).values(
            'tapd_workspace_id', 'tapd_workspace_status', 'workspace_type')
        if workspace_list:
            strategy = RepeatChecking()
            tapd_check_strategy = TapdCheckStrategy(strategy)
            for k, v in check_content_map.items():
                if v:
                    if TapdCheckTypeEnum.NOT_NULL.module == k:
                        strategy = NotNullChecking()
                        tapd_check_strategy.set_strategy(strategy)
                        tapd_check_strategy.execute_strategy(v, workspace_list)
                    elif TapdCheckTypeEnum.REPEAT_CHECK.module == k:
                        strategy = RepeatChecking()
                        tapd_check_strategy.set_strategy(strategy)
                        tapd_check_strategy.execute_strategy(v, workspace_list)
                    # elif TapdCheckTypeEnum.STORY_LEVEL.module == k:
                    #     strategy = StoryLevelChecking()
                    #     tapd_check_strategy.set_strategy(strategy)
                    #     tapd_check_strategy.execute_strategy(v, workspace_list)
                    elif TapdCheckTypeEnum.APP_BIND.module == k:
                        strategy = AppBindChecking()
                        tapd_check_strategy.set_strategy(strategy)
                        tapd_check_strategy.execute_strategy(v, workspace_list)


class StoreEntryFromGatewayStrategy(ABC):

    @abstractmethod
    def sync_data_from_gateway(self):
        pass


class TapdDataSyncToBizSer:
    def __init__(self, env_type, entry_type):
        self.entry_type = entry_type
        self.env_type = env_type

    def sync_data_to_biz(self):
        sync_strategy = None
        if self.entry_type == BizType.BUG.value:
            sync_strategy = TestBugSyncStrategy()
        elif self.entry_type == BizType.TASK.value and self.env_type == WorkspaceType.TEST.module_desc:
            sync_strategy = TestTaskSyncStrategy()
        elif self.entry_type == BizType.TASK.value and self.env_type == WorkspaceType.DEV.module_desc:
            sync_strategy = DevTaskSyncStrategy()
        elif self.entry_type == BizType.LAUNCH_FORM.value:
            sync_strategy = TestLaunchFormSyncStrategy()
        elif self.entry_type == BizType.STORY.value and self.env_type == WorkspaceType.DEV.module_desc:
            sync_strategy = DevStorySyncStrategy()
        elif self.entry_type == BizType.STORY.value and self.env_type == WorkspaceType.TEST.module_desc:
            # 研发需求的变化和测试计划的变化都会触发提测单的etl
            sync_strategy = TestStoryForSubmitSyncStrategy()
        elif self.entry_type == BizType.TIME_SHEET.value and self.env_type == WorkspaceType.DEV.module_desc:
            sync_strategy = DevTimeSheetSyncStrategy()
        elif self.entry_type == BizType.TIME_SHEET.value and self.env_type == WorkspaceType.TEST.module_desc:
            sync_strategy = TestTimeSheetSyncStrategy()
        else:
            raise Exception("不支持的实体类型,entry_type:{}".format(self.entry_type))
        context = BizDataSyncStrategyContext(sync_strategy)
        context.execute_strategy()


class BizDataSyncStrategyContext:

    def __init__(self, strategy):
        self.strategy = strategy

    def execute_strategy(self):
        return self.strategy.sync_data_from_gateway()


class TestBugSyncStrategy(StoreEntryFromGatewayStrategy):

    def sync_data_from_gateway(self):

        data_sync_ser = TapdEntryDataSyncLogSer()
        workspace_type = WorkspaceType.TEST.status
        current_time = datetime.datetime.now()
        modified = data_sync_ser.get_modified(workspace_type, BizType.BUG.value,
                                              TapdFunctionLogType.DATA_TO_BIZ.value, current_time)
        log.info('sync test bug modified'.format(modified))
        executor_result = True
        try:
            bug_ser = BizBugSer()
            ins_rows = bug_ser.sync_bug_to_biz(workspace_type, modified)
            log.info("同步bug业务数据成功，共同步{}条数据".format(ins_rows))
        except Exception as e:
            log.error("同步bug业务数据失败,modified:{}".format(modified))
            log.error(e)
            executor_result = False
            raise Exception(str(e))
        finally:
            if executor_result:
                data_sync_ser.save_success_log(BizType.BUG.value, TapdFunctionLogType.DATA_TO_BIZ.value,
                                               workspace_type, current_time)


class TestTaskSyncStrategy(StoreEntryFromGatewayStrategy):

    def sync_data_from_gateway(self):
        data_sync_ser = TapdEntryDataSyncLogSer()
        workspace_type = WorkspaceType.TEST.status
        current_time = datetime.datetime.now()
        modified = data_sync_ser.get_modified(workspace_type, BizType.TASK.value,
                                              TapdFunctionLogType.DATA_TO_BIZ.value, current_time)
        log.info('sync test task modified:{}'.format(modified))
        executor_result = True
        try:
            bug_ser = BizTaskSer()
            ins_rows = bug_ser.sync_test_task_to_biz(workspace_type, modified)
            log.info("同步任务业务数据成功，共同步{}条数据".format(ins_rows))
        except Exception as e:
            log.error("同步任务业务数据失败,modified:{}".format(modified))
            log.error(e)
            executor_result = False
            raise Exception(str(e))
        finally:
            if executor_result:
                data_sync_ser.save_success_log(BizType.TASK.value, TapdFunctionLogType.DATA_TO_BIZ.value,
                                               workspace_type, current_time)


class DevTaskSyncStrategy(StoreEntryFromGatewayStrategy):

    def sync_data_from_gateway(self):
        data_sync_ser = TapdEntryDataSyncLogSer()
        workspace_type = WorkspaceType.DEV.status
        current_time = datetime.datetime.now()
        modified = data_sync_ser.get_modified(workspace_type, BizType.TASK.value,
                                              TapdFunctionLogType.DATA_TO_BIZ.value, current_time)
        log.info('sync dev task modified:{}'.format(modified))
        executor_result = True
        try:
            bug_ser = BizTaskSer()
            ins_rows = bug_ser.sync_dev_task_to_biz(workspace_type, modified)
            log.info("同步研发任务业务数据成功，共同步{}条数据".format(ins_rows))
        except Exception as e:
            log.error("同步研发任务业务数据失败,modified:{}".format(modified))
            log.error(e)
            executor_result = False
            raise Exception(str(e))
        finally:
            if executor_result:
                data_sync_ser.save_success_log(BizType.TASK.value, TapdFunctionLogType.DATA_TO_BIZ.value,
                                               workspace_type, current_time)


class DevStorySyncStrategy(StoreEntryFromGatewayStrategy):

    def sync_data_from_gateway(self):
        data_sync_ser = TapdEntryDataSyncLogSer()
        workspace_type = WorkspaceType.DEV.status
        current_time = datetime.datetime.now()
        modified = data_sync_ser.get_modified(workspace_type, BizType.STORY.value,
                                              TapdFunctionLogType.DATA_TO_BIZ.value, current_time)
        log.info('sync dev story modified:{}'.format(modified))
        executor_result = True
        try:
            story_ser = BizStorySer()
            ins_rows = story_ser.sync_dev_story_to_biz(workspace_type, modified)
            log.info("同步研发故事业务数据成功，共同步{}条数据".format(ins_rows))
        except Exception as e:
            log.error("同步研发故事业务数据失败,modified:{}".format(modified))
            log.error(e)
            executor_result = False
            raise Exception(str(e))
        finally:
            if executor_result:
                data_sync_ser.save_success_log(BizType.STORY.value, TapdFunctionLogType.DATA_TO_BIZ.value,
                                               workspace_type, current_time)


class TestLaunchFormSyncStrategy(StoreEntryFromGatewayStrategy):

    def sync_data_from_gateway(self):
        data_sync_ser = TapdEntryDataSyncLogSer()
        workspace_type = WorkspaceType.TEST.status
        current_time = datetime.datetime.now()
        modified = data_sync_ser.get_modified(workspace_type, BizType.LAUNCH_FORM.value,
                                              TapdFunctionLogType.DATA_TO_BIZ.value, current_time)
        log.info('sync test_launch_form modified:{}'.format(modified))
        executor_result = True
        try:
            launch_form_ser = BizLaunchFormSer()
            ins_rows = launch_form_ser.sync_launch_form_to_biz(workspace_type, modified)
            log.info("同步发布评审业务数据成功，共同步{}条数据".format(ins_rows))
        except Exception as e:
            log.error("同步发布评审业务数据失败")
            log.error(e)
            executor_result = False
            raise Exception(str(e))
        finally:
            if executor_result:
                delete_date = get_delete_minute(current_time, 60)
                data_sync_ser.save_success_log(BizType.LAUNCH_FORM.value, TapdFunctionLogType.DATA_TO_BIZ.value,
                                               workspace_type, delete_date)


class TestStoryForSubmitSyncStrategy(StoreEntryFromGatewayStrategy):

    def sync_data_from_gateway(self):
        data_sync_ser = TapdEntryDataSyncLogSer()
        workspace_type = WorkspaceType.TEST.status
        current_time = datetime.datetime.now()
        modified = data_sync_ser.get_modified(workspace_type, BizType.STORY.value,
                                              TapdFunctionLogType.DATA_TO_BIZ.value, current_time)

        log.info('sync test_story_for_submit modified:{}'.format(modified))

        executor_result = True
        try:
            story_ser = BizStorySer()
            ins_rows = story_ser.sync_test_submit_story_to_biz(workspace_type, modified)
            log.info("同步提测单业务数据成功，共同步{}条数据".format(ins_rows))
            ups_rows = story_ser.sync_test_story_to_biz(workspace_type, modified)
            log.info("同步测试需求数据成功，共更新{}条数据".format(ups_rows))
        except Exception as e:
            log.error("同步提测单业务数据失败")
            log.error(e)
            executor_result = False
            raise Exception(str(e))
        finally:
            if executor_result:
                data_sync_ser.save_success_log(BizType.STORY.value, TapdFunctionLogType.DATA_TO_BIZ.value,
                                               workspace_type, current_time)


class DevTimeSheetSyncStrategy(StoreEntryFromGatewayStrategy):
    """研效开发工时同步策略实现。zt@2025-01-07"""
    def sync_data_from_gateway(self):

        data_sync_ser = TapdEntryDataSyncLogSer()
        workspace_type = WorkspaceType.DEV.status
        current_time = datetime.datetime.now()
        modified = data_sync_ser.get_modified(workspace_type,
                                              BizType.TIME_SHEET.value,
                                              TapdFunctionLogType.DATA_TO_BIZ.value,
                                              current_time)
        log.info('sync dev timesheet modified'.format(modified))
        executor_result = True
        try:
            timesheet_ser = BizTimeSheetSer()
            ins_count, upd_count, del_count = timesheet_ser.sync_dev_timesheet_to_biz(workspace_type, modified)
            log.info("同步研效开发工时数据成功，新增：{}，更新：{}，删除：{}。".format(ins_count, upd_count, del_count))
        except Exception as e:
            log.error("同步研效开发工时数据失败,modified:{}".format(modified))
            log.error(e)
            executor_result = False
            raise Exception(str(e))
        finally:
            if executor_result:
                data_sync_ser.save_success_log(BizType.TIME_SHEET.value, TapdFunctionLogType.DATA_TO_BIZ.value,
                                               workspace_type, current_time)


class TestTimeSheetSyncStrategy(StoreEntryFromGatewayStrategy):
    """测试开发工时同步策略实现。zt@2025-01-08"""
    def sync_data_from_gateway(self):

        data_sync_ser = TapdEntryDataSyncLogSer()
        workspace_type = WorkspaceType.TEST.status
        current_time = datetime.datetime.now()
        modified = data_sync_ser.get_modified(workspace_type,
                                              BizType.TIME_SHEET.value,
                                              TapdFunctionLogType.DATA_TO_BIZ.value,
                                              current_time)
        log.info('sync test timesheet modified'.format(modified))
        executor_result = True
        try:
            timesheet_ser = BizTimeSheetSer()
            ins_count, upd_count, del_count = timesheet_ser.sync_test_timesheet_to_biz(workspace_type, modified)
            log.info("同步研效测试工时成功，新增：{}，更新：{}，删除：{}。".format(ins_count, upd_count, del_count))
        except Exception as e:
            log.error("同步研效测试工时失败,modified:{}".format(modified))
            log.error(e)
            executor_result = False
            raise Exception(str(e))
        finally:
            if executor_result:
                data_sync_ser.save_success_log(BizType.TIME_SHEET.value, TapdFunctionLogType.DATA_TO_BIZ.value,
                                               workspace_type, current_time)
