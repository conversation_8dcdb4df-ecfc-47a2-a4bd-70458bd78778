import datetime
import enum
from abc import ABCMeta, abstractmethod

from dev_effective.model.models import DevEfficTestProject
from dev_effective.utils.biz_utils import TapdUtils
from mantis.settings import logger as log
from tapd_gateway.from_tapd.service.tapd_entry_story_ser import TapdEntityStorySer
from tapd_gateway.from_tapd.service.tapd_req_srv import TapdIterationSrv, TapdStorySrv


@enum.unique
class OperationFailModuleEnum(enum.Enum):
    STORY_DUPLICATION_NAME = ("story_duplication_name", "需求重名")
    LAUNCH_FORM_DUPLICATION_NAME = ("launch_form_duplication_name", "发布评审重名")
    STORY_INFO_INCOMPLETE = ("story_info_incomplete", "需求信息不完整")
    TASK_INFO_INCOMPLETE = ("task_info_incomplete", "任务信息不完整")
    TEST_PLAN_RELATIVE_DUPLICATION_STORY = ("test_plan_relative_duplication_story", "测试计划关联的需求重复")
    STORY_HAVE_CHILDREN = ("story_have_children", "测试需求存在层级关系（有子需求）")
    BUG_APP_SELECT_WRONG = ("bug_app_select_wrong", "缺陷的应用与需求中的研发迭代不一致")

    def __init__(self, module_name, module_desc):
        self.module_name = module_name
        self.module_desc = module_desc


# 定义请求类
class BizRequest():
    def __init__(self, workspace_id, params):
        self.workspace_id = workspace_id
        self.params = params


# 定义抽象基类
class Handler(metaclass=ABCMeta):
    @abstractmethod
    def set_next(self, handler):
        pass

    @abstractmethod
    def handle(self, request):
        pass


# 定义具体实现类
class CreateIterationHandler(Handler):
    def set_next(self, handler):
        self.next_handler = handler

    def handle(self, request):
        try:
            tapd_iteration_srv = TapdIterationSrv()
            iteration_result = tapd_iteration_srv.create_iteration(request.workspace_id, request.params)
            # self.next_handler.handle(request)
            return iteration_result
        except Exception as e:
            log.error(e)
            return None


class UpdateStoryHandler(Handler):
    def set_next(self, handler):
        self.next_handler = handler

    def handle(self, request):
        params = request.params
        try:
            tapd_story_srv = TapdStorySrv()
            story_result = tapd_story_srv.update_story_by_workspace_id(request.workspace_id,
                                                                       params)
            # self.next_handler.handle(request)
            return story_result
        except Exception as e:
            log.error(e)
            return None


class BizIterationSer:

    def create_iteration(self, workspace_id):
        tapd_story_srv = TapdEntityStorySer()
        story_list = tapd_story_srv.get_story_by_workspace_id(workspace_id)
        if story_list:
            story_map = {}
            for story in story_list:
                story_map[story.get('tapd_entry_id')] = story
            return self.__save_iteration(story_map)

    def __save_iteration(self, story_map):
        project_list = DevEfficTestProject.objects.filter(tapd_story_id__in=story_map.keys())
        db_project_id_map = {project.tapd_story_id: project.id for project in project_list}

        iteration_handler = CreateIterationHandler()
        story_handler = UpdateStoryHandler()
        iteration_handler.set_next(story_handler)
        ins_list = []
        cur_time = datetime.datetime.now()
        for k, v in story_map.items():
            k = int(k)
            db_id = None
            if k in db_project_id_map.keys():
                db_id = db_project_id_map.get(k)
            if not db_id:
                project_obj = DevEfficTestProject()
                project_obj.tapd_story_id = v.get('tapd_entry_id')
                project_obj.name = v.get('tapd_story_name')
                project_obj.owner = v.get('tapd_story_owner')
                tapd_story_begin = TapdUtils.date_str_to_date(v.get('tapd_story_begin'))
                project_obj.expected_start = tapd_story_begin
                tapd_story_due = TapdUtils.date_str_to_date(v.get('tapd_story_due'))
                project_obj.expected_end = tapd_story_due
                custom_field_10 = TapdUtils.date_str_to_date(v.get('tapd_story_custom_field_10'))
                custom_field_11 = TapdUtils.date_str_to_date(v.get('tapd_story_custom_field_11'))
                project_obj.actual_start = custom_field_10
                project_obj.actual_end = custom_field_11
                project_obj.effort = v.get('tapd_story_effort')
                project_obj.status = v.get('tapd_story_status')
                project_obj.team = v.get('tapd_story_custom_field_two')

                project_obj.delay_days = v.get('tapd_story_custom_field_four')
                project_obj.delay_description = v.get('tapd_story_custom_field_eight')
                project_obj.compatibility_description = "、".join(v.get('tapd_story_custom_field_12')).split(
                    "|")
                project_obj.test_summary = v.get('tapd_story_custom_field_9')
                project_obj.tp_iters = v.get('tapd_story_custom_field_13')
                project_obj.tms_iters = v.get('tapd_story_custom_field_14')
                project_obj.tms_h5_iters = v.get('tapd_story_custom_field_15')
                project_obj.fp_iters = v.get('tapd_story_custom_field_16')
                project_obj.fp_h5_app_iters = v.get('tapd_story_custom_field_17')
                project_obj.crm_iters = v.get('tapd_story_custom_field_18')
                project_obj.dev2test_date = v.get('tapd_story_custom_field_20')
                project_obj.task_division_ratio = v.get('tapd_story_custom_field_24')
                project_obj.business_system = v.get('tapd_story_custom_field_three')
                project_obj.data_source = 'TAPD'
                project_obj.create_user = 'howbuyscm'
                project_obj.update_user = 'howbuyscm'
                project_obj.create_time = cur_time
                project_obj.update_time = cur_time
                team = v.get('tapd_story_custom_field_two')

                tapd_iteration_obj = {'name': v.get('tapd_story_name'), 'workspace_id': v.get('tapd_workspace_id'),
                                      'creator': v.get('tapd_story_creator'),
                                      'startdate': TapdUtils.date_to_date_str(v.get('tapd_story_begin')),
                                      'enddate': TapdUtils.date_to_date_str(v.get('tapd_story_due')), "description": "平台自动创建",
                                      "status": "open",
                                      "custom_field_1": team}
                iteration_request = BizRequest(v.get('tapd_workspace_id'), tapd_iteration_obj)
                iteration_result = iteration_handler.handle(iteration_request)
                if iteration_result:
                    iteration_id = iteration_result.get('Iteration').get('id')
                    update_story_obj = {"iteration_id": iteration_id, "id": v.get('tapd_entry_id'),
                                        'workspace_id': v.get('tapd_workspace_id')}

                    story_request = BizRequest(v.get('tapd_workspace_id'), update_story_obj)
                    story_result = story_handler.handle(story_request)
                    project_obj.tapd_iteration_id = iteration_id
                    try:
                        iteration_ser = TapdIterationSrv()
                        iteration_ser.write_iterations_to_db([iteration_result])
                        story_ser = TapdStorySrv()
                        story_ser.write_stories_to_db([story_result])
                    except Exception as e:
                        log.error('测试故事创建迭代回填数据失败：故事ID{}'.format(project_obj.get('tapd_story_id')))
                        log.error(str(e))
                    ins_list.append(project_obj)
        if ins_list:
            DevEfficTestProject.objects.bulk_create(ins_list)
        return len(ins_list)
