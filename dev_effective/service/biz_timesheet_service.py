import datetime

from django.db import transaction

from dev_effective.model.models import DevEfficDevTaskTimeSheet
from dev_effective.model.models import DevEfficTestTaskTimeSheet
from mantis.settings import logger as log
from tapd_gateway.from_tapd.dao.tapd_req_dao import get_tapd_timesheet_for_test_task, get_tapd_timesheet_for_dev_task


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class BizTimeSheetSer:
    opt_user = "howbuyscm"

    def sync_dev_timesheet_to_biz(self, workspace_type, modified):
        db_cursor = get_tapd_timesheet_for_dev_task(workspace_type, modified)
        db_dict_list = dict_fetchall(db_cursor)

        ins_list, upd_list, del_list = self.__parse_cud_dev_task_timesheet(db_dict_list)
        log.info(">>>> ins_list = {}".format(ins_list))
        log.info(">>>> upd_list = {}".format(upd_list))
        log.info(">>>> del_list = {}".format(del_list))
        ins_count = self.__ins_dev_task_timesheet_list(ins_list)
        log.info(">>>> ins_count = {}".format(ins_count))
        upd_count = self.__upd_dev_task_timesheet_list(upd_list)
        log.info(">>>> upd_count = {}".format(upd_count))
        del_count = self.__del_dev_task_timesheet_list(del_list)
        log.info(">>>> del_count = {}".format(del_count))

        return ins_count, upd_count, del_count

    def __parse_cud_dev_task_timesheet(self, db_dict_list):
        ins_list = []
        upd_list = []
        del_list = []
        if db_dict_list:

            for db_timesheet_dict in db_dict_list:
                dev_timesheet_id = db_timesheet_dict.get("id")
                entry_status = db_timesheet_dict.get("entry_status")
                if entry_status and entry_status == 3:
                    del_list.append(db_timesheet_dict)
                    continue
                if dev_timesheet_id:
                    tapd_entry_id = db_timesheet_dict.get("tapd_entry_id")
                    tapd_workspace_id = db_timesheet_dict.get("tapd_workspace_id")
                    tapd_time_sheet_entity_id = db_timesheet_dict.get("tapd_time_sheet_entity_id")
                    tapd_time_sheet_entity_type = db_timesheet_dict.get("tapd_time_sheet_entity_type")
                    tapd_time_sheet_owner = db_timesheet_dict.get("tapd_time_sheet_owner")
                    tapd_time_sheet_created = db_timesheet_dict.get("tapd_time_sheet_created")
                    tapd_time_sheet_modified = db_timesheet_dict.get("tapd_time_sheet_modified")
                    tapd_time_sheet_spentdate = db_timesheet_dict.get("tapd_time_sheet_spentdate")
                    tapd_time_sheet_timespent = db_timesheet_dict.get("tapd_time_sheet_timespent")
                    tapd_time_sheet_timeremaining = db_timesheet_dict.get("tapd_time_sheet_timeremaining")
                    tapd_time_sheet_memo = db_timesheet_dict.get("tapd_time_sheet_memo")

                    dev_workspace_id = db_timesheet_dict.get("dev_workspace_id")
                    dev_task_id = db_timesheet_dict.get("dev_task_id")
                    dev_task_timesheet_id = db_timesheet_dict.get("dev_task_timesheet_id")
                    dev_task_timesheet_owner = db_timesheet_dict.get("dev_task_timesheet_owner")
                    dev_task_timesheet_created = db_timesheet_dict.get("dev_task_timesheet_created")
                    dev_task_timesheet_modified = db_timesheet_dict.get("dev_task_timesheet_modified")
                    dev_task_timesheet_spentdate = db_timesheet_dict.get("dev_task_timesheet_spentdate")
                    dev_task_timesheet_timespent = db_timesheet_dict.get("dev_task_timesheet_timespent")
                    dev_task_timesheet_timeremain = db_timesheet_dict.get("dev_task_timesheet_timeremain")
                    dev_task_timesheet_memo = db_timesheet_dict.get("dev_task_timesheet_memo")
                    is_upd = False
                    if not is_upd and (not dev_workspace_id or dev_workspace_id != tapd_workspace_id):
                        is_upd = True
                    if not is_upd and (not dev_task_id or dev_task_id != tapd_time_sheet_entity_id):
                        is_upd = True
                    if not is_upd and (not dev_task_timesheet_id or dev_task_timesheet_id != tapd_entry_id):
                        is_upd = True
                    if (not is_upd
                            and (not dev_task_timesheet_owner or dev_task_timesheet_owner != tapd_time_sheet_owner)):
                        is_upd = True
                    if (not is_upd
                            and (not dev_task_timesheet_created
                                 or dev_task_timesheet_created != tapd_time_sheet_created)):
                        is_upd = True
                    if (not is_upd
                            and (not dev_task_timesheet_modified
                                 or dev_task_timesheet_modified != tapd_time_sheet_modified)):
                        is_upd = True
                    if (not is_upd
                            and (not dev_task_timesheet_spentdate
                                 or dev_task_timesheet_spentdate != tapd_time_sheet_spentdate)):
                        is_upd = True
                    if (not is_upd
                            and (not dev_task_timesheet_timespent
                                 or dev_task_timesheet_timespent != tapd_time_sheet_timespent)):
                        is_upd = True
                    if (not is_upd
                            and (not dev_task_timesheet_timeremain
                                 or dev_task_timesheet_timeremain != tapd_time_sheet_timeremaining)):
                        is_upd = True
                    if (not is_upd
                            and (not dev_task_timesheet_memo
                                 or dev_task_timesheet_memo != tapd_time_sheet_memo)):
                        is_upd = True

                    if is_upd:
                        upd_list.append(db_timesheet_dict)
                else:
                    ins_list.append(db_timesheet_dict)
        return ins_list, upd_list, del_list

    def __ins_dev_task_timesheet_list(self, ins_dict_list,  curr_time=None, opt_user=None):
        ins_count = 0
        if ins_dict_list:
            if not curr_time:
                curr_time = datetime.datetime.now()
            if not opt_user:
                opt_user = self.opt_user

            ins_obj_list = []
            for ins_dict in ins_dict_list:
                if ins_dict:
                    ins_obj = DevEfficDevTaskTimeSheet(
                        create_time=curr_time,
                        create_user=opt_user,
                        update_time=curr_time,
                        update_user=opt_user,
                        stamp=0,
                        dev_task_timesheet_id=ins_dict["tapd_entry_id"],
                        dev_workspace_id=ins_dict["dev_workspace_id"],
                        dev_task_id=ins_dict["tapd_time_sheet_entity_id"],
                        dev_task_timesheet_owner=ins_dict["tapd_time_sheet_owner"],
                        dev_task_timesheet_created=ins_dict["tapd_time_sheet_created"],
                        dev_task_timesheet_modified=ins_dict["tapd_time_sheet_modified"],
                        dev_task_timesheet_spentdate=ins_dict["tapd_time_sheet_spentdate"],
                        dev_task_timesheet_timespent=ins_dict["tapd_time_sheet_timespent"],
                        dev_task_timesheet_timeremain=ins_dict["tapd_time_sheet_timeremain"],
                        dev_task_timesheet_memo=ins_dict["tapd_time_sheet_memo"],
                    )
                    ins_obj_list.append(ins_obj)
            if ins_obj_list:
                try:
                    with transaction.atomic():
                        DevEfficDevTaskTimeSheet.objects.bulk_create(ins_obj_list)
                        ins_count = len(ins_obj_list)
                except Exception as e:
                    log.error(e)

        return ins_count

    def __upd_dev_task_timesheet_list(self, upd_dict_list,  curr_time=None, opt_user=None):
        upd_count = 0
        if upd_dict_list:
            if not curr_time:
                curr_time = datetime.datetime.now()
            if not opt_user:
                opt_user = self.opt_user

            upd_obj_list = []
            for upd_dict in upd_dict_list:
                if upd_dict:
                    upd_obj = DevEfficDevTaskTimeSheet(
                        update_time=curr_time,
                        update_user=opt_user,

                        dev_task_timesheet_id=upd_dict["tapd_entry_id"],
                        dev_workspace_id=upd_dict["dev_workspace_id"],
                        dev_task_id=upd_dict["tapd_time_sheet_entity_id"],
                        dev_task_timesheet_owner=upd_dict["tapd_time_sheet_owner"],
                        dev_task_timesheet_created=upd_dict["tapd_time_sheet_created"],
                        dev_task_timesheet_modified=upd_dict["tapd_time_sheet_modified"],
                        dev_task_timesheet_spentdate=upd_dict["tapd_time_sheet_spentdate"],
                        dev_task_timesheet_timespent=upd_dict["tapd_time_sheet_timespent"],
                        dev_task_timesheet_timeremain=upd_dict["tapd_time_sheet_timeremain"],
                        dev_task_timesheet_memo=upd_dict["tapd_time_sheet_memo"],

                        id=upd_dict["id"],
                    )
                    upd_obj_list.append(upd_obj)
            if upd_obj_list:
                upd_fields = [
                    'update_user',
                    'update_time',
                    'dev_task_timesheet_id',
                    'dev_workspace_id',
                    'dev_task_id',
                    'dev_task_timesheet_owner',
                    'dev_task_timesheet_created',
                    'dev_task_timesheet_modified',
                    'dev_task_timesheet_spentdate',
                    'dev_task_timesheet_timespent',
                    'dev_task_timesheet_timeremain',
                    'dev_task_timesheet_memo',
                ]
                try:
                    with transaction.atomic():
                        DevEfficDevTaskTimeSheet.objects.bulk_update(upd_obj_list, upd_fields)
                        upd_count = len(upd_obj_list)
                except Exception as e:
                    log.error(e)

        return upd_count

    def __del_dev_task_timesheet_list(self, del_dict_list,  curr_time=None, opt_user=None):
        del_count = 0
        if del_dict_list:
            if not curr_time:
                curr_time = datetime.datetime.now()
            if not opt_user:
                opt_user = self.opt_user

            del_id_list = []
            for del_dict in del_dict_list:
                if del_dict:
                    del_id = del_dict['id']
                    if del_id:
                        del_id_list.append(del_id)

            if del_id_list:
                DevEfficDevTaskTimeSheet.objects.filter(id__in=del_id_list).delete()
                del_count = len(del_id_list)
        return del_count

    def sync_test_timesheet_to_biz(self, workspace_type, modified):
        db_cursor = get_tapd_timesheet_for_test_task(workspace_type, modified)
        db_dict_list = dict_fetchall(db_cursor)

        ins_list, upd_list, del_list = self.__parse_cud_test_task_timesheet(db_dict_list)
        log.info(">>>> ins_list = {}".format(ins_list))
        log.info(">>>> upd_list = {}".format(upd_list))
        log.info(">>>> del_list = {}".format(del_list))
        ins_count = self.__ins_test_task_timesheet_list(ins_list)
        log.info(">>>> ins_count = {}".format(ins_count))
        upd_count = self.__upd_test_task_timesheet_list(upd_list)
        log.info(">>>> upd_count = {}".format(upd_count))
        del_count = self.__del_test_task_timesheet_list(del_list)
        log.info(">>>> del_count = {}".format(del_count))

        return ins_count, upd_count, del_count

    def __parse_cud_test_task_timesheet(self, db_dict_list):
        ins_list = []
        upd_list = []
        del_list = []
        if db_dict_list:

            for db_timesheet_dict in db_dict_list:
                dev_timesheet_id = db_timesheet_dict.get("id")
                entry_status = db_timesheet_dict.get("entry_status")
                if entry_status and entry_status == 3:
                    del_list.append(db_timesheet_dict)
                    continue
                if dev_timesheet_id:
                    tapd_entry_id = db_timesheet_dict.get("tapd_entry_id")
                    tapd_workspace_id = db_timesheet_dict.get("tapd_workspace_id")
                    tapd_time_sheet_entity_id = db_timesheet_dict.get("tapd_time_sheet_entity_id")
                    tapd_time_sheet_entity_type = db_timesheet_dict.get("tapd_time_sheet_entity_type")
                    tapd_time_sheet_owner = db_timesheet_dict.get("tapd_time_sheet_owner")
                    tapd_time_sheet_created = db_timesheet_dict.get("tapd_time_sheet_created")
                    tapd_time_sheet_modified = db_timesheet_dict.get("tapd_time_sheet_modified")
                    tapd_time_sheet_spentdate = db_timesheet_dict.get("tapd_time_sheet_spentdate")
                    tapd_time_sheet_timespent = db_timesheet_dict.get("tapd_time_sheet_timespent")
                    tapd_time_sheet_timeremaining = db_timesheet_dict.get("tapd_time_sheet_timeremaining")
                    tapd_time_sheet_memo = db_timesheet_dict.get("tapd_time_sheet_memo")

                    test_workspace_id = db_timesheet_dict.get("test_workspace_id")
                    test_task_id = db_timesheet_dict.get("test_task_id")
                    test_task_timesheet_id = db_timesheet_dict.get("test_task_timesheet_id")
                    test_task_timesheet_owner = db_timesheet_dict.get("test_task_timesheet_owner")
                    test_task_timesheet_created = db_timesheet_dict.get("test_task_timesheet_created")
                    test_task_timesheet_modified = db_timesheet_dict.get("test_task_timesheet_modified")
                    test_task_timesheet_spentdate = db_timesheet_dict.get("test_task_timesheet_spentdate")
                    test_task_timesheet_timespent = db_timesheet_dict.get("test_task_timesheet_timespent")
                    test_task_timesheet_timeremain = db_timesheet_dict.get("test_task_timesheet_timeremain")
                    test_task_timesheet_memo = db_timesheet_dict.get("test_task_timesheet_memo")

                    is_upd = False
                    if not is_upd and (not test_workspace_id or test_workspace_id != tapd_workspace_id):
                        is_upd = True
                    if not is_upd and (not test_task_id or test_task_id != tapd_time_sheet_entity_id):
                        is_upd = True
                    if not is_upd and (not test_task_timesheet_id or test_task_timesheet_id != tapd_entry_id):
                        is_upd = True
                    if (not is_upd
                            and (not test_task_timesheet_owner or test_task_timesheet_owner != tapd_time_sheet_owner)):
                        is_upd = True
                    if (not is_upd
                            and (not test_task_timesheet_created
                                 or test_task_timesheet_created != tapd_time_sheet_created)):
                        is_upd = True
                    if (not is_upd
                            and (not test_task_timesheet_modified
                                 or test_task_timesheet_modified != tapd_time_sheet_modified)):
                        is_upd = True
                    if (not is_upd
                            and (not test_task_timesheet_spentdate
                                 or test_task_timesheet_spentdate != tapd_time_sheet_spentdate)):
                        is_upd = True
                    if (not is_upd
                            and (not test_task_timesheet_timespent
                                 or test_task_timesheet_timespent != tapd_time_sheet_timespent)):
                        is_upd = True
                    if (not is_upd
                            and (not test_task_timesheet_timeremain
                                 or test_task_timesheet_timeremain != tapd_time_sheet_timeremaining)):
                        is_upd = True
                    if (not is_upd
                            and (not test_task_timesheet_memo
                                 or test_task_timesheet_memo != tapd_time_sheet_memo)):
                        is_upd = True

                    if is_upd:
                        upd_list.append(db_timesheet_dict)
                else:
                    ins_list.append(db_timesheet_dict)
        return ins_list, upd_list, del_list

    def __ins_test_task_timesheet_list(self, ins_dict_list,  curr_time=None, opt_user=None):
        ins_count = 0
        if ins_dict_list:
            if not curr_time:
                curr_time = datetime.datetime.now()
            if not opt_user:
                opt_user = self.opt_user

            ins_obj_list = []
            for ins_dict in ins_dict_list:
                if ins_dict:
                    ins_obj = DevEfficTestTaskTimeSheet(
                        create_time=curr_time,
                        create_user=opt_user,
                        update_time=curr_time,
                        update_user=opt_user,
                        stamp=0,
                        test_task_timesheet_id=ins_dict["tapd_entry_id"],
                        test_workspace_id=ins_dict["test_workspace_id"],
                        test_task_id=ins_dict["tapd_time_sheet_entity_id"],
                        test_task_timesheet_owner=ins_dict["tapd_time_sheet_owner"],
                        test_task_timesheet_created=ins_dict["tapd_time_sheet_created"],
                        test_task_timesheet_modified=ins_dict["tapd_time_sheet_modified"],
                        test_task_timesheet_spentdate=ins_dict["tapd_time_sheet_spentdate"],
                        test_task_timesheet_timespent=ins_dict["tapd_time_sheet_timespent"],
                        test_task_timesheet_timeremain=ins_dict["tapd_time_sheet_timeremain"],
                        test_task_timesheet_memo=ins_dict["tapd_time_sheet_memo"],
                    )
                    ins_obj_list.append(ins_obj)
            if ins_obj_list:
                try:
                    with transaction.atomic():
                        DevEfficTestTaskTimeSheet.objects.bulk_create(ins_obj_list)
                        ins_count = len(ins_obj_list)
                except Exception as e:
                    log.error(e)

        return ins_count

    def __upd_test_task_timesheet_list(self, upd_dict_list,  curr_time=None, opt_user=None):
        upd_count = 0
        if upd_dict_list:
            if not curr_time:
                curr_time = datetime.datetime.now()
            if not opt_user:
                opt_user = self.opt_user

            upd_obj_list = []
            for upd_dict in upd_dict_list:
                if upd_dict:
                    upd_obj = DevEfficTestTaskTimeSheet(
                        update_time=curr_time,
                        update_user=opt_user,

                        test_task_timesheet_id=upd_dict["tapd_entry_id"],
                        test_workspace_id=upd_dict["test_workspace_id"],
                        test_task_id=upd_dict["tapd_time_sheet_entity_id"],
                        test_task_timesheet_owner=upd_dict["tapd_time_sheet_owner"],
                        test_task_timesheet_created=upd_dict["tapd_time_sheet_created"],
                        test_task_timesheet_modified=upd_dict["tapd_time_sheet_modified"],
                        test_task_timesheet_spentdate=upd_dict["tapd_time_sheet_spentdate"],
                        test_task_timesheet_timespent=upd_dict["tapd_time_sheet_timespent"],
                        test_task_timesheet_timeremain=upd_dict["tapd_time_sheet_timeremain"],
                        test_task_timesheet_memo=upd_dict["tapd_time_sheet_memo"],

                        id=upd_dict["id"],
                    )
                    upd_obj_list.append(upd_obj)
            if upd_obj_list:
                upd_fields = [
                    'update_user',
                    'update_time',
                    'test_task_timesheet_id',
                    'test_workspace_id',
                    'test_task_id',
                    'test_task_timesheet_owner',
                    'test_task_timesheet_created',
                    'test_task_timesheet_modified',
                    'test_task_timesheet_spentdate',
                    'test_task_timesheet_timespent',
                    'test_task_timesheet_timeremain',
                    'test_task_timesheet_memo',
                ]
                try:
                    with transaction.atomic():
                        DevEfficTestTaskTimeSheet.objects.bulk_update(upd_obj_list, upd_fields)
                        upd_count = len(upd_obj_list)
                except Exception as e:
                    log.error(e)

        return upd_count

    def __del_test_task_timesheet_list(self, del_dict_list,  curr_time=None, opt_user=None):
        del_count = 0
        if del_dict_list:
            if not curr_time:
                curr_time = datetime.datetime.now()
            if not opt_user:
                opt_user = self.opt_user

            del_id_list = []
            for del_dict in del_dict_list:
                if del_dict:
                    del_id = del_dict['id']
                    if del_id:
                        del_id_list.append(del_id)

            if del_id_list:
                DevEfficTestTaskTimeSheet.objects.filter(id__in=del_id_list).delete()
                del_count = len(del_id_list)
        return del_count
