import datetime
import json
from enum import Enum

from dev_effective.model.models import DevEfficTestReport
from mantis.settings import logger as log
from tapd_gateway.from_tapd.service.tapd_entry_iteration_ser import TapdEntityIterationSer
from tapd_gateway.from_tapd.service.tapd_entry_launch_form_ser import TapdEntityLaunchFormSer
from tapd_gateway.from_tapd.dao.tapd_req_dao import get_need_etl_tapd_launch_form
from tapd_gateway.from_tapd.service.tapd_req_srv import TapdLaunchFormSrv


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class TestReportStatusEnum(Enum):
    SUCCESS = "success"
    FAIL = "fail"
    APPROVING = "approving"


class BizLaunchFormSer:

    def sync_launch_form_to_biz(self, workspace_type, modified):
        cursor = get_need_etl_tapd_launch_form(workspace_type, modified)
        launch_form_list = dict_fetchall(cursor)
        ins_rows = self.etl_launch_form(launch_form_list)

        return ins_rows

    def etl_launch_form(self, launch_form_list, project_id=None):
        launch_form_map = {}
        ins_rows = 0
        if not launch_form_list:
            return ins_rows
        delete_launch_form_list = []
        upd_rows = 0
        for launch_form in launch_form_list:
            if launch_form.get('tapd_launch_form_status') and launch_form.get('tapd_launch_form_status') == 'abandon':
                delete_launch_form_list.append(launch_form.get('tapd_entry_id'))
            else:
                if project_id:
                    launch_form_map[project_id] = launch_form
                else:
                    launch_form_map[launch_form.get('project_id')] = launch_form
        db_launch_form_list = DevEfficTestReport.objects.filter(project_id__in=launch_form_map.keys())
        db_project_id_map = {launch_form.project_id: launch_form.id for launch_form in db_launch_form_list}
        ins_list = []
        upd_list = []
        cur_time = datetime.datetime.now()
        for k, v in launch_form_map.items():
            k = int(k)
            opt = DevEfficTestReport()
            launch_form_id = v.get('tapd_entry_id')
            opt.title = v.get('tapd_launch_form_title')
            opt.creator = v.get('tapd_launch_form_creator').split(';')[0] if v.get('tapd_launch_form_creator') else ''
            opt.approver = v.get('tapd_launch_form_archived_by').split(';')[0] if v.get(
                'tapd_launch_form_archived_by') else ''
            new_project_id = k
            if project_id:
                new_project_id = project_id
            test_report_status = self.__getTestReportStatus(v.get('tapd_launch_form_release_result'),
                                                            v.get('tapd_launch_form_status'))
            opt.status = test_report_status

            db_id = None
            if k in db_project_id_map.keys():
                db_id = db_project_id_map.get(k)
            if not db_id:
                opt.project_id = new_project_id
                opt.tapd_launch_form_id = int(launch_form_id)
                opt.data_source = 'TAPD'
                opt.create_user = 'howbuyscm'
                opt.create_time = cur_time
                ins_list.append(opt)
            else:
                opt.id = db_id
                opt.update_user = 'howbuyscm'
                opt.update_time = cur_time
                upd_list.append(opt)
        if ins_list:
            DevEfficTestReport.objects.bulk_create(ins_list)
            ins_rows = len(ins_list)

        if upd_list:
            upd_fields = [
                'title',
                'status',
                'update_time',
                'update_user',
                'creator',
                'approver'
            ]

            DevEfficTestReport.objects.bulk_update(upd_list, upd_fields)
            upd_rows = len(upd_list)
        if delete_launch_form_list:
            DevEfficTestReport.objects.filter(tapd_launch_form_id__in=delete_launch_form_list).delete()
        log.info('写入、修改、删除 测试报告表的数据 分别是新增{}条, 更新{}条，删除{}条'.format(ins_rows, upd_rows, len(delete_launch_form_list)))
        return ins_rows

    def create_launch_form(self, workspace_id):
        tapd_iteration_srv = TapdEntityIterationSer()
        iteration_list = tapd_iteration_srv.get_iterations_by_workspace_id(workspace_id)
        ins_rows = 0
        if iteration_list:
            iteration_map = {}
            for iteration in iteration_list:
                iteration_map[iteration.get('tapd_entry_id')] = iteration
            self.__save_launch_form(iteration_map)
            ins_rows = len(iteration_list)
        return ins_rows

    def __save_launch_form(self, iteration_map):
        cur_time = datetime.datetime.now()
        for k, v in iteration_map.items():
            iteration_id = v.get('tapd_entry_id')

            launch_form_ser = TapdEntityLaunchFormSer()
            launch_form_result = launch_form_ser.create_launch_form(v.get('tapd_workspace_id'), v)
            if launch_form_result:
                try:
                    launch_form_id = launch_form_result.get('LaunchForm').get('id')
                    status = launch_form_result.get('LaunchForm').get('status')
                    release_result = launch_form_result.get('LaunchForm').get('release_result')
                    title = launch_form_result.get('LaunchForm').get('title')
                    test_report_status = self.__getTestReportStatus(release_result, status)
                    DevEfficTestReport.objects.update_or_create(
                        defaults={"update_user": 'howbuyscm', "update_time": cur_time, "create_user": 'howbuyscm',
                                  "create_time": cur_time, "title": title,
                                  "data_source": "TAPD",
                                  "status": test_report_status},
                        tapd_launch_form_id=launch_form_id
                    )
                    launch_form_service = TapdLaunchFormSrv()
                    launch_form_service.write_launch_forms_to_db([launch_form_result])
                except Exception as e:
                    log.error('创建发布评审时，回填数据失败，iteration_id：{}, launch_form_id:{}'.format(iteration_id,
                                                                                                      launch_form_id))
                    log.error(str(e))

    def __getTestReportStatus(self, release_result, status):
        test_report_status = TestReportStatusEnum.APPROVING.value
        if release_result and status:
            if status == 'finished' and release_result == 'release_success':
                test_report_status = TestReportStatusEnum.SUCCESS.value
            elif status == 'finished' and release_result != 'release_success':
                test_report_status = TestReportStatusEnum.FAIL.value
        return test_report_status
