import datetime

from sqlalchemy import text

from dev_effective.dao.dev_effective_dao import TapdWorkspaceModuleConfDao
from dev_effective.model.models import DevEfficTestTask, DevEfficDevTask, DevEfficTestDevSubmit
from dev_effective.utils.biz_utils import TapdUtils
from mantis.pool import instance_db_session
from mantis.settings import logger as log
from tapd_gateway.from_tapd.api.views import BizType
from tapd_gateway.from_tapd.service.tapd_entry_test_plan_ser import TapdEntityTestPlanSer
from tapd_gateway.from_tapd.dao.tapd_req_dao import get_tapd_task_by_modified


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class BizTaskSer:

    def sync_test_task_to_biz(self, workspace_type, modified):
        cursor = get_tapd_task_by_modified(workspace_type, modified)
        task_list = dict_fetchall(cursor)
        ins_rows = self.update_or_create_test_tasks(task_list)

        return ins_rows

    def sync_dev_task_to_biz(self, workspace_type, modified):
        cursor = get_tapd_task_by_modified(workspace_type, modified)
        task_list = dict_fetchall(cursor)
        ins_rows = self.__update_or_create_dev_tasks(task_list)

        return ins_rows

    def __update_or_create_dev_tasks(self, task_list):
        task_map = {}
        ins_rows = 0
        upd_rows = 0
        if not task_list:
            return ins_rows
        delete_task_list = []
        for task in task_list:
            if task.get('entry_status') and task.get('entry_status') == 3:
                delete_task_list.append(task.get('tapd_entry_id'))
            else:
                task_map[task.get('tapd_entry_id')] = task
        db_task_list = DevEfficDevTask.objects.filter(tapd_task_id__in=task_map.keys())
        db_task_id_map = {task.tapd_task_id: task.id for task in db_task_list}
        ins_list = []
        upd_list = []
        cur_time = datetime.datetime.now()
        for k, v in task_map.items():
            k = int(k)
            task_obj = DevEfficDevTask()
            task_obj.name = v.get('tapd_task_name')
            task_obj.owner = v.get('tapd_task_owner').split(";")[0] if v.get('tapd_task_owner') else None
            task_obj.status = v.get('tapd_task_status')
            task_obj.creator = v.get('tapd_task_creator')
            task_obj.description = v.get('tapd_task_description')
            tapd_task_created = TapdUtils.date_str_to_date(v.get('tapd_task_created'))
            task_obj.created = tapd_task_created
            tapd_task_completed = TapdUtils.date_str_to_date(v.get('tapd_task_completed'))
            task_obj.completed = tapd_task_completed
            task_obj.effort_completed = v.get('tapd_task_effort_completed')
            task_obj.exceed = v.get('tapd_task_exceed')
            task_obj.remain = v.get('tapd_task_remain')
            task_obj.effort = v.get('tapd_task_effort')
            task_obj.dev_story_id = v.get('dev_story_id')

            workspace_id = v.get('tapd_workspace_id')
            dao = TapdWorkspaceModuleConfDao('任务类型', workspace_id, BizType.TASK.value)
            task_type = dao.get_custom_field_by_tapd_name()
            task_obj.task_type = v.get(task_type)

            db_id = None
            if k in db_task_id_map.keys():
                db_id = db_task_id_map.get(k)
            if not db_id:
                task_obj.tapd_task_id = v.get('tapd_entry_id')
                task_obj.data_source = 'TAPD'
                task_obj.create_user = 'howbuyscm'
                task_obj.create_time = cur_time
                task_obj.update_time = cur_time
                ins_list.append(task_obj)
            else:
                task_obj.id = db_id
                task_obj.update_user = 'howbuyscm'
                task_obj.update_time = cur_time
                upd_list.append(task_obj)
        if ins_list:
            DevEfficDevTask.objects.bulk_create(ins_list)
            ins_rows = len(ins_list)
        if upd_list:
            upd_fields = [
                'name',
                'owner',
                'status',
                'description',
                'completed',
                'effort_completed',
                'exceed',
                'remain',
                'effort',
                'task_type',
                'dev_story_id',
                'update_user',
                'update_time',
            ]
            DevEfficDevTask.objects.bulk_update(upd_list, upd_fields)
            upd_rows = len(upd_list)
        if delete_task_list:
            DevEfficDevTask.objects.filter(tapd_task_id__in=delete_task_list).delete()
        log.info('写入和修改、删除dev_effic_dev_task分别是{},{},{}'.format(ins_rows, upd_rows, len(delete_task_list)))
        return ins_rows

    def update_or_create_test_tasks(self, task_list, project_id=None):
        task_map = {}
        ins_rows = 0
        upd_rows = 0
        if not task_list:
            return ins_rows
        if project_id:
            DevEfficTestTask.objects.filter(project_id=project_id).delete()
        delete_task_list = []
        for task in task_list:
            if task.get('entry_status') and task.get('entry_status') == 3:
                delete_task_list.append(task.get('tapd_entry_id'))
            else:
                task_map[task.get('tapd_entry_id')] = task
        db_task_list = DevEfficTestTask.objects.filter(tapd_task_id__in=task_map.keys())
        db_task_id_map = {task.tapd_task_id: task.id for task in db_task_list}
        ins_list = []
        upd_list = []
        cur_time = datetime.datetime.now()
        for k, v in task_map.items():
            k = int(k)
            task_obj = DevEfficTestTask()

            task_obj.name = v.get('tapd_task_name')
            task_obj.description = v.get('tapd_task_description')
            task_obj.status = v.get('tapd_task_status')
            tapd_task_story_id = v.get('tapd_task_story_id')
            dev_submit = None
            if tapd_task_story_id != 0 and tapd_task_story_id != '0':
                test_plan_ser = TapdEntityTestPlanSer()
                dev_submit = test_plan_ser.get_dev_submit_id_by_story_id(tapd_task_story_id)[0] \
                    if test_plan_ser.get_dev_submit_id_by_story_id(tapd_task_story_id) else None
            if dev_submit:
                dev_submit_id = dev_submit.get('dev_submit_id')
                task_obj.dev_submit_id = dev_submit_id
            if project_id:
                task_obj.project_id = project_id
            else:
                task_obj.project_id = v.get('project_id')
            task_obj.task_type = v.get('tapd_task_custom_field_two')
            task_obj.owner = v.get('tapd_task_owner').split(";")[0] if v.get('tapd_task_owner') else None
            task_obj.creator = v.get('tapd_task_creator')
            tapd_task_created = TapdUtils.date_str_to_date(v.get('tapd_task_created'))
            task_obj.created = tapd_task_created
            tapd_task_completed = TapdUtils.date_str_to_date(v.get('tapd_task_completed'))
            task_obj.completed = tapd_task_completed
            task_obj.effort_completed = v.get('tapd_task_effort_completed')
            task_obj.exceed = v.get('tapd_task_exceed')
            task_obj.remain = v.get('tapd_task_remain')
            task_obj.effort = v.get('tapd_task_effort')

            db_id = None
            if k in db_task_id_map.keys():
                db_id = db_task_id_map.get(k)
            if not db_id:
                task_obj.tapd_task_id = v.get('tapd_entry_id')
                task_obj.data_source = 'TAPD'
                task_obj.create_user = 'howbuyscm'
                task_obj.create_time = cur_time
                task_obj.update_time = cur_time
                ins_list.append(task_obj)
            else:
                task_obj.id = db_id
                task_obj.update_user = 'howbuyscm'
                task_obj.update_time = cur_time
                upd_list.append(task_obj)
        if ins_list:
            DevEfficTestTask.objects.bulk_create(ins_list)
            ins_rows = len(ins_list)
        if upd_list:
            upd_fields = [
                'project_id',
                'name',
                'description',
                'status',
                'task_type',
                'owner',
                'creator',
                'created',
                'completed',
                'effort_completed',
                'exceed',
                'remain',
                'effort',
                'update_user',
                'update_time',
            ]

            DevEfficTestTask.objects.bulk_update(upd_list, upd_fields)
            upd_rows = len(upd_list)
        if delete_task_list:
            DevEfficTestTask.objects.filter(tapd_task_id__in=delete_task_list).delete()
        log.info('写入、修改、删除 测试任务表 分别是{},{}，{}'.format(ins_rows, upd_rows, len(delete_task_list)))
        return ins_rows


class TapdWorkspaceModuleConfDao:
    __query_tapd_custom_field_sql = '''
                            SELECT c.conf_column 
                            FROM tapd_workspace_module_conf c
                            JOIN tapd_workspace_module m ON c.module_id = m.id
                            JOIN tapd_workspace w ON m.workspace_id = w.id
                            WHERE c.tapd_name = :tapd_name AND m.module_name = :module_name 
                            AND w.tapd_workspace_id = :workspace_id
                            '''

    def __init__(self, tapd_name=None, workspace_id=None, module_name=None):
        self.__tapd_name = tapd_name
        self.__workspace_id = workspace_id
        self.__module_name = module_name

    def get_custom_field_by_tapd_name(self):
        db_session = instance_db_session()
        result = db_session.execute(
            text(self.__query_tapd_custom_field_sql),
            {'tapd_name': self.__tapd_name, 'module_name': self.__module_name, 'workspace_id': self.__workspace_id}
        ).scalar()
        db_session.close()
        return result
