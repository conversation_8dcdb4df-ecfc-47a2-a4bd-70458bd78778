import datetime
from mantis.settings import logger as log
from dev_effective.model.models import DevEfficTestBug
from dev_effective.utils.biz_utils import TapdUtils
from tapd_gateway.from_tapd.dao.tapd_req_dao import get_test_tapd_bug_by_modified


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class BizBugSer:

    def sync_bug_to_biz(self, workspace_type, modified):
        cursor = get_test_tapd_bug_by_modified(workspace_type, modified)
        bug_list = dict_fetchall(cursor)
        ins_rows = self.update_or_create_bugs(bug_list)

        return ins_rows

    def update_or_create_bugs(self, bugs_list, project_id=None):
        bug_map = {}
        ins_rows = 0
        upd_rows = 0
        # 如果传入项目id，则删除该项目下的所有bug，再插入，避免有缺陷被移出迭代而更新不到的情况 20240506 by fwm
        if project_id:
            DevEfficTestBug.objects.filter(project_id=project_id).delete()
        if not bugs_list:
            return ins_rows
        delete_bug_list = []
        for bug in bugs_list:
            if bug.get('entry_status') and bug.get('entry_status') == 3:
                delete_bug_list.append(bug.get('tapd_entry_id'))
            else:
                bug_map[bug.get('tapd_entry_id')] = bug
        db_bug_list = DevEfficTestBug.objects.filter(tapd_bug_id__in=bug_map.keys())
        db_bug_id_map = {bug.tapd_bug_id: bug.id for bug in db_bug_list}
        ins_list = []
        upd_list = []
        current_time = datetime.datetime.now()
        for k, v in bug_map.items():
            k = int(k)
            bug_obj = DevEfficTestBug()
            bug_obj.title = v.get('tapd_bug_title')
            bug_obj.severity = v.get('tapd_bug_severity')
            bug_obj.status = v.get('tapd_bug_status')
            bug_obj.bug_type = v.get('tapd_bug_bugtype')
            tapd_bug_fixer = None
            if v.get("tapd_bug_status") == 'closed':
                tapd_bug_fixer = v.get("tapd_bug_fixer").split("_")[0] if v.get("tapd_bug_fixer") else \
                    v.get("tapd_bug_current_owner").split(";")[0] if v.get("tapd_bug_current_owner") else None
            else:
                tapd_bug_fixer = v.get("tapd_bug_fixer").split("_")[0] if v.get("tapd_bug_fixer") else None
            bug_obj.fixer = tapd_bug_fixer
            if project_id:
                bug_obj.project_id = project_id
            else:
                bug_obj.project_id = v.get("project_id")
            bug_obj.reporter = v.get('tapd_bug_reporter')
            tapd_bug_resolved = TapdUtils.date_str_to_date(v.get('tapd_bug_resolved'))
            bug_obj.resolved = tapd_bug_resolved
            tapd_bug_created = TapdUtils.date_str_to_date(v.get('tapd_bug_created'))
            bug_obj.created = tapd_bug_created
            closed = TapdUtils.date_str_to_date(v.get('tapd_bug_closed'))
            bug_obj.closed = closed
            bug_obj.flows = v.get('tapd_bug_flows')
            bug_obj.tp_app = v.get('tp_app')
            bug_obj.tms_app = v.get('tms_app')
            bug_obj.fp_app = v.get('fp_app')
            bug_obj.crm_app = v.get('crm_app')
            bug_obj.tms_h5 = v.get('tms_h5')
            bug_obj.fp_h5_app = v.get('fp_h5_app')
            bug_obj.attribution_analysis = v.get('attribution_analysis')

            db_id = None
            if k in db_bug_id_map.keys():
                db_id = db_bug_id_map.get(k)
            if not db_id:
                bug_obj.tapd_bug_id = v.get('tapd_entry_id')
                bug_obj.data_source = 'TAPD'
                bug_obj.create_user = 'howbuyscm'
                bug_obj.create_time = current_time
                bug_obj.update_time = current_time
                ins_list.append(bug_obj)
            else:
                bug_obj.id = db_id
                bug_obj.update_user = 'howbuyscm'
                bug_obj.update_time = current_time
                upd_list.append(bug_obj)
        if ins_list:
            DevEfficTestBug.objects.bulk_create(ins_list)
            ins_rows = len(ins_list)

        if upd_list:
            upd_fields = [
                'project_id',
                'title',
                'severity',
                'bug_type',
                'status',
                'fixer',
                'reporter',
                'created',
                'resolved',
                'closed',
                'flows',
                'tp_app',
                'tms_app',
                'fp_app',
                'crm_app',
                'tms_h5',
                'fp_h5_app',
                'attribution_analysis',
                'update_user',
                'update_time',
            ]
            DevEfficTestBug.objects.bulk_update(upd_list, upd_fields)
            upd_rows = len(upd_list)
        if delete_bug_list:
            DevEfficTestBug.objects.filter(tapd_bug_id__in=delete_bug_list).delete()
        log.info('写入、修改、删除 biz_bug分别是{},{}，{}'.format(ins_rows, upd_rows, len(delete_bug_list)))
        return ins_rows