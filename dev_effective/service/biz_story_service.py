import json
import traceback
from datetime import datetime, timedelta
from enum import unique, Enum
from time import sleep

from django.db import connection, transaction

from dev_effective.model.models import DevEfficTestProject, DevEfficDevStory, DevEfficTestDevSubmit, \
    DevEfficTestDevSubmitBindStory
from dev_effective.utils.biz_utils import TapdUtils
from mantis.settings import logger as log, TAPD
from tapd_gateway.from_tapd.model.models import TapdEntryTestPlanBindStory, TapdEntryStory, TapdEntryWorkspace, \
    TapdWorkspaceStorySyncDetail
from tapd_gateway.from_tapd.dao.tapd_req_dao import get_tapd_entry_story_by_modified, get_product_workspace_map, \
    get_product_custom_field, get_product_tapd_entry_story, get_dev_story_dict, get_product_story_mapping_status, \
    get_product_story_status, get_work_item_map
from tapd_gateway.to_tapd.service.sync_to_dev_story_info import SyncToDevStory, UpdateTapdStory
from tapd_gateway.from_tapd.dao.tapd_req_dao import get_tapd_id_by_tapd_name
from dev_effective.dao.dev_effective_dao import BizStoryDao


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


@unique
class StoryMappingEnum(Enum):
    planning = (0, "未开始")
    starting_dev = (1, "研发中")
    complete = (2, "完成(上线)")
    stop = (3, "流程终止")


class BizStorySer:
    workitem_type_en_name = 'REQ'
    product_workspace_type = 5

    def sync_dev_story_to_biz(self, workspace_type, modified):
        cursor = get_tapd_entry_story_by_modified(workspace_type, modified)
        story_list = dict_fetchall(cursor)
        ins_rows = self.__update_or_create_dev_story(story_list)

        return ins_rows

    def sync_test_submit_story_to_biz(self, workspace_type, modified):
        workitem_type_id = TAPD.get("test_submit_story_workitem_type_id")
        cursor = get_tapd_entry_story_by_modified(workspace_type, modified, workitem_type_id=workitem_type_id)
        story_list = dict_fetchall(cursor)
        ins_rows = self.__update_or_create_test_dev_submit(story_list)
        return ins_rows

    def sync_test_story_to_biz(self, workspace_type, modified):
        workitem_type_id = TAPD.get("workitem_type_id")
        cursor = get_tapd_entry_story_by_modified(workspace_type, modified, workitem_type_id=workitem_type_id,
                                                  is_test_story=True)
        story_list = dict_fetchall(cursor)
        ups_rows = self.__update_test_project(story_list)
        return ups_rows

    def __update_or_create_test_dev_submit(self, story_list):
        story_map = {}
        ins_rows = 0
        upd_rows = 0
        delete_test_dev_submit_list = []

        if not story_list:
            return ins_rows

        for story in story_list:
            if story.get('entry_status') and story.get('entry_status') == 3:
                delete_test_dev_submit_list.append(story.get('tapd_entry_id'))
            else:
                story_map[story.get('tapd_entry_id')] = story

        db_test_dev_submit_list = DevEfficTestDevSubmit.objects.filter(tapd_story_id__in=story_map.keys())
        db_test_dev_submit_id_map = {test_dev_submit.tapd_story_id: test_dev_submit.id for test_dev_submit in
                                     db_test_dev_submit_list}
        ins_list = []
        upd_list = []
        cur_time = datetime.now()
        for k, v in story_map.items():
            k = int(k)
            submit_obj = DevEfficTestDevSubmit()

            submit_obj.project_id = v.get("project_id")
            submit_obj.name = v.get('tapd_story_name')
            submit_obj.description = v.get("tapd_story_description")
            submit_obj.status = v.get("tapd_story_status")
            submit_obj.owner = v.get("tapd_story_owner")
            submit_obj.creator = v.get("create_user")
            submit_obj.created = v.get("tapd_story_created")
            submit_obj.team = v.get("tapd_story_custom_field_three")
            if v.get("test_plan_id"):
                submit_obj.tapd_test_plan_id = v.get("test_plan_id")
            submit_obj.tapd_story_id = v.get('tapd_entry_id')

            if v.get("test_plan_status") == 3:
                submit_obj.tapd_test_plan_status = 0
            else:
                submit_obj.tapd_test_plan_status = 1

            db_id = None
            if k in db_test_dev_submit_id_map.keys():
                db_id = db_test_dev_submit_id_map.get(k)
            if not db_id:
                submit_obj.data_source = 'TAPD'
                submit_obj.create_user = 'howbuyscm'
                submit_obj.create_time = cur_time
                submit_obj.update_time = cur_time
                ins_list.append(submit_obj)
            else:
                submit_obj.id = db_id
                submit_obj.update_user = 'howbuyscm'
                submit_obj.update_time = cur_time
                upd_list.append(submit_obj)
        if ins_list:
            DevEfficTestDevSubmit.objects.bulk_create(ins_list)
            ins_rows = len(ins_list)
            self.__update_or_insert_test_dev_submit_story(ins_list)
        if upd_list:
            upd_fields = [
                'project_id',
                'name',
                'description',
                'status',
                'owner',
                'creator',
                'created',
                'tapd_test_plan_status',
                'team',
                'update_user',
                'update_time',
            ]
            DevEfficTestDevSubmit.objects.bulk_update(upd_list, upd_fields)
            upd_rows = len(upd_list)
            self.__update_or_insert_test_dev_submit_story(upd_list, is_update=True)
        if delete_test_dev_submit_list:
            with transaction.atomic():
                delete_id_list = DevEfficTestDevSubmit.objects.filter(
                    tapd_story_id__in=delete_test_dev_submit_list).values_list('id', flat=True)
                DevEfficTestDevSubmit.objects.filter(tapd_story_id__in=delete_test_dev_submit_list).delete()
                DevEfficTestDevSubmitBindStory.objects.filter(dev_submit_id__in=delete_id_list).delete()
        log.info('写入和修改、删除 dev_effic_test_dev_submit分别是{},{},{}'.format(ins_rows, upd_rows,
                                                                                  len(delete_test_dev_submit_list)))
        return ins_rows

    def __update_test_project(self, stories_list):
        # 新增测试计划有单独的逻辑，这里只做更新
        upd_rows = 0
        if stories_list:
            story_map = {}
            delete_project_list = []
            upd_list = []
            try:
                for story in stories_list:
                    if story.get('entry_status') and story.get('entry_status') == 3:
                        delete_project_list.append(story.get('tapd_entry_id'))
                    else:
                        story_map[story.get('tapd_entry_id')] = story
                project_list = DevEfficTestProject.objects.filter(tapd_story_id__in=story_map.keys())
                project_id_map = {project.tapd_story_id: project.id for project in project_list}
                for k, v in story_map.items():
                    k = int(k)
                    db_id = None
                    biz_project = DevEfficTestProject()
                    biz_project.tapd_iteration_id = v.get('tapd_story_iteration_id')
                    biz_project.name = v.get('tapd_story_name')
                    biz_project.owner = v.get('tapd_story_owner')
                    biz_project.status = v.get('tapd_story_status')
                    tapd_story_begin = TapdUtils.date_str_to_date(v.get('tapd_story_begin'))
                    biz_project.expected_start = tapd_story_begin
                    tapd_story_due = TapdUtils.date_str_to_date(v.get('tapd_story_due'))
                    biz_project.expected_end = tapd_story_due
                    biz_project.team = v.get('tapd_story_custom_field_two')

                    actual_start = TapdUtils.date_str_to_date(v.get('tapd_story_custom_field_10'))
                    biz_project.actual_start = actual_start
                    actual_end = TapdUtils.date_str_to_date(v.get('tapd_story_custom_field_11'))
                    biz_project.actual_end = actual_end
                    biz_project.effort = v.get('tapd_story_effort')
                    biz_project.delay_days = v.get('tapd_story_custom_field_four')
                    biz_project.delay_description = v.get('tapd_story_custom_field_eight')
                    biz_project.compatibility_description = "、".join(
                        v.get('tapd_story_custom_field_12').split("|"))
                    biz_project.test_summary = v.get('tapd_story_custom_field_9')
                    biz_project.tp_iters = v.get('tapd_story_custom_field_13')
                    biz_project.tms_iters = v.get('tapd_story_custom_field_14')
                    biz_project.tms_h5_iters = v.get('tapd_story_custom_field_15')
                    biz_project.fp_iters = v.get('tapd_story_custom_field_16')
                    biz_project.fp_h5_app_iters = v.get('tapd_story_custom_field_17')
                    biz_project.crm_iters = v.get('tapd_story_custom_field_18')
                    biz_project.dev2test_date = v.get('tapd_story_custom_field_20')
                    biz_project.task_division_ratio = v.get('tapd_story_custom_field_24')
                    biz_project.business_system = v.get('tapd_story_custom_field_three')
                    if k in project_id_map.keys():
                        db_id = project_id_map.get(k)

                    if db_id:
                        biz_project.id = db_id
                        upd_list.append(biz_project)

                if upd_list:
                    upd_fields = [
                        'tapd_iteration_id',
                        'name',
                        'status',
                        'expected_start',
                        'expected_end',
                        'team',
                        'owner',
                        'actual_start',
                        'actual_end',
                        'effort',
                        'delay_days',
                        'delay_description',
                        'compatibility_description',
                        'test_summary',
                        'tp_iters',
                        'tms_iters',
                        'tms_h5_iters',
                        'fp_iters',
                        'fp_h5_app_iters',
                        'crm_iters',
                        'dev2test_date',
                        'task_division_ratio',
                        'business_system',
                    ]
                    DevEfficTestProject.objects.bulk_update(upd_list, upd_fields)
                    upd_rows = len(upd_list)
                if delete_project_list:
                    log.info("待删除的test project story id: {}".format(delete_project_list))
                    DevEfficTestProject.objects.filter(tapd_story_id__in=delete_project_list).delete()

            except Exception as e:
                log.error("测试项目故事数据更新失败，原因为：{}".format(str(e)))
        return upd_rows

    def __update_or_insert_test_dev_submit_story(self, test_dev_submit_list, is_update=False):
        for test_dev_submit in test_dev_submit_list:
            with transaction.atomic():
                log.info("开始处理提测需求")
                log.info("tapd_test_plan_id={}, tapd_story_id={}".format(test_dev_submit.tapd_test_plan_id,
                                                                         test_dev_submit.tapd_story_id))
                test_dev_submit_obj = DevEfficTestDevSubmit.objects.filter(
                    tapd_test_plan_id=test_dev_submit.tapd_test_plan_id,
                    tapd_story_id=test_dev_submit.tapd_story_id).values(
                    'id').last()
                if test_dev_submit_obj:
                    obj = TapdEntryTestPlanBindStory.objects.filter(
                        tapd_test_plan_id=test_dev_submit.tapd_test_plan_id).values(
                        'relative_dev_story_ids').last()
                    log.info("开始处理提测需求 is_update={}".format(is_update))
                    if is_update:
                        log.info("开始处理提测需求 dev_submit_id={}".format(test_dev_submit_obj.get('id')))
                        DevEfficTestDevSubmitBindStory.objects.filter(
                            dev_submit_id=test_dev_submit_obj.get('id')).delete()
                    if obj and obj.get('relative_dev_story_ids'):
                        relative_dev_story_ids = obj.get('relative_dev_story_ids').split(',')
                        dev_story_ids = BizStoryDao.get_dev_leaf_story_id_for_dev_submit(relative_dev_story_ids)
                        log.info("开始处理提测需求 新的dev_story_ids={}".format(dev_story_ids))
                        if dev_story_ids:
                            ins_bind_list = []
                            for story_id in dev_story_ids:
                                bind = DevEfficTestDevSubmitBindStory()
                                bind.dev_submit_id = test_dev_submit_obj.get('id')
                                bind.dev_story_id = story_id
                                ins_bind_list.append(bind)
                            DevEfficTestDevSubmitBindStory.objects.bulk_create(ins_bind_list)

    def __update_or_create_dev_story(self, story_list):
        story_map = {}
        ins_rows = 0
        upd_rows = 0
        delete_story_list = []
        if not story_list:
            return ins_rows
        for story in story_list:
            if story.get('entry_status') and story.get('entry_status') == 3:
                delete_story_list.append(story.get('tapd_entry_id'))
            else:
                story_map[story.get('tapd_entry_id')] = story
        db_story_list = DevEfficDevStory.objects.filter(tapd_story_id__in=story_map.keys())
        db_story_id_map = {story.tapd_story_id: story.id for story in db_story_list}
        ins_list = []
        upd_list = []
        cur_time = datetime.now()
        for k, v in story_map.items():
            k = int(k)
            story_obj = DevEfficDevStory()
            story_obj.name = v.get('tapd_story_name')
            story_obj.owner = v.get('tapd_story_owner').split(";")[0] if v.get('tapd_story_owner') else None
            story_obj.status = v.get('tapd_story_status')
            story_obj.effort = v.get('tapd_story_effort')
            field_four_value = str(v.get('tapd_story_custom_field_four', ''))
            story_obj.delay_days = field_four_value if len(field_four_value) <= 10 else ''
            story_obj.delay_description = v.get('tapd_story_custom_field_eight')
            story_obj.team = v.get('tapd_story_custom_field_two')
            story_obj.size = v.get('tapd_story_size')
            story_obj.type = v.get('tapd_story_type')
            cursor = get_tapd_id_by_tapd_name(workspace_id=v.get('tapd_workspace_id'), tapd_name='2024公司项目')
            dict_list = dict_fetchall(cursor)
            story_obj.howbuy_project = v.get(dict_list[0].get('conf_column')) if dict_list else None
            expected_start = TapdUtils.date_str_to_date(v.get('tapd_story_begin'))
            story_obj.expected_start = expected_start
            expected_end = TapdUtils.date_str_to_date(v.get('tapd_story_due'))
            story_obj.expected_end = expected_end
            try:
                actual_start = TapdUtils.date_str_to_date(v.get('tapd_story_custom_field_10'))
            except Exception as e:
                log.error("对研发业务需求表dev_effic_dev_story无用的字段，忽略异常，并置空")
                actual_start = ''
            story_obj.actual_start = actual_start
            completed = TapdUtils.date_str_to_date(v.get('tapd_story_completed'))
            actual_end = completed.date() if completed else None
            story_obj.actual_end = actual_end

            db_id = None
            if k in db_story_id_map.keys():
                db_id = db_story_id_map.get(k)
            if not db_id:
                story_obj.tapd_story_id = v.get('tapd_entry_id')
                story_obj.data_source = 'TAPD'
                story_obj.create_user = 'howbuyscm'
                story_obj.create_time = cur_time
                story_obj.update_time = cur_time
                ins_list.append(story_obj)
            else:
                story_obj.tapd_story_id = v.get('tapd_entry_id')
                story_obj.id = db_id
                story_obj.update_user = 'howbuyscm'
                story_obj.update_time = cur_time
                upd_list.append(story_obj)
        if ins_list:
            DevEfficDevStory.objects.bulk_create(ins_list)
            ins_rows = len(ins_list)
        if upd_list:
            upd_fields = [
                'name',
                'owner',
                'status',
                'expected_start',
                'expected_end',
                'actual_start',
                'actual_end',
                'effort',
                'size',
                'type',
                'delay_days',
                'delay_description',
                'team',
                'update_user',
                'update_time',
                'tapd_story_id',
                'howbuy_project'
            ]
            DevEfficDevStory.objects.bulk_update(upd_list, upd_fields)
            # 研发需求的变更要触发对应的提测单关联需求的刷新
            test_dev_submit_list = self.__get_test_dev_submit_list(upd_list)
            self.__update_or_insert_test_dev_submit_story(test_dev_submit_list, is_update=True)

            upd_rows = len(upd_list)
        if delete_story_list:
            log.info("待删除的tapd story id: {}".format(delete_story_list))
            DevEfficDevStory.objects.filter(tapd_story_id__in=delete_story_list).delete()
        log.info('写入和修改、删除 dev_effic_dev_story分别是{},{},{}'.format(ins_rows, upd_rows, len(delete_story_list)))
        return ins_rows

    def __get_test_dev_submit_list(self, update_story_list):
        tapd_story_id_list = []
        for story_obj in update_story_list:
            if story_obj.tapd_story_id:
                tapd_story_id_list.append(story_obj.tapd_story_id)

        dev_story_id_list = DevEfficDevStory.objects.filter(tapd_story_id__in=tapd_story_id_list).values_list('id',
                                                                                                              flat=True)
        dev_submit_id_list = DevEfficTestDevSubmitBindStory.objects.filter(
            dev_story_id__in=dev_story_id_list).values_list(
            'dev_submit_id', flat=True).distinct()
        return DevEfficTestDevSubmit.objects.filter(id__in=dev_submit_id_list).all()

    def update_or_create_test_projects(self, stories_list):
        if stories_list:
            story_map = {}
            delete_project_list = []
            try:
                for story in stories_list:
                    if story.get('entry_status') and story.get('entry_status') == 3:
                        delete_project_list.append(story.get('tapd_entry_id'))
                    else:
                        story_map[story.get('tapd_entry_id')] = story
                project_list = DevEfficTestProject.objects.filter(tapd_story_id__in=story_map.keys())
                project_id_map = {project.tapd_story_id: project.id for project in project_list}
                ins_list = []
                upd_list = []
                for k, v in story_map.items():
                    k = int(k)
                    db_id = None
                    biz_project = DevEfficTestProject()
                    biz_project.tapd_iteration_id = v.get('tapd_story_iteration_id')
                    biz_project.name = v.get('tapd_story_name')
                    biz_project.owner = v.get('tapd_story_owner')
                    biz_project.status = v.get('tapd_story_status')
                    tapd_story_begin = TapdUtils.date_str_to_date(v.get('tapd_story_begin'))
                    biz_project.expected_start = tapd_story_begin
                    tapd_story_due = TapdUtils.date_str_to_date(v.get('tapd_story_due'))
                    biz_project.expected_end = tapd_story_due
                    biz_project.team = v.get('tapd_story_custom_field_two')

                    actual_start = TapdUtils.date_str_to_date(v.get('tapd_story_custom_field_10'))
                    biz_project.actual_start = actual_start
                    actual_end = TapdUtils.date_str_to_date(v.get('tapd_story_custom_field_11'))
                    biz_project.actual_end = actual_end
                    biz_project.effort = v.get('tapd_story_effort')
                    biz_project.delay_days = v.get('tapd_story_custom_field_four')
                    biz_project.delay_description = v.get('tapd_story_custom_field_eight')
                    biz_project.compatibility_description = "、".join(
                        v.get('tapd_story_custom_field_12').split("|"))
                    biz_project.test_summary = v.get('tapd_story_custom_field_9')
                    biz_project.tp_iters = v.get('tapd_story_custom_field_13')
                    biz_project.tms_iters = v.get('tapd_story_custom_field_14')
                    biz_project.tms_h5_iters = v.get('tapd_story_custom_field_15')
                    biz_project.fp_iters = v.get('tapd_story_custom_field_16')
                    biz_project.fp_h5_app_iters = v.get('tapd_story_custom_field_17')
                    biz_project.crm_iters = v.get('tapd_story_custom_field_18')
                    biz_project.dev2test_date = v.get('tapd_story_custom_field_20')
                    biz_project.task_division_ratio = v.get('tapd_story_custom_field_24')
                    biz_project.business_system = v.get('tapd_story_custom_field_three')
                    if k in project_id_map.keys():
                        db_id = project_id_map.get(k)

                    if not db_id:
                        biz_project.tapd_story_id = v.get('tapd_entry_id')
                        biz_project.data_source = 'TAPD'
                        ins_list.append(biz_project)

                    else:
                        biz_project.id = db_id
                        upd_list.append(biz_project)
                if ins_list:
                    DevEfficTestProject.objects.bulk_create(ins_list)
                if upd_list:
                    upd_fields = [
                        'tapd_iteration_id',
                        'name',
                        'status',
                        'expected_start',
                        'expected_end',
                        'team',
                        'owner',
                        'actual_start',
                        'actual_end',
                        'effort',
                        'delay_days',
                        'delay_description',
                        'compatibility_description',
                        'test_summary',
                        'tp_iters',
                        'tms_iters',
                        'tms_h5_iters',
                        'fp_iters',
                        'fp_h5_app_iters',
                        'crm_iters',
                        'dev2test_date',
                        'task_division_ratio',
                        'business_system',
                    ]
                    DevEfficTestProject.objects.bulk_update(upd_list, upd_fields)
                if delete_project_list:
                    log.info("待删除的test project story id: {}".format(delete_project_list))
                    DevEfficTestProject.objects.filter(tapd_story_id__in=delete_project_list).delete()
            except Exception as e:
                log.error("测试项目故事数据更新失败，原因为：{}".format(str(e)))

    # @classmethod
    def create_story_from_product_story(self, workspace_id, modified):
        try:
            # 需求同步10分钟一次，这里把时间范围往前推15分钟，保证
            time_threshold = datetime.now() - timedelta(minutes=30)
            workitem_type_id_list = get_product_workspace_map(workspace_id, self.workitem_type_en_name,
                                                              self.product_workspace_type)
            if not workitem_type_id_list:
                return None
            custom_field = get_product_custom_field(workspace_id, '研发团队')
            team = 'tapd_story_' + custom_field
            if not modified:
                modified = time_threshold
            story_list = get_product_tapd_entry_story(workspace_id, modified, team, workitem_type_id_list)
            log.info("workspace_id：{}, 开始同步需求到研发数量:{}".format(workspace_id, len(story_list)))
            if not story_list:
                return time_threshold
            workspace_list = TapdEntryWorkspace.objects.filter(workspace_is_active=True).values_list(
                'tapd_workspace_id', 'tapd_workspace_name')
            dev_story = SyncToDevStory()
            post_count = 0
            sync_fields = TAPD['sync_fields']
            work_item_map = {}
            try:
                work_item_config = TAPD['work_item_config']
                if work_item_config:
                    work_item_dict = json.loads(work_item_config)
                    mappings = []
                    # 遍历字典并转换键值对
                    for key, value in work_item_dict.items():
                        mappings.append((int(key), value.capitalize()))
                    work_item_map = get_work_item_map(mappings)
            except Exception as e:
                log.error("获取工作项映射失败，原因为：{}".format(str(e)))

            for item in story_list:
                src_story_id = item.get('tapd_entry_id')
                new_creator = item.get('tapd_story_creator')
                team_str = item.get('team')
                team_list = team_str.split('|')
                for team in team_list:
                    # 这里需要先判断是否已经同步过
                    workspace_info = workspace_list.filter(tapd_workspace_name=team).first()
                    if not workspace_info:
                        continue
                    dst_workspace_id = workspace_info[0]
                    dst_work_item_id = work_item_map.get(dst_workspace_id)
                    # sync_fields = "name,description,priority,attachment,business_value,status"
                    sync_detail = TapdWorkspaceStorySyncDetail.objects.filter(dst_workspace_id=dst_workspace_id,
                                                                              story_id=src_story_id)
                    if not sync_detail:
                        if post_count == 10:
                            sleep(30)
                            post_count = 0
                        log.info(
                            "开始同步需求到研发, workspace_id：{}, src_story_id:{}, dst_workspace_id:{}".format(
                                workspace_id,
                                src_story_id,
                                dst_workspace_id))

                        result = dev_story.post_to_tapd(workspace_id, src_story_id, dst_workspace_id, sync_fields,
                                                        new_creator, dst_work_item_id)
                        if result:
                            story_id = result.get('Story').get('id')
                            log.info(
                                "产品REQ创建研发需求成功, dst_workspace_id: {}，story_id：{}".format(dst_workspace_id,
                                                                                                   story_id))
                            TapdWorkspaceStorySyncDetail.objects.create(workspace_id=workspace_id,
                                                                        story_id=src_story_id,
                                                                        dst_workspace_id=dst_workspace_id,
                                                                        dst_story_id=story_id,
                                                                        create_user='howbuyscm',
                                                                        create_time=datetime.now())
                        else:
                            log.error(
                                "--------产品REQ创建研发需求失败--------------, workspace_id：{}, src_story_id:{}".format(
                                    workspace_id,
                                    src_story_id))
                        post_count += 1
        except Exception as e:
            log.error("创建story失败，原因为：{}".format(str(e)))
            traceback.print_exc()
            raise Exception("创建story失败，原因为：{}".format(str(e)))
        return time_threshold

    def sync_product_story_status_from_dev_story(self, workspace_id):
        try:
            story_dict = get_dev_story_dict(workspace_id)
            if story_dict:
                product_story_ids = list(story_dict.keys())
                story_mapping_status = get_product_story_mapping_status(workspace_id)
                product_story_status_dict = get_product_story_status(workspace_id, product_story_ids)
                story_ser = UpdateTapdStory()
                log.info("开始同步研发需求状态到产品, workspace_id：{}".format(workspace_id))
                post_count = 0
                for product_story_id, dev_story_list in story_dict.items():
                    dev_num = 0
                    complete_num = 0
                    stop_num = 0
                    target_status = None
                    for dev_story in dev_story_list:
                        log.info("workspace_id：{}, dev_story_id:{}, type_mapping_type:{}".format(workspace_id, dev_story.get(
                            'dev_story_id'), dev_story.get('type_mapping_type')))
                        if dev_story.get('type_mapping_type') == StoryMappingEnum.starting_dev.value[0]:
                            dev_num += 1
                        elif dev_story.get('type_mapping_type') == StoryMappingEnum.complete.value[0]:
                            complete_num += 1
                        elif dev_story.get('type_mapping_type') == StoryMappingEnum.stop.value[0]:
                            stop_num += 1
                    if complete_num == len(dev_story_list):
                        target_status = StoryMappingEnum.complete.value[0]
                    elif stop_num == len(dev_story_list):
                        target_status = StoryMappingEnum.stop.value[0]
                    elif dev_num > 0 or complete_num > 0:
                        target_status = StoryMappingEnum.starting_dev.value[0]
                    log.info("workspace_id：{}, product_story_id:{}, target_status:{}".format(workspace_id, product_story_id,
                                                                                             target_status))
                    log.info(
                        "workspace_id：{}, product_story_id:{}, current_status:{}".format(workspace_id, product_story_id,
                                                                                         product_story_status_dict.get(
                                                                                             product_story_id)))
                    if target_status:
                        for status_mapping in story_mapping_status:
                            tapd_column_options = status_mapping.get('tapd_column_options')
                            type_mapping_type = status_mapping.get('type_mapping_type')
                            if product_story_status_dict.get(product_story_id) in tapd_column_options:
                                if type_mapping_type != target_status:
                                    post_count = self._start_update_story_status(post_count, product_story_id, story_ser,
                                                                                tapd_column_options, workspace_id)
                            else:
                                if target_status == type_mapping_type:
                                    post_count = self._start_update_story_status(post_count, product_story_id,
                                                                                 story_ser,
                                                                                 tapd_column_options, workspace_id)

        except Exception as e:
            log.error("研发需求状态同步业务失败，原因为：{}".format(str(e)))
            traceback.print_exc()
            raise Exception("研发需求状态同步业务失败，原因为：{}".format(str(e)))

    def _start_update_story_status(self, post_count, product_story_id, story_ser, target_status, workspace_id):
        if post_count == 10:
            sleep(30)
            post_count = 0
        param_json = {"workspace_id": workspace_id, "id": product_story_id, "status": target_status}
        result = story_ser.post_to_tapd(param_json)
        post_count += 1
        if not result:
            log.error(
                "--------研发需求状态同步业务失败--------------, workspace_id：{}, product_story_id:{}".format(
                    workspace_id, product_story_id))
        return post_count
