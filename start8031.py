#!/usr/bin/python310

import os


class Driver:
    log_path = "~/logs/mantis-web8031.log"

    def start(self):
        if os.path.isfile(self.log_path):
            os.system("cat /dev/null > {}".format(self.log_path))
        os.system("nohup python3.x /home/<USER>/mantis/manage.py runserver 0.0.0.0:8031 > {} 2>&1 &".format(self.log_path))

    def stop(self):
        os.system('pkill -9 -f "manage.py runserver 0.0.0.0:8031"')

    def restart(self):
        self.stop()
        self.start()


if __name__ == "__main__":
    dr = Driver()
    dr.restart()
