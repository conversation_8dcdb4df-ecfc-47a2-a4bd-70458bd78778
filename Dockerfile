# 使用指定的基础镜像
FROM harbor.k8s.howbuy.com/pa/py3.10.11:base

# 设置环境变量
ENV UV_PYTHON=/usr/bin/python3.10
ENV PATH=$PATH:/root/.local/bin
ENV PYTHONPATH=/data/app

# 设置工作目录
WORKDIR /data/app

# 下载并安装uv
#RUN curl -fsSL https://astral.sh/uv/install.sh -o /tmp/install-uv.sh && \
#    sh /tmp/install-uv.sh && \
#    rm /tmp/install-uv.sh && \
#    echo 'export PATH=$PATH:/root/.local/bin' >> ~/.bashrc && \
#    source ~/.bashrc

# 复制并执行安装脚本
#COPY install-uv.sh /tmp/install-uv.sh

#WORKDIR /data/app

#RUN set -e && \
#    sh /tmp/install-uv.sh && \
#    rm /tmp/install-uv.sh && \
#    echo 'export PATH=$PATH:/root/.local/bin' >> ~/.bashrc && \
#    source ~/.bashrc

# 复制本地下载的uv安装包
COPY uv.tar.gz /tmp/
RUN cd /tmp && \
    tar xzf uv.tar.gz && \
    mv uv-x86_64-unknown-linux-gnu/uv /usr/local/bin/ && \
    chmod +x /usr/local/bin/uv && \
    rm -rf /tmp/uv-x86_64-unknown-linux-gnu* && \
    echo 'export PATH=$PATH:/root/.local/bin' >> ~/.bashrc && \
    source ~/.bashrc

# 验证uv安装
RUN uv --version

# 创建必要的目录
RUN mkdir -p /data/app/mantis \
    /data/logs/mantis \
    /home/<USER>
    /data/files/mantis

# 设置工作目录
WORKDIR /data/app/mantis

# 复制整个项目到容器中
COPY . /data/app/mantis/

# 卸载指定的依赖包
RUN /usr/local/bin/uv pip uninstall certifi cffi charset-normalizer click cryptography Django django-mcp djangorestframework greenlet h11 httpcore idna Jinja2 MarkupSafe mcp packaging pbr pipdeptree pycparser python-jenkins pytz simplejson six soupsieve spider-common-utils sqlparse sse-starlette typer urllib3 uvicorn wcwidth

# 安装requirements.txt中的依赖包
RUN /usr/local/bin/uv pip install -r /data/app/mantis/requirements.txt -i http://pypi.howbuy.pa/simple --trusted-host pypi.howbuy.pa

# 复制启动脚本
COPY start.sh /home/<USER>/start.sh

# 设置目录权限
RUN chown -R tomcat:tomcat /data/app /data/logs /home/<USER>/data/files/mantis && \
    chmod -R 755 /data/app /data/logs /home/<USER>
    chmod -R 777 /data/files/mantis /data/logs && \
    chmod +x /home/<USER>/start.sh

# 切换到tomcat用户
USER tomcat

# 设置容器启动命令
ENTRYPOINT ["/usr/bin/sh", "/home/<USER>/start.sh"]