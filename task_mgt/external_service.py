#
import os
import json
import datetime
import subprocess
from mantis.settings import QA_SCRIPTS, logger
from task_mgt.models import ServiceResults
from common.ssh_cnt import SSHConnectionManager
from task_mgt.tapd_mgt.config.external_service_config import qa_scripts_config, qa_scripts_python_version as python_version
import locale

class ExternalService(object):
    def __init__(self, srv_name, operator, params, exec_type=None):
        self.business_name = srv_name
        self.parameters = params
        self.operator = operator
        if exec_type:
            self.exec_type = exec_type
        else:
            self.exec_type = 'async'

    def insert_record_and_return_cmd(self):
        start_at = datetime.datetime.now()
        log_name = self.business_name + '_' + start_at.strftime("%Y%m%d_%H%M%S") + '.log'
        log_dir = qa_scripts_config[self.business_name]['log_dir']
        script_path = qa_scripts_config[self.business_name]['script_path']
        log_path = os.path.join(log_dir, log_name)

        obj = ServiceResults(
            business_name=self.business_name, script_params=json.dumps(self.parameters),
            operator=self.operator, log_path=log_path, start_at=start_at)
        obj.save()
        if self.exec_type and self.exec_type == 'sync':
            exec_cmd = 'nohup {} {} {} > {} 2>&1'.format(python_version, script_path, obj.id, log_path)
        else:
            exec_cmd = 'nohup {} {} {} &> {} &'.format(python_version, script_path, obj.id, log_path)
        obj.exec_cmd = exec_cmd
        obj.save()

        return exec_cmd, obj.id

    def shell_cmd(self, cmd):
        logger.info(cmd)
        p = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = p.communicate()
        rt_code = p.returncode
        stdout_info = stdout.decode()
        stderr_info = stderr.decode('utf-8', errors='replace')
        logger.info(stdout_info)
        return rt_code, stdout_info, stderr_info

    def shell_sync_cmd(self, cmd):
        logger.info(cmd)
        p = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout_info = p.stdout.decode(locale.getpreferredencoding(False))
        stderr_info = p.stderr.decode(locale.getpreferredencoding(False))
        rt_code = p.returncode
        logger.info('stdout_info日志输出:' + stdout_info)
        logger.info('stderr_info日志输出:' + stderr_info)
        return rt_code, stdout_info, stderr_info

    def call_local_service(self):
        try:
            sh_cmd, sid = self.insert_record_and_return_cmd()
            logger.info('Exec: ' + sh_cmd)
        except Exception as err:
            logger.error(str(err))
            logger.error('插入执行记录异常')
            return 1, None
        # with SSHConnectionManager(ip=self.ip, username=self.ssh_user, password=self.ssh_password) as ssh:
        #     stat, rst, err = ssh.exec_ssh(sh_cmd)
        #     print(rst)
        #     print(err)
        #     # todo 需要判断 stat ，当脚本启动报错的时候更新 ServiceResults结果
        if self.exec_type and self.exec_type == 'sync':
            rt_code, stdout_info, stderr_info = self.shell_sync_cmd(sh_cmd)
        else:
            rt_code, stdout_info, stderr_info = self.shell_cmd(sh_cmd)
        return rt_code, sid

    def call_external_service(self, ip, ssh_user, ssh_password):
        try:
            sh_cmd, sid = self.insert_record_and_return_cmd()
            logger.info('Exec: ' + sh_cmd)
        except Exception as err:
            logger.error(str(err))
            logger.error('插入执行记录异常')
            return 1, None
        with SSHConnectionManager(ip=ip, username=ssh_user, password=ssh_password) as ssh:
            stat, rst, err = ssh.exec_ssh(sh_cmd)
            print(rst)
            print(err)
            # todo 需要判断 stat ，当脚本启动报错的时候更新 ServiceResults结果

        return stat, sid

    @staticmethod
    def insert_repo_diff_result(business_name, params, operator, status, detail, ssh_cmd, start_at, end_at):
        obj = ServiceResults(
            business_name=business_name, script_params=params, status=status, detail=detail, exec_cmd=ssh_cmd,
            operator=operator, log_path='', start_at=start_at, end_at=end_at)
        obj.save()

    def call_repo_diff(self, ssh_cmd):
        start_at = datetime.datetime.now()
        with SSHConnectionManager(ip=self.ip, username=self.ssh_user, password=self.ssh_password) as ssh:
            rc, stdout, error = ssh.exec_ssh(ssh_cmd)
            end_at = datetime.datetime.now()
            if rc:
                self.insert_repo_diff_result(self.business_name, self.parameters, self.operator, 'failure', error,
                                             ssh_cmd, start_at, end_at)
                return error
            else:
                self.insert_repo_diff_result(self.business_name, self.parameters, self.operator, 'success', stdout,
                                             ssh_cmd, start_at, end_at)
                return stdout

if __name__ == '__main__':
    exec = ExternalService('test','wei.liu','')
    exec.call_service()