qa_scripts_python_version = '/data/app/.venv/bin/python3'
qa_scripts_home = '/data/app/mantis/qa_scripts'
qa_scripts_config = {
    'analyze_unit_test_data': {
        'description': '分析单元测试数据',
        'script_path': qa_scripts_home + '/job/router/urls.py',
        'script_params': '',
        'log_dir': '/data/logs/mantis/utest_report/'
    },
    'batch_analyze_unit_test_data': {
        'description': '批量分析单元测试数据',
        'script_path': qa_scripts_home + '/job/router/urls.py',
        'script_params': '',
        'log_dir': '/data/logs/mantis/utest_report/'
    },
    'create_test_report': {
        'description': '创建业务测试报告',
        'script_path': qa_scripts_home + '/job/router/urls.py',
        'script_params': '',
        'log_dir': '/data/logs/mantis/test_report/'
    }
}
