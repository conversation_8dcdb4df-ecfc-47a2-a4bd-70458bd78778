import os
import configparser
import json


class LoadConfig:
    config_dir = os.path.dirname(os.path.abspath(__file__))

    def __init__(self):
        self.local_settings = configparser.ConfigParser()

    def get_config_file(self):
        for file in os.listdir(self.config_dir):
            if file.endswith(".ini"):
                yield file

    def loading_all(self):
        all_dict = {}
        self.local_settings = configparser.ConfigParser()
        for file in self.get_config_file():
            self.local_settings.read(os.path.join(self.config_dir, file), encoding="utf-8")
            all_dict[file.replace(".ini", "")] = self.local_settings._sections
        return all_dict

    def loading(self, file_name):
        self.local_settings.read(os.path.join(self.config_dir, file_name+".ini"), encoding="utf-8")
        return self.local_settings._sections


if __name__ == "__main__":
    lc =LoadConfig()
    res = lc.loading("external_interface")["test_plans_count"]
    for i in json.loads(res["request_params"]):
        print (i)
