[test_plans_count]
request_type = get
request_address = /test_plans/count
request_params = workspace_id={}
request_result = {"status":"","data":"","info":""}
app_name = tapd
interface_desc = 获取项目下的测试计划数量

[test_plans]
request_type = get
request_address = /test_plans
request_params = workspace_id={}&limit={}&page={}
request_result = {"status":"","data":"","info":""}
app_name = tapd
interface_desc = 获取符合查询条件的所有测试计划（分页显示，默认一页30条）

[test_plans_update]
request_type = post
request_address = /test_plans
request_params = {"id":{},"workspace_id":{}}
request_result = {"status":"","data":"","info":""}
app_name = tapd
interface_desc = 编辑测试计划

[test_plans_create]
request_type = post
request_address = /test_plans
request_params = {"name":{},"workspace_id":{}}
request_result = {"status":"","data":"","info":""}
app_name = tapd
interface_desc = 创建测试计划

[iterations]
request_type = post
request_address = /iterations
request_params = {"name":{},"workspace_id":{},"startdate":{},"enddate":{},"creator":{}}
request_result = {"status":"","data":"","info":""}
app_name = tapd
interface_desc = 在项目下创建迭代

[iterations_count]
request_type = get
request_address = /iterations/count
request_params = workspace_id={}&id={}
request_result = {"status":"","data":"","info":""}
app_name = tapd
interface_desc = 获取项目下的测试计划数量

[iterations_get]
request_type = get
request_address = /iterations
request_params = workspace_id={}&limit={}&page={}&id={}
request_result = {"status":"","data":"","info":""}
app_name = tapd
interface_desc = 获取迭代信息接口

[bugs_count]
request_type = get
request_address = /bugs/count
request_params = workspace_id={}&iteration_id={}
request_params_start_time = workspace_id={}&created=>{}
request_result = {"status":"","data":"","info":""}
app_name = tapd
interface_desc = 获取项目下的缺陷数量

[bugs_get]
request_type = get
request_address = /bugs
request_params = workspace_id={}&limit={}&page={}&iteration_id={}
request_params_start_time = workspace_id={}&limit={}&page={}&created=>{}
request_result = {"status":"","data":"","info":""}
request_auto_create_params = workspace_id={}&custom_field_88={}
app_name = tapd
interface_desc = 获取项目缺陷信息接口

[stories_count]
request_type = get
request_address = /stories/count
request_params = workspace_id={}&iteration_id={}
request_result = {"status":"","data":"","info":""}
app_name = tapd
interface_desc = 获取项目下的需求数量

[stories_get]
request_type = get
request_address = /stories
request_params = workspace_id={}&limit={}&page={}&iteration_id={}
request_result = {"status":"","data":"","info":""}
app_name = tapd
interface_desc = 获取项目下的需求

[copy_story]
request_type = post
request_address = /stories/copy_story
request_params = {"workspace_id":{},"src_story_id":{},"dst_workspace_id":{},"sync_fields":{},"new_creator":{}}
request_result = {"status":"","data":"","info":""}
app_name = tapd
interface_desc = 产品REQ创建研发需求

[update_story]
request_type = post
request_address = /stories
request_params = {"workspace_id":{},"id":{}}
request_result = {"status":"","data":"","info":""}
app_name = tapd
interface_desc = 更新需求

[launch_forms_get]
request_type = get
request_address = /launch_forms
request_params = workspace_id={}&limit={}&page={}
request_result = {"status":"","data":"","info":""}
app_name = tapd
interface_desc = 获取发布评审记录

[launch_forms_count]
request_type = get
request_address = /launch_forms/count
request_params = workspace_id={}
request_result = {"status":"","data":"","info":""}
app_name = tapd
interface_desc = 获取发布评审数量


[update_story_select_field_options]
request_type = post
request_address = /custom_field_configs/update_story_select_field_options
request_params = workspace_id={}&options={}&id={}
request_result = {"status":"","data":"","info":""}
app_name = tapd
interface_desc = 更新需求自定义字段


[update_bug_select_field_options]
request_type = post
request_address = /custom_field_configs/update_bug_select_field_options
request_params = workspace_id={}&options={}&id={}
request_result = {"status":"","data":"","info":""}
app_name = tapd
interface_desc = 更新缺陷自定义字段