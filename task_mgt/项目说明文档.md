# Task Management 模块项目说明文档

## 项目概述

`task_mgt` 模块是 Mantis 系统中的任务管理核心模块，主要负责外部服务调用、脚本执行管理、Jenkins集成和任务队列管理。该模块为系统提供了统一的任务执行框架，支持同步和异步任务处理，是连接 Mantis 系统与外部执行环境的重要桥梁。

### 核心功能

- **外部服务调用**：统一管理和执行外部脚本和服务
- **任务执行管理**：支持同步和异步任务执行模式
- **Jenkins集成**：与Jenkins CI/CD系统的深度集成
- **执行记录管理**：完整的任务执行历史和状态跟踪
- **SSH远程执行**：支持远程服务器的脚本执行
- **队列管理**：任务队列的管理和监控

## 技术架构

```
┌─────────────────────────────────────────────────────────────┐
│                   Task Management 模块                     │
├─────────────────────────────────────────────────────────────┤
│  外部服务层 (external_service.py)                          │
│  ├── ExternalService           # 外部服务调用管理           │
│  ├── 本地服务调用              # call_local_service         │
│  ├── 远程服务调用              # call_external_service      │
│  ├── 同步/异步执行             # sync/async execution       │
│  └── SSH连接管理               # SSHConnectionManager       │
├─────────────────────────────────────────────────────────────┤
│  数据模型层 (models.py)                                     │
│  ├── ServiceResults            # 服务执行结果记录           │
│  ├── InterfaceResults          # 接口调用记录               │
│  ├── JenkinsResults            # Jenkins调用结果            │
│  └── QueueInfo                 # 队列信息管理               │
├─────────────────────────────────────────────────────────────┤
│  配置管理层 (tapd_mgt/config/)                             │
│  ├── external_service_config   # 外部服务配置               │
│  ├── analysis_ini              # 配置文件加载器             │
│  ├── external_interface.ini    # 外部接口配置               │
│  └── jenkins.ini               # Jenkins配置                │
└─────────────────────────────────────────────────────────────┘
```

## 核心模块

### 1. 外部服务管理 (ExternalService)

#### 主要功能

- **服务初始化**
  ```python
  def __init__(self, srv_name, operator, params, exec_type=None):
      self.business_name = srv_name      # 业务名称
      self.parameters = params           # 执行参数
      self.operator = operator           # 操作人员
      self.exec_type = exec_type         # 执行类型（sync/async）
  ```

- **执行记录管理**
  - 自动生成执行命令和日志路径
  - 记录执行开始时间和操作人员
  - 支持执行状态跟踪

- **本地服务调用**
  ```python
  def call_local_service(self):
      # 插入执行记录
      # 生成执行命令
      # 执行本地脚本
      # 返回执行结果
  ```

- **远程服务调用**
  ```python
  def call_external_service(self, ip, ssh_user, ssh_password):
      # SSH连接管理
      # 远程脚本执行
      # 结果收集和记录
  ```

#### 执行模式

- **异步执行（默认）**
  ```bash
  nohup python3.x script_path task_id &> log_path &
  ```

- **同步执行**
  ```bash
  nohup python3.x script_path task_id > log_path 2>&1
  ```

### 2. 数据模型层

#### ServiceResults - 服务执行结果

```python
class ServiceResults(models.Model):
    business_name = models.CharField(max_length=32)     # 业务名称
    operator = models.CharField(max_length=24)          # 操作人
    exec_cmd = models.CharField(max_length=256)         # 执行命令
    script_params = models.TextField()                  # 脚本参数
    log_path = models.CharField(max_length=128)         # 日志路径
    status = models.CharField(max_length=10)            # 执行状态
    detail = models.TextField()                         # 执行详情
    start_at = models.DateTimeField()                   # 开始时间
    end_at = models.DateTimeField()                     # 结束时间
```

**状态枚举**：
- `running`: 执行中
- `success`: 执行成功
- `failure`: 执行失败

#### InterfaceResults - 接口调用记录

```python
class InterfaceResults(models.Model):
    interface_name = models.CharField(max_length=50)    # 接口名称
    request_params = models.TextField()                 # 请求参数
    request_url = models.TextField()                    # 请求地址
    start_at = models.DateTimeField()                   # 开始时间
    end_at = models.DateTimeField()                     # 结束时间
    request_status = models.CharField(max_length=10)    # 请求状态
    request_result = models.TextField()                 # 请求结果
```

**请求类型**：
- `get`: GET请求
- `post`: POST请求
- `delete`: DELETE请求
- `put`: PUT请求

#### JenkinsResults - Jenkins调用结果

```python
class JenkinsResults(models.Model):
    job_name = models.CharField(max_length=50)          # Job名称
    request_params = models.TextField()                 # 请求参数
    request_url = models.TextField()                    # 请求地址
    build_id = models.IntegerField()                    # 构建ID
    request_status = models.CharField(max_length=10)    # 调用状态
    request_result = models.TextField()                 # 请求结果
    operator = models.CharField(max_length=30)          # 操作者
    action_id = models.IntegerField()                   # 行为ID
    business_name = models.CharField(max_length=32)     # 业务名称
    queue_item_id = models.IntegerField()               # 队列项ID
```

#### QueueInfo - 队列信息

```python
class QueueInfo(models.Model):
    key = models.CharField(max_length=24)               # 键
    value = models.TextField()                          # 值
    request_time = models.DateTimeField()               # 请求时间
```

### 3. 配置管理

#### 外部服务配置 (external_service_config.py)

```python
qa_scripts_config = {
    'analyze_unit_test_data': {
        'description': '分析单元测试数据',
        'script_path': '/data/app/mantis/qa_scripts/job/router/urls.py',
        'log_dir': '/data/logs/mantis/utest_report/'
    },
    'batch_analyze_unit_test_data': {
        'description': '批量分析单元测试数据',
        'script_path': '/data/app/mantis/qa_scripts/job/router/urls.py',
        'log_dir': '/data/logs/mantis/utest_report/'
    },
    'create_test_report': {
        'description': '创建业务测试报告',
        'script_path': '/data/app/mantis/qa_scripts/job/router/urls.py',
        'log_dir': '/data/logs/mantis/test_report/'
    }
}
```

#### 配置加载器 (analysis_ini.py)

```python
class LoadConfig:
    def loading_all(self):          # 加载所有配置文件
    def loading(self, file_name):   # 加载指定配置文件
    def get_config_file(self):      # 获取配置文件列表
```

**支持的配置文件格式**：
- `.ini` 格式配置文件
- 自动扫描配置目录
- 支持UTF-8编码

## 业务流程

### 任务执行流程

```
1. 任务请求 → ExternalService初始化
2. 参数验证 → 业务名称、操作人、参数检查
3. 记录创建 → ServiceResults表插入记录
4. 命令生成 → 根据执行类型生成shell命令
5. 任务执行 → 本地或远程执行
6. 状态更新 → 更新执行状态和结果
7. 日志记录 → 执行日志写入指定路径
```

### SSH远程执行流程

```
1. SSH连接建立 → SSHConnectionManager
2. 命令传输 → 远程服务器
3. 脚本执行 → 远程环境
4. 结果收集 → stdout/stderr
5. 连接关闭 → 资源清理
6. 结果记录 → 本地数据库
```

### Jenkins集成流程

```
1. Job触发请求 → Jenkins API
2. 队列项创建 → queue_item_id
3. 构建启动 → build_id分配
4. 执行监控 → 状态轮询
5. 结果收集 → 构建结果
6. 记录保存 → JenkinsResults表
```

## 与其他模块的集成

### 1. 与 qa_scripts 模块集成

**调用关系**：
```
task_mgt → qa_scripts (脚本执行)
```

**集成点**：
- **脚本路径**: `/data/app/mantis/qa_scripts/job/router/urls.py`
- **日志目录**: `/data/logs/mantis/`
- **执行参数**: 通过task_id传递

**业务场景**：
- 单元测试数据分析
- 批量测试报告生成
- 业务测试报告创建

### 2. 与 utest 模块集成

**数据流向**：
```
utest (分析请求) → task_mgt (任务执行) → qa_scripts (数据处理)
```

**集成配置**：
```python
'analyze_unit_test_data': {
    'description': '分析单元测试数据',
    'script_path': qa_scripts_home + '/job/router/urls.py',
    'log_dir': '/data/logs/mantis/utest_report/'
}
```

### 3. 与 test_report 模块集成

**任务类型**：
```python
'create_test_report': {
    'description': '创建业务测试报告',
    'script_path': qa_scripts_home + '/job/router/urls.py',
    'log_dir': '/data/logs/mantis/test_report/'
}
```

### 4. SSH连接管理

**依赖模块**：
```python
from common.ssh_cnt import SSHConnectionManager
```

**使用方式**：
```python
with SSHConnectionManager(ip=ip, username=ssh_user, password=ssh_password) as ssh:
    stat, rst, err = ssh.exec_ssh(sh_cmd)
```

## 配置说明

### 基础配置

```ini
[TASK_MGT]
# Python版本
python_version = python3.x

# QA脚本根目录
qa_scripts_home = /data/app/mantis/qa_scripts

# 日志根目录
log_root_dir = /data/logs/mantis/

# 默认执行类型
default_exec_type = async
```

### 外部服务配置

```ini
[EXTERNAL_SERVICES]
# 服务超时时间（秒）
service_timeout = 3600

# 最大重试次数
max_retry_count = 3

# 日志保留天数
log_retention_days = 30
```

### SSH配置

```ini
[SSH]
# 连接超时时间
connection_timeout = 30

# 命令执行超时时间
execution_timeout = 3600

# 最大并发连接数
max_connections = 10
```

### Jenkins配置

```ini
[JENKINS]
# Jenkins服务器地址
server_url = http://jenkins.example.com

# API认证信息
api_user = jenkins_user
api_token = jenkins_token

# 默认超时时间
default_timeout = 1800
```

## 部署指南

### 环境要求
- Python 3.8+
- Django 3.2+
- MySQL 5.7+
- SSH客户端支持
- 网络连接（用于远程执行）

### 安装步骤

1. **依赖安装**
   ```bash
   pip install paramiko  # SSH支持
   pip install requests   # HTTP请求
   ```

2. **数据库迁移**
   ```bash
   python manage.py makemigrations task_mgt
   python manage.py migrate
   ```

3. **配置文件设置**
   ```bash
   # 复制配置文件模板
   cp task_mgt/tapd_mgt/config/external_interface.ini.example \
      task_mgt/tapd_mgt/config/external_interface.ini
   
   # 编辑配置文件
   vim task_mgt/tapd_mgt/config/external_interface.ini
   ```

4. **目录权限设置**
   ```bash
   # 创建日志目录
   mkdir -p /data/logs/mantis/
   chmod 755 /data/logs/mantis/
   
   # 设置脚本目录权限
   chmod +x /data/app/mantis/qa_scripts/
   ```

5. **SSH密钥配置**（可选）
   ```bash
   # 生成SSH密钥对
   ssh-keygen -t rsa -b 2048
   
   # 配置免密登录
   ssh-copy-id user@remote-server
   ```

### 服务启动

```bash
# 启动Django服务
python manage.py runserver 0.0.0.0:8088

# 验证模块加载
python manage.py check task_mgt
```

## 监控和日志

### 日志配置

**系统日志**：
- **INFO**: 任务执行开始、结束、状态变更
- **WARNING**: 执行超时、重试、连接问题
- **ERROR**: 执行失败、系统错误、配置错误
- **DEBUG**: 详细的执行过程和参数信息

**任务日志**：
- **路径**: `/data/logs/mantis/{business_name}/`
- **格式**: `{business_name}_{timestamp}.log`
- **内容**: 脚本执行的stdout和stderr

### 关键监控指标

- **任务执行成功率**: 监控各业务类型的执行成功率
- **任务执行时长**: 跟踪任务执行时间分布
- **并发任务数量**: 监控同时执行的任务数量
- **SSH连接状态**: 监控远程连接的健康状态
- **Jenkins集成状态**: 监控Jenkins调用的成功率
- **队列长度**: 监控任务队列的积压情况

### 告警配置

```ini
[ALERTS]
# 任务失败率阈值
failure_rate_threshold = 0.1

# 执行时长阈值（秒）
execution_time_threshold = 3600

# 队列积压阈值
queue_backlog_threshold = 100

# SSH连接失败阈值
ssh_failure_threshold = 0.05
```

## 常见问题排查

### 1. 任务执行失败

**现象**: ServiceResults状态为failure
**排查步骤**:
1. 检查执行日志文件内容
2. 验证脚本路径和权限
3. 检查参数格式和内容
4. 确认Python环境和依赖
5. 查看系统资源使用情况

### 2. SSH连接失败

**现象**: 远程执行任务失败
**排查步骤**:
1. 测试SSH连接可达性
   ```bash
   ssh user@remote-server
   ```
2. 检查认证信息正确性
3. 验证网络连接状态
4. 查看SSH服务器日志
5. 检查防火墙配置

### 3. 脚本路径错误

**现象**: 找不到脚本文件
**排查步骤**:
1. 验证配置文件中的路径
2. 检查文件是否存在
   ```bash
   ls -la /data/app/mantis/qa_scripts/job/router/urls.py
   ```
3. 确认文件权限
4. 检查目录挂载状态

### 4. 日志文件写入失败

**现象**: 无法创建或写入日志文件
**排查步骤**:
1. 检查日志目录权限
   ```bash
   ls -ld /data/logs/mantis/
   ```
2. 验证磁盘空间
   ```bash
   df -h /data/logs/
   ```
3. 检查目录是否存在
4. 确认进程用户权限

### 5. Jenkins集成问题

**现象**: Jenkins调用失败
**排查步骤**:
1. 验证Jenkins服务器可达性
2. 检查API认证信息
3. 确认Job名称正确性
4. 查看Jenkins服务器日志
5. 测试API调用
   ```bash
   curl -u user:token http://jenkins.example.com/api/json
   ```

## 性能优化

### 1. 并发控制

```python
# 限制同时执行的任务数量
MAX_CONCURRENT_TASKS = 10

# 使用线程池管理
from concurrent.futures import ThreadPoolExecutor
executor = ThreadPoolExecutor(max_workers=MAX_CONCURRENT_TASKS)
```

### 2. 连接池管理

```python
# SSH连接池
class SSHConnectionPool:
    def __init__(self, max_connections=10):
        self.max_connections = max_connections
        self.connections = {}
    
    def get_connection(self, host):
        # 复用现有连接
        pass
```

### 3. 异步任务处理

```python
# 使用Celery进行异步任务处理
from celery import Celery

@celery.task
def execute_external_service(srv_name, operator, params):
    service = ExternalService(srv_name, operator, params)
    return service.call_local_service()
```

### 4. 缓存优化

```python
# 配置文件缓存
from django.core.cache import cache

def get_cached_config(config_name):
    cache_key = f"config_{config_name}"
    config = cache.get(cache_key)
    if not config:
        config = LoadConfig().loading(config_name)
        cache.set(cache_key, config, timeout=3600)
    return config
```

## 扩展开发

### 添加新的业务类型

1. **更新配置文件**
   ```python
   qa_scripts_config['new_business'] = {
       'description': '新业务描述',
       'script_path': '/path/to/new/script.py',
       'log_dir': '/data/logs/mantis/new_business/'
   }
   ```

2. **创建对应脚本**
   ```python
   # /path/to/new/script.py
   import sys
   
   def main(task_id):
       # 实现新业务逻辑
       pass
   
   if __name__ == '__main__':
       task_id = sys.argv[1]
       main(task_id)
   ```

3. **添加API接口**
   ```python
   class NewBusinessView(ViewSet):
       def create(self, request):
           service = ExternalService(
               'new_business',
               request.user.username,
               request.data
           )
           return service.call_local_service()
   ```

### 添加新的执行环境

1. **扩展ExternalService类**
   ```python
   def call_docker_service(self, image_name, container_config):
       # 实现Docker容器执行
       pass
   
   def call_k8s_service(self, namespace, pod_config):
       # 实现Kubernetes Pod执行
       pass
   ```

2. **添加配置支持**
   ```ini
   [DOCKER]
   default_image = python:3.8
   network_mode = bridge
   
   [KUBERNETES]
   cluster_config = /path/to/kubeconfig
   default_namespace = mantis
   ```

### 添加新的监控指标

1. **自定义指标收集**
   ```python
   from django.db.models import Count, Avg
   
   def collect_metrics():
       metrics = {
           'total_tasks': ServiceResults.objects.count(),
           'success_rate': ServiceResults.objects.filter(
               status='success'
           ).count() / ServiceResults.objects.count(),
           'avg_execution_time': ServiceResults.objects.aggregate(
               Avg('end_at') - Avg('start_at')
           )
       }
       return metrics
   ```

2. **集成监控系统**
   ```python
   # Prometheus集成
   from prometheus_client import Counter, Histogram
   
   task_counter = Counter('task_total', 'Total tasks', ['business_name', 'status'])
   task_duration = Histogram('task_duration_seconds', 'Task duration')
   ```

## 版本历史

### v1.0.0 (当前版本)
- 基础任务执行框架
- SSH远程执行支持
- Jenkins集成功能
- 配置管理系统
- 执行记录和日志管理

### 后续规划
- 增加Docker容器执行支持
- 实现Kubernetes集成
- 增强监控和告警功能
- 支持任务依赖和工作流
- 增加任务调度功能
- 实现分布式任务执行

## 联系信息

- **项目负责人**: 基础架构团队
- **技术支持**: 通过内部工单系统提交
- **文档更新**: 2024年12月

---

*本文档随项目版本更新，请以最新版本为准。*