from django.db import models


class InterfaceResults(models.Model):
    """接口调用记录
    """
    (RUNNING, SUCCESS, FAILURE, WARNING) = ('running', 'success', 'failure', 'warning')
    STATUS_CHOICE = ((RUNNING, '执行中'), (SUCCESS, '执行成功'), (FAILURE, '执行失败'))
    TYPE_CHOICE = (('get', 'get'), ('post', 'post'), ('delete', 'delete'), ('put', 'put'))
    interface_name = models.CharField(max_length=50, unique=True, verbose_name='接口名称')
    request_params = models.TextField(verbose_name='请求参数')
    request_url = models.TextField(verbose_name='请求地址')
    start_at = models.DateTimeField(blank=True, null=True, verbose_name='执行开始时间')
    end_at = models.DateTimeField(blank=True, null=True, verbose_name='执行结束时间')
    request_status = models.CharField(choices=STATUS_CHOICE, max_length=10, verbose_name='执行状态')
    request_result = models.TextField(verbose_name='请求结果')

    class Meta:
        db_table = 'task_mgt_interface_results'
        verbose_name = '接口记录'


class ServiceResults(models.Model):
    """服务结果"""
    (RUNNING, SUCCESS, FAILURE) = ('running', 'success', 'failure')
    STATUS_CHOICE = ((RUNNING, '执行中'), (SUCCESS, '执行成功'), (FAILURE, '执行失败'))
    business_name = models.CharField(max_length=32, verbose_name='业务名称', default=None)
    operator = models.CharField(blank=True, null=True, max_length=24, verbose_name='操作人')
    exec_cmd = models.CharField(max_length=256, verbose_name='脚本路径')
    script_params = models.TextField(verbose_name='参数')
    log_path = models.CharField(max_length=128, verbose_name='日志路径')
    status = models.CharField(blank=True, null=True, choices=STATUS_CHOICE, max_length=10, verbose_name='执行状态')
    detail = models.TextField(blank=True, null=True, verbose_name='详情')
    start_at = models.DateTimeField(blank=True, null=True, verbose_name='执行开始时间')
    end_at = models.DateTimeField(blank=True, null=True, verbose_name='执行结束时间')

    class Meta:
        db_table = 'task_mgt_service_results'
        verbose_name = '服务结果'


class JenkinsResults(models.Model):
    """jenkins 调用
    """
    (RUNNING, SUCCESS, FAILURE) = ('running', 'success', 'failure')
    STATUS_CHOICE = ((RUNNING, '执行中'), (SUCCESS, '执行成功'), (FAILURE, '执行失败'))
    job_name = models.CharField(max_length=50, verbose_name='job名称')
    request_params = models.TextField(verbose_name='请求参数', blank=True, null=True, )
    request_url = models.TextField(verbose_name='请求地址')
    start_at = models.DateTimeField(blank=True, null=True, verbose_name='调用开始')
    end_at = models.DateTimeField(blank=True, null=True, verbose_name='调用结束')
    build_id = models.IntegerField(blank=True, null=True, verbose_name='jenkins_job')
    request_status = models.CharField(choices=STATUS_CHOICE, max_length=10, verbose_name='调用结果')
    request_result = models.TextField(verbose_name='请求结果')
    operator = models.CharField(max_length=30, blank=True, null=True, verbose_name='操作者')
    action_id = models.IntegerField(verbose_name='行为id', blank=True, null=True)
    business_name = models.CharField(max_length=32, verbose_name='业务名称', default=None)
    queue_item_id = models.IntegerField(verbose_name='执行队列queue_item_id', blank=True, null=True)

    class Meta:
        db_table = 'task_mgt_jenkins_results'
        verbose_name = 'jenkins调用结果'


class QueueInfo(models.Model):
    """队列信息"""

    key = models.CharField(max_length=24, verbose_name='键')
    value = models.TextField(verbose_name='值')
    request_time = models.DateTimeField(blank=True, null=True, verbose_name='请求时间')

    class Meta:
        db_table = 'task_mgt_queue_info'
        verbose_name = '队列信息'