version = 1
revision = 2
requires-python = "==3.10.*"

[[package]]
name = "annotated-types"
version = "0.7.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/annotated-types/0.7.0/annotated_types-0.7.0.tar.gz", hash = "sha256:aff07c09a53a08bc8cfccb9c85b05f1aa9a2a6f23728d790723543408344ce89" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/annotated-types/0.7.0/annotated_types-0.7.0-py3-none-any.whl", hash = "sha256:1f02e8b43a8fbbc3f3e0d4f0f4bfc8131bcb4eebe8849b8e5c773f3a1c582a53" },
]

[[package]]
name = "anyio"
version = "4.9.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "exceptiongroup" },
    { name = "idna" },
    { name = "sniffio" },
    { name = "typing-extensions" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/anyio/4.9.0/anyio-4.9.0.tar.gz", hash = "sha256:673c0c244e15788651a4ff38710fea9675823028a6f08a5eda409e0c9840a028" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/anyio/4.9.0/anyio-4.9.0-py3-none-any.whl", hash = "sha256:9f76d541cad6e36af7beb62e978876f3b41e3e04f2c1fbf0884604c0a9c4d93c" },
]

[[package]]
name = "asgiref"
version = "3.8.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/asgiref/3.8.1/asgiref-3.8.1.tar.gz", hash = "sha256:c343bd80a0bec947a9860adb4c432ffa7db769836c64238fc34bdc3fec84d590" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/asgiref/3.8.1/asgiref-3.8.1-py3-none-any.whl", hash = "sha256:3e1e3ecc849832fe52ccf2cb6686b7a55f82bb1d6aee72a58826471390335e47" },
]

[[package]]
name = "astroid"
version = "3.3.10"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/astroid/3.3.10/astroid-3.3.10.tar.gz", hash = "sha256:c332157953060c6deb9caa57303ae0d20b0fbdb2e59b4a4f2a6ba49d0a7961ce" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/astroid/3.3.10/astroid-3.3.10-py3-none-any.whl", hash = "sha256:104fb9cb9b27ea95e847a94c003be03a9e039334a8ebca5ee27dafaf5c5711eb" },
]

[[package]]
name = "backports-tarfile"
version = "1.2.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/backports-tarfile/1.2.0/backports_tarfile-1.2.0.tar.gz", hash = "sha256:d75e02c268746e1b8144c278978b6e98e85de6ad16f8e4b0844a154557eca991" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/backports-tarfile/1.2.0/backports.tarfile-1.2.0-py3-none-any.whl", hash = "sha256:77e284d754527b01fb1e6fa8a1afe577858ebe4e9dad8919e34c862cb399bc34" },
]

[[package]]
name = "bcrypt"
version = "4.3.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0.tar.gz", hash = "sha256:3a3fd2204178b6d2adcf09cb4f6426ffef54762577a7c9b54c159008cb288c18" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp38-abi3-macosx_10_12_universal2.whl", hash = "sha256:f81b0ed2639568bf14749112298f9e4e2b28853dab50a8b357e31798686a036d" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp38-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:864f8f19adbe13b7de11ba15d85d4a428c7e2f344bac110f667676a0ff84924b" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp38-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:3e36506d001e93bffe59754397572f21bb5dc7c83f54454c990c74a468cd589e" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp38-abi3-manylinux_2_28_aarch64.whl", hash = "sha256:842d08d75d9fe9fb94b18b071090220697f9f184d4547179b60734846461ed59" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp38-abi3-manylinux_2_28_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:7c03296b85cb87db865d91da79bf63d5609284fc0cab9472fdd8367bbd830753" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp38-abi3-manylinux_2_28_x86_64.whl", hash = "sha256:62f26585e8b219cdc909b6a0069efc5e4267e25d4a3770a364ac58024f62a761" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp38-abi3-manylinux_2_34_aarch64.whl", hash = "sha256:beeefe437218a65322fbd0069eb437e7c98137e08f22c4660ac2dc795c31f8bb" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp38-abi3-manylinux_2_34_x86_64.whl", hash = "sha256:97eea7408db3a5bcce4a55d13245ab3fa566e23b4c67cd227062bb49e26c585d" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp38-abi3-musllinux_1_1_aarch64.whl", hash = "sha256:191354ebfe305e84f344c5964c7cd5f924a3bfc5d405c75ad07f232b6dffb49f" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp38-abi3-musllinux_1_1_x86_64.whl", hash = "sha256:41261d64150858eeb5ff43c753c4b216991e0ae16614a308a15d909503617732" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp38-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:33752b1ba962ee793fa2b6321404bf20011fe45b9afd2a842139de3011898fef" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp38-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:50e6e80a4bfd23a25f5c05b90167c19030cf9f87930f7cb2eacb99f45d1c3304" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp38-abi3-win32.whl", hash = "sha256:67a561c4d9fb9465ec866177e7aebcad08fe23aaf6fbd692a6fab69088abfc51" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp38-abi3-win_amd64.whl", hash = "sha256:584027857bc2843772114717a7490a37f68da563b3620f78a849bcb54dc11e62" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp39-abi3-macosx_10_12_universal2.whl", hash = "sha256:0d3efb1157edebfd9128e4e46e2ac1a64e0c1fe46fb023158a407c7892b0f8c3" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp39-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:08bacc884fd302b611226c01014eca277d48f0a05187666bca23aac0dad6fe24" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f6746e6fec103fcd509b96bacdfdaa2fbde9a553245dbada284435173a6f1aef" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp39-abi3-manylinux_2_28_aarch64.whl", hash = "sha256:afe327968aaf13fc143a56a3360cb27d4ad0345e34da12c7290f1b00b8fe9a8b" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp39-abi3-manylinux_2_28_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:d9af79d322e735b1fc33404b5765108ae0ff232d4b54666d46730f8ac1a43676" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp39-abi3-manylinux_2_28_x86_64.whl", hash = "sha256:f1e3ffa1365e8702dc48c8b360fef8d7afeca482809c5e45e653af82ccd088c1" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp39-abi3-manylinux_2_34_aarch64.whl", hash = "sha256:3004df1b323d10021fda07a813fd33e0fd57bef0e9a480bb143877f6cba996fe" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp39-abi3-manylinux_2_34_x86_64.whl", hash = "sha256:531457e5c839d8caea9b589a1bcfe3756b0547d7814e9ce3d437f17da75c32b0" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp39-abi3-musllinux_1_1_aarch64.whl", hash = "sha256:17a854d9a7a476a89dcef6c8bd119ad23e0f82557afbd2c442777a16408e614f" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp39-abi3-musllinux_1_1_x86_64.whl", hash = "sha256:6fb1fd3ab08c0cbc6826a2e0447610c6f09e983a281b919ed721ad32236b8b23" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp39-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:e965a9c1e9a393b8005031ff52583cedc15b7884fce7deb8b0346388837d6cfe" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp39-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:79e70b8342a33b52b55d93b3a59223a844962bef479f6a0ea318ebbcadf71505" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp39-abi3-win32.whl", hash = "sha256:b4d4e57f0a63fd0b358eb765063ff661328f69a04494427265950c71b992a39a" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-cp39-abi3-win_amd64.whl", hash = "sha256:e53e074b120f2877a35cc6c736b8eb161377caae8925c17688bd46ba56daaa5b" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-pp310-pypy310_pp73-manylinux_2_28_aarch64.whl", hash = "sha256:c950d682f0952bafcceaf709761da0a32a942272fad381081b51096ffa46cea1" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-pp310-pypy310_pp73-manylinux_2_28_x86_64.whl", hash = "sha256:107d53b5c67e0bbc3f03ebf5b030e0403d24dda980f8e244795335ba7b4a027d" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-pp310-pypy310_pp73-manylinux_2_34_aarch64.whl", hash = "sha256:b693dbb82b3c27a1604a3dff5bfc5418a7e6a781bb795288141e5f80cf3a3492" },
    { url = "http://pypi.howbuy.pa/packages/bcrypt/4.3.0/bcrypt-4.3.0-pp310-pypy310_pp73-manylinux_2_34_x86_64.whl", hash = "sha256:b6354d3760fcd31994a14c89659dee887f1351a06e5dac3c1142307172a79f90" },
]

[[package]]
name = "beautifulsoup4"
version = "4.12.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "soupsieve" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/beautifulsoup4/4.12.3/beautifulsoup4-4.12.3.tar.gz", hash = "sha256:74e3d1928edc070d21748185c46e3fb33490f22f52a3addee9aee0f4f7781051" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/beautifulsoup4/4.12.3/beautifulsoup4-4.12.3-py3-none-any.whl", hash = "sha256:b80878c9f40111313e55da8ba20bdba06d8fa3969fc68304167741bbf9e082ed" },
]

[[package]]
name = "certifi"
version = "2025.4.26"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/certifi/2025.4.26/certifi-2025.4.26.tar.gz", hash = "sha256:0a816057ea3cdefcef70270d2c515e4506bbc954f417fa5ade2021213bb8f0c6" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/certifi/2025.4.26/certifi-2025.4.26-py3-none-any.whl", hash = "sha256:30350364dfe371162649852c63336a15c70c6510c2ad5015b21c2345311805f3" },
]

[[package]]
name = "cffi"
version = "1.17.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "pycparser" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/cffi/1.17.1/cffi-1.17.1.tar.gz", hash = "sha256:1c39c6016c32bc48dd54561950ebd6836e1670f2ae46128f67cf49e789c52824" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/cffi/1.17.1/cffi-1.17.1-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:df8b1c11f177bc2313ec4b2d46baec87a5f3e71fc8b45dab2ee7cae86d9aba14" },
    { url = "http://pypi.howbuy.pa/packages/cffi/1.17.1/cffi-1.17.1-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:8f2cdc858323644ab277e9bb925ad72ae0e67f69e804f4898c070998d50b1a67" },
    { url = "http://pypi.howbuy.pa/packages/cffi/1.17.1/cffi-1.17.1-cp310-cp310-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:edae79245293e15384b51f88b00613ba9f7198016a5948b5dddf4917d4d26382" },
    { url = "http://pypi.howbuy.pa/packages/cffi/1.17.1/cffi-1.17.1-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:45398b671ac6d70e67da8e4224a065cec6a93541bb7aebe1b198a61b58c7b702" },
    { url = "http://pypi.howbuy.pa/packages/cffi/1.17.1/cffi-1.17.1-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:ad9413ccdeda48c5afdae7e4fa2192157e991ff761e7ab8fdd8926f40b160cc3" },
    { url = "http://pypi.howbuy.pa/packages/cffi/1.17.1/cffi-1.17.1-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:5da5719280082ac6bd9aa7becb3938dc9f9cbd57fac7d2871717b1feb0902ab6" },
    { url = "http://pypi.howbuy.pa/packages/cffi/1.17.1/cffi-1.17.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:2bb1a08b8008b281856e5971307cc386a8e9c5b625ac297e853d36da6efe9c17" },
    { url = "http://pypi.howbuy.pa/packages/cffi/1.17.1/cffi-1.17.1-cp310-cp310-musllinux_1_1_aarch64.whl", hash = "sha256:045d61c734659cc045141be4bae381a41d89b741f795af1dd018bfb532fd0df8" },
    { url = "http://pypi.howbuy.pa/packages/cffi/1.17.1/cffi-1.17.1-cp310-cp310-musllinux_1_1_i686.whl", hash = "sha256:6883e737d7d9e4899a8a695e00ec36bd4e5e4f18fabe0aca0efe0a4b44cdb13e" },
    { url = "http://pypi.howbuy.pa/packages/cffi/1.17.1/cffi-1.17.1-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:6b8b4a92e1c65048ff98cfe1f735ef8f1ceb72e3d5f0c25fdb12087a23da22be" },
    { url = "http://pypi.howbuy.pa/packages/cffi/1.17.1/cffi-1.17.1-cp310-cp310-win32.whl", hash = "sha256:c9c3d058ebabb74db66e431095118094d06abf53284d9c81f27300d0e0d8bc7c" },
    { url = "http://pypi.howbuy.pa/packages/cffi/1.17.1/cffi-1.17.1-cp310-cp310-win_amd64.whl", hash = "sha256:0f048dcf80db46f0098ccac01132761580d28e28bc0f78ae0d58048063317e15" },
]

[[package]]
name = "charset-normalizer"
version = "3.4.2"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2.tar.gz", hash = "sha256:5baececa9ecba31eff645232d59845c07aa030f0c81ee70184a90d35099a0e63" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-macosx_10_9_universal2.whl", hash = "sha256:7c48ed483eb946e6c04ccbe02c6b4d1d48e51944b6db70f697e089c193404941" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:b2d318c11350e10662026ad0eb71bb51c7812fc8590825304ae0bdd4ac283acd" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:9cbfacf36cb0ec2897ce0ebc5d08ca44213af24265bd56eca54bee7923c48fd6" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:18dd2e350387c87dabe711b86f83c9c78af772c748904d372ade190b5c7c9d4d" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:8075c35cd58273fee266c58c0c9b670947c19df5fb98e7b66710e04ad4e9ff86" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:5bf4545e3b962767e5c06fe1738f951f77d27967cb2caa64c28be7c4563e162c" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:7a6ab32f7210554a96cd9e33abe3ddd86732beeafc7a28e9955cdf22ffadbab0" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:b33de11b92e9f75a2b545d6e9b6f37e398d86c3e9e9653c4864eb7e89c5773ef" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-musllinux_1_2_ppc64le.whl", hash = "sha256:8755483f3c00d6c9a77f490c17e6ab0c8729e39e6390328e42521ef175380ae6" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-musllinux_1_2_s390x.whl", hash = "sha256:68a328e5f55ec37c57f19ebb1fdc56a248db2e3e9ad769919a58672958e8f366" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:21b2899062867b0e1fde9b724f8aecb1af14f2778d69aacd1a5a1853a597a5db" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-win32.whl", hash = "sha256:e8082b26888e2f8b36a042a58307d5b917ef2b1cacab921ad3323ef91901c71a" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-cp310-cp310-win_amd64.whl", hash = "sha256:f69a27e45c43520f5487f27627059b64aaf160415589230992cec34c5e18a509" },
    { url = "http://pypi.howbuy.pa/packages/charset-normalizer/3.4.2/charset_normalizer-3.4.2-py3-none-any.whl", hash = "sha256:7f56930ab0abd1c45cd15be65cc741c28b1c9a34876ce8c17a2fa107810c0af0" },
]

[[package]]
name = "chinese-calendar"
version = "1.10.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/chinese-calendar/1.10.0/chinese_calendar-1.10.0.tar.gz", hash = "sha256:1e8c731fb21f79249becfdfff55bf097a7163e3ca76fb5b8107c499cc873ac42" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/chinese-calendar/1.10.0/chinese_calendar-1.10.0-py2.py3-none-any.whl", hash = "sha256:9ab1b535a2c41edb0d48a43762c314b51b9ffb36c19e7cd3963b306a20ba30c6" },
]

[[package]]
name = "click"
version = "8.2.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/click/8.2.1/click-8.2.1.tar.gz", hash = "sha256:27c491cc05d968d271d5a1db13e3b5a184636d9d930f148c50b038f0d0646202" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/click/8.2.1/click-8.2.1-py3-none-any.whl", hash = "sha256:61a3265b914e850b85317d0b3109c7f8cd35a670f963866005d6ef1d5175a12b" },
]

[[package]]
name = "click-aliases"
version = "1.0.5"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "click" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/click-aliases/1.0.5/click_aliases-1.0.5.tar.gz", hash = "sha256:e37d4cabbaad68e1c48ec0f063a59dfa15f0e7450ec901bd1ce4f4b954bc881d" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/click-aliases/1.0.5/click_aliases-1.0.5-py3-none-any.whl", hash = "sha256:cbb83a348acc00809fe18b6da13a7f6307bc71b3c5f69cc730e012dfb4bbfdc3" },
]

[[package]]
name = "colorama"
version = "0.4.6"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/colorama/0.4.6/colorama-0.4.6.tar.gz", hash = "sha256:08695f5cb7ed6e0531a20572697297273c47b8cae5a63ffc6d6ed5c201be6e44" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/colorama/0.4.6/colorama-0.4.6-py2.py3-none-any.whl", hash = "sha256:4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6" },
]

[[package]]
name = "coreapi"
version = "2.3.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "coreschema" },
    { name = "itypes" },
    { name = "requests" },
    { name = "uritemplate" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/coreapi/2.3.3/coreapi-2.3.3.tar.gz", hash = "sha256:46145fcc1f7017c076a2ef684969b641d18a2991051fddec9458ad3f78ffc1cb" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/coreapi/2.3.3/coreapi-2.3.3-py2.py3-none-any.whl", hash = "sha256:bf39d118d6d3e171f10df9ede5666f63ad80bba9a29a8ec17726a66cf52ee6f3" },
]

[[package]]
name = "coreschema"
version = "0.0.4"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "jinja2" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/coreschema/0.0.4/coreschema-0.0.4.tar.gz", hash = "sha256:9503506007d482ab0867ba14724b93c18a33b22b6d19fb419ef2d239dd4a1607" }

[[package]]
name = "croniter"
version = "3.0.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "python-dateutil" },
    { name = "pytz" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/croniter/3.0.3/croniter-3.0.3.tar.gz", hash = "sha256:34117ec1741f10a7bd0ec3ad7d8f0eb8fa457a2feb9be32e6a2250e158957668" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/croniter/3.0.3/croniter-3.0.3-py2.py3-none-any.whl", hash = "sha256:b3bd11f270dc54ccd1f2397b813436015a86d30ffc5a7a9438eec1ed916f2101" },
]

[[package]]
name = "cryptography"
version = "3.4.8"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "cffi" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/cryptography/3.4.8/cryptography-3.4.8.tar.gz", hash = "sha256:94cc5ed4ceaefcbe5bf38c8fba6a21fc1d365bb8fb826ea1688e3370b2e24a1c" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/cryptography/3.4.8/cryptography-3.4.8-cp36-abi3-macosx_10_10_x86_64.whl", hash = "sha256:a00cf305f07b26c351d8d4e1af84ad7501eca8a342dedf24a7acb0e7b7406e14" },
    { url = "http://pypi.howbuy.pa/packages/cryptography/3.4.8/cryptography-3.4.8-cp36-abi3-macosx_11_0_arm64.whl", hash = "sha256:f44d141b8c4ea5eb4dbc9b3ad992d45580c1d22bf5e24363f2fbf50c2d7ae8a7" },
    { url = "http://pypi.howbuy.pa/packages/cryptography/3.4.8/cryptography-3.4.8-cp36-abi3-manylinux_2_12_x86_64.manylinux2010_x86_64.whl", hash = "sha256:0a7dcbcd3f1913f664aca35d47c1331fce738d44ec34b7be8b9d332151b0b01e" },
    { url = "http://pypi.howbuy.pa/packages/cryptography/3.4.8/cryptography-3.4.8-cp36-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:34dae04a0dce5730d8eb7894eab617d8a70d0c97da76b905de9efb7128ad7085" },
    { url = "http://pypi.howbuy.pa/packages/cryptography/3.4.8/cryptography-3.4.8-cp36-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:1eb7bb0df6f6f583dd8e054689def236255161ebbcf62b226454ab9ec663746b" },
    { url = "http://pypi.howbuy.pa/packages/cryptography/3.4.8/cryptography-3.4.8-cp36-abi3-manylinux_2_24_x86_64.whl", hash = "sha256:9965c46c674ba8cc572bc09a03f4c649292ee73e1b683adb1ce81e82e9a6a0fb" },
    { url = "http://pypi.howbuy.pa/packages/cryptography/3.4.8/cryptography-3.4.8-cp36-abi3-musllinux_1_1_aarch64.whl", hash = "sha256:3c4129fc3fdc0fa8e40861b5ac0c673315b3c902bbdc05fc176764815b43dd1d" },
    { url = "http://pypi.howbuy.pa/packages/cryptography/3.4.8/cryptography-3.4.8-cp36-abi3-musllinux_1_1_x86_64.whl", hash = "sha256:695104a9223a7239d155d7627ad912953b540929ef97ae0c34c7b8bf30857e89" },
    { url = "http://pypi.howbuy.pa/packages/cryptography/3.4.8/cryptography-3.4.8-cp36-abi3-win32.whl", hash = "sha256:21ca464b3a4b8d8e86ba0ee5045e103a1fcfac3b39319727bc0fc58c09c6aff7" },
    { url = "http://pypi.howbuy.pa/packages/cryptography/3.4.8/cryptography-3.4.8-cp36-abi3-win_amd64.whl", hash = "sha256:3520667fda779eb788ea00080124875be18f2d8f0848ec00733c0ec3bb8219fc" },
]

[[package]]
name = "dill"
version = "0.4.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/dill/0.4.0/dill-0.4.0.tar.gz", hash = "sha256:0633f1d2df477324f53a895b02c901fb961bdbf65a17122586ea7019292cbcf0" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/dill/0.4.0/dill-0.4.0-py3-none-any.whl", hash = "sha256:44f54bf6412c2c8464c14e8243eb163690a9800dbe2c367330883b19c7561049" },
]

[[package]]
name = "diskcache"
version = "5.6.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/diskcache/5.6.3/diskcache-5.6.3.tar.gz", hash = "sha256:2c3a3fa2743d8535d832ec61c2054a1641f41775aa7c556758a109941e33e4fc" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/diskcache/5.6.3/diskcache-5.6.3-py3-none-any.whl", hash = "sha256:5e31b2d5fbad117cc363ebaf6b689474db18a1f6438bc82358b024abd4c2ca19" },
]

[[package]]
name = "distro"
version = "1.9.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/distro/1.9.0/distro-1.9.0.tar.gz", hash = "sha256:2fa77c6fd8940f116ee1d6b94a2f90b13b5ea8d019b98bc8bafdcabcdd9bdbed" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/distro/1.9.0/distro-1.9.0-py3-none-any.whl", hash = "sha256:7bffd925d65168f85027d8da9af6bddab658135b840670a223589bc0c8ef02b2" },
]

[[package]]
name = "django"
version = "4.1.13"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "asgiref" },
    { name = "sqlparse" },
    { name = "tzdata", marker = "sys_platform == 'win32'" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/django/4.1.13/Django-4.1.13.tar.gz", hash = "sha256:94a3f471e833c8f124ee7a2de11e92f633991d975e3fa5bdd91e8abd66426318" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/django/4.1.13/Django-4.1.13-py3-none-any.whl", hash = "sha256:04ab3f6f46d084a0bba5a2c9a93a3a2eb3fe81589512367a75f79ee8acf790ce" },
]

[[package]]
name = "django-cors-headers"
version = "3.14.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "django" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/django-cors-headers/3.14.0/django_cors_headers-3.14.0.tar.gz", hash = "sha256:5fbd58a6fb4119d975754b2bc090f35ec160a8373f276612c675b00e8a138739" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/django-cors-headers/3.14.0/django_cors_headers-3.14.0-py3-none-any.whl", hash = "sha256:684180013cc7277bdd8702b80a3c5a4b3fcae4abb2bf134dceb9f5dfe300228e" },
]

[[package]]
name = "django-mcp-server"
version = "0.5.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "django" },
    { name = "djangorestframework" },
    { name = "inflection" },
    { name = "mcp" },
    { name = "uritemplate" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/django-mcp-server/0.5.3/django_mcp_server-0.5.3.tar.gz", hash = "sha256:fa8a8ad32063ada7ac56c6d6c64ac60d61c59071df6af3dd4bbe0a37cbedbb87" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/django-mcp-server/0.5.3/django_mcp_server-0.5.3-py3-none-any.whl", hash = "sha256:2d350854c0423de29067fd090bba363263a041e5e3bc7cd4a00f147c61d95c24" },
]

[[package]]
name = "django-python3-ldap"
version = "0.13.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "django" },
    { name = "ldap3" },
    { name = "pyasn1" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/django-python3-ldap/0.13.1/django-python3-ldap-0.13.1.tar.gz", hash = "sha256:02a41536165c9783903fb55e055e2d5717349cf3b4f270f972c46d747fc03dcd" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/django-python3-ldap/0.13.1/django_python3_ldap-0.13.1-py3-none-any.whl", hash = "sha256:f42622246517761233f363309ff85cdf9ebb27ea3f5661f786afbd54932c2ec9" },
]

[[package]]
name = "djangorestframework"
version = "3.15.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "django" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/djangorestframework/3.15.0/djangorestframework-3.15.0.tar.gz", hash = "sha256:3f4a263012e1b263bf49a4907eb4cfe14de840a09b1ba64596d01a9c54835919" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/djangorestframework/3.15.0/djangorestframework-3.15.0-py3-none-any.whl", hash = "sha256:5fa616048a7ec287fdaab3148aa5151efb73f7f8be1e23a9d18484e61e672695" },
]

[[package]]
name = "djangorestframework-simplejwt"
version = "5.3.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "django" },
    { name = "djangorestframework" },
    { name = "pyjwt" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/djangorestframework-simplejwt/5.3.0/djangorestframework_simplejwt-5.3.0.tar.gz", hash = "sha256:8e4c5dfca8d11c0b8a66dfd8a4e3fc1c6aa7ea188d10907ff91c942f4b52ed66" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/djangorestframework-simplejwt/5.3.0/djangorestframework_simplejwt-5.3.0-py3-none-any.whl", hash = "sha256:631d7ae2ed4365d7196a35d3cc0f6d382f7bd3361fb24c894f8f92b4da5db27d" },
]

[[package]]
name = "docutils"
version = "0.21.2"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/docutils/0.21.2/docutils-0.21.2.tar.gz", hash = "sha256:3a6b18732edf182daa3cd12775bbb338cf5691468f91eeeb109deff6ebfa986f" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/docutils/0.21.2/docutils-0.21.2-py3-none-any.whl", hash = "sha256:dafca5b9e384f0e419294eb4d2ff9fa826435bf15f15b7bd45723e8ad76811b2" },
]

[[package]]
name = "et-xmlfile"
version = "2.0.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/et-xmlfile/2.0.0/et_xmlfile-2.0.0.tar.gz", hash = "sha256:dab3f4764309081ce75662649be815c4c9081e88f0837825f90fd28317d4da54" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/et-xmlfile/2.0.0/et_xmlfile-2.0.0-py3-none-any.whl", hash = "sha256:7a91720bc756843502c3b7504c77b8fe44217c85c537d85037f0f536151b2caa" },
]

[[package]]
name = "exceptiongroup"
version = "1.3.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/exceptiongroup/1.3.0/exceptiongroup-1.3.0.tar.gz", hash = "sha256:b241f5885f560bc56a59ee63ca4c6a8bfa46ae4ad651af316d4e81817bb9fd88" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/exceptiongroup/1.3.0/exceptiongroup-1.3.0-py3-none-any.whl", hash = "sha256:4d111e6e0c13d0644cad6ddaa7ed0261a0b36971f6d23e7ec9b4b9097da78a10" },
]

[[package]]
name = "greenlet"
version = "3.2.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/greenlet/3.2.3/greenlet-3.2.3.tar.gz", hash = "sha256:8b0dd8ae4c0d6f5e54ee55ba935eeb3d735a9b58a8a1e5b5cbab64e01a39f365" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-macosx_11_0_universal2.whl", hash = "sha256:1afd685acd5597349ee6d7a88a8bec83ce13c106ac78c196ee9dde7c04fe87be" },
    { url = "http://pypi.howbuy.pa/packages/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:761917cac215c61e9dc7324b2606107b3b292a8349bdebb31503ab4de3f559ac" },
    { url = "http://pypi.howbuy.pa/packages/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-manylinux2014_ppc64le.manylinux_2_17_ppc64le.whl", hash = "sha256:a433dbc54e4a37e4fff90ef34f25a8c00aed99b06856f0119dcf09fbafa16392" },
    { url = "http://pypi.howbuy.pa/packages/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-manylinux2014_s390x.manylinux_2_17_s390x.whl", hash = "sha256:72e77ed69312bab0434d7292316d5afd6896192ac4327d44f3d613ecb85b037c" },
    { url = "http://pypi.howbuy.pa/packages/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:68671180e3849b963649254a882cd544a3c75bfcd2c527346ad8bb53494444db" },
    { url = "http://pypi.howbuy.pa/packages/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-manylinux_2_24_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:49c8cfb18fb419b3d08e011228ef8a25882397f3a859b9fe1436946140b6756b" },
    { url = "http://pypi.howbuy.pa/packages/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-musllinux_1_1_aarch64.whl", hash = "sha256:efc6dc8a792243c31f2f5674b670b3a95d46fa1c6a912b8e310d6f542e7b0712" },
    { url = "http://pypi.howbuy.pa/packages/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:731e154aba8e757aedd0781d4b240f1225b075b4409f1bb83b05ff410582cf00" },
    { url = "http://pypi.howbuy.pa/packages/greenlet/3.2.3/greenlet-3.2.3-cp310-cp310-win_amd64.whl", hash = "sha256:96c20252c2f792defe9a115d3287e14811036d51e78b3aaddbee23b69b216302" },
]

[[package]]
name = "h11"
version = "0.16.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/h11/0.16.0/h11-0.16.0.tar.gz", hash = "sha256:4e35b956cf45792e4caa5885e69fba00bdbc6ffafbfa020300e549b208ee5ff1" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/h11/0.16.0/h11-0.16.0-py3-none-any.whl", hash = "sha256:63cf8bbe7522de3bf65932fda1d9c2772064ffb3dae62d55932da54b31cb6c86" },
]

[[package]]
name = "httpcore"
version = "1.0.9"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "certifi" },
    { name = "h11" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/httpcore/1.0.9/httpcore-1.0.9.tar.gz", hash = "sha256:6e34463af53fd2ab5d807f399a9b45ea31c3dfa2276f15a2c3f00afff6e176e8" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/httpcore/1.0.9/httpcore-1.0.9-py3-none-any.whl", hash = "sha256:2d400746a40668fc9dec9810239072b40b4484b640a8c38fd654a024c7a1bf55" },
]

[[package]]
name = "httpx"
version = "0.28.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "anyio" },
    { name = "certifi" },
    { name = "httpcore" },
    { name = "idna" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/httpx/0.28.1/httpx-0.28.1.tar.gz", hash = "sha256:75e98c5f16b0f35b567856f597f06ff2270a374470a5c2392242528e3e3e42fc" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/httpx/0.28.1/httpx-0.28.1-py3-none-any.whl", hash = "sha256:d909fcccc110f8c7faf814ca82a9a4d816bc5a6dbfea25d6591d6985b8ba59ad" },
]

[[package]]
name = "httpx-sse"
version = "0.4.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/httpx-sse/0.4.0/httpx-sse-0.4.0.tar.gz", hash = "sha256:1e81a3a3070ce322add1d3529ed42eb5f70817f45ed6ec915ab753f961139721" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/httpx-sse/0.4.0/httpx_sse-0.4.0-py3-none-any.whl", hash = "sha256:f329af6eae57eaa2bdfd962b42524764af68075ea87370a2de920af5341e318f" },
]

[[package]]
name = "idna"
version = "3.10"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/idna/3.10/idna-3.10.tar.gz", hash = "sha256:12f65c9b470abda6dc35cf8e63cc574b1c52b11df2c86030af0ac09b01b13ea9" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/idna/3.10/idna-3.10-py3-none-any.whl", hash = "sha256:946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3" },
]

[[package]]
name = "importlib-metadata"
version = "8.7.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "zipp" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/importlib-metadata/8.7.0/importlib_metadata-8.7.0.tar.gz", hash = "sha256:d13b81ad223b890aa16c5471f2ac3056cf76c5f10f82d6f9292f0b415f389000" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/importlib-metadata/8.7.0/importlib_metadata-8.7.0-py3-none-any.whl", hash = "sha256:e5dd1551894c77868a30651cef00984d50e1002d06942a7101d34870c5f02afd" },
]

[[package]]
name = "inflect"
version = "5.6.2"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/inflect/5.6.2/inflect-5.6.2.tar.gz", hash = "sha256:aadc7ed73928f5e014129794bbac03058cca35d0a973a5fc4eb45c7fa26005f9" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/inflect/5.6.2/inflect-5.6.2-py3-none-any.whl", hash = "sha256:b45d91a4a28a4e617ff1821117439b06eaa86e2a4573154af0149e9be6687238" },
]

[[package]]
name = "inflection"
version = "0.5.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/inflection/0.5.1/inflection-0.5.1.tar.gz", hash = "sha256:1a29730d366e996aaacffb2f1f1cb9593dc38e2ddd30c91250c6dde09ea9b417" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/inflection/0.5.1/inflection-0.5.1-py2.py3-none-any.whl", hash = "sha256:f38b2b640938a4f35ade69ac3d053042959b62a0f1076a5bbaa1b9526605a8a2" },
]

[[package]]
name = "isort"
version = "6.0.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/isort/6.0.1/isort-6.0.1.tar.gz", hash = "sha256:1cb5df28dfbc742e490c5e41bad6da41b805b0a8be7bc93cd0fb2a8a890ac450" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/isort/6.0.1/isort-6.0.1-py3-none-any.whl", hash = "sha256:2dc5d7f65c9678d94c88dfc29161a320eec67328bc97aad576874cb4be1e9615" },
]

[[package]]
name = "itypes"
version = "1.2.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/itypes/1.2.0/itypes-1.2.0.tar.gz", hash = "sha256:af886f129dea4a2a1e3d36595a2d139589e4dd287f5cab0b40e799ee81570ff1" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/itypes/1.2.0/itypes-1.2.0-py2.py3-none-any.whl", hash = "sha256:03da6872ca89d29aef62773672b2d408f490f80db48b23079a4b194c86dd04c6" },
]

[[package]]
name = "jaraco-classes"
version = "3.4.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "more-itertools" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/jaraco-classes/3.4.0/jaraco.classes-3.4.0.tar.gz", hash = "sha256:47a024b51d0239c0dd8c8540c6c7f484be3b8fcf0b2d85c13825780d3b3f3acd" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/jaraco-classes/3.4.0/jaraco.classes-3.4.0-py3-none-any.whl", hash = "sha256:f662826b6bed8cace05e7ff873ce0f9283b5c924470fe664fff1c2f00f581790" },
]

[[package]]
name = "jaraco-context"
version = "6.0.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "backports-tarfile" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/jaraco-context/6.0.1/jaraco_context-6.0.1.tar.gz", hash = "sha256:9bae4ea555cf0b14938dc0aee7c9f32ed303aa20a3b73e7dc80111628792d1b3" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/jaraco-context/6.0.1/jaraco.context-6.0.1-py3-none-any.whl", hash = "sha256:f797fc481b490edb305122c9181830a3a5b76d84ef6d1aef2fb9b47ab956f9e4" },
]

[[package]]
name = "jaraco-functools"
version = "4.1.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "more-itertools" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/jaraco-functools/4.1.0/jaraco_functools-4.1.0.tar.gz", hash = "sha256:70f7e0e2ae076498e212562325e805204fc092d7b4c17e0e86c959e249701a9d" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/jaraco-functools/4.1.0/jaraco.functools-4.1.0-py3-none-any.whl", hash = "sha256:ad159f13428bc4acbf5541ad6dec511f91573b90fba04df61dafa2a1231cf649" },
]

[[package]]
name = "jeepney"
version = "0.9.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/jeepney/0.9.0/jeepney-0.9.0.tar.gz", hash = "sha256:cf0e9e845622b81e4a28df94c40345400256ec608d0e55bb8a3feaa9163f5732" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/jeepney/0.9.0/jeepney-0.9.0-py3-none-any.whl", hash = "sha256:97e5714520c16fc0a45695e5365a2e11b81ea79bba796e26f9f1d178cb182683" },
]

[[package]]
name = "jinja2"
version = "3.1.6"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "markupsafe" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/jinja2/3.1.6/jinja2-3.1.6.tar.gz", hash = "sha256:0137fb05990d35f1275a587e9aee6d56da821fc83491a0fb838183be43f66d6d" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/jinja2/3.1.6/jinja2-3.1.6-py3-none-any.whl", hash = "sha256:85ece4451f492d0c13c5dd7c13a64681a86afae63a5f347908daf103ce6d2f67" },
]

[[package]]
name = "jiter"
version = "0.10.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/jiter/0.10.0/jiter-0.10.0.tar.gz", hash = "sha256:07a7142c38aacc85194391108dc91b5b57093c978a9932bd86a36862759d9500" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/jiter/0.10.0/jiter-0.10.0-cp310-cp310-macosx_10_12_x86_64.whl", hash = "sha256:cd2fb72b02478f06a900a5782de2ef47e0396b3e1f7d5aba30daeb1fce66f303" },
    { url = "http://pypi.howbuy.pa/packages/jiter/0.10.0/jiter-0.10.0-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:32bb468e3af278f095d3fa5b90314728a6916d89ba3d0ffb726dd9bf7367285e" },
    { url = "http://pypi.howbuy.pa/packages/jiter/0.10.0/jiter-0.10.0-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:aa8b3e0068c26ddedc7abc6fac37da2d0af16b921e288a5a613f4b86f050354f" },
    { url = "http://pypi.howbuy.pa/packages/jiter/0.10.0/jiter-0.10.0-cp310-cp310-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:286299b74cc49e25cd42eea19b72aa82c515d2f2ee12d11392c56d8701f52224" },
    { url = "http://pypi.howbuy.pa/packages/jiter/0.10.0/jiter-0.10.0-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:6ed5649ceeaeffc28d87fb012d25a4cd356dcd53eff5acff1f0466b831dda2a7" },
    { url = "http://pypi.howbuy.pa/packages/jiter/0.10.0/jiter-0.10.0-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:b2ab0051160cb758a70716448908ef14ad476c3774bd03ddce075f3c1f90a3d6" },
    { url = "http://pypi.howbuy.pa/packages/jiter/0.10.0/jiter-0.10.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:03997d2f37f6b67d2f5c475da4412be584e1cec273c1cfc03d642c46db43f8cf" },
    { url = "http://pypi.howbuy.pa/packages/jiter/0.10.0/jiter-0.10.0-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:c404a99352d839fed80d6afd6c1d66071f3bacaaa5c4268983fc10f769112e90" },
    { url = "http://pypi.howbuy.pa/packages/jiter/0.10.0/jiter-0.10.0-cp310-cp310-musllinux_1_1_aarch64.whl", hash = "sha256:66e989410b6666d3ddb27a74c7e50d0829704ede652fd4c858e91f8d64b403d0" },
    { url = "http://pypi.howbuy.pa/packages/jiter/0.10.0/jiter-0.10.0-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:b532d3af9ef4f6374609a3bcb5e05a1951d3bf6190dc6b176fdb277c9bbf15ee" },
    { url = "http://pypi.howbuy.pa/packages/jiter/0.10.0/jiter-0.10.0-cp310-cp310-win32.whl", hash = "sha256:da9be20b333970e28b72edc4dff63d4fec3398e05770fb3205f7fb460eb48dd4" },
    { url = "http://pypi.howbuy.pa/packages/jiter/0.10.0/jiter-0.10.0-cp310-cp310-win_amd64.whl", hash = "sha256:f59e533afed0c5b0ac3eba20d2548c4a550336d8282ee69eb07b37ea526ee4e5" },
]

[[package]]
name = "keyring"
version = "25.6.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "importlib-metadata" },
    { name = "jaraco-classes" },
    { name = "jaraco-context" },
    { name = "jaraco-functools" },
    { name = "jeepney", marker = "sys_platform == 'linux'" },
    { name = "pywin32-ctypes", marker = "sys_platform == 'win32'" },
    { name = "secretstorage", marker = "sys_platform == 'linux'" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/keyring/25.6.0/keyring-25.6.0.tar.gz", hash = "sha256:0b39998aa941431eb3d9b0d4b2460bc773b9df6fed7621c2dfb291a7e0187a66" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/keyring/25.6.0/keyring-25.6.0-py3-none-any.whl", hash = "sha256:552a3f7af126ece7ed5c89753650eec89c7eaae8617d0aa4d9ad2b75111266bd" },
]

[[package]]
name = "ldap3"
version = "2.9.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "pyasn1" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/ldap3/2.9.1/ldap3-2.9.1.tar.gz", hash = "sha256:f3e7fc4718e3f09dda568b57100095e0ce58633bcabbed8667ce3f8fbaa4229f" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/ldap3/2.9.1/ldap3-2.9.1-py2.py3-none-any.whl", hash = "sha256:5869596fc4948797020d3f03b7939da938778a0f9e2009f7a072ccf92b8e8d70" },
]

[[package]]
name = "mantis"
version = "0.1.0"
source = { virtual = "." }
dependencies = [
    { name = "beautifulsoup4" },
    { name = "chinese-calendar" },
    { name = "coreapi" },
    { name = "coreschema" },
    { name = "croniter" },
    { name = "cryptography" },
    { name = "diskcache" },
    { name = "django" },
    { name = "django-cors-headers" },
    { name = "django-mcp-server" },
    { name = "django-python3-ldap" },
    { name = "djangorestframework" },
    { name = "djangorestframework-simplejwt" },
    { name = "mysql-connector-python" },
    { name = "mysqlclient" },
    { name = "nacos-sdk-python" },
    { name = "numpy" },
    { name = "openai" },
    { name = "openpyxl" },
    { name = "pandas" },
    { name = "paramiko" },
    { name = "peewee" },
    { name = "pyecharts" },
    { name = "pymysql" },
    { name = "python-gitlab" },
    { name = "python-jenkins" },
    { name = "python-sonarqube-api" },
    { name = "rocketmq" },
    { name = "spider-common-utils" },
    { name = "sqlalchemy" },
    { name = "treelib" },
    { name = "websocket-client" },
    { name = "xlrd" },
    { name = "xlwt" },
]

[package.dev-dependencies]
dev = [
    { name = "nexus3-cli" },
    { name = "pylint" },
]

[package.metadata]
requires-dist = [
    { name = "beautifulsoup4", specifier = "~=4.12.2" },
    { name = "chinese-calendar", specifier = ">=1.10.0" },
    { name = "coreapi", specifier = "==2.3.3" },
    { name = "coreschema", specifier = "==0.0.4" },
    { name = "croniter", specifier = "==3.0.3" },
    { name = "cryptography", specifier = "==3.4.8" },
    { name = "diskcache", specifier = "==5.6.3" },
    { name = "django", specifier = "==4.1.13" },
    { name = "django-cors-headers", specifier = "==3.14.0" },
    { name = "django-mcp-server", specifier = "==0.5.3" },
    { name = "django-python3-ldap", specifier = "==0.13.1" },
    { name = "djangorestframework", specifier = "==3.15.0" },
    { name = "djangorestframework-simplejwt", specifier = "==5.3.0" },
    { name = "mysql-connector-python", specifier = "~=8.0.33" },
    { name = "mysqlclient", specifier = "==2.1.0" },
    { name = "nacos-sdk-python", specifier = "==0.1.6" },
    { name = "numpy", specifier = "==1.26.0" },
    { name = "openai", specifier = "==1.59.4" },
    { name = "openpyxl", specifier = "==3.1.5" },
    { name = "pandas", specifier = "==2.2.3" },
    { name = "paramiko", specifier = "==2.7.2" },
    { name = "peewee", specifier = "==3.14.9" },
    { name = "pyecharts", specifier = "==1.9.1" },
    { name = "pymysql", specifier = "==1.0.2" },
    { name = "python-gitlab", specifier = "==2.5.0" },
    { name = "python-jenkins", specifier = "==1.8.0" },
    { name = "python-sonarqube-api", specifier = ">=1.3.6,<2.0.0", index = "http://pypi.howbuy.pa/simple" },
    { name = "rocketmq", specifier = "==0.4.4" },
    { name = "spider-common-utils", specifier = ">=0.1.25", index = "http://pypi.howbuy.pa/simple" },
    { name = "sqlalchemy", specifier = "==1.4.44" },
    { name = "treelib", specifier = "==1.5.5" },
    { name = "websocket-client", specifier = "~=1.6.1" },
    { name = "xlrd", specifier = "==1.2.0" },
    { name = "xlwt", specifier = "==1.3.0" },
]

[package.metadata.requires-dev]
dev = [
    { name = "nexus3-cli", specifier = "~=4.1.8" },
    { name = "pylint", specifier = ">=3.3.6" },
]

[[package]]
name = "markupsafe"
version = "3.0.2"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/markupsafe/3.0.2/markupsafe-3.0.2.tar.gz", hash = "sha256:ee55d3edf80167e48ea11a923c7386f4669df67d7994554387f84e7d8b0a2bf0" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/markupsafe/3.0.2/MarkupSafe-3.0.2-cp310-cp310-macosx_10_9_universal2.whl", hash = "sha256:7e94c425039cde14257288fd61dcfb01963e658efbc0ff54f5306b06054700f8" },
    { url = "http://pypi.howbuy.pa/packages/markupsafe/3.0.2/MarkupSafe-3.0.2-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:9e2d922824181480953426608b81967de705c3cef4d1af983af849d7bd619158" },
    { url = "http://pypi.howbuy.pa/packages/markupsafe/3.0.2/MarkupSafe-3.0.2-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:38a9ef736c01fccdd6600705b09dc574584b89bea478200c5fbf112a6b0d5579" },
    { url = "http://pypi.howbuy.pa/packages/markupsafe/3.0.2/MarkupSafe-3.0.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:bbcb445fa71794da8f178f0f6d66789a28d7319071af7a496d4d507ed566270d" },
    { url = "http://pypi.howbuy.pa/packages/markupsafe/3.0.2/MarkupSafe-3.0.2-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:57cb5a3cf367aeb1d316576250f65edec5bb3be939e9247ae594b4bcbc317dfb" },
    { url = "http://pypi.howbuy.pa/packages/markupsafe/3.0.2/MarkupSafe-3.0.2-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:3809ede931876f5b2ec92eef964286840ed3540dadf803dd570c3b7e13141a3b" },
    { url = "http://pypi.howbuy.pa/packages/markupsafe/3.0.2/MarkupSafe-3.0.2-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:e07c3764494e3776c602c1e78e298937c3315ccc9043ead7e685b7f2b8d47b3c" },
    { url = "http://pypi.howbuy.pa/packages/markupsafe/3.0.2/MarkupSafe-3.0.2-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:b424c77b206d63d500bcb69fa55ed8d0e6a3774056bdc4839fc9298a7edca171" },
    { url = "http://pypi.howbuy.pa/packages/markupsafe/3.0.2/MarkupSafe-3.0.2-cp310-cp310-win32.whl", hash = "sha256:fcabf5ff6eea076f859677f5f0b6b5c1a51e70a376b0579e0eadef8db48c6b50" },
    { url = "http://pypi.howbuy.pa/packages/markupsafe/3.0.2/MarkupSafe-3.0.2-cp310-cp310-win_amd64.whl", hash = "sha256:6af100e168aa82a50e186c82875a5893c5597a0c1ccdb0d8b40240b1f28b969a" },
]

[[package]]
name = "mccabe"
version = "0.7.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/mccabe/0.7.0/mccabe-0.7.0.tar.gz", hash = "sha256:348e0240c33b60bbdf4e523192ef919f28cb2c3d7d5c7794f74009290f236325" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/mccabe/0.7.0/mccabe-0.7.0-py2.py3-none-any.whl", hash = "sha256:6c2d30ab6be0e4a46919781807b4f0d834ebdd6c6e3dca0bda5a15f863427b6e" },
]

[[package]]
name = "mcp"
version = "1.9.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "anyio" },
    { name = "httpx" },
    { name = "httpx-sse" },
    { name = "pydantic" },
    { name = "pydantic-settings" },
    { name = "python-multipart" },
    { name = "sse-starlette" },
    { name = "starlette" },
    { name = "uvicorn", marker = "sys_platform != 'emscripten'" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/mcp/1.9.3/mcp-1.9.3.tar.gz", hash = "sha256:587ba38448e81885e5d1b84055cfcc0ca56d35cd0c58f50941cab01109405388" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/mcp/1.9.3/mcp-1.9.3-py3-none-any.whl", hash = "sha256:69b0136d1ac9927402ed4cf221d4b8ff875e7132b0b06edd446448766f34f9b9" },
]

[[package]]
name = "more-itertools"
version = "10.7.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/more-itertools/10.7.0/more_itertools-10.7.0.tar.gz", hash = "sha256:9fddd5403be01a94b204faadcff459ec3568cf110265d3c54323e1e866ad29d3" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/more-itertools/10.7.0/more_itertools-10.7.0-py3-none-any.whl", hash = "sha256:d43980384673cb07d2f7d2d918c616b30c659c089ee23953f601d6609c67510e" },
]

[[package]]
name = "multi-key-dict"
version = "2.0.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/multi-key-dict/2.0.3/multi_key_dict-2.0.3.tar.gz", hash = "sha256:deebdec17aa30a1c432cb3f437e81f8621e1c0542a0c0617a74f71e232e9939e" }

[[package]]
name = "mysql-connector-python"
version = "8.0.33"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "protobuf" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/mysql-connector-python/8.0.33/mysql-connector-python-8.0.33.tar.gz", hash = "sha256:9775331fa60b5d5a6925781d77eee4384e2b54a12dea694ffdefd1cf1a9c0fdb" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/mysql-connector-python/8.0.33/mysql_connector_python-8.0.33-cp310-cp310-macosx_12_0_arm64.whl", hash = "sha256:241483065ad062256985e082e3cbb3e7d1d6d2275cee17c66d22525b09096201" },
    { url = "http://pypi.howbuy.pa/packages/mysql-connector-python/8.0.33/mysql_connector_python-8.0.33-cp310-cp310-macosx_12_0_x86_64.whl", hash = "sha256:9f5eb33e29742c5f8ef23df2d3f0de0e46f4325e4324016e15aba7f8665a68c0" },
    { url = "http://pypi.howbuy.pa/packages/mysql-connector-python/8.0.33/mysql_connector_python-8.0.33-cp310-cp310-manylinux1_i686.whl", hash = "sha256:4c82fb70f44f2469c0879434c1d8ee3162f56a40cc8f5ca1cc4d97f06c84cd43" },
    { url = "http://pypi.howbuy.pa/packages/mysql-connector-python/8.0.33/mysql_connector_python-8.0.33-cp310-cp310-manylinux1_x86_64.whl", hash = "sha256:016662c6252f2c5f47805d9168187be1316d0c1d7109f9fe668482c3d6e5711d" },
    { url = "http://pypi.howbuy.pa/packages/mysql-connector-python/8.0.33/mysql_connector_python-8.0.33-cp310-cp310-win_amd64.whl", hash = "sha256:46ff8a10c13f39996d60f45c30cf2ea15e883bc71d58259ed2fea0a5a6fb93a3" },
    { url = "http://pypi.howbuy.pa/packages/mysql-connector-python/8.0.33/mysql_connector_python-8.0.33-py2.py3-none-any.whl", hash = "sha256:c20a85a69af41d2d7d5cf52106f0b9473775819d189487c6ff3d3f3946931ca2" },
]

[[package]]
name = "mysqlclient"
version = "2.1.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/mysqlclient/2.1.0/mysqlclient-2.1.0.tar.gz", hash = "sha256:973235686f1b720536d417bf0a0d39b4ab3d5086b2b6ad5e6752393428c02b12" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/mysqlclient/2.1.0/mysqlclient-2.1.0-cp310-cp310-win_amd64.whl", hash = "sha256:02c8826e6add9b20f4cb12dcf016485f7b1d6e30356a1204d05431867a1b3947" },
]

[[package]]
name = "nacos-sdk-python"
version = "0.1.6"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/nacos-sdk-python/0.1.6/nacos-sdk-python-0.1.6.tar.gz", hash = "sha256:0027aed8a199d7d421dbff6eb99bc955efe53286b660f5aa40a01dd09f566153" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/nacos-sdk-python/0.1.6/nacos_sdk_python-0.1.6-py2.py3-none-any.whl", hash = "sha256:e5bdcdf3788d24ae259fa3816cd3443ab08fc9f526930bf86b7c0a55202494ee" },
]

[[package]]
name = "nexus3-cli"
version = "4.1.9"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "click" },
    { name = "click-aliases" },
    { name = "inflect" },
    { name = "requests" },
    { name = "semver" },
    { name = "texttable" },
    { name = "twine" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/nexus3-cli/4.1.9/nexus3-cli-4.1.9.tar.gz", hash = "sha256:8b632cbe7b9dfcc9a5755571d74277027f518e25605be6dc359c7c07afd17089" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/nexus3-cli/4.1.9/nexus3_cli-4.1.9-py3-none-any.whl", hash = "sha256:095f34914e5552caf312136a65dff8ab8df2b1cabc5a7ac9eb7c1724c183f815" },
]

[[package]]
name = "nh3"
version = "0.2.21"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/nh3/0.2.21/nh3-0.2.21.tar.gz", hash = "sha256:4990e7ee6a55490dbf00d61a6f476c9a3258e31e711e13713b2ea7d6616f670e" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-macosx_10_12_x86_64.macosx_11_0_arm64.macosx_10_12_universal2.whl", hash = "sha256:a772dec5b7b7325780922dd904709f0f5f3a79fbf756de5291c01370f6df0967" },
    { url = "http://pypi.howbuy.pa/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:d002b648592bf3033adfd875a48f09b8ecc000abd7f6a8769ed86b6ccc70c759" },
    { url = "http://pypi.howbuy.pa/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:2a5174551f95f2836f2ad6a8074560f261cf9740a48437d6151fd2d4d7d617ab" },
    { url = "http://pypi.howbuy.pa/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-manylinux_2_17_ppc64.manylinux2014_ppc64.whl", hash = "sha256:b8d55ea1fc7ae3633d758a92aafa3505cd3cc5a6e40470c9164d54dff6f96d42" },
    { url = "http://pypi.howbuy.pa/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:6ae319f17cd8960d0612f0f0ddff5a90700fa71926ca800e9028e7851ce44a6f" },
    { url = "http://pypi.howbuy.pa/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:63ca02ac6f27fc80f9894409eb61de2cb20ef0a23740c7e29f9ec827139fa578" },
    { url = "http://pypi.howbuy.pa/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:a5f77e62aed5c4acad635239ac1290404c7e940c81abe561fd2af011ff59f585" },
    { url = "http://pypi.howbuy.pa/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:087ffadfdcd497658c3adc797258ce0f06be8a537786a7217649fc1c0c60c293" },
    { url = "http://pypi.howbuy.pa/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:ac7006c3abd097790e611fe4646ecb19a8d7f2184b882f6093293b8d9b887431" },
    { url = "http://pypi.howbuy.pa/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-musllinux_1_2_armv7l.whl", hash = "sha256:6141caabe00bbddc869665b35fc56a478eb774a8c1dfd6fba9fe1dfdf29e6efa" },
    { url = "http://pypi.howbuy.pa/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-musllinux_1_2_i686.whl", hash = "sha256:20979783526641c81d2f5bfa6ca5ccca3d1e4472474b162c6256745fbfe31cd1" },
    { url = "http://pypi.howbuy.pa/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:a7ea28cd49293749d67e4fcf326c554c83ec912cd09cd94aa7ec3ab1921c8283" },
    { url = "http://pypi.howbuy.pa/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-win32.whl", hash = "sha256:6c9c30b8b0d291a7c5ab0967ab200598ba33208f754f2f4920e9343bdd88f79a" },
    { url = "http://pypi.howbuy.pa/packages/nh3/0.2.21/nh3-0.2.21-cp38-abi3-win_amd64.whl", hash = "sha256:bb0014948f04d7976aabae43fcd4cb7f551f9f8ce785a4c9ef66e6c2590f8629" },
]

[[package]]
name = "numpy"
version = "1.26.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/numpy/1.26.0/numpy-1.26.0.tar.gz", hash = "sha256:f93fc78fe8bf15afe2b8d6b6499f1c73953169fad1e9a8dd086cdff3190e7fdf" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/numpy/1.26.0/numpy-1.26.0-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:f8db2f125746e44dce707dd44d4f4efeea8d7e2b43aace3f8d1f235cfa2733dd" },
    { url = "http://pypi.howbuy.pa/packages/numpy/1.26.0/numpy-1.26.0-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:0621f7daf973d34d18b4e4bafb210bbaf1ef5e0100b5fa750bd9cde84c7ac292" },
    { url = "http://pypi.howbuy.pa/packages/numpy/1.26.0/numpy-1.26.0-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:51be5f8c349fdd1a5568e72713a21f518e7d6707bcf8503b528b88d33b57dc68" },
    { url = "http://pypi.howbuy.pa/packages/numpy/1.26.0/numpy-1.26.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:767254ad364991ccfc4d81b8152912e53e103ec192d1bb4ea6b1f5a7117040be" },
    { url = "http://pypi.howbuy.pa/packages/numpy/1.26.0/numpy-1.26.0-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:436c8e9a4bdeeee84e3e59614d38c3dbd3235838a877af8c211cfcac8a80b8d3" },
    { url = "http://pypi.howbuy.pa/packages/numpy/1.26.0/numpy-1.26.0-cp310-cp310-win32.whl", hash = "sha256:c2e698cb0c6dda9372ea98a0344245ee65bdc1c9dd939cceed6bb91256837896" },
    { url = "http://pypi.howbuy.pa/packages/numpy/1.26.0/numpy-1.26.0-cp310-cp310-win_amd64.whl", hash = "sha256:09aaee96c2cbdea95de76ecb8a586cb687d281c881f5f17bfc0fb7f5890f6b91" },
]

[[package]]
name = "openai"
version = "1.59.4"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "anyio" },
    { name = "distro" },
    { name = "httpx" },
    { name = "jiter" },
    { name = "pydantic" },
    { name = "sniffio" },
    { name = "tqdm" },
    { name = "typing-extensions" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/openai/1.59.4/openai-1.59.4.tar.gz", hash = "sha256:b946dc5a2308dc1e03efbda80bf1cd64b6053b536851ad519f57ee44401663d2" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/openai/1.59.4/openai-1.59.4-py3-none-any.whl", hash = "sha256:82113498699998e98104f87c19a890e82df9b01251a0395484360575d3a1d98a" },
]

[[package]]
name = "openpyxl"
version = "3.1.5"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "et-xmlfile" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/openpyxl/3.1.5/openpyxl-3.1.5.tar.gz", hash = "sha256:cf0e3cf56142039133628b5acffe8ef0c12bc902d2aadd3e0fe5878dc08d1050" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/openpyxl/3.1.5/openpyxl-3.1.5-py2.py3-none-any.whl", hash = "sha256:5282c12b107bffeef825f4617dc029afaf41d0ea60823bbb665ef3079dc79de2" },
]

[[package]]
name = "pandas"
version = "2.2.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "numpy" },
    { name = "python-dateutil" },
    { name = "pytz" },
    { name = "tzdata" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/pandas/2.2.3/pandas-2.2.3.tar.gz", hash = "sha256:4f18ba62b61d7e192368b84517265a99b4d7ee8912f8708660fb4a366cc82667" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pandas/2.2.3/pandas-2.2.3-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:1948ddde24197a0f7add2bdc4ca83bf2b1ef84a1bc8ccffd95eda17fd836ecb5" },
    { url = "http://pypi.howbuy.pa/packages/pandas/2.2.3/pandas-2.2.3-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:381175499d3802cde0eabbaf6324cce0c4f5d52ca6f8c377c29ad442f50f6348" },
    { url = "http://pypi.howbuy.pa/packages/pandas/2.2.3/pandas-2.2.3-cp310-cp310-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:d9c45366def9a3dd85a6454c0e7908f2b3b8e9c138f5dc38fed7ce720d8453ed" },
    { url = "http://pypi.howbuy.pa/packages/pandas/2.2.3/pandas-2.2.3-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:86976a1c5b25ae3f8ccae3a5306e443569ee3c3faf444dfd0f41cda24667ad57" },
    { url = "http://pypi.howbuy.pa/packages/pandas/2.2.3/pandas-2.2.3-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:b8661b0238a69d7aafe156b7fa86c44b881387509653fdf857bebc5e4008ad42" },
    { url = "http://pypi.howbuy.pa/packages/pandas/2.2.3/pandas-2.2.3-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:37e0aced3e8f539eccf2e099f65cdb9c8aa85109b0be6e93e2baff94264bdc6f" },
    { url = "http://pypi.howbuy.pa/packages/pandas/2.2.3/pandas-2.2.3-cp310-cp310-win_amd64.whl", hash = "sha256:56534ce0746a58afaf7942ba4863e0ef81c9c50d3f0ae93e9497d6a41a057645" },
]

[[package]]
name = "paramiko"
version = "2.7.2"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "bcrypt" },
    { name = "cryptography" },
    { name = "pynacl" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/paramiko/2.7.2/paramiko-2.7.2.tar.gz", hash = "sha256:7f36f4ba2c0d81d219f4595e35f70d56cc94f9ac40a6acdf51d6ca210ce65035" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/paramiko/2.7.2/paramiko-2.7.2-py2.py3-none-any.whl", hash = "sha256:4f3e316fef2ac628b05097a637af35685183111d4bc1b5979bd397c2ab7b5898" },
]

[[package]]
name = "pbr"
version = "6.1.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "setuptools" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/pbr/6.1.1/pbr-6.1.1.tar.gz", hash = "sha256:93ea72ce6989eb2eed99d0f75721474f69ad88128afdef5ac377eb797c4bf76b" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pbr/6.1.1/pbr-6.1.1-py2.py3-none-any.whl", hash = "sha256:38d4daea5d9fa63b3f626131b9d34947fd0c8be9b05a29276870580050a25a76" },
]

[[package]]
name = "peewee"
version = "3.14.9"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/peewee/3.14.9/peewee-3.14.9.tar.gz", hash = "sha256:69c1b88dc89b184231cc1ce6df241075aca5cec43e89749cc4a63108f9ceea47" }

[[package]]
name = "pkginfo"
version = "1.12.1.2"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/pkginfo/1.12.1.2/pkginfo-1.12.1.2.tar.gz", hash = "sha256:5cd957824ac36f140260964eba3c6be6442a8359b8c48f4adf90210f33a04b7b" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pkginfo/1.12.1.2/pkginfo-1.12.1.2-py3-none-any.whl", hash = "sha256:c783ac885519cab2c34927ccfa6bf64b5a704d7c69afaea583dd9b7afe969343" },
]

[[package]]
name = "platformdirs"
version = "4.3.8"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/platformdirs/4.3.8/platformdirs-4.3.8.tar.gz", hash = "sha256:3d512d96e16bcb959a814c9f348431070822a6496326a4be0911c40b5a74c2bc" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/platformdirs/4.3.8/platformdirs-4.3.8-py3-none-any.whl", hash = "sha256:ff7059bb7eb1179e2685604f4aaf157cfd9535242bd23742eadc3c13542139b4" },
]

[[package]]
name = "prettytable"
version = "3.16.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "wcwidth" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/prettytable/3.16.0/prettytable-3.16.0.tar.gz", hash = "sha256:3c64b31719d961bf69c9a7e03d0c1e477320906a98da63952bc6698d6164ff57" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/prettytable/3.16.0/prettytable-3.16.0-py3-none-any.whl", hash = "sha256:b5eccfabb82222f5aa46b798ff02a8452cf530a352c31bddfa29be41242863aa" },
]

[[package]]
name = "protobuf"
version = "3.20.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/protobuf/3.20.3/protobuf-3.20.3.tar.gz", hash = "sha256:2e3427429c9cffebf259491be0af70189607f365c2f41c7c3764af6f337105f2" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/protobuf/3.20.3/protobuf-3.20.3-cp310-cp310-manylinux2014_aarch64.whl", hash = "sha256:f4bd856d702e5b0d96a00ec6b307b0f51c1982c2bf9c0052cf9019e9a544ba99" },
    { url = "http://pypi.howbuy.pa/packages/protobuf/3.20.3/protobuf-3.20.3-cp310-cp310-manylinux_2_12_x86_64.manylinux2010_x86_64.whl", hash = "sha256:9aae4406ea63d825636cc11ffb34ad3379335803216ee3a856787bcf5ccc751e" },
    { url = "http://pypi.howbuy.pa/packages/protobuf/3.20.3/protobuf-3.20.3-cp310-cp310-win32.whl", hash = "sha256:28545383d61f55b57cf4df63eebd9827754fd2dc25f80c5253f9184235db242c" },
    { url = "http://pypi.howbuy.pa/packages/protobuf/3.20.3/protobuf-3.20.3-cp310-cp310-win_amd64.whl", hash = "sha256:67a3598f0a2dcbc58d02dd1928544e7d88f764b47d4a286202913f0b2801c2e7" },
    { url = "http://pypi.howbuy.pa/packages/protobuf/3.20.3/protobuf-3.20.3-py2.py3-none-any.whl", hash = "sha256:a7ca6d488aa8ff7f329d4c545b2dbad8ac31464f1d8b1c87ad1346717731e4db" },
]

[[package]]
name = "pyasn1"
version = "0.4.8"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/pyasn1/0.4.8/pyasn1-0.4.8.tar.gz", hash = "sha256:aef77c9fb94a3ac588e87841208bdec464471d9871bd5050a287cc9a475cd0ba" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pyasn1/0.4.8/pyasn1-0.4.8-py2.py3-none-any.whl", hash = "sha256:39c7e2ec30515947ff4e87fb6f456dfc6e84857d34be479c9d4a4ba4bf46aa5d" },
]

[[package]]
name = "pycparser"
version = "2.22"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/pycparser/2.22/pycparser-2.22.tar.gz", hash = "sha256:491c8be9c040f5390f5bf44a5b07752bd07f56edf992381b05c701439eec10f6" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pycparser/2.22/pycparser-2.22-py3-none-any.whl", hash = "sha256:c3702b6d3dd8c7abc1afa565d7e63d53a1d0bd86cdc24edd75470f4de499cfcc" },
]

[[package]]
name = "pydantic"
version = "2.11.5"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "annotated-types" },
    { name = "pydantic-core" },
    { name = "typing-extensions" },
    { name = "typing-inspection" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/pydantic/2.11.5/pydantic-2.11.5.tar.gz", hash = "sha256:7f853db3d0ce78ce8bbb148c401c2cdd6431b3473c0cdff2755c7690952a7b7a" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pydantic/2.11.5/pydantic-2.11.5-py3-none-any.whl", hash = "sha256:f9c26ba06f9747749ca1e5c94d6a85cb84254577553c8785576fd38fa64dc0f7" },
]

[[package]]
name = "pydantic-core"
version = "2.33.2"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2.tar.gz", hash = "sha256:7cb8bc3605c29176e1b105350d2e6474142d7c1bd1d9327c4a9bdb46bf827acc" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp310-cp310-macosx_10_12_x86_64.whl", hash = "sha256:2b3d326aaef0c0399d9afffeb6367d5e26ddc24d351dbc9c636840ac355dc5d8" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:0e5b2671f05ba48b94cb90ce55d8bdcaaedb8ba00cc5359f6810fc918713983d" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:0069c9acc3f3981b9ff4cdfaf088e98d83440a4c7ea1bc07460af3d4dc22e72d" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp310-cp310-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:d53b22f2032c42eaaf025f7c40c2e3b94568ae077a606f006d206a463bc69572" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:0405262705a123b7ce9f0b92f123334d67b70fd1f20a9372b907ce1080c7ba02" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:4b25d91e288e2c4e0662b8038a28c6a07eaac3e196cfc4ff69de4ea3db992a1b" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:6bdfe4b3789761f3bcb4b1ddf33355a71079858958e3a552f16d5af19768fef2" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:efec8db3266b76ef9607c2c4c419bdb06bf335ae433b80816089ea7585816f6a" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp310-cp310-musllinux_1_1_aarch64.whl", hash = "sha256:031c57d67ca86902726e0fae2214ce6770bbe2f710dc33063187a68744a5ecac" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp310-cp310-musllinux_1_1_armv7l.whl", hash = "sha256:f8de619080e944347f5f20de29a975c2d815d9ddd8be9b9b7268e2e3ef68605a" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:73662edf539e72a9440129f231ed3757faab89630d291b784ca99237fb94db2b" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp310-cp310-win32.whl", hash = "sha256:0a39979dcbb70998b0e505fb1556a1d550a0781463ce84ebf915ba293ccb7e22" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-cp310-cp310-win_amd64.whl", hash = "sha256:b0379a2b24882fef529ec3b4987cb5d003b9cda32256024e6fe1586ac45fc640" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp310-pypy310_pp73-macosx_10_12_x86_64.whl", hash = "sha256:5c4aa4e82353f65e548c476b37e64189783aa5384903bfea4f41580f255fddfa" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp310-pypy310_pp73-macosx_11_0_arm64.whl", hash = "sha256:d946c8bf0d5c24bf4fe333af284c59a19358aa3ec18cb3dc4370080da1e8ad29" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp310-pypy310_pp73-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:87b31b6846e361ef83fedb187bb5b4372d0da3f7e28d85415efa92d6125d6e6d" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp310-pypy310_pp73-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:aa9d91b338f2df0508606f7009fde642391425189bba6d8c653afd80fd6bb64e" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp310-pypy310_pp73-manylinux_2_5_i686.manylinux1_i686.whl", hash = "sha256:2058a32994f1fde4ca0480ab9d1e75a0e8c87c22b53a3ae66554f9af78f2fe8c" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp310-pypy310_pp73-musllinux_1_1_aarch64.whl", hash = "sha256:0e03262ab796d986f978f79c943fc5f620381be7287148b8010b4097f79a39ec" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp310-pypy310_pp73-musllinux_1_1_armv7l.whl", hash = "sha256:1a8695a8d00c73e50bff9dfda4d540b7dee29ff9b8053e38380426a85ef10052" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp310-pypy310_pp73-musllinux_1_1_x86_64.whl", hash = "sha256:fa754d1850735a0b0e03bcffd9d4b4343eb417e47196e4485d9cca326073a42c" },
    { url = "http://pypi.howbuy.pa/packages/pydantic-core/2.33.2/pydantic_core-2.33.2-pp310-pypy310_pp73-win_amd64.whl", hash = "sha256:a11c8d26a50bfab49002947d3d237abe4d9e4b5bdc8846a63537b6488e197808" },
]

[[package]]
name = "pydantic-settings"
version = "2.9.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "pydantic" },
    { name = "python-dotenv" },
    { name = "typing-inspection" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/pydantic-settings/2.9.1/pydantic_settings-2.9.1.tar.gz", hash = "sha256:c509bf79d27563add44e8446233359004ed85066cd096d8b510f715e6ef5d268" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pydantic-settings/2.9.1/pydantic_settings-2.9.1-py3-none-any.whl", hash = "sha256:59b4f431b1defb26fe620c71a7d3968a710d719f5f4cdbbdb7926edeb770f6ef" },
]

[[package]]
name = "pyecharts"
version = "1.9.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "jinja2" },
    { name = "prettytable" },
    { name = "simplejson" },
]
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pyecharts/1.9.1/pyecharts-1.9.1-py3-none-any.whl", hash = "sha256:5a4cab66bacfc644919ef652bc9e9985f532aba832af9f2992e8e3802eb718e2" },
]

[[package]]
name = "pygments"
version = "2.19.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/pygments/2.19.1/pygments-2.19.1.tar.gz", hash = "sha256:61c16d2a8576dc0649d9f39e089b5f02bcd27fba10d8fb4dcc28173f7a45151f" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pygments/2.19.1/pygments-2.19.1-py3-none-any.whl", hash = "sha256:9ea1544ad55cecf4b8242fab6dd35a93bbce657034b0611ee383099054ab6d8c" },
]

[[package]]
name = "pyjwt"
version = "2.10.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/pyjwt/2.10.1/pyjwt-2.10.1.tar.gz", hash = "sha256:3cc5772eb20009233caf06e9d8a0577824723b44e6648ee0a2aedb6cf9381953" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pyjwt/2.10.1/PyJWT-2.10.1-py3-none-any.whl", hash = "sha256:dcdd193e30abefd5debf142f9adfcdd2b58004e644f25406ffaebd50bd98dacb" },
]

[[package]]
name = "pylint"
version = "3.3.7"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "astroid" },
    { name = "colorama", marker = "sys_platform == 'win32'" },
    { name = "dill" },
    { name = "isort" },
    { name = "mccabe" },
    { name = "platformdirs" },
    { name = "tomli" },
    { name = "tomlkit" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/pylint/3.3.7/pylint-3.3.7.tar.gz", hash = "sha256:2b11de8bde49f9c5059452e0c310c079c746a0a8eeaa789e5aa966ecc23e4559" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pylint/3.3.7/pylint-3.3.7-py3-none-any.whl", hash = "sha256:43860aafefce92fca4cf6b61fe199cdc5ae54ea28f9bf4cd49de267b5195803d" },
]

[[package]]
name = "pymysql"
version = "1.0.2"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/pymysql/1.0.2/PyMySQL-1.0.2.tar.gz", hash = "sha256:816927a350f38d56072aeca5dfb10221fe1dc653745853d30a216637f5d7ad36" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pymysql/1.0.2/PyMySQL-1.0.2-py3-none-any.whl", hash = "sha256:41fc3a0c5013d5f039639442321185532e3e2c8924687abe6537de157d403641" },
]

[[package]]
name = "pynacl"
version = "1.5.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "cffi" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/pynacl/1.5.0/PyNaCl-1.5.0.tar.gz", hash = "sha256:8ac7448f09ab85811607bdd21ec2464495ac8b7c66d146bf545b0f08fb9220ba" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-macosx_10_10_universal2.whl", hash = "sha256:401002a4aaa07c9414132aaed7f6836ff98f59277a234704ff66878c2ee4a0d1" },
    { url = "http://pypi.howbuy.pa/packages/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.manylinux_2_24_aarch64.whl", hash = "sha256:52cb72a79269189d4e0dc537556f4740f7f0a9ec41c1322598799b0bdad4ef92" },
    { url = "http://pypi.howbuy.pa/packages/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:a36d4a9dda1f19ce6e03c9a784a2921a4b726b02e1c736600ca9c22029474394" },
    { url = "http://pypi.howbuy.pa/packages/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_24_x86_64.whl", hash = "sha256:0c84947a22519e013607c9be43706dd42513f9e6ae5d39d3613ca1e142fba44d" },
    { url = "http://pypi.howbuy.pa/packages/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:06b8f6fa7f5de8d5d2f7573fe8c863c051225a27b61e6860fd047b1775807858" },
    { url = "http://pypi.howbuy.pa/packages/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-musllinux_1_1_aarch64.whl", hash = "sha256:a422368fc821589c228f4c49438a368831cb5bbc0eab5ebe1d7fac9dded6567b" },
    { url = "http://pypi.howbuy.pa/packages/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-musllinux_1_1_x86_64.whl", hash = "sha256:61f642bf2378713e2c2e1de73444a3778e5f0a38be6fee0fe532fe30060282ff" },
    { url = "http://pypi.howbuy.pa/packages/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-win32.whl", hash = "sha256:e46dae94e34b085175f8abb3b0aaa7da40767865ac82c928eeb9e57e1ea8a543" },
    { url = "http://pypi.howbuy.pa/packages/pynacl/1.5.0/PyNaCl-1.5.0-cp36-abi3-win_amd64.whl", hash = "sha256:20f42270d27e1b6a29f54032090b972d97f0a1b0948cc52392041ef7831fee93" },
]

[[package]]
name = "python-dateutil"
version = "2.9.0.post0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "six" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/python-dateutil/2.9.0.post0/python-dateutil-2.9.0.post0.tar.gz", hash = "sha256:37dd54208da7e1cd875388217d5e00ebd4179249f90fb72437e91a35459a0ad3" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/python-dateutil/2.9.0.post0/python_dateutil-2.9.0.post0-py2.py3-none-any.whl", hash = "sha256:a8b2bc7bffae282281c8140a97d3aa9c14da0b136dfe83f850eea9a5f7470427" },
]

[[package]]
name = "python-dotenv"
version = "1.1.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/python-dotenv/1.1.0/python_dotenv-1.1.0.tar.gz", hash = "sha256:41f90bc6f5f177fb41f53e87666db362025010eb28f60a01c9143bfa33a2b2d5" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/python-dotenv/1.1.0/python_dotenv-1.1.0-py3-none-any.whl", hash = "sha256:d7c01d9e2293916c18baf562d95698754b0dbbb5e74d457c45d4f6561fb9d55d" },
]

[[package]]
name = "python-gitlab"
version = "2.5.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "requests" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/python-gitlab/2.5.0/python-gitlab-2.5.0.tar.gz", hash = "sha256:68b42aafd4b620ab2534ff78a52584c7f799e4e55d5ac297eab4263066e6f74b" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/python-gitlab/2.5.0/python_gitlab-2.5.0-py3-none-any.whl", hash = "sha256:61270e1f8ac24fe248f41834c840c6465d2e0b216493d3508d296a02960122c0" },
]

[[package]]
name = "python-jenkins"
version = "1.8.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "multi-key-dict" },
    { name = "pbr" },
    { name = "requests" },
    { name = "setuptools" },
    { name = "six" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/python-jenkins/1.8.0/python-jenkins-1.8.0.tar.gz", hash = "sha256:0180a5463f68e2e0110f382b4248d2284bc68481db4a16fcbf61f4f55801c31f" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/python-jenkins/1.8.0/python_jenkins-1.8.0-py3-none-any.whl", hash = "sha256:f40f54505da1be96160ef571363903c8d2d1eae8fa706dbe701211833f440b80" },
]

[[package]]
name = "python-multipart"
version = "0.0.20"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/python-multipart/0.0.20/python_multipart-0.0.20.tar.gz", hash = "sha256:8dd0cab45b8e23064ae09147625994d090fa46f5b0d1e13af944c331a7fa9d13" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/python-multipart/0.0.20/python_multipart-0.0.20-py3-none-any.whl", hash = "sha256:8a62d3a8335e06589fe01f2a3e178cdcc632f3fbe0d492ad9ee0ec35aab1f104" },
]

[[package]]
name = "python-sonarqube-api"
version = "1.3.6"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "requests" },
]
wheels = [
    { url = "http://pypi.howbuy.pa/packages/python-sonarqube-api/1.3.6/python_sonarqube_api-1.3.6-py3-none-any.whl", hash = "sha256:971196c4f9b2aea66ad09deb4f0370d7057cfc4d2fd40f602f2ea44457cba6b8" },
]

[[package]]
name = "pytz"
version = "2025.2"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/pytz/2025.2/pytz-2025.2.tar.gz", hash = "sha256:360b9e3dbb49a209c21ad61809c7fb453643e048b38924c765813546746e81c3" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pytz/2025.2/pytz-2025.2-py2.py3-none-any.whl", hash = "sha256:5ddf76296dd8c44c26eb8f4b6f35488f3ccbf6fbbd7adee0b7262d43f0ec2f00" },
]

[[package]]
name = "pywin32-ctypes"
version = "0.2.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/pywin32-ctypes/0.2.3/pywin32-ctypes-0.2.3.tar.gz", hash = "sha256:d162dc04946d704503b2edc4d55f3dba5c1d539ead017afa00142c38b9885755" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/pywin32-ctypes/0.2.3/pywin32_ctypes-0.2.3-py3-none-any.whl", hash = "sha256:8a1513379d709975552d202d942d9837758905c8d01eb82b8bcc30918929e7b8" },
]

[[package]]
name = "readme-renderer"
version = "44.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "docutils" },
    { name = "nh3" },
    { name = "pygments" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/readme-renderer/44.0/readme_renderer-44.0.tar.gz", hash = "sha256:8712034eabbfa6805cacf1402b4eeb2a73028f72d1166d6f5cb7f9c047c5d1e1" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/readme-renderer/44.0/readme_renderer-44.0-py3-none-any.whl", hash = "sha256:2fbca89b81a08526aadf1357a8c2ae889ec05fb03f5da67f9769c9a592166151" },
]

[[package]]
name = "requests"
version = "2.32.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "certifi" },
    { name = "charset-normalizer" },
    { name = "idna" },
    { name = "urllib3" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/requests/2.32.3/requests-2.32.3.tar.gz", hash = "sha256:55365417734eb18255590a9ff9eb97e9e1da868d4ccd6402399eaf68af20a760" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/requests/2.32.3/requests-2.32.3-py3-none-any.whl", hash = "sha256:70761cfe03c773ceb22aa2f671b4757976145175cdfca038c02654d061d6dcc6" },
]

[[package]]
name = "requests-toolbelt"
version = "1.0.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "requests" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/requests-toolbelt/1.0.0/requests-toolbelt-1.0.0.tar.gz", hash = "sha256:7681a0a3d047012b5bdc0ee37d7f8f07ebe76ab08caeccfc3921ce23c88d5bc6" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/requests-toolbelt/1.0.0/requests_toolbelt-1.0.0-py2.py3-none-any.whl", hash = "sha256:cccfdd665f0a24fcf4726e690f65639d272bb0637b9b92dfd91a5568ccf6bd06" },
]

[[package]]
name = "rfc3986"
version = "2.0.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/rfc3986/2.0.0/rfc3986-2.0.0.tar.gz", hash = "sha256:97aacf9dbd4bfd829baad6e6309fa6573aaf1be3f6fa735c8ab05e46cecb261c" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/rfc3986/2.0.0/rfc3986-2.0.0-py2.py3-none-any.whl", hash = "sha256:50b1502b60e289cb37883f3dfd34532b8873c7de9f49bb546641ce9cbd256ebd" },
]

[[package]]
name = "rocketmq"
version = "0.4.4"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/rocketmq/0.4.4/rocketmq-0.4.4.tar.gz", hash = "sha256:ac10affce56ecfbfac82efe5a22c050c36749365c6c080f22c9d486798d537dc" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/rocketmq/0.4.4/rocketmq-0.4.4-py2.py3-none-macosx_10_14_x86_64.whl", hash = "sha256:5ec68221dfaaffc386d5331d54d2eeb1d6c63325eb4cea4698e21d3b97310c21" },
    { url = "http://pypi.howbuy.pa/packages/rocketmq/0.4.4/rocketmq-0.4.4-py2.py3-none-manylinux1_x86_64.whl", hash = "sha256:19a10219f5074d1050e88cdbdfed3903a374b3f83c0dd2c8773e1e93e33ad02d" },
]

[[package]]
name = "secretstorage"
version = "3.3.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "cryptography" },
    { name = "jeepney" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/secretstorage/3.3.3/SecretStorage-3.3.3.tar.gz", hash = "sha256:2403533ef369eca6d2ba81718576c5e0f564d5cca1b58f73a8b23e7d4eeebd77" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/secretstorage/3.3.3/SecretStorage-3.3.3-py3-none-any.whl", hash = "sha256:f356e6628222568e3af06f2eba8df495efa13b3b63081dafd4f7d9a7b7bc9f99" },
]

[[package]]
name = "semver"
version = "2.13.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/semver/2.13.0/semver-2.13.0.tar.gz", hash = "sha256:fa0fe2722ee1c3f57eac478820c3a5ae2f624af8264cbdf9000c980ff7f75e3f" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/semver/2.13.0/semver-2.13.0-py2.py3-none-any.whl", hash = "sha256:ced8b23dceb22134307c1b8abfa523da14198793d9787ac838e70e29e77458d4" },
]

[[package]]
name = "setuptools"
version = "65.7.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/setuptools/65.7.0/setuptools-65.7.0.tar.gz", hash = "sha256:4d3c92fac8f1118bb77a22181355e29c239cabfe2b9effdaa665c66b711136d7" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/setuptools/65.7.0/setuptools-65.7.0-py3-none-any.whl", hash = "sha256:8ab4f1dbf2b4a65f7eec5ad0c620e84c34111a68d3349833494b9088212214dd" },
]

[[package]]
name = "simplejson"
version = "3.20.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/simplejson/3.20.1/simplejson-3.20.1.tar.gz", hash = "sha256:e64139b4ec4f1f24c142ff7dcafe55a22b811a74d86d66560c8815687143037d" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/simplejson/3.20.1/simplejson-3.20.1-cp310-cp310-macosx_10_9_universal2.whl", hash = "sha256:e580aa65d5f6c3bf41b9b4afe74be5d5ddba9576701c107c772d936ea2b5043a" },
    { url = "http://pypi.howbuy.pa/packages/simplejson/3.20.1/simplejson-3.20.1-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:4a586ce4f78cec11f22fe55c5bee0f067e803aab9bad3441afe2181693b5ebb5" },
    { url = "http://pypi.howbuy.pa/packages/simplejson/3.20.1/simplejson-3.20.1-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:74a1608f9e6e8c27a4008d70a54270868306d80ed48c9df7872f9f4b8ac87808" },
    { url = "http://pypi.howbuy.pa/packages/simplejson/3.20.1/simplejson-3.20.1-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:03db8cb64154189a92a7786209f24e391644f3a3fa335658be2df2af1960b8d8" },
    { url = "http://pypi.howbuy.pa/packages/simplejson/3.20.1/simplejson-3.20.1-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:eea7e2b7d858f6fdfbf0fe3cb846d6bd8a45446865bc09960e51f3d473c2271b" },
    { url = "http://pypi.howbuy.pa/packages/simplejson/3.20.1/simplejson-3.20.1-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:e66712b17d8425bb7ff8968d4c7c7fd5a2dd7bd63728b28356223c000dd2f91f" },
    { url = "http://pypi.howbuy.pa/packages/simplejson/3.20.1/simplejson-3.20.1-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:a2cc4f6486f9f515b62f5831ff1888886619b84fc837de68f26d919ba7bbdcbc" },
    { url = "http://pypi.howbuy.pa/packages/simplejson/3.20.1/simplejson-3.20.1-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:a3c2df555ee4016148fa192e2b9cd9e60bc1d40769366134882685e90aee2a1e" },
    { url = "http://pypi.howbuy.pa/packages/simplejson/3.20.1/simplejson-3.20.1-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:78520f04b7548a5e476b5396c0847e066f1e0a4c0c5e920da1ad65e95f410b11" },
    { url = "http://pypi.howbuy.pa/packages/simplejson/3.20.1/simplejson-3.20.1-cp310-cp310-musllinux_1_2_ppc64le.whl", hash = "sha256:f4bd49ecde87b0fe9f55cc971449a32832bca9910821f7072bbfae1155eaa007" },
    { url = "http://pypi.howbuy.pa/packages/simplejson/3.20.1/simplejson-3.20.1-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:7eaae2b88eb5da53caaffdfa50e2e12022553949b88c0df4f9a9663609373f72" },
    { url = "http://pypi.howbuy.pa/packages/simplejson/3.20.1/simplejson-3.20.1-cp310-cp310-win32.whl", hash = "sha256:e836fb88902799eac8debc2b642300748f4860a197fa3d9ea502112b6bb8e142" },
    { url = "http://pypi.howbuy.pa/packages/simplejson/3.20.1/simplejson-3.20.1-cp310-cp310-win_amd64.whl", hash = "sha256:b122a19b552b212fc3b5b96fc5ce92333d4a9ac0a800803e1f17ebb16dac4be5" },
    { url = "http://pypi.howbuy.pa/packages/simplejson/3.20.1/simplejson-3.20.1-py3-none-any.whl", hash = "sha256:8a6c1bbac39fa4a79f83cbf1df6ccd8ff7069582a9fd8db1e52cea073bc2c697" },
]

[[package]]
name = "six"
version = "1.17.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/six/1.17.0/six-1.17.0.tar.gz", hash = "sha256:ff70335d468e7eb6ec65b95b99d3a2836546063f63acc5171de367e834932a81" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/six/1.17.0/six-1.17.0-py2.py3-none-any.whl", hash = "sha256:4721f391ed90541fddacab5acf947aa0d3dc7d27b2e1e8eda2be8970586c3274" },
]

[[package]]
name = "sniffio"
version = "1.3.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/sniffio/1.3.1/sniffio-1.3.1.tar.gz", hash = "sha256:f4324edc670a0f49750a81b895f35c3adb843cca46f0530f79fc1babb23789dc" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/sniffio/1.3.1/sniffio-1.3.1-py3-none-any.whl", hash = "sha256:2f6da418d1f1e0fddd844478f41680e794e6051915791a034ff65e5f100525a2" },
]

[[package]]
name = "soupsieve"
version = "2.7"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/soupsieve/2.7/soupsieve-2.7.tar.gz", hash = "sha256:ad282f9b6926286d2ead4750552c8a6142bc4c783fd66b0293547c8fe6ae126a" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/soupsieve/2.7/soupsieve-2.7-py3-none-any.whl", hash = "sha256:6e60cc5c1ffaf1cebcc12e8188320b72071e922c2e897f737cadce79ad5d30c4" },
]

[[package]]
name = "spider-common-utils"
version = "0.1.25"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "paramiko" },
    { name = "python-jenkins" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/spider-common-utils/0.1.25/spider-common-utils-0.1.25.tar.gz", hash = "md5:bd3d29282f2fbbbbe06a3e2c6d0bb41a" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/spider-common-utils/0.1.25/spider_common_utils-0.1.25-py3-none-any.whl", hash = "md5:e63349fbce0fa09135691a5f5b4be03b" },
]

[[package]]
name = "sqlalchemy"
version = "1.4.44"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "greenlet", marker = "platform_machine == 'AMD64' or platform_machine == 'WIN32' or platform_machine == 'aarch64' or platform_machine == 'amd64' or platform_machine == 'ppc64le' or platform_machine == 'win32' or platform_machine == 'x86_64'" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/sqlalchemy/1.4.44/SQLAlchemy-1.4.44.tar.gz", hash = "sha256:2dda5f96719ae89b3ec0f1b79698d86eb9aecb1d54e990abb3fdd92c04b46a90" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/sqlalchemy/1.4.44/SQLAlchemy-1.4.44-cp310-cp310-macosx_10_15_x86_64.whl", hash = "sha256:65a0ad931944fcb0be12a8e0ac322dbd3ecf17c53f088bc10b6da8f0caac287b" },
    { url = "http://pypi.howbuy.pa/packages/sqlalchemy/1.4.44/SQLAlchemy-1.4.44-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:595b185041a4dc5c685283ea98c2f67bbfa47bb28e4a4f5b27ebf40684e7a9f8" },
    { url = "http://pypi.howbuy.pa/packages/sqlalchemy/1.4.44/SQLAlchemy-1.4.44-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_12_x86_64.manylinux2010_x86_64.whl", hash = "sha256:80ead36fb1d676cc019586ffdc21c7e906ce4bf243fe4021e4973dae332b6038" },
    { url = "http://pypi.howbuy.pa/packages/sqlalchemy/1.4.44/SQLAlchemy-1.4.44-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:68e0cd5d32a32c4395168d42f2fefbb03b817ead3a8f3704b8bd5697c0b26c24" },
    { url = "http://pypi.howbuy.pa/packages/sqlalchemy/1.4.44/SQLAlchemy-1.4.44-cp310-cp310-win32.whl", hash = "sha256:ae1ed1ebc407d2f66c6f0ec44ef7d56e3f455859df5494680e2cf89dad8e3ae0" },
    { url = "http://pypi.howbuy.pa/packages/sqlalchemy/1.4.44/SQLAlchemy-1.4.44-cp310-cp310-win_amd64.whl", hash = "sha256:6f0ea4d7348feb5e5d0bf317aace92e28398fa9a6e38b7be9ec1f31aad4a8039" },
]

[[package]]
name = "sqlparse"
version = "0.5.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/sqlparse/0.5.3/sqlparse-0.5.3.tar.gz", hash = "sha256:09f67787f56a0b16ecdbde1bfc7f5d9c3371ca683cfeaa8e6ff60b4807ec9272" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/sqlparse/0.5.3/sqlparse-0.5.3-py3-none-any.whl", hash = "sha256:cf2196ed3418f3ba5de6af7e82c694a9fbdbfecccdfc72e281548517081f16ca" },
]

[[package]]
name = "sse-starlette"
version = "2.3.6"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "anyio" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/sse-starlette/2.3.6/sse_starlette-2.3.6.tar.gz", hash = "sha256:0382336f7d4ec30160cf9ca0518962905e1b69b72d6c1c995131e0a703b436e3" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/sse-starlette/2.3.6/sse_starlette-2.3.6-py3-none-any.whl", hash = "sha256:d49a8285b182f6e2228e2609c350398b2ca2c36216c2675d875f81e93548f760" },
]

[[package]]
name = "starlette"
version = "0.47.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "anyio" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/starlette/0.47.0/starlette-0.47.0.tar.gz", hash = "sha256:1f64887e94a447fed5f23309fb6890ef23349b7e478faa7b24a851cd4eb844af" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/starlette/0.47.0/starlette-0.47.0-py3-none-any.whl", hash = "sha256:9d052d4933683af40ffd47c7465433570b4949dc937e20ad1d73b34e72f10c37" },
]

[[package]]
name = "texttable"
version = "1.7.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/texttable/1.7.0/texttable-1.7.0.tar.gz", hash = "sha256:2d2068fb55115807d3ac77a4ca68fa48803e84ebb0ee2340f858107a36522638" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/texttable/1.7.0/texttable-1.7.0-py2.py3-none-any.whl", hash = "sha256:72227d592c82b3d7f672731ae73e4d1f88cd8e2ef5b075a7a7f01a23a3743917" },
]

[[package]]
name = "tomli"
version = "2.2.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/tomli/2.2.1/tomli-2.2.1.tar.gz", hash = "sha256:cd45e1dc79c835ce60f7404ec8119f2eb06d38b1deba146f07ced3bbc44505ff" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/tomli/2.2.1/tomli-2.2.1-py3-none-any.whl", hash = "sha256:cb55c73c5f4408779d0cf3eef9f762b9c9f147a77de7b258bef0a5628adc85cc" },
]

[[package]]
name = "tomlkit"
version = "0.13.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/tomlkit/0.13.3/tomlkit-0.13.3.tar.gz", hash = "sha256:430cf247ee57df2b94ee3fbe588e71d362a941ebb545dec29b53961d61add2a1" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/tomlkit/0.13.3/tomlkit-0.13.3-py3-none-any.whl", hash = "sha256:c89c649d79ee40629a9fda55f8ace8c6a1b42deb912b2a8fd8d942ddadb606b0" },
]

[[package]]
name = "tqdm"
version = "4.67.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/tqdm/4.67.1/tqdm-4.67.1.tar.gz", hash = "sha256:f8aef9c52c08c13a65f30ea34f4e5aac3fd1a34959879d7e59e63027286627f2" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/tqdm/4.67.1/tqdm-4.67.1-py3-none-any.whl", hash = "sha256:26445eca388f82e72884e0d580d5464cd801a3ea01e63e5601bdff9ba6a48de2" },
]

[[package]]
name = "treelib"
version = "1.5.5"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/treelib/1.5.5/treelib-1.5.5.tar.gz", hash = "sha256:44695f7048b0bd82b45a4fe976611f9fb52902506249d84db255976a5e8738e0" }

[[package]]
name = "twine"
version = "3.8.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "colorama" },
    { name = "importlib-metadata" },
    { name = "keyring" },
    { name = "pkginfo" },
    { name = "readme-renderer" },
    { name = "requests" },
    { name = "requests-toolbelt" },
    { name = "rfc3986" },
    { name = "tqdm" },
    { name = "urllib3" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/twine/3.8.0/twine-3.8.0.tar.gz", hash = "sha256:8efa52658e0ae770686a13b675569328f1fba9837e5de1867bfe5f46a9aefe19" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/twine/3.8.0/twine-3.8.0-py3-none-any.whl", hash = "sha256:d0550fca9dc19f3d5e8eadfce0c227294df0a2a951251a4385797c8a6198b7c8" },
]

[[package]]
name = "typing-extensions"
version = "4.14.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/typing-extensions/4.14.0/typing_extensions-4.14.0.tar.gz", hash = "sha256:8676b788e32f02ab42d9e7c61324048ae4c6d844a399eebace3d4979d75ceef4" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/typing-extensions/4.14.0/typing_extensions-4.14.0-py3-none-any.whl", hash = "sha256:a1514509136dd0b477638fc68d6a91497af5076466ad0fa6c338e44e359944af" },
]

[[package]]
name = "typing-inspection"
version = "0.4.1"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/typing-inspection/0.4.1/typing_inspection-0.4.1.tar.gz", hash = "sha256:6ae134cc0203c33377d43188d4064e9b357dba58cff3185f22924610e70a9d28" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/typing-inspection/0.4.1/typing_inspection-0.4.1-py3-none-any.whl", hash = "sha256:389055682238f53b04f7badcb49b989835495a96700ced5dab2d8feae4b26f51" },
]

[[package]]
name = "tzdata"
version = "2025.2"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/tzdata/2025.2/tzdata-2025.2.tar.gz", hash = "sha256:b60a638fcc0daffadf82fe0f57e53d06bdec2f36c4df66280ae79bce6bd6f2b9" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/tzdata/2025.2/tzdata-2025.2-py2.py3-none-any.whl", hash = "sha256:1a403fada01ff9221ca8044d701868fa132215d84beb92242d9acd2147f667a8" },
]

[[package]]
name = "uritemplate"
version = "4.2.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/uritemplate/4.2.0/uritemplate-4.2.0.tar.gz", hash = "sha256:480c2ed180878955863323eea31b0ede668795de182617fef9c6ca09e6ec9d0e" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/uritemplate/4.2.0/uritemplate-4.2.0-py3-none-any.whl", hash = "sha256:962201ba1c4edcab02e60f9a0d3821e82dfc5d2d6662a21abd533879bdb8a686" },
]

[[package]]
name = "urllib3"
version = "2.4.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/urllib3/2.4.0/urllib3-2.4.0.tar.gz", hash = "sha256:414bc6535b787febd7567804cc015fee39daab8ad86268f1310a9250697de466" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/urllib3/2.4.0/urllib3-2.4.0-py3-none-any.whl", hash = "sha256:4e16665048960a0900c702d4a66415956a584919c03361cac9f1df5c5dd7e813" },
]

[[package]]
name = "uvicorn"
version = "0.34.3"
source = { registry = "http://pypi.howbuy.pa/simple" }
dependencies = [
    { name = "click" },
    { name = "h11" },
    { name = "typing-extensions" },
]
sdist = { url = "http://pypi.howbuy.pa/packages/uvicorn/0.34.3/uvicorn-0.34.3.tar.gz", hash = "sha256:35919a9a979d7a59334b6b10e05d77c1d0d574c50e0fc98b8b1a0f165708b55a" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/uvicorn/0.34.3/uvicorn-0.34.3-py3-none-any.whl", hash = "sha256:16246631db62bdfbf069b0645177d6e8a77ba950cfedbfd093acef9444e4d885" },
]

[[package]]
name = "wcwidth"
version = "0.2.13"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/wcwidth/0.2.13/wcwidth-0.2.13.tar.gz", hash = "sha256:72ea0c06399eb286d978fdedb6923a9eb47e1c486ce63e9b4e64fc18303972b5" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/wcwidth/0.2.13/wcwidth-0.2.13-py2.py3-none-any.whl", hash = "sha256:3da69048e4540d84af32131829ff948f1e022c1c6bdb8d6102117aac784f6859" },
]

[[package]]
name = "websocket-client"
version = "1.6.4"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/websocket-client/1.6.4/websocket-client-1.6.4.tar.gz", hash = "sha256:b3324019b3c28572086c4a319f91d1dcd44e6e11cd340232978c684a7650d0df" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/websocket-client/1.6.4/websocket_client-1.6.4-py3-none-any.whl", hash = "sha256:084072e0a7f5f347ef2ac3d8698a5e0b4ffbfcab607628cadabc650fc9a83a24" },
]

[[package]]
name = "xlrd"
version = "1.2.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/xlrd/1.2.0/xlrd-1.2.0.tar.gz", hash = "sha256:546eb36cee8db40c3eaa46c351e67ffee6eeb5fa2650b71bc4c758a29a1b29b2" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/xlrd/1.2.0/xlrd-1.2.0-py2.py3-none-any.whl", hash = "sha256:e551fb498759fa3a5384a94ccd4c3c02eb7c00ea424426e212ac0c57be9dfbde" },
]

[[package]]
name = "xlwt"
version = "1.3.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/xlwt/1.3.0/xlwt-1.3.0.tar.gz", hash = "sha256:c59912717a9b28f1a3c2a98fd60741014b06b043936dcecbc113eaaada156c88" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/xlwt/1.3.0/xlwt-1.3.0-py2.py3-none-any.whl", hash = "sha256:a082260524678ba48a297d922cc385f58278b8aa68741596a87de01a9c628b2e" },
]

[[package]]
name = "zipp"
version = "3.23.0"
source = { registry = "http://pypi.howbuy.pa/simple" }
sdist = { url = "http://pypi.howbuy.pa/packages/zipp/3.23.0/zipp-3.23.0.tar.gz", hash = "sha256:a07157588a12518c9d4034df3fbbee09c814741a33ff63c05fa29d26a2404166" }
wheels = [
    { url = "http://pypi.howbuy.pa/packages/zipp/3.23.0/zipp-3.23.0-py3-none-any.whl", hash = "sha256:071652d6115ed432f5ce1d34c336c0adfd6a884660d1e9712a256d3d3bd4b14e" },
]
