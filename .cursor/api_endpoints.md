## Mantis API Endpoints

说明：以下为项目当前对外暴露的 HTTP 接口清单。根路由在 `mantis/urls.py` 中统一前缀为 `mantis/`（除 `admin/` 以外）。各子模块大多通过 DRF `DefaultRouter` 注册，常见路由形态如下：

- 列表/新增: /<prefix>/<endpoint>/
- 详情: /<prefix>/<endpoint>/{id}/

部分显式 `path()` 路由保持其定义形式（可能不带结尾 `/`）。

### 根级路由
- /admin/
- /mantis/token/
- /mantis/token/refresh/
- /mantis/docs/

### /mantis/user/
- 显式路由
  - /mantis/user/login/
- 路由前缀（DRF Router）
  - /mantis/user/user_info/
  - /mantis/user/user_info/{id}/

### /mantis/coverage/
- /mantis/coverage/getCoverageReport/
- /mantis/coverage/getCoverageReport/{id}/

### /mantis/utest/
- /mantis/utest/analyze_unit_test_data/
- /mantis/utest/analyze_unit_test_data/{id}/
- /mantis/utest/check_unit_test_result/
- /mantis/utest/check_unit_test_result/{id}/
- /mantis/utest/batch_analyze_unit_test_data/
- /mantis/utest/batch_analyze_unit_test_data/{id}/

### /mantis/test_report/tapd/
- /mantis/test_report/tapd/get_iteration_from_tapd/
- /mantis/test_report/tapd/get_iteration_from_tapd/{id}/
- /mantis/test_report/tapd/get_bug_from_tapd/
- /mantis/test_report/tapd/get_bug_from_tapd/{id}/
- /mantis/test_report/tapd/get_stories_from_tapd/
- /mantis/test_report/tapd/get_stories_from_tapd/{id}/
- /mantis/test_report/tapd/get_launch_forms_status/
- /mantis/test_report/tapd/get_launch_forms_status/{id}/

### /mantis/test_report/
- /mantis/test_report/iter/
- /mantis/test_report/iter/{id}/
- /mantis/test_report/testset_report/
- /mantis/test_report/testset_report/{id}/
- /mantis/test_report/create_test_report/
- /mantis/test_report/create_test_report/{id}/
- /mantis/test_report/get_test_report/
- /mantis/test_report/get_test_report/{id}/
- /mantis/test_report/get_testing_bug_info/
- /mantis/test_report/get_testing_bug_info/{id}/
- /mantis/test_report/get_iteration_quality_report_status/
- /mantis/test_report/get_iteration_quality_report_status/{id}/
- /mantis/test_report/create_test_flow_report/
- /mantis/test_report/create_test_flow_report/{id}/
- /mantis/test_report/get_test_flow_report_lib/
- /mantis/test_report/get_test_flow_report_lib/{id}/
- /mantis/test_report/get_test_flow_result_by_app_and_branch/
- /mantis/test_report/get_test_flow_result_by_app_and_branch/{id}/
- /mantis/test_report/get_iteration_quality_report_for_spider/
- /mantis/test_report/get_iteration_quality_report_for_spider/{id}/

### /mantis/code_quality/
- /mantis/code_quality/analysis_ccn_report/
- /mantis/code_quality/analysis_ccn_report/{id}/
- /mantis/code_quality/analysis_p3c_report/
- /mantis/code_quality/analysis_p3c_report/{id}/
- /mantis/code_quality/ccn_entrance_guard/
- /mantis/code_quality/ccn_entrance_guard/{id}/
- /mantis/code_quality/p3c_entrance_guard/
- /mantis/code_quality/p3c_entrance_guard/{id}/
- /mantis/code_quality/analysis_sonar_report/
- /mantis/code_quality/analysis_sonar_report/{id}/
- /mantis/code_quality/sonar_entrance_guard/
- /mantis/code_quality/sonar_entrance_guard/{id}/

### /mantis/assistant/
- /mantis/assistant/chatgpt/
- /mantis/assistant/chatgpt/{id}/
- /mantis/assistant/chatgpt4/
- /mantis/assistant/chatgpt4/{id}/
- /mantis/assistant/knowledge/
- /mantis/assistant/knowledge/{id}/
- /mantis/assistant/hbapidoc_fighter/
- /mantis/assistant/hbapidoc_fighter/{id}/
- /mantis/assistant/coding_finder/
- /mantis/assistant/coding_finder/{id}/
- /mantis/assistant/blueocean_trouble_shooter/
- /mantis/assistant/blueocean_trouble_shooter/{id}/
- /mantis/assistant/table_data_fighter/
- /mantis/assistant/table_data_fighter/{id}/
- /mantis/assistant/sparkgpt/
- /mantis/assistant/sparkgpt/{id}/
- /mantis/assistant/sparkgpt35/
- /mantis/assistant/sparkgpt35/{id}/
- /mantis/assistant/sparkgptmax32/
- /mantis/assistant/sparkgptmax32/{id}/
- /mantis/assistant/sparkgptpro128/
- /mantis/assistant/sparkgptpro128/{id}/
- /mantis/assistant/table_data_fighter_spark/
- /mantis/assistant/table_data_fighter_spark/{id}/
- /mantis/assistant/table_data_fighter_low/
- /mantis/assistant/table_data_fighter_low/{id}/
- /mantis/assistant/jks_trouble_shooter_view/
- /mantis/assistant/jks_trouble_shooter_view/{id}/
- /mantis/assistant/general_gpt/
- /mantis/assistant/general_gpt/{id}/
- /mantis/assistant/general_gpt_lite/
- /mantis/assistant/general_gpt_lite/{id}/
- /mantis/assistant/optional_gpt/
- /mantis/assistant/optional_gpt/{id}/
- /mantis/assistant/question_answer/
- /mantis/assistant/question_answer/{id}/
- /mantis/assistant/hunyuan_turbo/
- /mantis/assistant/hunyuan_turbo/{id}/
- /mantis/assistant/hunyuan_t1/
- /mantis/assistant/hunyuan_t1/{id}/
- /mantis/assistant/hunyuan_lite/
- /mantis/assistant/hunyuan_lite/{id}/
- /mantis/assistant/hunyuan_std256/
- /mantis/assistant/hunyuan_std256/{id}/
- /mantis/assistant/deepseekv3/
- /mantis/assistant/deepseekv3/{id}/

### /mantis/code_fetcher/
- /mantis/code_fetcher/git_commits_view/
- /mantis/code_fetcher/git_commits_view/{id}/
- /mantis/code_fetcher/git_master_view/
- /mantis/code_fetcher/git_master_view/{id}/
- /mantis/code_fetcher/git_analyst_view/
- /mantis/code_fetcher/git_analyst_view/{id}/

### /mantis/tapd_gateway/
- /mantis/tapd_gateway/create_tapd_entry/
- /mantis/tapd_gateway/create_tapd_entry/{id}/
- /mantis/tapd_gateway/get_tapd_entry_status/
- /mantis/tapd_gateway/get_tapd_entry_status/{id}/
- /mantis/tapd_gateway/create_test_story_form_dev_test_plan/
- /mantis/tapd_gateway/create_test_story_form_dev_test_plan/{id}/
- /mantis/tapd_gateway/sync_data_to_tapd_custom_field/
- /mantis/tapd_gateway/sync_data_to_tapd_custom_field/{id}/
- /mantis/tapd_gateway/delete_request_limit_data/
- /mantis/tapd_gateway/delete_request_limit_data/{id}/

### /mantis/tapd_gateway/from_tapd/
- /mantis/tapd_gateway/from_tapd/sync_data_from_tapd/
- /mantis/tapd_gateway/from_tapd/sync_data_from_tapd/{id}/

### /mantis/quality/
- /mantis/quality/get_bug_create_status/
- /mantis/quality/get_bug_create_status/{id}/

### /mantis/measurement/
- /mantis/measurement/create_flow_schedule_info/
- /mantis/measurement/create_flow_schedule_info/{id}/
- /mantis/measurement/create_flow_run_record/
- /mantis/measurement/create_flow_run_record/{id}/
- /mantis/measurement/create_auto_test_record/
- /mantis/measurement/create_auto_test_record/{id}/
- /mantis/measurement/sync_auto_test_case_record/
- /mantis/measurement/sync_auto_test_case_record/{id}/
- /mantis/measurement/create_flow_run_record_testset/
- /mantis/measurement/create_flow_run_record_testset/{id}/
- /mantis/measurement/create_flow_app_deploy_info/
- /mantis/measurement/create_flow_app_deploy_info/{id}/
- /mantis/measurement/create_biz_stakeholder/
- /mantis/measurement/create_biz_stakeholder/{id}/
- /mantis/measurement/upd_user_team_info/
- /mantis/measurement/upd_user_team_info/{id}/
- /mantis/measurement/create_testset_result/
- /mantis/measurement/create_testset_result/{id}/
- /mantis/measurement/get_person_quality_dashboard/
- /mantis/measurement/get_person_quality_dashboard/{id}/
- /mantis/measurement/get_every_auto_test_result/
- /mantis/measurement/get_every_auto_test_result/{id}/
- /mantis/measurement/get_pers_capa_type/
- /mantis/measurement/get_pers_capa_type/{id}/
- /mantis/measurement/get_pers_capa_for_check/
- /mantis/measurement/get_pers_capa_for_check/{id}/
- /mantis/measurement/get_pers_capa_for_output/
- /mantis/measurement/get_pers_capa_for_output/{id}/
- /mantis/measurement/get_pers_capa_for_quality/
- /mantis/measurement/get_pers_capa_for_quality/{id}/
- /mantis/measurement/get_person_dev_effective/
- /mantis/measurement/get_person_dev_effective/{id}/
- /mantis/measurement/get_biz_flow_run_result/
- /mantis/measurement/get_biz_flow_run_result/{id}/
- /mantis/measurement/create_app_team_owner/
- /mantis/measurement/create_app_team_owner/{id}/
- /mantis/measurement/get_app_auto_test_check_result/
- /mantis/measurement/get_app_auto_test_check_result/{id}/
- /mantis/measurement/get_app_auto_test_status/
- /mantis/measurement/get_app_auto_test_status/{id}/
- /mantis/measurement/get_person_iteration_dashboard/
- /mantis/measurement/get_person_iteration_dashboard/{id}/
- /mantis/measurement/get_person_iteration_Linechart/
- /mantis/measurement/get_person_iteration_Linechart/{id}/

### /mantis/mcp/
- 显式路由
  - /mantis/mcp/devops_info
  - /mantis/mcp/devops_ctrl


