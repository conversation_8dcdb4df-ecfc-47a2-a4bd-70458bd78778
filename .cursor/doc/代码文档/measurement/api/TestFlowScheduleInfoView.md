### 接口名称
- 定时自动化任务同步

### 接口说明
- 用途：从外部平台同步自动化测试编排的定时任务配置，落库至平台便于后续度量与展示。
- 背景：自动化编排任务由外部平台统一维护，本平台需周期性同步其生效状态与执行时间。
- 使用场景：定时任务或人工触发该接口，更新本地的定时自动化任务信息。

### 接口类型
- HTTP

### 接口地址或方法签名
- 请求方式：GET
- URL：/mantis/measurement/create_flow_schedule_info/

### 请求参数表
- 本接口无请求参数。

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|---|---|---|---|---|---|
| 无 | 无 | 无 | 否 | - | - |

### 响应参数表
- 统一包裹结构：`{ "status": "success|failed|dicey", "data": Any, "msg": String }`

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|---|---|---|---|---|---|
| 状态 | status | String | 是 | success | 业务状态：success/failed/dicey |
| 数据 | data | Any | 否 | "" | 业务数据（本接口为空） |
| 提示信息 | msg | String | 否 | 自动化定时任务同步成功 | 人类可读提示 |

- 成功示例：
```json
{
  "status": "success",
  "data": "",
  "msg": "自动化定时任务同步成功"
}
```
- 失败示例：
```json
{
  "status": "failed",
  "data": "",
  "msg": "自动化定时任务同步失败"
}
```

### 返回码说明
- HTTP 状态码：始终 200
- 业务状态码：见 `status` 字段

| 返回码 | 含义 | 备注 |
|---|---|---|
| 200 + success | 同步成功 | 已完成数据落库 |
| 200 + failed | 同步失败 | 外部接口异常或数据处理异常 |

### 关键业务逻辑说明
- 控制器：`measurement.api.measurement_api.TestFlowScheduleInfoView.list`
- 服务：`measurement.service.flow_schedule_config_service.TestFlowScheduleInfoSer.create_or_update_flow_schedule_info`
- 核心流程：
  1. 组装外部接口地址：`SPIDER["url"] + 'external_interaction/mantis_mgt/get_all_test_flow_list/'`，GET 拉取所有自动化编排任务列表。
  2. 遍历外部返回 `flow_list`：
     - 以 `(biz_code, biz_iter_branch, biz_flow_name, suite_code)` 作为幂等唯一键查询本地是否已存在。
     - 计算下一次执行时间：`execute_time = cron_to_time(item.cron)`（基于 `croniter`）。
     - 通用审计字段：`create_user/update_user` 使用 `TAPD["sync_user"]`，时间戳为当前时间。
     - 已存在：加入批量更新列表，更新 `is_active/execute_time/schedule_creator/schedule_updater`。
     - 不存在：补齐基础标识字段并加入批量新增列表。
  3. 执行 `bulk_create(ins_list)` 与 `bulk_update(upd_list, [...])` 落库。
  4. 全流程异常捕获，成功/失败统一返回 200，业务状态由 `status` 区分。

### 流程图（Mermaid）
```mermaid
flowchart TD
    A[触发请求 GET /mantis/measurement/create_flow_schedule_info/] --> B[Controller TestFlowScheduleInfoView.list]
    B --> C[调用 Service.create_or_update_flow_schedule_info]
    C --> D[请求外部 SPIDER 获取 flow_list]
    D -->|成功| E{按唯一键查库}
    D -->|失败| X[记录日志并返回 failed]
    E -->|存在| F[加入更新列表: is_active, execute_time, creator, updater]
    E -->|不存在| G[加入新增列表: 业务标识+审计字段]
    F --> H[bulk_update]
    G --> I[bulk_create]
    H --> J[返回 success]
    I --> J[返回 success]
```

### 时序图（Mermaid）
```mermaid
sequenceDiagram
    participant FE as 前端/任务调度
    participant GW as 网关
    participant CTL as Controller
    participant SVC as Service
    participant EXT as 外部SPIDER
    participant DB as 数据库

    FE->>GW: GET /mantis/measurement/create_flow_schedule_info/
    GW->>CTL: 转发请求
    CTL->>SVC: create_or_update_flow_schedule_info()
    SVC->>EXT: GET /external_interaction/mantis_mgt/get_all_test_flow_list/
    EXT-->>SVC: flow_list
    SVC->>DB: 查询并 bulk_update / bulk_create
    DB-->>SVC: OK
    SVC-->>CTL: 处理结果
    CTL-->>GW: 200 {status: success, msg}
    GW-->>FE: 200 {status: success, msg}
```

### 异常处理机制
- 外部接口返回非期望状态或请求异常：记录错误日志，返回 `{status: failed, msg: "自动化定时任务同步失败"}`。
- 数据库批量写入异常：被统一异常捕获并按失败返回。
- 所有异常均返回 HTTP 200，由业务状态 `status` 判定成功与否。

### 调用的公共模块或外部依赖
- 公共模块：
  - `mantis.settings.ApiResult`：统一响应结构。
  - `measurement.service.measurement_utils.send_request`：HTTP 调用封装。
  - `measurement.service.measurement_utils.cron_to_time`：将 cron 表达式转换为下一次执行时间（`HH:mm:ss`）。
- 外部依赖：
  - `croniter`：cron 解析。
  - 外部平台 `SPIDER["url"]`：提供编排任务清单。

### 幂等性与安全性说明
- 幂等性：按 `(biz_code, biz_iter_branch, biz_flow_name, suite_code)` 唯一键匹配更新，整体为幂等同步。
- 鉴权：`authentication_classes = []`，无鉴权（建议在网关层限制来源与频率）。
- 限流：未实现（建议在网关/调度侧配置）。
- 验签：无。

### 涉及表名
| 表名 | 表注释 |
|---|---|
| `test_flow_schedule_info` | 定时自动化测试任务 |

### 备注与风险点
- 外部依赖可用性：SPIDER 接口不可用将导致同步失败（但仍返回 HTTP 200）。
- GET 触发写操作：不符合严格的 REST 语义，建议仅用于内部调用并在网关限制。
- 时间计算：`cron_to_time` 基于当前时间计算下一次执行时间，需注意时区配置（`Asia/Shanghai`）。
- 批量写入：大批量数据时建议评估事务耗时与锁竞争。
