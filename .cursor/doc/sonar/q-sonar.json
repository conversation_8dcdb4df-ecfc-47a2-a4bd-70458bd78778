{"total": 504, "p": 1, "ps": 500, "rules": [{"key": "java:S3751", "name": "\"@RequestMapping\" methods should be \"public\"", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["owasp-a6", "spring"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "java:S5301", "name": "\"ActiveMQConnectionFactory\" should not be vulnerable to malicious code deserialization", "severity": "MINOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a8"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S2254", "name": "\"HttpServletRequest.getRequestedSessionId()\" should not be used", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a2", "sans-top25-porous"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S2115", "name": "A secure password should be used when connecting to a database", "severity": "BLOCKER", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a2", "owasp-a3"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S4434", "name": "Allowing deserialization of LDAP objects is security-sensitive", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a8"], "lang": "java", "langName": "Java", "params": [], "type": "SECURITY_HOTSPOT"}, {"key": "findbugs:JUA_DONT_ASSERT_INSTANCEOF_IN_TESTS", "name": "Bad practice -  Asserting value of instanceof in tests is not recommended.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CO_ABSTRACT_SELF", "name": "Bad practice - Abstract class defines covariant compareTo() method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EQ_ABSTRACT_SELF", "name": "Bad practice - Abstract class defines covariant equals() method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DMI_ENTRY_SETS_MAY_REUSE_ENTRY_OBJECTS", "name": "Bad practice - Adding elements of an entry set may fail due to reuse of Entry objects", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SW_SWING_METHODS_INVOKED_IN_SWING_THREAD", "name": "Bad practice - Certain swing methods need to be invoked in Swing thread", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:BIT_SIGNED_CHECK", "name": "Bad practice - Check for sign of bitwise operation", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CN_IMPLEMENTS_CLONE_BUT_NOT_CLONEABLE", "name": "Bad practice - Class defines clone() but doesn't implement Cloneable", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EQ_COMPARETO_USE_OBJECT_EQUALS", "name": "Bad practice - Class defines compareTo(...) and uses Object.equals()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:HE_EQUALS_USE_HASHCODE", "name": "Bad practice - Class defines equals() and uses Object.hashCode()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:HE_EQUALS_NO_HASHCODE", "name": "Bad practice - Class defines equals() but not hashCode()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:HE_HASHCODE_USE_OBJECT_EQUALS", "name": "Bad practice - Class defines hashCode() and uses Object.equals()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:HE_HASHCODE_NO_EQUALS", "name": "Bad practice - Class defines hashCode() but not equals()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CN_IDIOM", "name": "Bad practice - Class implements Cloneable but does not define or use clone method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:HE_INHERITS_EQUALS_USE_HASHCODE", "name": "Bad practice - Class inherits equals() and uses Object.hashCode()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_NO_SUITABLE_CONSTRUCTOR_FOR_EXTERNALIZATION", "name": "Bad practice - Class is Externalizable but doesn't define a void constructor", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_CLASS_NOT_EXCEPTION", "name": "Bad practice - Class is not derived from an Exception, even though it is named as such", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_NO_SUITABLE_CONSTRUCTOR", "name": "Bad practice - Class is Serializable but its superclass doesn't define a void constructor", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_NO_SERIALVERSIONID", "name": "Bad practice - Class is Serializable, but doesn't define serialVersionUID", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_CLASS_NAMING_CONVENTION", "name": "Bad practice - Class names should start with an upper case letter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_SAME_SIMPLE_NAME_AS_INTERFACE", "name": "Bad practice - Class names shouldn't shadow simple name of implemented interface", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_SAME_SIMPLE_NAME_AS_SUPERCLASS", "name": "Bad practice - Class names shouldn't shadow simple name of superclass", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CN_IDIOM_NO_SUPER_CALL", "name": "Bad practice - clone method does not call super.clone()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_CLONE_COULD_RETURN_NULL", "name": "Bad practice - Clone method may return null", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_COMPARATOR_SHOULD_BE_SERIALIZABLE", "name": "Bad practice - Comparator doesn't implement Serializable", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CO_COMPARETO_INCORRECT_FLOATING", "name": "Bad practice - compareTo()/compare() incorrectly handles float or double value", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CO_COMPARETO_RESULTS_MIN_VALUE", "name": "Bad practice - compareTo()/compare() returns Integer.MIN_VALUE", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ES_COMPARING_STRINGS_WITH_EQ", "name": "Bad practice - Comparison of String objects using == or !=", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ES_COMPARING_PARAMETER_STRING_WITH_EQ", "name": "Bad practice - Comparison of String parameter using == or !=", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_CONFUSING", "name": "Bad practice - Confusing method names", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CO_SELF_NO_OBJECT", "name": "Bad practice - Covariant compareTo() method defined", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EQ_SELF_NO_OBJECT", "name": "Bad practice - Covariant equals() method defined", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:AM_CREATES_EMPTY_JAR_FILE_ENTRY", "name": "Bad practice - Creates an empty jar file entry", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:AM_CREATES_EMPTY_ZIP_FILE_ENTRY", "name": "Bad practice - Creates an empty zip file entry", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:PZ_DONT_REUSE_ENTRY_OBJECTS_IN_ITERATORS", "name": "Bad practice - Don't reuse entry objects in iterators", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DMI_USING_REMOVEALL_TO_CLEAR_COLLECTION", "name": "Bad practice - Don't use removeAll to clear a collection", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:IMSE_DONT_CATCH_IMSE", "name": "Bad practice - Dubious catching of IllegalMonitorStateException", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FI_EMPTY", "name": "Bad practice - Empty finalizer should be deleted", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ME_MUTABLE_ENUM_FIELD", "name": "Bad practice - Enum field is public and mutable", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EQ_CHECK_FOR_OPERAND_NOT_COMPATIBLE_WITH_THIS", "name": "Bad practice - Equals checks for incompatible operand", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EQ_GETCLASS_AND_CLASS_CONSTANT", "name": "Bad practice - equals method fails for subtypes", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:BC_EQUALS_METHOD_SHOULD_WORK_FOR_ALL_OBJECTS", "name": "Bad practice - Equals method should not assume anything about the type of its argument", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_EQUALS_SHOULD_HANDLE_NULL_ARGUMENT", "name": "Bad practice - equals() method does not check for null argument", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FI_EXPLICIT_INVOCATION", "name": "Bad practice - Explicit invocation of finalizer", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_FIELD_NAMING_CONVENTION", "name": "Bad practice - Field names should start with a lower case letter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:JCIP_FIELD_ISNT_FINAL_IN_IMMUTABLE_CLASS", "name": "Bad practice - Fields of immutable classes should be final", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FI_MISSING_SUPER_CALL", "name": "Bad practice - Finalizer does not call superclass finalizer", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FI_USELESS", "name": "Bad practice - Finalizer does nothing but call superclass finalizer", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FI_NULLIFY_SUPER", "name": "Bad practice - Finalizer nullifies superclass finalizer", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FI_FINALIZER_NULLS_FIELDS", "name": "Bad practice - Finalizer nulls fields", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FI_FINALIZER_ONLY_NULLS_FIELDS", "name": "Bad practice - Finalizer only nulls fields", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:VA_FORMAT_STRING_USES_NEWLINE", "name": "Bad practice - Format string should use %n rather than \\n", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:IT_NO_SUCH_ELEMENT", "name": "Bad practice - Iterator next() method cannot throw NoSuchElementException", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_WRONG_PACKAGE_INTENTIONAL", "name": "Bad practice - Method doesn't override method in superclass due to wrong package for parameter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RV_RETURN_VALUE_IGNORED_BAD_PRACTICE", "name": "Bad practice - Method ignores exceptional return value", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RR_NOT_CHECKED", "name": "Bad practice - Method ignores results of InputStream.read()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SR_NOT_CHECKED", "name": "Bad practice - Method ignores results of InputStream.skip()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:THROWS_METHOD_THROWS_RUNTIMEEXCEPTION", "name": "Bad practice - <PERSON> intentionally throws RuntimeException.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DM_RUN_FINALIZERS_ON_EXIT", "name": "Bad practice - Method invokes dangerous method runFinalizersOnExit", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DM_EXIT", "name": "Bad practice - Method invokes System.exit(...)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:THROWS_METHOD_THROWS_CLAUSE_BASIC_EXCEPTION", "name": "Bad practice - Method lists Exception in its throws clause.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:THROWS_METHOD_THROWS_CLAUSE_THROWABLE", "name": "Bad practice - Method lists Throwable in its throws clause.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ODR_OPEN_DATABASE_RESOURCE", "name": "Bad practice - Method may fail to close database resource", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ODR_OP<PERSON>_DATABASE_RESOURCE_EXCEPTION_PATH", "name": "Bad practice - Method may fail to close database resource on exception", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:OS_OPEN_STREAM", "name": "Bad practice - Method may fail to close stream", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:OS_OPEN_STREAM_EXCEPTION_PATH", "name": "Bad practice - Method may fail to close stream on exception", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DE_MIGHT_DROP", "name": "Bad practice - Method might drop exception", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DE_MIGHT_IGNORE", "name": "Bad practice - Method might ignore exception", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_METHOD_NAMING_CONVENTION", "name": "Bad practice - Method names should start with a lower case letter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_BOOLEAN_RETURN_NULL", "name": "Bad practice - Method with Boolean return type returns explicit null", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ISC_INSTANTIATE_STATIC_CLASS", "name": "Bad practice - Needless instantiation of class that only supplies static methods", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RV_NEGATING_RESULT_OF_COMPARETO", "name": "Bad practice - Negating the result of compareTo()/compare()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_BAD_FIELD_INNER_CLASS", "name": "Bad practice - Non-serializable class has a serializable inner class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_BAD_FIELD_STORE", "name": "Bad practice - Non-serializable value stored into instance field of a serializable class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_BAD_FIELD", "name": "Bad practice - Non-transient non-serializable instance field in serializable class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ME_ENUM_FIELD_SETTER", "name": "Bad practice - Public enum method unconditionally sets its field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DMI_RANDOM_USED_ONLY_ONCE", "name": "Bad practice - Random object created and used only once", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CNT_ROUGH_CONSTANT_VALUE", "name": "Bad practice - Rough value of known constant found", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_INNER_CLASS", "name": "Bad practice - Serializable inner class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_NONFINAL_SERIALVERSIONID", "name": "Bad practice - serialVersionUID isn't final", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_NONLONG_SERIALVERSIONID", "name": "Bad practice - serialVersionUID isn't long", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_NONSTATIC_SERIALVERSIONID", "name": "Bad practice - serialVersionUID isn't static", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SI_INSTANCE_BEFORE_FINALS_ASSIGNED", "name": "Bad practice - Static initializer creates instance before all static final fields assigned", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:J2EE_STORE_OF_NON_SERIALIZABLE_OBJECT_INTO_SESSION", "name": "Bad practice - Store of non serializable object into HttpSession", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:IC_SUPERCLASS_USES_SUBCLASS_DURING_INITIALIZATION", "name": "Bad practice - Superclass uses subclass during initialization", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RC_REF_COMPARISON_BAD_PRACTICE_BOOLEAN", "name": "Bad practice - Suspicious reference comparison of Boolean values", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RC_REF_COMPARISON_BAD_PRACTICE", "name": "Bad practice - Suspicious reference comparison to constant", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_READ_RESOLVE_MUST_RETURN_OBJECT", "name": "Bad practice - The readResolve method must be declared with a return type of Object.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_TOSTRING_COULD_RETURN_NULL", "name": "Bad practice - toString method may return null", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_TRANSIENT_FIELD_NOT_RESTORED", "name": "Bad practice - Transient field that isn't set by deserialization.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:GC_UNCHECKED_TYPE_IN_GENERIC_CALL", "name": "Bad practice - Unchecked type in generic call", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UI_INHERITANCE_UNSAFE_GETRESOURCE", "name": "Bad practice - Usage of GetResource may be unsafe if class is extended", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_FUTURE_KEYWORD_USED_AS_IDENTIFIER", "name": "Bad practice - Use of identifier that is a keyword in later versions of Java", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_FUTURE_KEYWORD_USED_AS_MEMBER_IDENTIFIER", "name": "Bad practice - Use of identifier that is a keyword in later versions of Java", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_VERY_CONFUSING_INTENTIONAL", "name": "Bad practice - Very confusing method names (but perhaps intentional)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "java:S2647", "name": "Basic authentication should not be used", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a3", "sans-top25-porous"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S5547", "name": "Cipher algorithms should be robust", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cert", "cwe", "owasp-a3", "owasp-a6", "privacy", "sans-top25-porous"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S3329", "name": "Cipher Block Chaining IV's should be unpredictable", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S2658", "name": "Classes should not be loaded dynamically", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:RE_POSSIBLE_UNINTENDED_PATTERN", "name": "Correctness - \".\" or \"|\" used for regular expression", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:ICAST_BAD_SHIFT_AMOUNT", "name": "Correctness - 32 bit int shifted by an amount not in the range -31..31", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IL_CONTAINER_ADDED_TO_ITSELF", "name": "Correctness - A collection is added to itself", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BC_NULL_INSTANCEOF", "name": "Correctness - A known null value is checked to see if it is an instance of a type", "severity": "MAJOR", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NULL_INSTANCEOF", "name": "Correctness - A known null value is checked to see if it is an instance of a type", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IP_PARAMETER_IS_DEAD_BUT_OVERWRITTEN", "name": "Correctness - A parameter is dead upon entry to a method but overwritten", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IL_INFINITE_LOOP", "name": "Correctness - An apparent infinite loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IL_INFINITE_RECURSIVE_LOOP", "name": "Correctness - An apparent infinite recursive loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NM_METHOD_CONSTRUCTOR_CONFUSION", "name": "Correctness - Apparent method/constructor confusion", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RANGE_ARRAY_INDEX", "name": "Correctness - Array index is out of bounds", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RANGE_ARRAY_LENGTH", "name": "Correctness - Array length is out of bounds", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RANGE_ARRAY_OFFSET", "name": "Correctness - Array offset is out of bounds", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BAC_BAD_APPLET_CONSTRUCTOR", "name": "Correctness - Bad Applet Constructor relies on uninitialized AppletStub", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RV_ABSOLUTE_VALUE_OF_HASHCODE", "name": "Correctness - Bad attempt to compute absolute value of signed 32-bit hashcode", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RV_ABSOLUTE_VALUE_OF_RANDOM_INT", "name": "Correctness - Bad attempt to compute absolute value of signed random integer", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:INT_BAD_COMPARISON_WITH_INT_VALUE", "name": "Correctness - Bad comparison of int value with long constant", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:INT_BAD_COMPARISON_WITH_NONNEGATIVE_VALUE", "name": "Correctness - Bad comparison of nonnegative value with negative constant or zero", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:INT_BAD_COMPARISON_WITH_SIGNED_BYTE", "name": "Correctness - Bad comparison of signed byte", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_BAD_MONTH", "name": "Correctness - Bad constant value for month", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_BIGDECIMAL_CONSTRUCTED_FROM_DOUBLE", "name": "Correctness - BigDecimal constructed from double that isn't represented precisely", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BIT_ADD_OF_SIGNED_BYTE", "name": "Correctness - Bitwise add of signed byte value", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BIT_IOR_OF_SIGNED_BYTE", "name": "Correctness - Bitwise OR of signed byte value", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EC_UNRELATED_INTERFACES", "name": "Correctness - Call to equals() comparing different interface types", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EC_UNRELATED_TYPES", "name": "Correctness - Call to equals() comparing different types", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EC_UNRELATED_CLASS_AND_INTERFACE", "name": "Correctness - Call to equals() comparing unrelated class and interface", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EC_NULL_ARG", "name": "Correctness - Call to equals(null)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_ANNOTATION_IS_NOT_VISIBLE_TO_REFLECTION", "name": "Correctness - Cannot use reflection to check for presence of annotation without runtime retention", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BIT_SIGNED_CHECK_HIGH_BIT", "name": "Correctness - Check for sign of bitwise operation involving negative number", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BIT_AND_ZZ", "name": "Correctness - Check to see if ((...) & 0) == 0", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NM_BAD_EQUAL", "name": "Correctness - Class defines equal(Object); should it be equals(Object)?", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:MF_CLASS_MASKS_FIELD", "name": "Correctness - Class defines field that masks a superclass field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NM_LCASE_HASHCODE", "name": "Correctness - Class defines hashcode(); should it be hashCode()?", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NM_LCASE_TOSTRING", "name": "Correctness - Class defines tostring(); should it be toString()?", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:VR_UNRESOLVABLE_REFERENCE", "name": "Correctness - Class makes reference to unresolvable class or method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BOA_BADLY_OVERRIDDEN_ADAPTER", "name": "Correctness - Class overrides a method implemented in super class Adapter wrongly", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_CLOSING_NULL", "name": "Correctness - close() invoked on a value that is always null", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RV_CHECK_COMPARETO_FOR_SPECIFIC_RETURN_VALUE", "name": "Correctness - Code checks for specific values returned by compareTo", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_COLLECTIONS_SHOULD_NOT_CONTAIN_THEMSELVES", "name": "Correctness - Collections should not contain themselves", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:TQ_COMPARING_VALUES_WITH_INCOMPATIBLE_TYPE_QUALIFIERS", "name": "Correctness - Comparing values with incompatible type qualifiers", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EQ_DONT_DEFINE_EQUALS_FOR_ENUM", "name": "Correctness - Covariant equals() method defined for enum", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EQ_SELF_USE_OBJECT", "name": "Correctness - Covariant equals() method defined, Object.equals(Object) inherited", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_SCHEDULED_THREAD_POOL_EXECUTOR_WITH_ZERO_CORE_THREADS", "name": "Correctness - Creation of ScheduledThreadPoolExecutor with zero core threads", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_DOH", "name": "Correctness - D'oh! A nonsensical method invocation", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EOS_BAD_END_OF_STREAM_CHECK", "name": "Correctness - Data read is converted before comparison to -1", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SF_DEAD_STORE_DUE_TO_SWITCH_FALLTHROUGH", "name": "Correctness - Dead store due to switch statement fall through", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SF_DEAD_STORE_DUE_TO_SWITCH_FALLTHROUGH_TO_THROW", "name": "Correctness - Dead store due to switch statement fall through to throw", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DLS_DEAD_STORE_OF_CLASS_LITERAL", "name": "Correctness - Dead store of class literal", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SIC_THREADLOCAL_DEADLY_EMBRACE", "name": "Correctness - Deadly embrace of non-static inner class and thread local", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:FL_FLOATS_AS_LOOP_COUNTERS", "name": "Correctness - Do not use floating-point variables as loop counters", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IO_APPENDING_TO_OBJECT_OUTPUT_STREAM", "name": "Correctness - Doomed attempt to append to an object output stream", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:FE_TEST_IF_EQUAL_TO_NOT_A_NUMBER", "name": "Correctness - Doomed test for equality to NaN", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_LONG_BITS_TO_DOUBLE_INVOKED_ON_INT", "name": "Correctness - Double.longBitsToDouble invoked on an int", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EQ_ALWAYS_FALSE", "name": "Correctness - equals method always returns false", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EQ_ALWAYS_TRUE", "name": "Correctness - equals method always returns true", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EQ_COMPARING_CLASS_NAMES", "name": "Correctness - equals method compares class names rather than class objects", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EQ_OVERRIDING_EQUALS_NOT_SYMMETRIC", "name": "Correctness - equals method overrides equals in superclass and may not be symmetric", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EQ_OTHER_NO_OBJECT", "name": "Correctness - equals() method defined that doesn't override equals(Object)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EQ_OTHER_USE_OBJECT", "name": "Correctness - equals() method defined that doesn't override Object.equals(Object)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EC_ARRAY_AND_NONARRAY", "name": "Correctness - equals() used to compare array and nonarray", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EC_INCOMPATIBLE_ARRAY_COMPARE", "name": "Correctness - equals(...) used to compare incompatible arrays", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RV_EXCEPTION_NOT_THROWN", "name": "Correctness - Exception created and dropped rather than thrown", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UWF_NULL_FIELD", "name": "Correctness - Field only ever set to null", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RE_CANT_USE_FILE_SEPARATOR_AS_REGULAR_EXPRESSION", "name": "Correctness - File.separator used for regular expression", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_FUTILE_ATTEMPT_TO_CHANGE_MAXPOOL_SIZE_OF_SCHEDULED_THREAD_POOL_EXECUTOR", "name": "Correctness - Futile attempt to change max pool size of ScheduledThreadPoolExecutor", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_CALLING_NEXT_FROM_HASNEXT", "name": "Correctness - hasNext method invokes next", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BC_IMPOSSIBLE_CAST", "name": "Correctness - Impossible cast", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BC_IMPOSSIBLE_CAST_PRIMITIVE_ARRAY", "name": "Correctness - Impossible cast involving primitive array", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BC_IMPOSSIBLE_DOWNCAST", "name": "Correctness - Impossible downcast", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BC_IMPOSSIBLE_DOWNCAST_OF_TOARRAY", "name": "Correctness - Impossible downcast of toArray() result", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BIT_IOR", "name": "Correctness - Incompatible bit masks", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BIT_AND", "name": "Correctness - Incompatible bit masks", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_INVALID_MIN_MAX", "name": "Correctness - Incorrect combination of Math.max and Math.min", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BC_IMPOSSIBLE_INSTANCEOF", "name": "Correctness - instanceof will always return false", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:ICAST_INT_CAST_TO_FLOAT_PASSED_TO_ROUND", "name": "Correctness - int value cast to float and then passed to Math.round", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:ICAST_INT_2_LONG_AS_INSTANT", "name": "Correctness - int value converted to long and used as absolute time", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IM_MULTIPLYING_RESULT_OF_IREM", "name": "Correctness - Integer multiply of result of integer remainder", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:ICAST_INT_CAST_TO_DOUBLE_PASSED_TO_CEIL", "name": "Correctness - Integral value cast to double and then passed to Math.ceil", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RE_BAD_SYNTAX_FOR_REGULAR_EXPRESSION", "name": "Correctness - Invalid syntax for regular expression", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EC_BAD_ARRAY_COMPARE", "name": "Correctness - Invocation of equals() on an array, which is equivalent to ==", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_INVOKING_HASHCODE_ON_ARRAY", "name": "Correctness - Invocation of hashCode on an array", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_INVOKING_TOSTRING_ON_ARRAY", "name": "Correctness - Invocation of toString on an array", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_INVOKING_TOSTRING_ON_ANONYMOUS_ARRAY", "name": "Correctness - Invocation of toString on an unnamed array", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IJU_ASSERT_METHOD_INVOKED_FROM_RUN_METHOD", "name": "Correctness - J<PERSON><PERSON><PERSON> assertion in run method will not be noticed by JUnit", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:QBA_QUESTIONABLE_BOOLEAN_ASSIGNMENT", "name": "Correctness - Method assigns boolean literal in boolean expression", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SQL_BAD_PREPARED_STATEMENT_ACCESS", "name": "Correctness - Method attempts to access a prepared statement parameter with index 0", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BRSA_BAD_RESULTSET_ACCESS", "name": "Correctness - Method attempts to access a result set field with index 0", "severity": "MAJOR", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SQL_BAD_RESULTSET_ACCESS", "name": "Correctness - Method attempts to access a result set field with index 0", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NULL_PARAM_DEREF", "name": "Correctness - Method call passes null for non-null parameter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NULL_PARAM_DEREF_ALL_TARGETS_DANGEROUS", "name": "Correctness - Method call passes null for non-null parameter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NONNULL_PARAM_VIOLATION", "name": "Correctness - Method call passes null to a non-null parameter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:M<PERSON>_METHOD_MASKS_FIELD", "name": "Correctness - Method defines a variable that obscures a field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_ARGUMENT_MIGHT_BE_NULL", "name": "Correctness - Method does not check for null argument", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NM_WRONG_PACKAGE", "name": "Correctness - Method doesn't override method in superclass due to wrong package for parameter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RV_RETURN_VALUE_IGNORED", "name": "Correctness - Method ignores return value", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NONNULL_RETURN_VIOLATION", "name": "Correctness - Method may return null, but is declared @Nonnull", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SE_METHOD_MUST_BE_PRIVATE", "name": "Correctness - Method must be private in order for serialization to work", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:FL_MATH_USING_FLOAT_PRECISION", "name": "Correctness - Method performs math using floating point precision", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_OPTIONAL_RETURN_NULL", "name": "Correctness - Method with Optional return type returns explicit null", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:FB_MISSING_EXPECTED_WARNING", "name": "Correctness - Missing expected or desired warning from SpotBugs", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:GC_UNRELATED_TYPES", "name": "Correctness - No relationship between generic parameter and method argument", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NONNULL_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR", "name": "Correctness - Non-null field is not initialized", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NULL_PARAM_DEREF_NONVIRTUAL", "name": "Correctness - Non-virtual method call passes null for non-null parameter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SA_FIELD_SELF_COMPUTATION", "name": "Correctness - Nonsensical self computation involving a field (e.g., x & x)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SA_LOCAL_SELF_COMPUTATION", "name": "Correctness - Nonsensical self computation involving a variable (e.g., x & x)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_ALWAYS_NULL", "name": "Correctness - <PERSON><PERSON> pointer dereference", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_ALWAYS_NULL_EXCEPTION", "name": "Correctness - <PERSON><PERSON> pointer dereference in method on exception path", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_GUARANTEED_DEREF", "name": "Correctness - Null value is guaranteed to be dereferenced", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RCN_REDUNDANT_NULLCHECK_WOULD_HAVE_BEEN_A_NPE", "name": "Correctness - Nullcheck of value previously dereferenced", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DLS_OVERWRITTEN_INCREMENT", "name": "Correctness - Overwritten increment", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BSHIFT_WRONG_ADD_PRIORITY", "name": "Correctness - Possible bad parsing of shift operation", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NULL_ON_SOME_PATH", "name": "Correctness - Possible null pointer dereference", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NULL_ON_SOME_PATH_EXCEPTION", "name": "Correctness - Possible null pointer dereference in method on exception path", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:CAA_COVARIANT_ARRAY_ELEMENT_STORE", "name": "Correctness - Possibly incompatible element is stored in covariant array", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:VA_PRIMITIVE_ARRAY_PASSED_TO_OBJECT_VARARG", "name": "Correctness - Primitive array passed to function expecting a variable number of object arguments", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RV_01_TO_INT", "name": "Correctness - Random value from 0 to 1 is coerced to the integer 0", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_UNWRITTEN_FIELD", "name": "Correctness - Read of unwritten field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RpC_REPEATED_CONDITIONAL_TEST", "name": "Correctness - Repeated conditional tests", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_ARGUMENTS_WRONG_ORDER", "name": "Correctness - Reversed method arguments", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SA_FIELD_SELF_ASSIGNMENT", "name": "Correctness - Self assignment of field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SA_LOCAL_SELF_ASSIGNMENT_INSTEAD_OF_FIELD", "name": "Correctness - Self assignment of local rather than assignment to field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SA_FIELD_SELF_COMPARISON", "name": "Correctness - Self comparison of field with itself", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SA_LOCAL_SELF_COMPARISON", "name": "Correctness - Self comparison of value with itself", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:HE_SIGNATURE_DECLARES_HASHING_OF_UNHASHABLE_CLASS", "name": "Correctness - Signature declares use of unhashable class in hashed construct", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:STI_INTERRUPTED_ON_UNKNOWNTHREAD", "name": "Correctness - Static Thread.interrupted() method invoked on thread instance", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_STORE_INTO_NONNULL_FIELD", "name": "Correctness - Store of null value into field annotated @Nonnull", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RANGE_STRING_INDEX", "name": "Correctness - String index is out of bounds", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:OVERRIDING_METHODS_MUST_INVOKE_SUPER", "name": "Correctness - Super method is annotated with @OverridingMethodsMustInvokeSuper, but the overriding method isn't calling the super method.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RC_REF_COMPARISON", "name": "Correctness - Suspicious reference comparison", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IJU_BAD_SUITE_METHOD", "name": "Correctness - TestCase declares a bad suite method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IJU_SETUP_NO_SUPER", "name": "Correctness - TestCase defines setUp that doesn't call super.setUp()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IJU_TEARDOWN_NO_SUPER", "name": "Correctness - TestCase defines tearDown that doesn't call super.tearDown()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IJU_NO_TESTS", "name": "Correctness - TestCase has no tests", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IJU_SUITE_NOT_STATIC", "name": "Correctness - TestCase implements a non-static suite method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SE_READ_RESOLVE_IS_STATIC", "name": "Correctness - The readResolve method must not be declared as a static method.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UMAC_UNCALLABLE_METHOD_OF_ANONYMOUS_CLASS", "name": "Correctness - Uncallable method defined in anonymous class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:FB_UNEXPECTED_WARNING", "name": "Correctness - Unexpected/undesired warning from SpotBugs", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UR_UNINIT_READ", "name": "Correctness - Uninitialized read of field in constructor", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UR_UNINIT_READ_CALLED_FROM_SUPER_CONSTRUCTOR", "name": "Correctness - Uninitialized read of field method called from constructor of superclass", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SIO_SUPERFLUOUS_INSTANCEOF", "name": "Correctness - Unnecessary type check done using instanceof operator", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:STI_INTERRUPTED_ON_CURRENTTHREAD", "name": "Correctness - Unneeded use of currentThread() call, to call interrupted()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UWF_UNWRITTEN_FIELD", "name": "Correctness - Unwritten field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:HE_USE_OF_UNHASHABLE_CLASS", "name": "Correctness - Use of class without a hashCode() method in a hashed data structure", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DLS_DEAD_LOCAL_INCREMENT_IN_RETURN", "name": "Correctness - Useless increment in return statement", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_VACUOUS_CALL_TO_EASYMOCK_METHOD", "name": "Correctness - Useless/vacuous call to EasyMock method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EC_UNRELATED_TYPES_USING_POINTER_EQUALITY", "name": "Correctness - Using pointer equality to compare different types", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_VACUOUS_SELF_COLLECTION_CALL", "name": "Correctness - Vacuous call to collections", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:TQ_ALWAYS_VALUE_USED_WHERE_NEVER_REQUIRED", "name": "Correctness - Value annotated as carrying a type qualifier used where a value that must not carry that qualifier is required", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:TQ_NEVER_VALUE_USED_WHERE_ALWAYS_REQUIRED", "name": "Correctness - Value annotated as never carrying a type qualifier used where value carrying that qualifier is required", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_GUARANTEED_DEREF_ON_EXCEPTION_PATH", "name": "Correctness - Value is null and guaranteed to be dereferenced on exception path", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:TQ_MAYBE_SOURCE_VALUE_REACHES_NEVER_SINK", "name": "Correctness - Value that might carry a type qualifier is always used in a way prohibits it from having that type qualifier", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:TQ_MAYBE_SOURCE_VALUE_REACHES_ALWAYS_SINK", "name": "Correctness - Value that might not carry a type qualifier is always used in a way requires that type qualifier", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:TQ_UNKNOWN_VALUE_USED_WHERE_ALWAYS_STRICTLY_REQUIRED", "name": "Correctness - Value without a type qualifier used where a value is required to have that qualifier", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NM_VERY_CONFUSING", "name": "Correctness - Very confusing method names", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "java:S4426", "name": "Cryptographic keys should be robust", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a3", "owasp-a6", "privacy", "rules"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S3355", "name": "Defined filters should be used", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S5542", "name": "Encryption algorithms should be used with secure mode and padding scheme", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cert", "cwe", "owasp-a3", "owasp-a6", "privacy", "sans-top25-porous"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S1989", "name": "Exceptions should not be thrown from servlet methods", "severity": "MINOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cert", "cwe", "error-handling", "owasp-a3"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:DM_CONVERT_CASE", "name": "I18n - Consider using Locale parameterized version of invoked method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["i18n"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DM_DEFAULT_ENCODING", "name": "I18n - Reliance on default encoding", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["i18n"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "java:S4433", "name": "LDAP connections should be authenticated", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a2"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:MC_OVERRIDABLE_METHOD_CALL_IN_CONSTRUCTOR", "name": "Malicious code - An overridable method is called from a constructor", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MC_OVERRIDABLE_METHOD_CALL_IN_CLONE", "name": "Malicious code - An overridable method is called from the clone() method.", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DP_CREATE_CLASSLOADER_INSIDE_DO_PRIVILEGED", "name": "Malicious code - Classloaders should only be created inside doPrivileged block", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:PERM_SUPER_NOT_CALLED_IN_GETPERMISSIONS", "name": "Malicious code - Custom class loader does not call its superclass's getPermissions()", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_MUTABLE_ARRAY", "name": "Malicious code - Field is a mutable array", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_MUTABLE_COLLECTION", "name": "Malicious code - Field is a mutable collection", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_MUTABLE_COLLECTION_PKGPROTECT", "name": "Malicious code - Field is a mutable collection which should be package protected", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_MUTABLE_HASHTABLE", "name": "Malicious code - Field is a mutable Hashtable", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_CANNOT_BE_FINAL", "name": "Malicious code - Field isn't final and cannot be protected from malicious code", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_SHOULD_BE_FINAL", "name": "Malicious code - Field isn't final but should be", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_SHOULD_BE_REFACTORED_TO_BE_FINAL", "name": "Malicious code - Field isn't final but should be refactored to be so", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_FINAL_PKGPROTECT", "name": "Malicious code - Field should be both final and package protected", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_OOI_PKGPROTECT", "name": "Malicious code - Field should be moved out of an interface and made package protected", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_PKGPROTECT", "name": "Malicious code - Field should be package protected", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FI_PUBLIC_SHOULD_BE_PROTECTED", "name": "Malicious code - Finalizer should be protected, not public", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EI_EXPOSE_BUF2", "name": "Malicious code - May expose internal representation by creating a buffer which incorporates reference to array", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EI_EXPOSE_REP2", "name": "Malicious code - May expose internal representation by incorporating reference to mutable object", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EI_EXPOSE_BUF", "name": "Malicious code - May expose internal representation by returning a buffer sharing non-public data", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_EXPOSE_BUF", "name": "Malicious code - May expose internal representation by returning a buffer sharing non-public data", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EI_EXPOSE_REP", "name": "Malicious code - May expose internal representation by returning reference to mutable object", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EI_EXPOSE_STATIC_BUF2", "name": "Malicious code - May expose internal static state by creating a buffer which stores an external array into a static field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EI_EXPOSE_STATIC_REP2", "name": "Malicious code - May expose internal static state by storing a mutable object into a static field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DP_DO_INSIDE_DO_PRIVILEGED", "name": "Malicious code - Method invoked that should be only be invoked inside a doPrivileged block", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:USC_POTENTIAL_SECURITY_CHECK_BASED_ON_UNTRUSTED_SOURCE", "name": "Malicious code - Potential security check based on untrusted source.", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:REFLC_REFLECTION_MAY_INCREASE_ACCESSIBILITY_OF_CLASS", "name": "Malicious code - Public method uses reflection to create a class it gets in its parameter which could increase the accessibility of any class", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:REFLF_REFLECTION_MAY_INCREASE_ACCESSIBILITY_OF_FIELD", "name": "Malicious code - Public method uses reflection to modify a field it gets in its parameter which could increase the accessibility of any class", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_EXPOSE_REP", "name": "Malicious code - Public static method may expose internal representation by returning array", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DM_USELESS_THREAD", "name": "Multi-threading - A thread was created using the default empty run method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:VO_VOLATILE_REFERENCE_TO_ARRAY", "name": "Multi-threading - A volatile reference to an array doesn't treat the array elements as volatile", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:VO_VOLATILE_INCREMENT", "name": "Multi-threading - An increment to a volatile field isn't atomic", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:STCAL_INVOKE_ON_STATIC_CALENDAR_INSTANCE", "name": "Multi-threading - Call to static Calendar", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:STCAL_INVOKE_ON_STATIC_DATE_FORMAT_INSTANCE", "name": "Multi-threading - Call to static DateFormat", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RS_READOBJECT_SYNC", "name": "Multi-threading - Class's readObject() method is synchronized", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:WS_WRITEOBJECT_SYNC", "name": "Multi-threading - Class's writeObject() method is synchronized but nothing else is", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:WA_AWAIT_NOT_IN_LOOP", "name": "Multi-threading - Condition.await() not in loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SC_START_IN_CTOR", "name": "Multi-threading - Constru<PERSON> invokes Thread.start()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:ESync_EMPTY_SYNC", "name": "Multi-threading - Empty synchronized block", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IS_FIELD_NOT_GUARDED", "name": "Multi-threading - Field not guarded against concurrent access", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IS_INCONSISTENT_SYNC", "name": "Multi-threading - Inconsistent synchronization", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IS2_INCONSISTENT_SYNC", "name": "Multi-threading - Inconsistent synchronization", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:LI_LAZY_INIT_UPDATE_STATIC", "name": "Multi-threading - Incorrect lazy initialization and update of static field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:LI_LAZY_INIT_INSTANCE", "name": "Multi-threading - Incorrect lazy initialization of instance field", "severity": "MAJOR", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:LI_LAZY_INIT_STATIC", "name": "Multi-threading - Incorrect lazy initialization of static field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SSD_DO_NOT_USE_INSTANCE_LOCK_ON_SHARED_STATIC_DATA", "name": "Multi-threading - Instance level lock was used on a shared static data", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RU_INVOKE_RUN", "name": "Multi-threading - Invokes run on a thread (did you mean to start it instead?)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SWL_SLEEP_WITH_LOCK_HELD", "name": "Multi-threading - Method calls Thread.sleep() with a lock held", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UL_UNRELEASED_LOCK_EXCEPTION_PATH", "name": "Multi-threading - Method does not release lock on all exception paths", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UL_UNRELEASED_LOCK", "name": "Multi-threading - Method does not release lock on all paths", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SP_SPIN_ON_FIELD", "name": "Multi-threading - Method spins on field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:M<PERSON>_SYNC_ON_UPDATED_FIELD", "name": "Multi-threading - Method synchronizes on an updated field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:MWN_MISMATCHED_NOTIFY", "name": "Multi-threading - Mismatched notify()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:MWN_MISMATCHED_WAIT", "name": "Multi-threading - Mismatched wait()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_MONITOR_WAIT_ON_CONDITION", "name": "Multi-threading - Monitor wait() called on Condition", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:MSF_MUTABLE_SERVLET_FIELD", "name": "Multi-threading - Mutable servlet field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NN_NAKED_NOTIFY", "name": "Multi-threading - Naked notify", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:TLW_TWO_LOCK_NOTIFY", "name": "Multi-threading - Notify with two locks held", "severity": "MAJOR", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DC_DOUBLECHECK", "name": "Multi-threading - Possible double-check of field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DC_PARTIALLY_CONSTRUCTED", "name": "Multi-threading - Possible exposure of partially initialized object", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RV_RETURN_VALUE_OF_PUTIFABSENT_IGNORED", "name": "Multi-threading - Return value of putIfAbsent ignored, value passed to putIfAbsent reused", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:AT_OPERATION_SEQUENCE_ON_CONCURRENT_ABSTRACTION", "name": "Multi-threading - Sequence of calls to concurrent abstraction may not be atomic", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:STCAL_STATIC_CALENDAR_INSTANCE", "name": "Multi-threading - Static Calendar field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:STCAL_STATIC_SIMPLE_DATE_FORMAT_INSTANCE", "name": "Multi-threading - Static DateFormat", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DL_SYNCHRONIZATION_ON_BOOLEAN", "name": "Multi-threading - Synchronization on Boolean", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DL_SYNCHRONIZATION_ON_BOXED_PRIMITIVE", "name": "Multi-threading - Synchronization on boxed primitive", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DL_SYNCHRONIZATION_ON_UNSHARED_BOXED_PRIMITIVE", "name": "Multi-threading - Synchronization on boxed primitive values", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:<PERSON><PERSON>_SYNC_ON_FIELD_TO_GUARD_CHANGING_THAT_FIELD", "name": "Multi-threading - Synchronization on field in futile attempt to guard that field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:WL_USING_GETCLASS_RATHER_THAN_CLASS_LITERAL", "name": "Multi-threading - Synchronization on getClass rather than class literal", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DL_SYNCHRONIZATION_ON_SHARED_CONSTANT", "name": "Multi-threading - Synchronization on interned String", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:JLM_JSR166_LOCK_MONITORENTER", "name": "Multi-threading - Synchronization performed on Lock", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:JLM_JSR166_UTILCONCURRENT_MONITORENTER", "name": "Multi-threading - Synchronization performed on util.concurrent instance", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_SYNC_AND_NULL_CHECK_FIELD", "name": "Multi-threading - Synchronize and null check on the same field.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UW_UNCOND_WAIT", "name": "Multi-threading - Unconditional wait", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UG_SYNC_SET_UNSYNC_GET", "name": "Multi-threading - Unsynchronized get method, synchronized set method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:JML_JSR166_CALLING_WAIT_RATHER_THAN_AWAIT", "name": "Multi-threading - Using monitor style wait methods on util.concurrent abstraction", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NO_NOTIFY_NOT_NOTIFYALL", "name": "Multi-threading - Using notify() rather than notifyAll()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:WA_NOT_IN_LOOP", "name": "Multi-threading - Wait not in loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:TLW_TWO_LOCK_WAIT", "name": "Multi-threading - Wait with two locks held", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "java:S5679", "name": "OpenSAML2 should be configured to prevent authentication bypass", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["owasp-a2", "owasp-a9", "spring"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S5344", "name": "Passwords should not be stored in plain-text or with a fast hashing algorithm", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a2", "owasp-a3", "owasp-a6", "sans-top25-porous", "spring"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:BX_UNBOXING_IMMEDIATELY_REBOXED", "name": "Performance - Boxed value is unboxed and then immediately reboxed", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_BOXED_PRIMITIVE_FOR_COMPARE", "name": "Performance - Boxing a primitive to compare", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_BOXED_PRIMITIVE_FOR_PARSING", "name": "Performance - Boxing/unboxing to parse a primitive", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SIC_INNER_SHOULD_BE_STATIC_ANON", "name": "Performance - Could be refactored into a named static inner class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SIC_INNER_SHOULD_BE_STATIC_NEEDS_THIS", "name": "Performance - Could be refactored into a static inner class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_GC", "name": "Performance - Explicit garbage collection; extremely dubious except in benchmarking code", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:HSC_HUGE_SHARED_STRING_CONSTANT", "name": "Performance - Huge string constants is duplicated across multiple class files", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:WMI_WRONG_MAP_ITERATOR", "name": "Performance - Inefficient use of keySet iterator instead of entrySet iterator", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IIO_INEFFICIENT_INDEX_OF", "name": "Performance - Inefficient use of String.indexOf(String)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IIO_INEFFICIENT_LAST_INDEX_OF", "name": "Performance - Inefficient use of String.lastIndexOf(String)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_COLLECTION_OF_URLS", "name": "Performance - Maps and sets of URLs can be performance hogs", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IMA_INEFFICIENT_MEMBER_ACCESS", "name": "Performance - Method accesses a private member variable of owning class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_BOXED_PRIMITIVE_TOSTRING", "name": "Performance - Method allocates a boxed primitive just to call toString", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_NEW_FOR_GETCLASS", "name": "Performance - Method allocates an object, only to get the class object", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IIL_PATTERN_COMPILE_IN_LOOP", "name": "Performance - Method calls Pattern.compile in a loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IIL_PREPARE_STATEMENT_IN_LOOP", "name": "Performance - Method calls prepareStatement in a loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UM_UNNECESSARY_MATH", "name": "Performance - Method calls static Math class method on a constant value", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IIL_PATTERN_COMPILE_IN_LOOP_INDIRECT", "name": "Performance - Method compiles the regular expression in a loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SBSC_USE_STRINGBUFFER_CONCATENATION", "name": "Performance - Method concatenates strings using + in a loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_BOOLEAN_CTOR", "name": "Performance - Method invokes inefficient Boolean constructor; use Boolean.valueOf(...) instead", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_FP_NUMBER_CTOR", "name": "Performance - Method invokes inefficient floating-point Number constructor; use static valueOf instead", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_STRING_VOID_CTOR", "name": "Performance - Method invokes inefficient new String() constructor", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_STRING_CTOR", "name": "Performance - Method invokes inefficient new String(String) constructor", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_NUMBER_CTOR", "name": "Performance - Method invokes inefficient Number constructor; use static valueOf instead", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_STRING_TOSTRING", "name": "Performance - Method invokes toString() method on a String", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:ITA_INEFFICIENT_TO_ARRAY", "name": "Performance - Method uses toArray() with zero-length array argument", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IIL_ELEMENTS_GET_LENGTH_IN_LOOP", "name": "Performance - NodeList.getLength() called in a loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BX_BOXING_IMMEDIATELY_UNBOXED", "name": "Performance - Primitive value is boxed and then immediately unboxed", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BX_BOXING_IMMEDIATELY_UNBOXED_TO_PERFORM_COERCION", "name": "Performance - Primitive value is boxed then unboxed to perform primitive coercion", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BX_UNBOXED_AND_COERCED_FOR_TERNARY_OPERATOR", "name": "Performance - Primitive value is unboxed and coerced for ternary operator", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UPM_UNCALLED_PRIVATE_METHOD", "name": "Performance - Private method is never called", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SIC_INNER_SHOULD_BE_STATIC", "name": "Performance - Should be a static inner class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_BLOCKING_METHODS_ON_URL", "name": "Performance - The equals and hashCode methods of URL are blocking", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:URF_UNREAD_FIELD", "name": "Performance - Unread field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SS_SHOULD_BE_STATIC", "name": "Performance - Unread field: should this field be static?", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UUF_UNUSED_FIELD", "name": "Performance - Unused field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_NEXTINT_VIA_NEXTDOUBLE", "name": "Performance - Use the nextInt method of Random rather than nextDouble to generate a random integer", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SQL_PREPARED_STATEMENT_GENERATED_FROM_NONCONSTANT_STRING", "name": "Security - A prepared statement is generated from a nonconstant String", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:PT_ABSOLUTE_PATH_TRAVERSAL", "name": "Security - Absolute path traversal in servlet", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:PADDING_ORACLE", "name": "Security - Cipher is susceptible to Padding Oracle", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:CIPHER_INTEGRITY", "name": "Security - Cipher with no integrity", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:HTTPONLY_COOKIE", "name": "Security - <PERSON><PERSON> without the HttpOnly flag", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:INSECURE_COOKIE", "name": "Security - <PERSON><PERSON> without the secure flag", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:DEFAULT_HTTP_CLIENT", "name": "Security - DefaultHttpClient with default constructor is not compatible with TLS 1.2", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:DES_USAGE", "name": "Security - DES is insecure", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:TDES_USAGE", "name": "Security - DESede is insecure", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:ECB_MODE", "name": "Security - ECB mode is insecure", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:DMI_EMPTY_DB_PASSWORD", "name": "Security - Empty database password", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:ANDROID_EXTERNAL_FILE_ACCESS", "name": "Security - External file access (Android)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["android", "cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:HARD_CODE_KEY", "name": "Security - Hard coded key", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:HARD_CODE_PASSWORD", "name": "Security - Hard coded password", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:DMI_CONSTANT_DB_PASSWORD", "name": "Security - Hardcoded constant database password", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:WEAK_HOSTNAME_VERIFIER", "name": "Security - HostnameVerifier that accept any signed certificates", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:HRS_REQUEST_PARAMETER_TO_COOKIE", "name": "Security - HTTP cookie formed from untrusted input", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:HTTP_PARAMETER_POLLUTION", "name": "Security - HTTP Parameter Pollution", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:HRS_REQUEST_PARAMETER_TO_HTTP_HEADER", "name": "Security - HTTP Response splitting vulnerability", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:BEAN_PROPERTY_INJECTION", "name": "Security - JavaBeans Property Injection", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:WEAK_MESSAGE_DIGEST_MD5", "name": "Security - MD2, MD4 and MD5 are weak hash functions", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:SQL_NONCONSTANT_STRING_PASSED_TO_EXECUTE", "name": "Security - Nonconstant string passed to execute or addBatch method on an SQL statement", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:PERMISSIVE_CORS", "name": "Security - Overly permissive CORS policy", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:COOKIE_PERSISTENT", "name": "Security - Persistent <PERSON><PERSON>", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:COMMAND_INJECTION", "name": "Security - Potential Command Injection", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:HTTP_RESPONSE_SPLITTING", "name": "Security - Potential HTTP Response Splitting", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SQL_INJECTION_JDBC", "name": "Security - Potential JDBC Injection", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SQL_INJECTION_SPRING_JDBC", "name": "Security - Potential JDBC Injection (Spring JDBC)", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:PREDICTABLE_RANDOM", "name": "Security - Predictable pseudorandom number generator", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:PT_RELATIVE_PATH_TRAVERSAL", "name": "Security - Relative path traversal in servlet", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:XSS_REQUEST_PARAMETER_TO_SERVLET_WRITER", "name": "Security - Servlet reflected cross site scripting vulnerability", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["owasp-a3"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:XSS_REQUEST_PARAMETER_TO_SEND_ERROR", "name": "Security - Servlet reflected cross site scripting vulnerability in error page", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["owasp-a3"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING", "name": "Security - Spring CSRF unrestricted RequestMapping", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S4830", "name": "Server certificates should be verified during SSL/TLS connections", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cert", "cwe", "owasp-a3", "owasp-a6", "privacy", "ssl"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S5527", "name": "Server hostnames should be verified during SSL/TLS connections", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a3", "owasp-a6", "privacy", "ssl"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:USM_USELESS_ABSTRACT_METHOD", "name": "Style - Abstract Method is already defined in implemented interface", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DMI_UNSUPPORTED_METHOD", "name": "Style - Call to unsupported method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:IM_BAD_CHECK_FOR_ODD", "name": "Style - Check for oddness that won't work for negative numbers", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EQ_DOESNT_OVERRIDE_EQUALS", "name": "Style - Class doesn't override equals in superclass", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:PS_PUBLIC_SEMAPHORES", "name": "Style - Class exposes synchronization and semaphores in its public interface", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MTIA_SUSPECT_SERVLET_INSTANCE_FIELD", "name": "Style - Class extends Servlet class and uses instance variables", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MTIA_SUSPECT_STRUTS_INSTANCE_FIELD", "name": "Style - Class extends Struts Action class and uses instance variables", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RI_REDUNDANT_INTERFACES", "name": "Style - Class implements same interface as superclass", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CI_CONFUSED_INHERITANCE", "name": "Style - Class is final but declares protected field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DMI_HARDCODED_ABSOLUTE_FILENAME", "name": "Style - Code contains a hard coded reference to an absolute pathname", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:QF_QUESTIONABLE_FOR_LOOP", "name": "Style - Complicated, subtle or wrong increment in for-loop", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:IM_AVERAGE_COMPUTATION_COULD_OVERFLOW", "name": "Style - Computation of average could overflow", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UC_USELESS_CONDITION", "name": "Style - Condition has no effect", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UC_USELESS_CONDITION_TYPE", "name": "Style - Condition has no effect due to the variable type", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:PZLA_PREFER_ZERO_LENGTH_ARRAYS", "name": "Style - Consider returning a zero length array rather than null", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CAA_COVARIANT_ARRAY_FIELD", "name": "Style - Covariant array assignment to a field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CAA_COVARIANT_ARRAY_LOCAL", "name": "Style - Covariant array assignment to a local variable", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CAA_COVARIANT_ARRAY_RETURN", "name": "Style - Covariant array is returned from the method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DLS_DEAD_LOCAL_STORE_OF_NULL", "name": "Style - Dead store of null to local variable", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DLS_DEAD_LOCAL_STORE", "name": "Style - Dead store to local variable", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DLS_DEAD_LOCAL_STORE_SHADOWS_FIELD", "name": "Style - Dead store to local variable that shadows field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_DEREFERENCE_OF_READLINE_VALUE", "name": "Style - Dereference of the result of readLine() without nullcheck", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SA_FIELD_DOUBLE_ASSIGNMENT", "name": "Style - Double assignment of field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SA_LOCAL_DOUBLE_ASSIGNMENT", "name": "Style - Double assignment of local variable", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:REC_CATCH_EXCEPTION", "name": "Style - Exception is caught when Except<PERSON> is not thrown", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UWF_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR", "name": "Style - Field not initialized in constructor but dereferenced without null check", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_IMMEDIATE_DEREFERENCE_OF_READLINE", "name": "Style - Immediate dereference of the result of readLine()", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:IC_INIT_CIRCULARITY", "name": "Style - Initialization circularity", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:BC_VACUOUS_INSTANCEOF", "name": "Style - instanceof will always return true", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:INT_BAD_REM_BY_1", "name": "Style - Integer remainder modulo 1", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ICAST_IDIV_CAST_TO_DOUBLE", "name": "Style - Integral division result cast to double or float", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DMI_USELESS_SUBSTRING", "name": "Style - Invocation of substring(0), which returns the original value", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_LOAD_OF_KNOWN_NULL_VALUE", "name": "Style - Load of known null value", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RV_CHECK_FOR_POSITIVE_INDEXOF", "name": "Style - Method checks to see if result of String.indexOf is positive", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:XFB_XML_FACTORY_BYPASS", "name": "Style - Method directly allocates a specific implementation of xml interfaces", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RV_DONT_JUST_NULL_CHECK_READLINE", "name": "Style - Method discards result of readLine after checking if it is non-null", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RV_RETURN_VALUE_IGNORED_INFERRED", "name": "Style - Method ignores return value, is this OK?", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_METHOD_RETURN_RELAXING_ANNOTATION", "name": "Style - Method relaxes nullness annotation on return value", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:USM_USELESS_SUBCLASS_METHOD", "name": "Style - Method superfluously delegates to parent class method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_METHOD_PARAMETER_TIGHTENS_ANNOTATION", "name": "Style - Method tightens nullness annotation on parameter", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_METHOD_PARAMETER_RELAXING_ANNOTATION", "name": "Style - Method tightens nullness annotation on parameter", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DB_DUPLICATE_BRANCHES", "name": "Style - Method uses the same code for two branches", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DB_DUPLICATE_SWITCH_CLAUSES", "name": "Style - Method uses the same code for two switch clauses", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DMI_NONSERIALIZABLE_OBJECT_WRITTEN", "name": "Style - Non serializable object written to ObjectOutput", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DCN_NULLPOINTER_EXCEPTION", "name": "Style - <PERSON><PERSON><PERSON><PERSON>er<PERSON>x<PERSON> caught", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE", "name": "Style - Parameter must be non-null but is marked as nullable", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE", "name": "Style - Possible null pointer dereference due to return value of called method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_NULL_ON_SOME_PATH_MIGHT_BE_INFEASIBLE", "name": "Style - Possible null pointer dereference on branch that might be infeasible", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:IA_AMBIGUOUS_INVOCATION_OF_INHERITED_OR_OUTER_METHOD", "name": "Style - Potentially ambiguous invocation of either an inherited or outer method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NS_DANGEROUS_NON_SHORT_CIRCUIT", "name": "Style - Potentially dangerous use of non-short-circuit logic", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_PRIVATE_READ_RESOLVE_NOT_INHERITED", "name": "Style - Private readResolve method not inherited by subclasses", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:BC_BAD_CAST_TO_ABSTRACT_COLLECTION", "name": "Style - Questionable cast to abstract collection", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:BC_BAD_CAST_TO_CONCRETE_COLLECTION", "name": "Style - Questionable cast to concrete collection", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NS_NON_SHORT_CIRCUIT", "name": "Style - Questionable use of non-short-circuit logic", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD", "name": "Style - Read of unwritten public or protected field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RCN_REDUNDANT_COMPARISON_OF_NULL_AND_NONNULL_VALUE", "name": "Style - Redundant comparison of non-null value to null", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RCN_REDUNDANT_COMPARISON_TWO_NULL_VALUES", "name": "Style - Redundant comparison of two null values", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RCN_REDUNDANT_NULLCHECK_OF_NONNULL_VALUE", "name": "Style - Redundant nullcheck of value known to be non-null", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RCN_REDUNDANT_NULLCHECK_OF_NULL_VALUE", "name": "Style - Redundant nullcheck of value known to be null", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RV_REM_OF_RANDOM_INT", "name": "Style - Remainder of 32-bit signed random integer", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RV_REM_OF_HASHCODE", "name": "Style - Re<PERSON>inder of hashCode could be negative", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ICAST_INTEGER_MULTIPLY_CAST_TO_LONG", "name": "Style - Result of integer multiplication cast to long", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RV_RETURN_VALUE_IGNORED_NO_SIDE_EFFECT", "name": "Style - Return value of method without side effect is ignored", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SA_LOCAL_SELF_ASSIGNMENT", "name": "Style - Self assignment of local variable", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SF_SWITCH_NO_DEFAULT", "name": "Style - Switch statement found where default case is missing", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SF_SWITCH_FALLTHROUGH", "name": "Style - Switch statement found where one case falls through to the next case", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CD_CIRCULAR_DEPENDENCY", "name": "Style - Test for circular dependencies among classes", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FE_FLOATING_POINT_EQUALITY", "name": "Style - Test for floating point equality", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DMI_THREAD_PASSED_WHERE_RUNNABLE_EXPECTED", "name": "Style - <PERSON>hr<PERSON> passed where <PERSON><PERSON><PERSON> expected", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_TRANSIENT_FIELD_OF_NONSERIALIZABLE_CLASS", "name": "Style - Transient field of class that isn't Serializable.", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:BC_UNCONFIRMED_CAST", "name": "Style - Unchecked/unconfirmed cast", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:BC_UNCONFIRMED_CAST_OF_RETURN_VALUE", "name": "Style - Unchecked/unconfirmed cast of return value from method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:URF_UNREAD_PUBLIC_OR_PROTECTED_FIELD", "name": "Style - Unread public/protected field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ICAST_QUESTIONABLE_UNSIGNED_RIGHT_SHIFT", "name": "Style - Unsigned right shift cast to short/byte", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UUF_UNUSED_PUBLIC_OR_PROTECTED_FIELD", "name": "Style - Unused public or protected field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EQ_UNUSUAL", "name": "Style - Unusual equals method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UWF_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD", "name": "Style - Unwritten public or protected field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DLS_DEAD_LOCAL_STORE_IN_RETURN", "name": "Style - Useless assignment in return statement", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UCF_USELESS_CONTROL_FLOW", "name": "Style - Useless control flow", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UCF_USELESS_CONTROL_FLOW_NEXT_LINE", "name": "Style - Useless control flow to next line", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UC_USELESS_VOID_METHOD", "name": "Style - Useless non-empty void method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UC_USELESS_OBJECT", "name": "Style - Useless object created", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UC_USELESS_OBJECT_STACK", "name": "Style - Useless object created on stack", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:INT_VACUOUS_BIT_OPERATION", "name": "Style - Vacuous bit mask operation on integer value", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:INT_VACUOUS_COMPARISON", "name": "Style - Vacuous comparison of integer value", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:TQ_EXPLICIT_UNKNOWN_SOURCE_VALUE_REACHES_ALWAYS_SINK", "name": "Style - Value required to have type qualifier, but marked as unknown", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}], "actives": {"java:S3751": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "java:S5301": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "MINOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "java:S2254": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "java:S2115": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "java:S4434": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findbugs:JUA_DONT_ASSERT_INSTANCEOF_IN_TESTS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:CO_ABSTRACT_SELF": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EQ_ABSTRACT_SELF": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_ENTRY_SETS_MAY_REUSE_ENTRY_OBJECTS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SW_SWING_METHODS_INVOKED_IN_SWING_THREAD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BIT_SIGNED_CHECK": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:CN_IMPLEMENTS_CLONE_BUT_NOT_CLONEABLE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EQ_COMPARETO_USE_OBJECT_EQUALS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:HE_EQUALS_USE_HASHCODE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:HE_EQUALS_NO_HASHCODE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:HE_HASHCODE_USE_OBJECT_EQUALS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:HE_HASHCODE_NO_EQUALS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:CN_IDIOM": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:HE_INHERITS_EQUALS_USE_HASHCODE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_NO_SUITABLE_CONSTRUCTOR_FOR_EXTERNALIZATION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_CLASS_NOT_EXCEPTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_NO_SUITABLE_CONSTRUCTOR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_NO_SERIALVERSIONID": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_CLASS_NAMING_CONVENTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_SAME_SIMPLE_NAME_AS_INTERFACE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_SAME_SIMPLE_NAME_AS_SUPERCLASS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:CN_IDIOM_NO_SUPER_CALL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_CLONE_COULD_RETURN_NULL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_COMPARATOR_SHOULD_BE_SERIALIZABLE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:CO_COMPARETO_INCORRECT_FLOATING": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:CO_COMPARETO_RESULTS_MIN_VALUE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ES_COMPARING_STRINGS_WITH_EQ": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ES_COMPARING_PARAMETER_STRING_WITH_EQ": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_CONFUSING": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:CO_SELF_NO_OBJECT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EQ_SELF_NO_OBJECT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:AM_CREATES_EMPTY_JAR_FILE_ENTRY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:AM_CREATES_EMPTY_ZIP_FILE_ENTRY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:PZ_DONT_REUSE_ENTRY_OBJECTS_IN_ITERATORS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_USING_REMOVEALL_TO_CLEAR_COLLECTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IMSE_DONT_CATCH_IMSE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:FI_EMPTY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ME_MUTABLE_ENUM_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EQ_CHECK_FOR_OPERAND_NOT_COMPATIBLE_WITH_THIS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EQ_GETCLASS_AND_CLASS_CONSTANT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BC_EQUALS_METHOD_SHOULD_WORK_FOR_ALL_OBJECTS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_EQUALS_SHOULD_HANDLE_NULL_ARGUMENT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:FI_EXPLICIT_INVOCATION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_FIELD_NAMING_CONVENTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:JCIP_FIELD_ISNT_FINAL_IN_IMMUTABLE_CLASS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:FI_MISSING_SUPER_CALL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:FI_USELESS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:FI_NULLIFY_SUPER": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:FI_FINALIZER_NULLS_FIELDS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:FI_FINALIZER_ONLY_NULLS_FIELDS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:VA_FORMAT_STRING_USES_NEWLINE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IT_NO_SUCH_ELEMENT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_WRONG_PACKAGE_INTENTIONAL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RV_RETURN_VALUE_IGNORED_BAD_PRACTICE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RR_NOT_CHECKED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SR_NOT_CHECKED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:THROWS_METHOD_THROWS_RUNTIMEEXCEPTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_RUN_FINALIZERS_ON_EXIT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_EXIT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:THROWS_METHOD_THROWS_CLAUSE_BASIC_EXCEPTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:THROWS_METHOD_THROWS_CLAUSE_THROWABLE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ODR_OPEN_DATABASE_RESOURCE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ODR_OPEN_DATABASE_RESOURCE_EXCEPTION_PATH": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:OS_OPEN_STREAM": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:OS_OPEN_STREAM_EXCEPTION_PATH": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DE_MIGHT_DROP": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DE_MIGHT_IGNORE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_METHOD_NAMING_CONVENTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_BOOLEAN_RETURN_NULL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ISC_INSTANTIATE_STATIC_CLASS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RV_NEGATING_RESULT_OF_COMPARETO": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_BAD_FIELD_INNER_CLASS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_BAD_FIELD_STORE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_BAD_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ME_ENUM_FIELD_SETTER": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_RANDOM_USED_ONLY_ONCE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:CNT_ROUGH_CONSTANT_VALUE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_INNER_CLASS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_NONFINAL_SERIALVERSIONID": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_NONLONG_SERIALVERSIONID": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_NONSTATIC_SERIALVERSIONID": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SI_INSTANCE_BEFORE_FINALS_ASSIGNED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:J2EE_STORE_OF_NON_SERIALIZABLE_OBJECT_INTO_SESSION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IC_SUPERCLASS_USES_SUBCLASS_DURING_INITIALIZATION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RC_REF_COMPARISON_BAD_PRACTICE_BOOLEAN": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RC_REF_COMPARISON_BAD_PRACTICE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_READ_RESOLVE_MUST_RETURN_OBJECT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_TOSTRING_COULD_RETURN_NULL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_TRANSIENT_FIELD_NOT_RESTORED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:GC_UNCHECKED_TYPE_IN_GENERIC_CALL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UI_INHERITANCE_UNSAFE_GETRESOURCE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_FUTURE_KEYWORD_USED_AS_IDENTIFIER": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_FUTURE_KEYWORD_USED_AS_MEMBER_IDENTIFIER": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_VERY_CONFUSING_INTENTIONAL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "java:S2647": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "java:S5547": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "java:S3329": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "java:S2658": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findbugs:RE_POSSIBLE_UNINTENDED_PATTERN": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ICAST_BAD_SHIFT_AMOUNT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IL_CONTAINER_ADDED_TO_ITSELF": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BC_NULL_INSTANCEOF": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_NULL_INSTANCEOF": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IP_PARAMETER_IS_DEAD_BUT_OVERWRITTEN": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IL_INFINITE_LOOP": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IL_INFINITE_RECURSIVE_LOOP": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_METHOD_CONSTRUCTOR_CONFUSION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RANGE_ARRAY_INDEX": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RANGE_ARRAY_LENGTH": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RANGE_ARRAY_OFFSET": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BAC_BAD_APPLET_CONSTRUCTOR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RV_ABSOLUTE_VALUE_OF_HASHCODE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RV_ABSOLUTE_VALUE_OF_RANDOM_INT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:INT_BAD_COMPARISON_WITH_INT_VALUE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:INT_BAD_COMPARISON_WITH_NONNEGATIVE_VALUE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:INT_BAD_COMPARISON_WITH_SIGNED_BYTE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_BAD_MONTH": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_BIGDECIMAL_CONSTRUCTED_FROM_DOUBLE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BIT_ADD_OF_SIGNED_BYTE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BIT_IOR_OF_SIGNED_BYTE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EC_UNRELATED_INTERFACES": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EC_UNRELATED_TYPES": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EC_UNRELATED_CLASS_AND_INTERFACE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EC_NULL_ARG": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_ANNOTATION_IS_NOT_VISIBLE_TO_REFLECTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BIT_SIGNED_CHECK_HIGH_BIT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BIT_AND_ZZ": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_BAD_EQUAL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MF_CLASS_MASKS_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_LCASE_HASHCODE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_LCASE_TOSTRING": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:VR_UNRESOLVABLE_REFERENCE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BOA_BADLY_OVERRIDDEN_ADAPTER": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_CLOSING_NULL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RV_CHECK_COMPARETO_FOR_SPECIFIC_RETURN_VALUE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_COLLECTIONS_SHOULD_NOT_CONTAIN_THEMSELVES": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:TQ_COMPARING_VALUES_WITH_INCOMPATIBLE_TYPE_QUALIFIERS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EQ_DONT_DEFINE_EQUALS_FOR_ENUM": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EQ_SELF_USE_OBJECT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_SCHEDULED_THREAD_POOL_EXECUTOR_WITH_ZERO_CORE_THREADS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_DOH": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EOS_BAD_END_OF_STREAM_CHECK": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SF_DEAD_STORE_DUE_TO_SWITCH_FALLTHROUGH": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SF_DEAD_STORE_DUE_TO_SWITCH_FALLTHROUGH_TO_THROW": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DLS_DEAD_STORE_OF_CLASS_LITERAL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SIC_THREADLOCAL_DEADLY_EMBRACE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:FL_FLOATS_AS_LOOP_COUNTERS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IO_APPENDING_TO_OBJECT_OUTPUT_STREAM": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:FE_TEST_IF_EQUAL_TO_NOT_A_NUMBER": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_LONG_BITS_TO_DOUBLE_INVOKED_ON_INT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EQ_ALWAYS_FALSE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EQ_ALWAYS_TRUE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EQ_COMPARING_CLASS_NAMES": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EQ_OVERRIDING_EQUALS_NOT_SYMMETRIC": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EQ_OTHER_NO_OBJECT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EQ_OTHER_USE_OBJECT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EC_ARRAY_AND_NONARRAY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EC_INCOMPATIBLE_ARRAY_COMPARE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RV_EXCEPTION_NOT_THROWN": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UWF_NULL_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RE_CANT_USE_FILE_SEPARATOR_AS_REGULAR_EXPRESSION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_FUTILE_ATTEMPT_TO_CHANGE_MAXPOOL_SIZE_OF_SCHEDULED_THREAD_POOL_EXECUTOR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_CALLING_NEXT_FROM_HASNEXT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BC_IMPOSSIBLE_CAST": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BC_IMPOSSIBLE_CAST_PRIMITIVE_ARRAY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BC_IMPOSSIBLE_DOWNCAST": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BC_IMPOSSIBLE_DOWNCAST_OF_TOARRAY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BIT_IOR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BIT_AND": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_INVALID_MIN_MAX": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BC_IMPOSSIBLE_INSTANCEOF": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ICAST_INT_CAST_TO_FLOAT_PASSED_TO_ROUND": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ICAST_INT_2_LONG_AS_INSTANT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IM_MULTIPLYING_RESULT_OF_IREM": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ICAST_INT_CAST_TO_DOUBLE_PASSED_TO_CEIL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RE_BAD_SYNTAX_FOR_REGULAR_EXPRESSION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EC_BAD_ARRAY_COMPARE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_INVOKING_HASHCODE_ON_ARRAY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_INVOKING_TOSTRING_ON_ARRAY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_INVOKING_TOSTRING_ON_ANONYMOUS_ARRAY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IJU_ASSERT_METHOD_INVOKED_FROM_RUN_METHOD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:QBA_QUESTIONABLE_BOOLEAN_ASSIGNMENT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SQL_BAD_PREPARED_STATEMENT_ACCESS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BRSA_BAD_RESULTSET_ACCESS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SQL_BAD_RESULTSET_ACCESS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_NULL_PARAM_DEREF": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_NULL_PARAM_DEREF_ALL_TARGETS_DANGEROUS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_NONNULL_PARAM_VIOLATION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MF_METHOD_MASKS_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_ARGUMENT_MIGHT_BE_NULL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_WRONG_PACKAGE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RV_RETURN_VALUE_IGNORED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_NONNULL_RETURN_VIOLATION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_METHOD_MUST_BE_PRIVATE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:FL_MATH_USING_FLOAT_PRECISION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_OPTIONAL_RETURN_NULL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:FB_MISSING_EXPECTED_WARNING": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:GC_UNRELATED_TYPES": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_NONNULL_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_NULL_PARAM_DEREF_NONVIRTUAL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SA_FIELD_SELF_COMPUTATION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SA_LOCAL_SELF_COMPUTATION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_ALWAYS_NULL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_ALWAYS_NULL_EXCEPTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_GUARANTEED_DEREF": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RCN_REDUNDANT_NULLCHECK_WOULD_HAVE_BEEN_A_NPE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DLS_OVERWRITTEN_INCREMENT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BSHIFT_WRONG_ADD_PRIORITY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_NULL_ON_SOME_PATH": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_NULL_ON_SOME_PATH_EXCEPTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:CAA_COVARIANT_ARRAY_ELEMENT_STORE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:VA_PRIMITIVE_ARRAY_PASSED_TO_OBJECT_VARARG": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RV_01_TO_INT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_UNWRITTEN_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RpC_REPEATED_CONDITIONAL_TEST": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_ARGUMENTS_WRONG_ORDER": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SA_FIELD_SELF_ASSIGNMENT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SA_LOCAL_SELF_ASSIGNMENT_INSTEAD_OF_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SA_FIELD_SELF_COMPARISON": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SA_LOCAL_SELF_COMPARISON": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:HE_SIGNATURE_DECLARES_HASHING_OF_UNHASHABLE_CLASS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:STI_INTERRUPTED_ON_UNKNOWNTHREAD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_STORE_INTO_NONNULL_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RANGE_STRING_INDEX": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:OVERRIDING_METHODS_MUST_INVOKE_SUPER": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RC_REF_COMPARISON": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IJU_BAD_SUITE_METHOD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IJU_SETUP_NO_SUPER": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IJU_TEARDOWN_NO_SUPER": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IJU_NO_TESTS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IJU_SUITE_NOT_STATIC": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_READ_RESOLVE_IS_STATIC": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UMAC_UNCALLABLE_METHOD_OF_ANONYMOUS_CLASS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:FB_UNEXPECTED_WARNING": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UR_UNINIT_READ": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UR_UNINIT_READ_CALLED_FROM_SUPER_CONSTRUCTOR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SIO_SUPERFLUOUS_INSTANCEOF": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:STI_INTERRUPTED_ON_CURRENTTHREAD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UWF_UNWRITTEN_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:HE_USE_OF_UNHASHABLE_CLASS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DLS_DEAD_LOCAL_INCREMENT_IN_RETURN": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_VACUOUS_CALL_TO_EASYMOCK_METHOD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EC_UNRELATED_TYPES_USING_POINTER_EQUALITY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_VACUOUS_SELF_COLLECTION_CALL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:TQ_ALWAYS_VALUE_USED_WHERE_NEVER_REQUIRED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:TQ_NEVER_VALUE_USED_WHERE_ALWAYS_REQUIRED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_GUARANTEED_DEREF_ON_EXCEPTION_PATH": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:TQ_MAYBE_SOURCE_VALUE_REACHES_NEVER_SINK": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:TQ_MAYBE_SOURCE_VALUE_REACHES_ALWAYS_SINK": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:TQ_UNKNOWN_VALUE_USED_WHERE_ALWAYS_STRICTLY_REQUIRED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NM_VERY_CONFUSING": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "java:S4426": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "java:S3355": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "java:S5542": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "java:S1989": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "MINOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findbugs:DM_CONVERT_CASE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_DEFAULT_ENCODING": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "java:S4433": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findbugs:MC_OVERRIDABLE_METHOD_CALL_IN_CONSTRUCTOR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MC_OVERRIDABLE_METHOD_CALL_IN_CLONE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DP_CREATE_CLASSLOADER_INSIDE_DO_PRIVILEGED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:PERM_SUPER_NOT_CALLED_IN_GETPERMISSIONS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MS_MUTABLE_ARRAY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MS_MUTABLE_COLLECTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MS_MUTABLE_COLLECTION_PKGPROTECT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MS_MUTABLE_HASHTABLE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MS_CANNOT_BE_FINAL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MS_SHOULD_BE_FINAL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MS_SHOULD_BE_REFACTORED_TO_BE_FINAL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MS_FINAL_PKGPROTECT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MS_OOI_PKGPROTECT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MS_PKGPROTECT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:FI_PUBLIC_SHOULD_BE_PROTECTED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EI_EXPOSE_BUF2": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EI_EXPOSE_REP2": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EI_EXPOSE_BUF": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MS_EXPOSE_BUF": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EI_EXPOSE_REP": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EI_EXPOSE_STATIC_BUF2": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EI_EXPOSE_STATIC_REP2": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DP_DO_INSIDE_DO_PRIVILEGED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:USC_POTENTIAL_SECURITY_CHECK_BASED_ON_UNTRUSTED_SOURCE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:REFLC_REFLECTION_MAY_INCREASE_ACCESSIBILITY_OF_CLASS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:REFLF_REFLECTION_MAY_INCREASE_ACCESSIBILITY_OF_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MS_EXPOSE_REP": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_USELESS_THREAD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:VO_VOLATILE_REFERENCE_TO_ARRAY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:VO_VOLATILE_INCREMENT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:STCAL_INVOKE_ON_STATIC_CALENDAR_INSTANCE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:STCAL_INVOKE_ON_STATIC_DATE_FORMAT_INSTANCE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RS_READOBJECT_SYNC": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:WS_WRITEOBJECT_SYNC": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:WA_AWAIT_NOT_IN_LOOP": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SC_START_IN_CTOR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ESync_EMPTY_SYNC": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IS_FIELD_NOT_GUARDED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IS_INCONSISTENT_SYNC": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IS2_INCONSISTENT_SYNC": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:LI_LAZY_INIT_UPDATE_STATIC": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:LI_LAZY_INIT_INSTANCE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:LI_LAZY_INIT_STATIC": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SSD_DO_NOT_USE_INSTANCE_LOCK_ON_SHARED_STATIC_DATA": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RU_INVOKE_RUN": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SWL_SLEEP_WITH_LOCK_HELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UL_UNRELEASED_LOCK_EXCEPTION_PATH": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UL_UNRELEASED_LOCK": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SP_SPIN_ON_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ML_SYNC_ON_UPDATED_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MWN_MISMATCHED_NOTIFY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MWN_MISMATCHED_WAIT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_MONITOR_WAIT_ON_CONDITION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MSF_MUTABLE_SERVLET_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NN_NAKED_NOTIFY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:TLW_TWO_LOCK_NOTIFY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DC_DOUBLECHECK": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DC_PARTIALLY_CONSTRUCTED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RV_RETURN_VALUE_OF_PUTIFABSENT_IGNORED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:AT_OPERATION_SEQUENCE_ON_CONCURRENT_ABSTRACTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:STCAL_STATIC_CALENDAR_INSTANCE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:STCAL_STATIC_SIMPLE_DATE_FORMAT_INSTANCE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DL_SYNCHRONIZATION_ON_BOOLEAN": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DL_SYNCHRONIZATION_ON_BOXED_PRIMITIVE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DL_SYNCHRONIZATION_ON_UNSHARED_BOXED_PRIMITIVE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ML_SYNC_ON_FIELD_TO_GUARD_CHANGING_THAT_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:WL_USING_GETCLASS_RATHER_THAN_CLASS_LITERAL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DL_SYNCHRONIZATION_ON_SHARED_CONSTANT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:JLM_JSR166_LOCK_MONITORENTER": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:JLM_JSR166_UTILCONCURRENT_MONITORENTER": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_SYNC_AND_NULL_CHECK_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UW_UNCOND_WAIT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UG_SYNC_SET_UNSYNC_GET": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:JML_JSR166_CALLING_WAIT_RATHER_THAN_AWAIT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NO_NOTIFY_NOT_NOTIFYALL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:WA_NOT_IN_LOOP": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:TLW_TWO_LOCK_WAIT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "java:S5679": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "java:S5344": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findbugs:BX_UNBOXING_IMMEDIATELY_REBOXED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_BOXED_PRIMITIVE_FOR_COMPARE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_BOXED_PRIMITIVE_FOR_PARSING": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SIC_INNER_SHOULD_BE_STATIC_ANON": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SIC_INNER_SHOULD_BE_STATIC_NEEDS_THIS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_GC": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:HSC_HUGE_SHARED_STRING_CONSTANT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:WMI_WRONG_MAP_ITERATOR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IIO_INEFFICIENT_INDEX_OF": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IIO_INEFFICIENT_LAST_INDEX_OF": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_COLLECTION_OF_URLS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IMA_INEFFICIENT_MEMBER_ACCESS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_BOXED_PRIMITIVE_TOSTRING": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_NEW_FOR_GETCLASS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IIL_PATTERN_COMPILE_IN_LOOP": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IIL_PREPARE_STATEMENT_IN_LOOP": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UM_UNNECESSARY_MATH": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IIL_PATTERN_COMPILE_IN_LOOP_INDIRECT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SBSC_USE_STRINGBUFFER_CONCATENATION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_BOOLEAN_CTOR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_FP_NUMBER_CTOR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_STRING_VOID_CTOR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_STRING_CTOR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_NUMBER_CTOR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_STRING_TOSTRING": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ITA_INEFFICIENT_TO_ARRAY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IIL_ELEMENTS_GET_LENGTH_IN_LOOP": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BX_BOXING_IMMEDIATELY_UNBOXED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BX_BOXING_IMMEDIATELY_UNBOXED_TO_PERFORM_COERCION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BX_UNBOXED_AND_COERCED_FOR_TERNARY_OPERATOR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UPM_UNCALLED_PRIVATE_METHOD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SIC_INNER_SHOULD_BE_STATIC": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_BLOCKING_METHODS_ON_URL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:URF_UNREAD_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SS_SHOULD_BE_STATIC": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UUF_UNUSED_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DM_NEXTINT_VIA_NEXTDOUBLE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SQL_PREPARED_STATEMENT_GENERATED_FROM_NONCONSTANT_STRING": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:PT_ABSOLUTE_PATH_TRAVERSAL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findsecbugs:PADDING_ORACLE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findsecbugs:CIPHER_INTEGRITY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findsecbugs:HTTPONLY_COOKIE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findsecbugs:INSECURE_COOKIE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findsecbugs:DEFAULT_HTTP_CLIENT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findsecbugs:DES_USAGE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findsecbugs:TDES_USAGE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findsecbugs:ECB_MODE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findbugs:DMI_EMPTY_DB_PASSWORD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findsecbugs:ANDROID_EXTERNAL_FILE_ACCESS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findsecbugs:HARD_CODE_KEY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findsecbugs:HARD_CODE_PASSWORD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findbugs:DMI_CONSTANT_DB_PASSWORD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findsecbugs:WEAK_HOSTNAME_VERIFIER": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findbugs:HRS_REQUEST_PARAMETER_TO_COOKIE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findsecbugs:HTTP_PARAMETER_POLLUTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findbugs:HRS_REQUEST_PARAMETER_TO_HTTP_HEADER": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findsecbugs:BEAN_PROPERTY_INJECTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findsecbugs:WEAK_MESSAGE_DIGEST_MD5": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findbugs:SQL_NONCONSTANT_STRING_PASSED_TO_EXECUTE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findsecbugs:PERMISSIVE_CORS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findsecbugs:COOKIE_PERSISTENT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findsecbugs:COMMAND_INJECTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findsecbugs:HTTP_RESPONSE_SPLITTING": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findsecbugs:SQL_INJECTION_JDBC": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findsecbugs:SQL_INJECTION_SPRING_JDBC": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findsecbugs:PREDICTABLE_RANDOM": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findbugs:PT_RELATIVE_PATH_TRAVERSAL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:XSS_REQUEST_PARAMETER_TO_SERVLET_WRITER": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:XSS_REQUEST_PARAMETER_TO_SEND_ERROR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findsecbugs:SPRING_CSRF_UNRESTRICTED_REQUEST_MAPPING": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "MINOR", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "java:S4830": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "java:S5527": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "findbugs:USM_USELESS_ABSTRACT_METHOD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_UNSUPPORTED_METHOD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IM_BAD_CHECK_FOR_ODD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EQ_DOESNT_OVERRIDE_EQUALS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:PS_PUBLIC_SEMAPHORES": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MTIA_SUSPECT_SERVLET_INSTANCE_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:MTIA_SUSPECT_STRUTS_INSTANCE_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RI_REDUNDANT_INTERFACES": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:CI_CONFUSED_INHERITANCE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_HARDCODED_ABSOLUTE_FILENAME": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:QF_QUESTIONABLE_FOR_LOOP": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IM_AVERAGE_COMPUTATION_COULD_OVERFLOW": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UC_USELESS_CONDITION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UC_USELESS_CONDITION_TYPE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:PZLA_PREFER_ZERO_LENGTH_ARRAYS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:CAA_COVARIANT_ARRAY_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:CAA_COVARIANT_ARRAY_LOCAL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:CAA_COVARIANT_ARRAY_RETURN": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DLS_DEAD_LOCAL_STORE_OF_NULL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DLS_DEAD_LOCAL_STORE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DLS_DEAD_LOCAL_STORE_SHADOWS_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_DEREFERENCE_OF_READLINE_VALUE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SA_FIELD_DOUBLE_ASSIGNMENT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SA_LOCAL_DOUBLE_ASSIGNMENT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:REC_CATCH_EXCEPTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UWF_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_IMMEDIATE_DEREFERENCE_OF_READLINE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IC_INIT_CIRCULARITY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BC_VACUOUS_INSTANCEOF": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:INT_BAD_REM_BY_1": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ICAST_IDIV_CAST_TO_DOUBLE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_USELESS_SUBSTRING": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_LOAD_OF_KNOWN_NULL_VALUE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RV_CHECK_FOR_POSITIVE_INDEXOF": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:XFB_XML_FACTORY_BYPASS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RV_DONT_JUST_NULL_CHECK_READLINE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RV_RETURN_VALUE_IGNORED_INFERRED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_METHOD_RETURN_RELAXING_ANNOTATION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:USM_USELESS_SUBCLASS_METHOD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_METHOD_PARAMETER_TIGHTENS_ANNOTATION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_METHOD_PARAMETER_RELAXING_ANNOTATION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DB_DUPLICATE_BRANCHES": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DB_DUPLICATE_SWITCH_CLAUSES": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_NONSERIALIZABLE_OBJECT_WRITTEN": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DCN_NULLPOINTER_EXCEPTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:34+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_NULL_ON_SOME_PATH_MIGHT_BE_INFEASIBLE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:IA_AMBIGUOUS_INVOCATION_OF_INHERITED_OR_OUTER_METHOD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NS_DANGEROUS_NON_SHORT_CIRCUIT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_PRIVATE_READ_RESOLVE_NOT_INHERITED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BC_BAD_CAST_TO_ABSTRACT_COLLECTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BC_BAD_CAST_TO_CONCRETE_COLLECTION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NS_NON_SHORT_CIRCUIT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:NP_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RCN_REDUNDANT_COMPARISON_OF_NULL_AND_NONNULL_VALUE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RCN_REDUNDANT_COMPARISON_TWO_NULL_VALUES": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RCN_REDUNDANT_NULLCHECK_OF_NONNULL_VALUE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RCN_REDUNDANT_NULLCHECK_OF_NULL_VALUE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RV_REM_OF_RANDOM_INT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RV_REM_OF_HASHCODE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ICAST_INTEGER_MULTIPLY_CAST_TO_LONG": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:RV_RETURN_VALUE_IGNORED_NO_SIDE_EFFECT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SA_LOCAL_SELF_ASSIGNMENT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SF_SWITCH_NO_DEFAULT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SF_SWITCH_FALLTHROUGH": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:CD_CIRCULAR_DEPENDENCY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:FE_FLOATING_POINT_EQUALITY": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DMI_THREAD_PASSED_WHERE_RUNNABLE_EXPECTED": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:SE_TRANSIENT_FIELD_OF_NONSERIALIZABLE_CLASS": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BC_UNCONFIRMED_CAST": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:BC_UNCONFIRMED_CAST_OF_RETURN_VALUE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:URF_UNREAD_PUBLIC_OR_PROTECTED_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ICAST_QUESTIONABLE_UNSIGNED_RIGHT_SHIFT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UUF_UNUSED_PUBLIC_OR_PROTECTED_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:EQ_UNUSUAL": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UWF_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:DLS_DEAD_LOCAL_STORE_IN_RETURN": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UCF_USELESS_CONTROL_FLOW": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UCF_USELESS_CONTROL_FLOW_NEXT_LINE": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UC_USELESS_VOID_METHOD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UC_USELESS_OBJECT": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:UC_USELESS_OBJECT_STACK": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:INT_VACUOUS_BIT_OPERATION": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:INT_VACUOUS_COMPARISON": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:TQ_EXPLICIT_UNKNOWN_SOURCE_VALUE_REACHES_ALWAYS_SINK": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}]}, "qProfiles": {"AYWlkrLqNIXMz9ZdzrmE": {"name": "q-sonar", "lang": "java", "langName": "Java", "parent": "AYWlabzyNIXMz9Zdzq24"}, "AYWlabzyNIXMz9Zdzq24": {"name": "q-sonar-base", "lang": "java", "langName": "Java", "parent": "AYWlFw9PNIXMz9Zdzpfn"}}}