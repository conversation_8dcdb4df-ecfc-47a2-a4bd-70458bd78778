{"total": 618, "p": 1, "ps": 500, "rules": [{"key": "java:S3751", "name": "\"@RequestMapping\" methods should be \"public\"", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["owasp-a6", "spring"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "java:S5301", "name": "\"ActiveMQConnectionFactory\" should not be vulnerable to malicious code deserialization", "severity": "MINOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a8"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S3066", "name": "\"enum\" fields should not be publicly mutable", "severity": "MINOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "java:S2976", "name": "\"File.createTempFile\" should not be used to create a directory", "severity": "CRITICAL", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S3510", "name": "\"HostnameVerifier.verify\" should not always return true", "severity": "BLOCKER", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S4601", "name": "\"HttpSecurity\" URL patterns should be correctly ordered", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["owasp-a6", "spring"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S2254", "name": "\"HttpServletRequest.getRequestedSessionId()\" should not be used", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a2", "sans-top25-porous"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S2258", "name": "\"javax.crypto.NullCipher\" should not be used for anything other than testing", "severity": "BLOCKER", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S4347", "name": "\"SecureRandom\" seeds should not be predictable", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cert", "cwe", "owasp-a6", "pitfall"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S2115", "name": "A secure password should be used when connecting to a database", "severity": "BLOCKER", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a2", "owasp-a3"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S4432", "name": "AES encryption algorithm should be used with secured mode", "severity": "CRITICAL", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S4434", "name": "Allowing deserialization of LDAP objects is security-sensitive", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a8"], "lang": "java", "langName": "Java", "params": [], "type": "SECURITY_HOTSPOT"}, {"key": "findbugs:JUA_DONT_ASSERT_INSTANCEOF_IN_TESTS", "name": "Bad practice -  Asserting value of instanceof in tests is not recommended.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CO_ABSTRACT_SELF", "name": "Bad practice - Abstract class defines covariant compareTo() method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EQ_ABSTRACT_SELF", "name": "Bad practice - Abstract class defines covariant equals() method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DMI_ENTRY_SETS_MAY_REUSE_ENTRY_OBJECTS", "name": "Bad practice - Adding elements of an entry set may fail due to reuse of Entry objects", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SW_SWING_METHODS_INVOKED_IN_SWING_THREAD", "name": "Bad practice - Certain swing methods need to be invoked in Swing thread", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:BIT_SIGNED_CHECK", "name": "Bad practice - Check for sign of bitwise operation", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CN_IMPLEMENTS_CLONE_BUT_NOT_CLONEABLE", "name": "Bad practice - Class defines clone() but doesn't implement Cloneable", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EQ_COMPARETO_USE_OBJECT_EQUALS", "name": "Bad practice - Class defines compareTo(...) and uses Object.equals()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:HE_EQUALS_USE_HASHCODE", "name": "Bad practice - Class defines equals() and uses Object.hashCode()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:HE_EQUALS_NO_HASHCODE", "name": "Bad practice - Class defines equals() but not hashCode()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:HE_HASHCODE_USE_OBJECT_EQUALS", "name": "Bad practice - Class defines hashCode() and uses Object.equals()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:HE_HASHCODE_NO_EQUALS", "name": "Bad practice - Class defines hashCode() but not equals()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CN_IDIOM", "name": "Bad practice - Class implements Cloneable but does not define or use clone method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:HE_INHERITS_EQUALS_USE_HASHCODE", "name": "Bad practice - Class inherits equals() and uses Object.hashCode()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_NO_SUITABLE_CONSTRUCTOR_FOR_EXTERNALIZATION", "name": "Bad practice - Class is Externalizable but doesn't define a void constructor", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_CLASS_NOT_EXCEPTION", "name": "Bad practice - Class is not derived from an Exception, even though it is named as such", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_NO_SUITABLE_CONSTRUCTOR", "name": "Bad practice - Class is Serializable but its superclass doesn't define a void constructor", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_NO_SERIALVERSIONID", "name": "Bad practice - Class is Serializable, but doesn't define serialVersionUID", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_CLASS_NAMING_CONVENTION", "name": "Bad practice - Class names should start with an upper case letter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_SAME_SIMPLE_NAME_AS_INTERFACE", "name": "Bad practice - Class names shouldn't shadow simple name of implemented interface", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_SAME_SIMPLE_NAME_AS_SUPERCLASS", "name": "Bad practice - Class names shouldn't shadow simple name of superclass", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CN_IDIOM_NO_SUPER_CALL", "name": "Bad practice - clone method does not call super.clone()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_CLONE_COULD_RETURN_NULL", "name": "Bad practice - Clone method may return null", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_COMPARATOR_SHOULD_BE_SERIALIZABLE", "name": "Bad practice - Comparator doesn't implement Serializable", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CO_COMPARETO_INCORRECT_FLOATING", "name": "Bad practice - compareTo()/compare() incorrectly handles float or double value", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CO_COMPARETO_RESULTS_MIN_VALUE", "name": "Bad practice - compareTo()/compare() returns Integer.MIN_VALUE", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ES_COMPARING_STRINGS_WITH_EQ", "name": "Bad practice - Comparison of String objects using == or !=", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ES_COMPARING_PARAMETER_STRING_WITH_EQ", "name": "Bad practice - Comparison of String parameter using == or !=", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_CONFUSING", "name": "Bad practice - Confusing method names", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CO_SELF_NO_OBJECT", "name": "Bad practice - Covariant compareTo() method defined", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EQ_SELF_NO_OBJECT", "name": "Bad practice - Covariant equals() method defined", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:AM_CREATES_EMPTY_JAR_FILE_ENTRY", "name": "Bad practice - Creates an empty jar file entry", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:AM_CREATES_EMPTY_ZIP_FILE_ENTRY", "name": "Bad practice - Creates an empty zip file entry", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:PZ_DONT_REUSE_ENTRY_OBJECTS_IN_ITERATORS", "name": "Bad practice - Don't reuse entry objects in iterators", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DMI_USING_REMOVEALL_TO_CLEAR_COLLECTION", "name": "Bad practice - Don't use removeAll to clear a collection", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:IMSE_DONT_CATCH_IMSE", "name": "Bad practice - Dubious catching of IllegalMonitorStateException", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FI_EMPTY", "name": "Bad practice - Empty finalizer should be deleted", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ME_MUTABLE_ENUM_FIELD", "name": "Bad practice - Enum field is public and mutable", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EQ_CHECK_FOR_OPERAND_NOT_COMPATIBLE_WITH_THIS", "name": "Bad practice - Equals checks for incompatible operand", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EQ_GETCLASS_AND_CLASS_CONSTANT", "name": "Bad practice - equals method fails for subtypes", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:BC_EQUALS_METHOD_SHOULD_WORK_FOR_ALL_OBJECTS", "name": "Bad practice - Equals method should not assume anything about the type of its argument", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_EQUALS_SHOULD_HANDLE_NULL_ARGUMENT", "name": "Bad practice - equals() method does not check for null argument", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FI_EXPLICIT_INVOCATION", "name": "Bad practice - Explicit invocation of finalizer", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_FIELD_NAMING_CONVENTION", "name": "Bad practice - Field names should start with a lower case letter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:JCIP_FIELD_ISNT_FINAL_IN_IMMUTABLE_CLASS", "name": "Bad practice - Fields of immutable classes should be final", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FI_MISSING_SUPER_CALL", "name": "Bad practice - Finalizer does not call superclass finalizer", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FI_USELESS", "name": "Bad practice - Finalizer does nothing but call superclass finalizer", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FI_NULLIFY_SUPER", "name": "Bad practice - Finalizer nullifies superclass finalizer", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FI_FINALIZER_NULLS_FIELDS", "name": "Bad practice - Finalizer nulls fields", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FI_FINALIZER_ONLY_NULLS_FIELDS", "name": "Bad practice - Finalizer only nulls fields", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:VA_FORMAT_STRING_USES_NEWLINE", "name": "Bad practice - Format string should use %n rather than \\n", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:IT_NO_SUCH_ELEMENT", "name": "Bad practice - Iterator next() method cannot throw NoSuchElementException", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_WRONG_PACKAGE_INTENTIONAL", "name": "Bad practice - Method doesn't override method in superclass due to wrong package for parameter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RV_RETURN_VALUE_IGNORED_BAD_PRACTICE", "name": "Bad practice - Method ignores exceptional return value", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RR_NOT_CHECKED", "name": "Bad practice - Method ignores results of InputStream.read()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SR_NOT_CHECKED", "name": "Bad practice - Method ignores results of InputStream.skip()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:THROWS_METHOD_THROWS_RUNTIMEEXCEPTION", "name": "Bad practice - <PERSON> intentionally throws RuntimeException.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DM_RUN_FINALIZERS_ON_EXIT", "name": "Bad practice - Method invokes dangerous method runFinalizersOnExit", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DM_EXIT", "name": "Bad practice - Method invokes System.exit(...)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:THROWS_METHOD_THROWS_CLAUSE_BASIC_EXCEPTION", "name": "Bad practice - Method lists Exception in its throws clause.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:THROWS_METHOD_THROWS_CLAUSE_THROWABLE", "name": "Bad practice - Method lists Throwable in its throws clause.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ODR_OPEN_DATABASE_RESOURCE", "name": "Bad practice - Method may fail to close database resource", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ODR_OP<PERSON>_DATABASE_RESOURCE_EXCEPTION_PATH", "name": "Bad practice - Method may fail to close database resource on exception", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:OS_OPEN_STREAM", "name": "Bad practice - Method may fail to close stream", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:OS_OPEN_STREAM_EXCEPTION_PATH", "name": "Bad practice - Method may fail to close stream on exception", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DE_MIGHT_DROP", "name": "Bad practice - Method might drop exception", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DE_MIGHT_IGNORE", "name": "Bad practice - Method might ignore exception", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_METHOD_NAMING_CONVENTION", "name": "Bad practice - Method names should start with a lower case letter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_BOOLEAN_RETURN_NULL", "name": "Bad practice - Method with Boolean return type returns explicit null", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ISC_INSTANTIATE_STATIC_CLASS", "name": "Bad practice - Needless instantiation of class that only supplies static methods", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RV_NEGATING_RESULT_OF_COMPARETO", "name": "Bad practice - Negating the result of compareTo()/compare()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_BAD_FIELD_INNER_CLASS", "name": "Bad practice - Non-serializable class has a serializable inner class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_BAD_FIELD_STORE", "name": "Bad practice - Non-serializable value stored into instance field of a serializable class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_BAD_FIELD", "name": "Bad practice - Non-transient non-serializable instance field in serializable class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ME_ENUM_FIELD_SETTER", "name": "Bad practice - Public enum method unconditionally sets its field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DMI_RANDOM_USED_ONLY_ONCE", "name": "Bad practice - Random object created and used only once", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CNT_ROUGH_CONSTANT_VALUE", "name": "Bad practice - Rough value of known constant found", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_INNER_CLASS", "name": "Bad practice - Serializable inner class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_NONFINAL_SERIALVERSIONID", "name": "Bad practice - serialVersionUID isn't final", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_NONLONG_SERIALVERSIONID", "name": "Bad practice - serialVersionUID isn't long", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_NONSTATIC_SERIALVERSIONID", "name": "Bad practice - serialVersionUID isn't static", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SI_INSTANCE_BEFORE_FINALS_ASSIGNED", "name": "Bad practice - Static initializer creates instance before all static final fields assigned", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:J2EE_STORE_OF_NON_SERIALIZABLE_OBJECT_INTO_SESSION", "name": "Bad practice - Store of non serializable object into HttpSession", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:IC_SUPERCLASS_USES_SUBCLASS_DURING_INITIALIZATION", "name": "Bad practice - Superclass uses subclass during initialization", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RC_REF_COMPARISON_BAD_PRACTICE_BOOLEAN", "name": "Bad practice - Suspicious reference comparison of Boolean values", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RC_REF_COMPARISON_BAD_PRACTICE", "name": "Bad practice - Suspicious reference comparison to constant", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_READ_RESOLVE_MUST_RETURN_OBJECT", "name": "Bad practice - The readResolve method must be declared with a return type of Object.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_TOSTRING_COULD_RETURN_NULL", "name": "Bad practice - toString method may return null", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_TRANSIENT_FIELD_NOT_RESTORED", "name": "Bad practice - Transient field that isn't set by deserialization.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:GC_UNCHECKED_TYPE_IN_GENERIC_CALL", "name": "Bad practice - Unchecked type in generic call", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UI_INHERITANCE_UNSAFE_GETRESOURCE", "name": "Bad practice - Usage of GetResource may be unsafe if class is extended", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_FUTURE_KEYWORD_USED_AS_IDENTIFIER", "name": "Bad practice - Use of identifier that is a keyword in later versions of Java", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_FUTURE_KEYWORD_USED_AS_MEMBER_IDENTIFIER", "name": "Bad practice - Use of identifier that is a keyword in later versions of Java", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NM_VERY_CONFUSING_INTENTIONAL", "name": "Bad practice - Very confusing method names (but perhaps intentional)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["bad-practice"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "java:S2647", "name": "Basic authentication should not be used", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a3", "sans-top25-porous"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S5547", "name": "Cipher algorithms should be robust", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cert", "cwe", "owasp-a3", "owasp-a6", "privacy", "sans-top25-porous"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S3329", "name": "Cipher Block Chaining IV's should be unpredictable", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S2658", "name": "Classes should not be loaded dynamically", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:RE_POSSIBLE_UNINTENDED_PATTERN", "name": "Correctness - \".\" or \"|\" used for regular expression", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:ICAST_BAD_SHIFT_AMOUNT", "name": "Correctness - 32 bit int shifted by an amount not in the range -31..31", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IL_CONTAINER_ADDED_TO_ITSELF", "name": "Correctness - A collection is added to itself", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BC_NULL_INSTANCEOF", "name": "Correctness - A known null value is checked to see if it is an instance of a type", "severity": "MAJOR", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NULL_INSTANCEOF", "name": "Correctness - A known null value is checked to see if it is an instance of a type", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IP_PARAMETER_IS_DEAD_BUT_OVERWRITTEN", "name": "Correctness - A parameter is dead upon entry to a method but overwritten", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IL_INFINITE_LOOP", "name": "Correctness - An apparent infinite loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IL_INFINITE_RECURSIVE_LOOP", "name": "Correctness - An apparent infinite recursive loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NM_METHOD_CONSTRUCTOR_CONFUSION", "name": "Correctness - Apparent method/constructor confusion", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RANGE_ARRAY_INDEX", "name": "Correctness - Array index is out of bounds", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RANGE_ARRAY_LENGTH", "name": "Correctness - Array length is out of bounds", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RANGE_ARRAY_OFFSET", "name": "Correctness - Array offset is out of bounds", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BAC_BAD_APPLET_CONSTRUCTOR", "name": "Correctness - Bad Applet Constructor relies on uninitialized AppletStub", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RV_ABSOLUTE_VALUE_OF_HASHCODE", "name": "Correctness - Bad attempt to compute absolute value of signed 32-bit hashcode", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RV_ABSOLUTE_VALUE_OF_RANDOM_INT", "name": "Correctness - Bad attempt to compute absolute value of signed random integer", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:INT_BAD_COMPARISON_WITH_INT_VALUE", "name": "Correctness - Bad comparison of int value with long constant", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:INT_BAD_COMPARISON_WITH_NONNEGATIVE_VALUE", "name": "Correctness - Bad comparison of nonnegative value with negative constant or zero", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:INT_BAD_COMPARISON_WITH_SIGNED_BYTE", "name": "Correctness - Bad comparison of signed byte", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_BAD_MONTH", "name": "Correctness - Bad constant value for month", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_BIGDECIMAL_CONSTRUCTED_FROM_DOUBLE", "name": "Correctness - BigDecimal constructed from double that isn't represented precisely", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BIT_ADD_OF_SIGNED_BYTE", "name": "Correctness - Bitwise add of signed byte value", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BIT_IOR_OF_SIGNED_BYTE", "name": "Correctness - Bitwise OR of signed byte value", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EC_UNRELATED_INTERFACES", "name": "Correctness - Call to equals() comparing different interface types", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EC_UNRELATED_TYPES", "name": "Correctness - Call to equals() comparing different types", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EC_UNRELATED_CLASS_AND_INTERFACE", "name": "Correctness - Call to equals() comparing unrelated class and interface", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EC_NULL_ARG", "name": "Correctness - Call to equals(null)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_ANNOTATION_IS_NOT_VISIBLE_TO_REFLECTION", "name": "Correctness - Cannot use reflection to check for presence of annotation without runtime retention", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BIT_SIGNED_CHECK_HIGH_BIT", "name": "Correctness - Check for sign of bitwise operation involving negative number", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BIT_AND_ZZ", "name": "Correctness - Check to see if ((...) & 0) == 0", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NM_BAD_EQUAL", "name": "Correctness - Class defines equal(Object); should it be equals(Object)?", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:MF_CLASS_MASKS_FIELD", "name": "Correctness - Class defines field that masks a superclass field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NM_LCASE_HASHCODE", "name": "Correctness - Class defines hashcode(); should it be hashCode()?", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NM_LCASE_TOSTRING", "name": "Correctness - Class defines tostring(); should it be toString()?", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:VR_UNRESOLVABLE_REFERENCE", "name": "Correctness - Class makes reference to unresolvable class or method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BOA_BADLY_OVERRIDDEN_ADAPTER", "name": "Correctness - Class overrides a method implemented in super class Adapter wrongly", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_CLOSING_NULL", "name": "Correctness - close() invoked on a value that is always null", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RV_CHECK_COMPARETO_FOR_SPECIFIC_RETURN_VALUE", "name": "Correctness - Code checks for specific values returned by compareTo", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_COLLECTIONS_SHOULD_NOT_CONTAIN_THEMSELVES", "name": "Correctness - Collections should not contain themselves", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:TQ_COMPARING_VALUES_WITH_INCOMPATIBLE_TYPE_QUALIFIERS", "name": "Correctness - Comparing values with incompatible type qualifiers", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EQ_DONT_DEFINE_EQUALS_FOR_ENUM", "name": "Correctness - Covariant equals() method defined for enum", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EQ_SELF_USE_OBJECT", "name": "Correctness - Covariant equals() method defined, Object.equals(Object) inherited", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_SCHEDULED_THREAD_POOL_EXECUTOR_WITH_ZERO_CORE_THREADS", "name": "Correctness - Creation of ScheduledThreadPoolExecutor with zero core threads", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_DOH", "name": "Correctness - D'oh! A nonsensical method invocation", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EOS_BAD_END_OF_STREAM_CHECK", "name": "Correctness - Data read is converted before comparison to -1", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SF_DEAD_STORE_DUE_TO_SWITCH_FALLTHROUGH", "name": "Correctness - Dead store due to switch statement fall through", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SF_DEAD_STORE_DUE_TO_SWITCH_FALLTHROUGH_TO_THROW", "name": "Correctness - Dead store due to switch statement fall through to throw", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DLS_DEAD_STORE_OF_CLASS_LITERAL", "name": "Correctness - Dead store of class literal", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SIC_THREADLOCAL_DEADLY_EMBRACE", "name": "Correctness - Deadly embrace of non-static inner class and thread local", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:FL_FLOATS_AS_LOOP_COUNTERS", "name": "Correctness - Do not use floating-point variables as loop counters", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IO_APPENDING_TO_OBJECT_OUTPUT_STREAM", "name": "Correctness - Doomed attempt to append to an object output stream", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:FE_TEST_IF_EQUAL_TO_NOT_A_NUMBER", "name": "Correctness - Doomed test for equality to NaN", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_LONG_BITS_TO_DOUBLE_INVOKED_ON_INT", "name": "Correctness - Double.longBitsToDouble invoked on an int", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EQ_ALWAYS_FALSE", "name": "Correctness - equals method always returns false", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EQ_ALWAYS_TRUE", "name": "Correctness - equals method always returns true", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EQ_COMPARING_CLASS_NAMES", "name": "Correctness - equals method compares class names rather than class objects", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EQ_OVERRIDING_EQUALS_NOT_SYMMETRIC", "name": "Correctness - equals method overrides equals in superclass and may not be symmetric", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EQ_OTHER_NO_OBJECT", "name": "Correctness - equals() method defined that doesn't override equals(Object)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EQ_OTHER_USE_OBJECT", "name": "Correctness - equals() method defined that doesn't override Object.equals(Object)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EC_ARRAY_AND_NONARRAY", "name": "Correctness - equals() used to compare array and nonarray", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EC_INCOMPATIBLE_ARRAY_COMPARE", "name": "Correctness - equals(...) used to compare incompatible arrays", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RV_EXCEPTION_NOT_THROWN", "name": "Correctness - Exception created and dropped rather than thrown", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UWF_NULL_FIELD", "name": "Correctness - Field only ever set to null", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RE_CANT_USE_FILE_SEPARATOR_AS_REGULAR_EXPRESSION", "name": "Correctness - File.separator used for regular expression", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_FUTILE_ATTEMPT_TO_CHANGE_MAXPOOL_SIZE_OF_SCHEDULED_THREAD_POOL_EXECUTOR", "name": "Correctness - Futile attempt to change max pool size of ScheduledThreadPoolExecutor", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_CALLING_NEXT_FROM_HASNEXT", "name": "Correctness - hasNext method invokes next", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BC_IMPOSSIBLE_CAST", "name": "Correctness - Impossible cast", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BC_IMPOSSIBLE_CAST_PRIMITIVE_ARRAY", "name": "Correctness - Impossible cast involving primitive array", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BC_IMPOSSIBLE_DOWNCAST", "name": "Correctness - Impossible downcast", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BC_IMPOSSIBLE_DOWNCAST_OF_TOARRAY", "name": "Correctness - Impossible downcast of toArray() result", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BIT_IOR", "name": "Correctness - Incompatible bit masks", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BIT_AND", "name": "Correctness - Incompatible bit masks", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_INVALID_MIN_MAX", "name": "Correctness - Incorrect combination of Math.max and Math.min", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BC_IMPOSSIBLE_INSTANCEOF", "name": "Correctness - instanceof will always return false", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:ICAST_INT_CAST_TO_FLOAT_PASSED_TO_ROUND", "name": "Correctness - int value cast to float and then passed to Math.round", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:ICAST_INT_2_LONG_AS_INSTANT", "name": "Correctness - int value converted to long and used as absolute time", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IM_MULTIPLYING_RESULT_OF_IREM", "name": "Correctness - Integer multiply of result of integer remainder", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:ICAST_INT_CAST_TO_DOUBLE_PASSED_TO_CEIL", "name": "Correctness - Integral value cast to double and then passed to Math.ceil", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RE_BAD_SYNTAX_FOR_REGULAR_EXPRESSION", "name": "Correctness - Invalid syntax for regular expression", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EC_BAD_ARRAY_COMPARE", "name": "Correctness - Invocation of equals() on an array, which is equivalent to ==", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_INVOKING_HASHCODE_ON_ARRAY", "name": "Correctness - Invocation of hashCode on an array", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_INVOKING_TOSTRING_ON_ARRAY", "name": "Correctness - Invocation of toString on an array", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_INVOKING_TOSTRING_ON_ANONYMOUS_ARRAY", "name": "Correctness - Invocation of toString on an unnamed array", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IJU_ASSERT_METHOD_INVOKED_FROM_RUN_METHOD", "name": "Correctness - J<PERSON><PERSON><PERSON> assertion in run method will not be noticed by JUnit", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:QBA_QUESTIONABLE_BOOLEAN_ASSIGNMENT", "name": "Correctness - Method assigns boolean literal in boolean expression", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SQL_BAD_PREPARED_STATEMENT_ACCESS", "name": "Correctness - Method attempts to access a prepared statement parameter with index 0", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BRSA_BAD_RESULTSET_ACCESS", "name": "Correctness - Method attempts to access a result set field with index 0", "severity": "MAJOR", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SQL_BAD_RESULTSET_ACCESS", "name": "Correctness - Method attempts to access a result set field with index 0", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NULL_PARAM_DEREF", "name": "Correctness - Method call passes null for non-null parameter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NULL_PARAM_DEREF_ALL_TARGETS_DANGEROUS", "name": "Correctness - Method call passes null for non-null parameter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NONNULL_PARAM_VIOLATION", "name": "Correctness - Method call passes null to a non-null parameter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:M<PERSON>_METHOD_MASKS_FIELD", "name": "Correctness - Method defines a variable that obscures a field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_ARGUMENT_MIGHT_BE_NULL", "name": "Correctness - Method does not check for null argument", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NM_WRONG_PACKAGE", "name": "Correctness - Method doesn't override method in superclass due to wrong package for parameter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RV_RETURN_VALUE_IGNORED", "name": "Correctness - Method ignores return value", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NONNULL_RETURN_VIOLATION", "name": "Correctness - Method may return null, but is declared @Nonnull", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SE_METHOD_MUST_BE_PRIVATE", "name": "Correctness - Method must be private in order for serialization to work", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "fb-contrib:USFW_UNSYNCHRONIZED_SINGLETON_FIELD_WRITES", "name": "Correctness - Method of Singleton class writes to a field in an unsynchronized manner", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:FL_MATH_USING_FLOAT_PRECISION", "name": "Correctness - Method performs math using floating point precision", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_OPTIONAL_RETURN_NULL", "name": "Correctness - Method with Optional return type returns explicit null", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:FB_MISSING_EXPECTED_WARNING", "name": "Correctness - Missing expected or desired warning from SpotBugs", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:GC_UNRELATED_TYPES", "name": "Correctness - No relationship between generic parameter and method argument", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NONNULL_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR", "name": "Correctness - Non-null field is not initialized", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NULL_PARAM_DEREF_NONVIRTUAL", "name": "Correctness - Non-virtual method call passes null for non-null parameter", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SA_FIELD_SELF_COMPUTATION", "name": "Correctness - Nonsensical self computation involving a field (e.g., x & x)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SA_LOCAL_SELF_COMPUTATION", "name": "Correctness - Nonsensical self computation involving a variable (e.g., x & x)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_ALWAYS_NULL", "name": "Correctness - <PERSON><PERSON> pointer dereference", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_ALWAYS_NULL_EXCEPTION", "name": "Correctness - <PERSON><PERSON> pointer dereference in method on exception path", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_GUARANTEED_DEREF", "name": "Correctness - Null value is guaranteed to be dereferenced", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RCN_REDUNDANT_NULLCHECK_WOULD_HAVE_BEEN_A_NPE", "name": "Correctness - Nullcheck of value previously dereferenced", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DLS_OVERWRITTEN_INCREMENT", "name": "Correctness - Overwritten increment", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BSHIFT_WRONG_ADD_PRIORITY", "name": "Correctness - Possible bad parsing of shift operation", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NULL_ON_SOME_PATH", "name": "Correctness - Possible null pointer dereference", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_NULL_ON_SOME_PATH_EXCEPTION", "name": "Correctness - Possible null pointer dereference in method on exception path", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:CAA_COVARIANT_ARRAY_ELEMENT_STORE", "name": "Correctness - Possibly incompatible element is stored in covariant array", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:VA_PRIMITIVE_ARRAY_PASSED_TO_OBJECT_VARARG", "name": "Correctness - Primitive array passed to function expecting a variable number of object arguments", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RV_01_TO_INT", "name": "Correctness - Random value from 0 to 1 is coerced to the integer 0", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_UNWRITTEN_FIELD", "name": "Correctness - Read of unwritten field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RpC_REPEATED_CONDITIONAL_TEST", "name": "Correctness - Repeated conditional tests", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_ARGUMENTS_WRONG_ORDER", "name": "Correctness - Reversed method arguments", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SA_FIELD_SELF_ASSIGNMENT", "name": "Correctness - Self assignment of field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SA_LOCAL_SELF_ASSIGNMENT_INSTEAD_OF_FIELD", "name": "Correctness - Self assignment of local rather than assignment to field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SA_FIELD_SELF_COMPARISON", "name": "Correctness - Self comparison of field with itself", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SA_LOCAL_SELF_COMPARISON", "name": "Correctness - Self comparison of value with itself", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:HE_SIGNATURE_DECLARES_HASHING_OF_UNHASHABLE_CLASS", "name": "Correctness - Signature declares use of unhashable class in hashed construct", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:STI_INTERRUPTED_ON_UNKNOWNTHREAD", "name": "Correctness - Static Thread.interrupted() method invoked on thread instance", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_STORE_INTO_NONNULL_FIELD", "name": "Correctness - Store of null value into field annotated @Nonnull", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RANGE_STRING_INDEX", "name": "Correctness - String index is out of bounds", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:OVERRIDING_METHODS_MUST_INVOKE_SUPER", "name": "Correctness - Super method is annotated with @OverridingMethodsMustInvokeSuper, but the overriding method isn't calling the super method.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RC_REF_COMPARISON", "name": "Correctness - Suspicious reference comparison", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IJU_BAD_SUITE_METHOD", "name": "Correctness - TestCase declares a bad suite method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IJU_SETUP_NO_SUPER", "name": "Correctness - TestCase defines setUp that doesn't call super.setUp()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IJU_TEARDOWN_NO_SUPER", "name": "Correctness - TestCase defines tearDown that doesn't call super.tearDown()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IJU_NO_TESTS", "name": "Correctness - TestCase has no tests", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IJU_SUITE_NOT_STATIC", "name": "Correctness - TestCase implements a non-static suite method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SE_READ_RESOLVE_IS_STATIC", "name": "Correctness - The readResolve method must not be declared as a static method.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UMAC_UNCALLABLE_METHOD_OF_ANONYMOUS_CLASS", "name": "Correctness - Uncallable method defined in anonymous class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:FB_UNEXPECTED_WARNING", "name": "Correctness - Unexpected/undesired warning from SpotBugs", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UR_UNINIT_READ", "name": "Correctness - Uninitialized read of field in constructor", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UR_UNINIT_READ_CALLED_FROM_SUPER_CONSTRUCTOR", "name": "Correctness - Uninitialized read of field method called from constructor of superclass", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SIO_SUPERFLUOUS_INSTANCEOF", "name": "Correctness - Unnecessary type check done using instanceof operator", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:STI_INTERRUPTED_ON_CURRENTTHREAD", "name": "Correctness - Unneeded use of currentThread() call, to call interrupted()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UWF_UNWRITTEN_FIELD", "name": "Correctness - Unwritten field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:HE_USE_OF_UNHASHABLE_CLASS", "name": "Correctness - Use of class without a hashCode() method in a hashed data structure", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DLS_DEAD_LOCAL_INCREMENT_IN_RETURN", "name": "Correctness - Useless increment in return statement", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_VACUOUS_CALL_TO_EASYMOCK_METHOD", "name": "Correctness - Useless/vacuous call to EasyMock method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:EC_UNRELATED_TYPES_USING_POINTER_EQUALITY", "name": "Correctness - Using pointer equality to compare different types", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_VACUOUS_SELF_COLLECTION_CALL", "name": "Correctness - Vacuous call to collections", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:TQ_ALWAYS_VALUE_USED_WHERE_NEVER_REQUIRED", "name": "Correctness - Value annotated as carrying a type qualifier used where a value that must not carry that qualifier is required", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:TQ_NEVER_VALUE_USED_WHERE_ALWAYS_REQUIRED", "name": "Correctness - Value annotated as never carrying a type qualifier used where value carrying that qualifier is required", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_GUARANTEED_DEREF_ON_EXCEPTION_PATH", "name": "Correctness - Value is null and guaranteed to be dereferenced on exception path", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:TQ_MAYBE_SOURCE_VALUE_REACHES_NEVER_SINK", "name": "Correctness - Value that might carry a type qualifier is always used in a way prohibits it from having that type qualifier", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:TQ_MAYBE_SOURCE_VALUE_REACHES_ALWAYS_SINK", "name": "Correctness - Value that might not carry a type qualifier is always used in a way requires that type qualifier", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:TQ_UNKNOWN_VALUE_USED_WHERE_ALWAYS_STRICTLY_REQUIRED", "name": "Correctness - Value without a type qualifier used where a value is required to have that qualifier", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NM_VERY_CONFUSING", "name": "Correctness - Very confusing method names", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["correctness"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "java:S4426", "name": "Cryptographic keys should be robust", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a3", "owasp-a6", "privacy", "rules"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S2277", "name": "Cryptographic RSA algorithms should always incorporate OAEP (Optimal Asymmetric Encryption Padding)", "severity": "CRITICAL", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a3", "owasp-a6", "sans-top25-porous"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S3281", "name": "Default EJB interceptors should be declared in \"ejb-jar.xml\"", "severity": "BLOCKER", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S3355", "name": "Defined filters should be used", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S5542", "name": "Encryption algorithms should be used with secure mode and padding scheme", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cert", "cwe", "owasp-a3", "owasp-a6", "privacy", "sans-top25-porous"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S1989", "name": "Exceptions should not be thrown from servlet methods", "severity": "MINOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cert", "cwe", "error-handling", "owasp-a3"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S4275", "name": "Getters and setters should access the expected fields", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["pitfall"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "java:S2089", "name": "HTTP referers should not be relied on", "severity": "CRITICAL", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:DM_CONVERT_CASE", "name": "I18n - Consider using Locale parameterized version of invoked method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["i18n"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DM_DEFAULT_ENCODING", "name": "I18n - Reliance on default encoding", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["i18n"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "java:S4433", "name": "LDAP connections should be authenticated", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a2"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:MC_OVERRIDABLE_METHOD_CALL_IN_CONSTRUCTOR", "name": "Malicious code - An overridable method is called from a constructor", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MC_OVERRIDABLE_METHOD_CALL_IN_CLONE", "name": "Malicious code - An overridable method is called from the clone() method.", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DP_CREATE_CLASSLOADER_INSIDE_DO_PRIVILEGED", "name": "Malicious code - Classloaders should only be created inside doPrivileged block", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:PERM_SUPER_NOT_CALLED_IN_GETPERMISSIONS", "name": "Malicious code - Custom class loader does not call its superclass's getPermissions()", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_MUTABLE_ARRAY", "name": "Malicious code - Field is a mutable array", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_MUTABLE_COLLECTION", "name": "Malicious code - Field is a mutable collection", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_MUTABLE_COLLECTION_PKGPROTECT", "name": "Malicious code - Field is a mutable collection which should be package protected", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_MUTABLE_HASHTABLE", "name": "Malicious code - Field is a mutable Hashtable", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_CANNOT_BE_FINAL", "name": "Malicious code - Field isn't final and cannot be protected from malicious code", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_SHOULD_BE_FINAL", "name": "Malicious code - Field isn't final but should be", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_SHOULD_BE_REFACTORED_TO_BE_FINAL", "name": "Malicious code - Field isn't final but should be refactored to be so", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_FINAL_PKGPROTECT", "name": "Malicious code - Field should be both final and package protected", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_OOI_PKGPROTECT", "name": "Malicious code - Field should be moved out of an interface and made package protected", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_PKGPROTECT", "name": "Malicious code - Field should be package protected", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FI_PUBLIC_SHOULD_BE_PROTECTED", "name": "Malicious code - Finalizer should be protected, not public", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EI_EXPOSE_BUF2", "name": "Malicious code - May expose internal representation by creating a buffer which incorporates reference to array", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EI_EXPOSE_REP2", "name": "Malicious code - May expose internal representation by incorporating reference to mutable object", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EI_EXPOSE_BUF", "name": "Malicious code - May expose internal representation by returning a buffer sharing non-public data", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_EXPOSE_BUF", "name": "Malicious code - May expose internal representation by returning a buffer sharing non-public data", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EI_EXPOSE_REP", "name": "Malicious code - May expose internal representation by returning reference to mutable object", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EI_EXPOSE_STATIC_BUF2", "name": "Malicious code - May expose internal static state by creating a buffer which stores an external array into a static field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EI_EXPOSE_STATIC_REP2", "name": "Malicious code - May expose internal static state by storing a mutable object into a static field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DP_DO_INSIDE_DO_PRIVILEGED", "name": "Malicious code - Method invoked that should be only be invoked inside a doPrivileged block", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:USC_POTENTIAL_SECURITY_CHECK_BASED_ON_UNTRUSTED_SOURCE", "name": "Malicious code - Potential security check based on untrusted source.", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:REFLC_REFLECTION_MAY_INCREASE_ACCESSIBILITY_OF_CLASS", "name": "Malicious code - Public method uses reflection to create a class it gets in its parameter which could increase the accessibility of any class", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:REFLF_REFLECTION_MAY_INCREASE_ACCESSIBILITY_OF_FIELD", "name": "Malicious code - Public method uses reflection to modify a field it gets in its parameter which could increase the accessibility of any class", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MS_EXPOSE_REP", "name": "Malicious code - Public static method may expose internal representation by returning array", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["malicious-code"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "java:S2039", "name": "Member variable visibility should be specified", "severity": "MINOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DM_USELESS_THREAD", "name": "Multi-threading - A thread was created using the default empty run method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:VO_VOLATILE_REFERENCE_TO_ARRAY", "name": "Multi-threading - A volatile reference to an array doesn't treat the array elements as volatile", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:VO_VOLATILE_INCREMENT", "name": "Multi-threading - An increment to a volatile field isn't atomic", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:STCAL_INVOKE_ON_STATIC_CALENDAR_INSTANCE", "name": "Multi-threading - Call to static Calendar", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:STCAL_INVOKE_ON_STATIC_DATE_FORMAT_INSTANCE", "name": "Multi-threading - Call to static DateFormat", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RS_READOBJECT_SYNC", "name": "Multi-threading - Class's readObject() method is synchronized", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:WS_WRITEOBJECT_SYNC", "name": "Multi-threading - Class's writeObject() method is synchronized but nothing else is", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:WA_AWAIT_NOT_IN_LOOP", "name": "Multi-threading - Condition.await() not in loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SC_START_IN_CTOR", "name": "Multi-threading - Constru<PERSON> invokes Thread.start()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:ESync_EMPTY_SYNC", "name": "Multi-threading - Empty synchronized block", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IS_FIELD_NOT_GUARDED", "name": "Multi-threading - Field not guarded against concurrent access", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IS_INCONSISTENT_SYNC", "name": "Multi-threading - Inconsistent synchronization", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IS2_INCONSISTENT_SYNC", "name": "Multi-threading - Inconsistent synchronization", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:LI_LAZY_INIT_UPDATE_STATIC", "name": "Multi-threading - Incorrect lazy initialization and update of static field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:LI_LAZY_INIT_INSTANCE", "name": "Multi-threading - Incorrect lazy initialization of instance field", "severity": "MAJOR", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:LI_LAZY_INIT_STATIC", "name": "Multi-threading - Incorrect lazy initialization of static field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SSD_DO_NOT_USE_INSTANCE_LOCK_ON_SHARED_STATIC_DATA", "name": "Multi-threading - Instance level lock was used on a shared static data", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RU_INVOKE_RUN", "name": "Multi-threading - Invokes run on a thread (did you mean to start it instead?)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SWL_SLEEP_WITH_LOCK_HELD", "name": "Multi-threading - Method calls Thread.sleep() with a lock held", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UL_UNRELEASED_LOCK_EXCEPTION_PATH", "name": "Multi-threading - Method does not release lock on all exception paths", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UL_UNRELEASED_LOCK", "name": "Multi-threading - Method does not release lock on all paths", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SP_SPIN_ON_FIELD", "name": "Multi-threading - Method spins on field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:M<PERSON>_SYNC_ON_UPDATED_FIELD", "name": "Multi-threading - Method synchronizes on an updated field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:MWN_MISMATCHED_NOTIFY", "name": "Multi-threading - Mismatched notify()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:MWN_MISMATCHED_WAIT", "name": "Multi-threading - Mismatched wait()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_MONITOR_WAIT_ON_CONDITION", "name": "Multi-threading - Monitor wait() called on Condition", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:MSF_MUTABLE_SERVLET_FIELD", "name": "Multi-threading - Mutable servlet field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NN_NAKED_NOTIFY", "name": "Multi-threading - Naked notify", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:TLW_TWO_LOCK_NOTIFY", "name": "Multi-threading - Notify with two locks held", "severity": "MAJOR", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DC_DOUBLECHECK", "name": "Multi-threading - Possible double-check of field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DC_PARTIALLY_CONSTRUCTED", "name": "Multi-threading - Possible exposure of partially initialized object", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:RV_RETURN_VALUE_OF_PUTIFABSENT_IGNORED", "name": "Multi-threading - Return value of putIfAbsent ignored, value passed to putIfAbsent reused", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:AT_OPERATION_SEQUENCE_ON_CONCURRENT_ABSTRACTION", "name": "Multi-threading - Sequence of calls to concurrent abstraction may not be atomic", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:STCAL_STATIC_CALENDAR_INSTANCE", "name": "Multi-threading - Static Calendar field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:STCAL_STATIC_SIMPLE_DATE_FORMAT_INSTANCE", "name": "Multi-threading - Static DateFormat", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DL_SYNCHRONIZATION_ON_BOOLEAN", "name": "Multi-threading - Synchronization on Boolean", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DL_SYNCHRONIZATION_ON_BOXED_PRIMITIVE", "name": "Multi-threading - Synchronization on boxed primitive", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DL_SYNCHRONIZATION_ON_UNSHARED_BOXED_PRIMITIVE", "name": "Multi-threading - Synchronization on boxed primitive values", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:<PERSON><PERSON>_SYNC_ON_FIELD_TO_GUARD_CHANGING_THAT_FIELD", "name": "Multi-threading - Synchronization on field in futile attempt to guard that field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:WL_USING_GETCLASS_RATHER_THAN_CLASS_LITERAL", "name": "Multi-threading - Synchronization on getClass rather than class literal", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DL_SYNCHRONIZATION_ON_SHARED_CONSTANT", "name": "Multi-threading - Synchronization on interned String", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:JLM_JSR166_LOCK_MONITORENTER", "name": "Multi-threading - Synchronization performed on Lock", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:JLM_JSR166_UTILCONCURRENT_MONITORENTER", "name": "Multi-threading - Synchronization performed on util.concurrent instance", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NP_SYNC_AND_NULL_CHECK_FIELD", "name": "Multi-threading - Synchronize and null check on the same field.", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UW_UNCOND_WAIT", "name": "Multi-threading - Unconditional wait", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UG_SYNC_SET_UNSYNC_GET", "name": "Multi-threading - Unsynchronized get method, synchronized set method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:JML_JSR166_CALLING_WAIT_RATHER_THAN_AWAIT", "name": "Multi-threading - Using monitor style wait methods on util.concurrent abstraction", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:NO_NOTIFY_NOT_NOTIFYALL", "name": "Multi-threading - Using notify() rather than notifyAll()", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:WA_NOT_IN_LOOP", "name": "Multi-threading - Wait not in loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:TLW_TWO_LOCK_WAIT", "name": "Multi-threading - Wait with two locks held", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["multi-threading"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "java:S2386", "name": "Mutable fields should not be \"public static\"", "severity": "MINOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cert", "cwe", "unpredictable"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "java:S2278", "name": "Neither DES (Data Encryption Standard) nor DESede (3DES) should be used", "severity": "BLOCKER", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": ["cert", "cwe", "owasp-a6", "sans-top25-porous"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S5679", "name": "OpenSAML2 should be configured to prevent authentication bypass", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["owasp-a2", "owasp-a9", "spring"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S5344", "name": "Passwords should not be stored in plain-text or with a fast hashing algorithm", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a2", "owasp-a3", "owasp-a6", "sans-top25-porous", "spring"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:BX_UNBOXING_IMMEDIATELY_REBOXED", "name": "Performance - Boxed value is unboxed and then immediately reboxed", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_BOXED_PRIMITIVE_FOR_COMPARE", "name": "Performance - Boxing a primitive to compare", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_BOXED_PRIMITIVE_FOR_PARSING", "name": "Performance - Boxing/unboxing to parse a primitive", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SIC_INNER_SHOULD_BE_STATIC_ANON", "name": "Performance - Could be refactored into a named static inner class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SIC_INNER_SHOULD_BE_STATIC_NEEDS_THIS", "name": "Performance - Could be refactored into a static inner class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_GC", "name": "Performance - Explicit garbage collection; extremely dubious except in benchmarking code", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:HSC_HUGE_SHARED_STRING_CONSTANT", "name": "Performance - Huge string constants is duplicated across multiple class files", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:WMI_WRONG_MAP_ITERATOR", "name": "Performance - Inefficient use of keySet iterator instead of entrySet iterator", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IIO_INEFFICIENT_INDEX_OF", "name": "Performance - Inefficient use of String.indexOf(String)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IIO_INEFFICIENT_LAST_INDEX_OF", "name": "Performance - Inefficient use of String.lastIndexOf(String)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_COLLECTION_OF_URLS", "name": "Performance - Maps and sets of URLs can be performance hogs", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IMA_INEFFICIENT_MEMBER_ACCESS", "name": "Performance - Method accesses a private member variable of owning class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_BOXED_PRIMITIVE_TOSTRING", "name": "Performance - Method allocates a boxed primitive just to call toString", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_NEW_FOR_GETCLASS", "name": "Performance - Method allocates an object, only to get the class object", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IIL_PATTERN_COMPILE_IN_LOOP", "name": "Performance - Method calls Pattern.compile in a loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IIL_PREPARE_STATEMENT_IN_LOOP", "name": "Performance - Method calls prepareStatement in a loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UM_UNNECESSARY_MATH", "name": "Performance - Method calls static Math class method on a constant value", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IIL_PATTERN_COMPILE_IN_LOOP_INDIRECT", "name": "Performance - Method compiles the regular expression in a loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SBSC_USE_STRINGBUFFER_CONCATENATION", "name": "Performance - Method concatenates strings using + in a loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_BOOLEAN_CTOR", "name": "Performance - Method invokes inefficient Boolean constructor; use Boolean.valueOf(...) instead", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_FP_NUMBER_CTOR", "name": "Performance - Method invokes inefficient floating-point Number constructor; use static valueOf instead", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_STRING_VOID_CTOR", "name": "Performance - Method invokes inefficient new String() constructor", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_STRING_CTOR", "name": "Performance - Method invokes inefficient new String(String) constructor", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_NUMBER_CTOR", "name": "Performance - Method invokes inefficient Number constructor; use static valueOf instead", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_STRING_TOSTRING", "name": "Performance - Method invokes toString() method on a String", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "fb-contrib:SEO_SUBOPTIMAL_EXPRESSION_ORDER", "name": "Performance - Method orders expressions in a conditional in a sub optimal way", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:ITA_INEFFICIENT_TO_ARRAY", "name": "Performance - Method uses toArray() with zero-length array argument", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:IIL_ELEMENTS_GET_LENGTH_IN_LOOP", "name": "Performance - NodeList.getLength() called in a loop", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BX_BOXING_IMMEDIATELY_UNBOXED", "name": "Performance - Primitive value is boxed and then immediately unboxed", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BX_BOXING_IMMEDIATELY_UNBOXED_TO_PERFORM_COERCION", "name": "Performance - Primitive value is boxed then unboxed to perform primitive coercion", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:BX_UNBOXED_AND_COERCED_FOR_TERNARY_OPERATOR", "name": "Performance - Primitive value is unboxed and coerced for ternary operator", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UPM_UNCALLED_PRIVATE_METHOD", "name": "Performance - Private method is never called", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SIC_INNER_SHOULD_BE_STATIC", "name": "Performance - Should be a static inner class", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DMI_BLOCKING_METHODS_ON_URL", "name": "Performance - The equals and hashCode methods of URL are blocking", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:URF_UNREAD_FIELD", "name": "Performance - Unread field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:SS_SHOULD_BE_STATIC", "name": "Performance - Unread field: should this field be static?", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:UUF_UNUSED_FIELD", "name": "Performance - Unused field", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findbugs:DM_NEXTINT_VIA_NEXTDOUBLE", "name": "Performance - Use the nextInt method of Random rather than nextDouble to generate a random integer", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["performance"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "java:S4684", "name": "Persistent entities should not be used as arguments of \"@RequestMapping\" methods", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a5", "spring"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S2275", "name": "Printf-style format strings should not lead to unexpected behavior at runtime", "severity": "BLOCKER", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cert"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "java:S899", "name": "Return values should not be ignored when they contain the operation status code", "severity": "MINOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cert", "cwe", "error-handling"], "lang": "java", "langName": "Java", "params": [], "type": "BUG"}, {"key": "findsecbugs:MALICIOUS_XSLT", "name": "Security - A malicious XSLT could be provided", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["injection", "owasp-a1", "owasp-a4", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:SQL_PREPARED_STATEMENT_GENERATED_FROM_NONCONSTANT_STRING", "name": "Security - A prepared statement is generated from a nonconstant String", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:PT_ABSOLUTE_PATH_TRAVERSAL", "name": "Security - Absolute path traversal in servlet", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:LDAP_ANONYMOUS", "name": "Security - Anonymous LDAP bind", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:AWS_QUERY_INJECTION", "name": "Security - AWS Query Injection", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:BAD_HEXA_CONVERSION", "name": "Security - Bad hexadecimal concatenation", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:BLOWFISH_KEY_SIZE", "name": "Security - Blowfish usage with short key", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:ANDROID_BROADCAST", "name": "Security - Broadcast (Android)", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["android", "cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:PADDING_ORACLE", "name": "Security - Cipher is susceptible to Padding Oracle", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:CIPHER_INTEGRITY", "name": "Security - Cipher with no integrity", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:HTTPONLY_COOKIE", "name": "Security - <PERSON><PERSON> without the HttpOnly flag", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:INSECURE_COOKIE", "name": "Security - <PERSON><PERSON> without the secure flag", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:DEFAULT_HTTP_CLIENT", "name": "Security - DefaultHttpClient with default constructor is not compatible with TLS 1.2", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:DES_USAGE", "name": "Security - DES is insecure", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:TDES_USAGE", "name": "Security - DESede is insecure", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:WICKET_XSS1", "name": "Security - Disabling HTML escaping put the application at risk for XSS", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a3", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:ECB_MODE", "name": "Security - ECB mode is insecure", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:DMI_EMPTY_DB_PASSWORD", "name": "Security - Empty database password", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:RPC_ENABLED_EXTENSIONS", "name": "Security - Enabling extensions in Apache XML RPC server or client.", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:ANDROID_EXTERNAL_FILE_ACCESS", "name": "Security - External file access (Android)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["android", "cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:WEAK_FILENAMEUTILS", "name": "Security - FilenameUtils not filtering null bytes", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:FORMAT_STRING_MANIPULATION", "name": "Security - Format String Manipulation", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:JAXRS_ENDPOINT", "name": "Security - Found JAX-RS REST endpoint", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:JAXWS_ENDPOINT", "name": "Security - Found JAX-WS SOAP endpoint", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SPRING_ENDPOINT", "name": "Security - Found Spring endpoint", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:STRUTS1_ENDPOINT", "name": "Security - Found Struts 1 endpoint", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:STRUTS2_ENDPOINT", "name": "Security - Found Struts 2 endpoint", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:TAPESTRY_ENDPOINT", "name": "Security - Found Tapestry page", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:WICKET_ENDPOINT", "name": "Security - Found Wicket WebPage", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:HARD_CODE_KEY", "name": "Security - Hard coded key", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:HARD_CODE_PASSWORD", "name": "Security - Hard coded password", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:DMI_CONSTANT_DB_PASSWORD", "name": "Security - Hardcoded constant database password", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:HAZELCAST_SYMMETRIC_ENCRYPTION", "name": "Security - Hazelcast symmetric encryption", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:WEAK_HOSTNAME_VERIFIER", "name": "Security - HostnameVerifier that accept any signed certificates", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:HRS_REQUEST_PARAMETER_TO_COOKIE", "name": "Security - HTTP cookie formed from untrusted input", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SERVLET_HEADER", "name": "Security - HTTP headers untrusted", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:HTTP_PARAMETER_POLLUTION", "name": "Security - HTTP Parameter Pollution", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:HRS_REQUEST_PARAMETER_TO_HTTP_HEADER", "name": "Security - HTTP Response splitting vulnerability", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:INFORMATION_EXPOSURE_THROUGH_AN_ERROR_MESSAGE", "name": "Security - Information Exposure Through An Error Message", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:INSECURE_SMTP_SSL", "name": "Security - Insecure SMTP SSL connection", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:BEAN_PROPERTY_INJECTION", "name": "Security - JavaBeans Property Injection", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:LDAP_ENTRY_POISONING", "name": "Security - LDAP Entry Poisoning", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:WEAK_MESSAGE_DIGEST_MD5", "name": "Security - MD2, MD4 and MD5 are weak hash functions", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:CUSTOM_MESSAGE_DIGEST", "name": "Security - Message digest is custom", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:SQL_NONCONSTANT_STRING_PASSED_TO_EXECUTE", "name": "Security - Nonconstant string passed to execute or addBatch method on an SQL statement", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:NULL_CIPHER", "name": "Security - NullCipher is insecure", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:OBJECT_DESERIALIZATION", "name": "Security - Object deserialization is used in {1}", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:PERMISSIVE_CORS", "name": "Security - Overly permissive CORS policy", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:COOKIE_PERSISTENT", "name": "Security - Persistent <PERSON><PERSON>", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SQL_INJECTION_ANDROID", "name": "Security - Potential Android SQL Injection", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["android", "cwe", "injection", "owasp-a1", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SEAM_LOG_INJECTION", "name": "Security - Potential code injection in Seam logging call", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:EL_INJECTION", "name": "Security - Potential code injection when using Expression Language (EL)", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:OGNL_INJECTION", "name": "Security - Potential code injection when using OGNL expression", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SCRIPT_ENGINE_INJECTION", "name": "Security - Potential code injection when using Script Engine", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SPEL_INJECTION", "name": "Security - Potential code injection when using Spring Expression", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:COMMAND_INJECTION", "name": "Security - Potential Command Injection", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:CRLF_INJECTION_LOGS", "name": "Security - Potential CRLF Injection for logs", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:EXTERNAL_CONFIG_CONTROL", "name": "Security - Potential external control of configuration", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:HTTP_RESPONSE_SPLITTING", "name": "Security - Potential HTTP Response Splitting", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SQL_INJECTION_JDBC", "name": "Security - Potential JDBC Injection", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SQL_INJECTION_SPRING_JDBC", "name": "Security - Potential JDBC Injection (Spring JDBC)", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:LDAP_INJECTION", "name": "Security - Potential LDAP Injection", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SQL_INJECTION", "name": "Security - Potential SQL Injection", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SQL_INJECTION_TURBINE", "name": "Security - Potential SQL Injection with Turbine", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SQL_INJECTION_HIBERNATE", "name": "Security - Potential SQL/HQL Injection (Hibernate)", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SQL_INJECTION_JDO", "name": "Security - Potential SQL/JDOQL Injection (JDO)", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SQL_INJECTION_JPA", "name": "Security - Potential SQL/JPQL Injection (JPA)", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:TEMPLATE_INJECTION_FREEMARKER", "name": "Security - Potential template injection with Freemarker", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:TEMPLATE_INJECTION_VELOCITY", "name": "Security - Potential template injection with Velocity", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:XPATH_INJECTION", "name": "Security - Potential XPath Injection", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:XSS_SERVLET", "name": "Security - Potential XSS in Servlet", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a3", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:COOKIE_USAGE", "name": "Security - Potentially sensitive data in a cookie", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:REDOS", "name": "Security - Regex DOS (ReDOS)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:PT_RELATIVE_PATH_TRAVERSAL", "name": "Security - Relative path traversal in servlet", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:REQUESTDISPATCHER_FILE_DISCLOSURE", "name": "Security - RequestDispatcher File Disclosure", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:RSA_KEY_SIZE", "name": "Security - RSA usage with short key", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:RSA_NO_PADDING", "name": "Security - RSA with no padding is insecure", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:XSS_REQUEST_PARAMETER_TO_SERVLET_WRITER", "name": "Security - Servlet reflected cross site scripting vulnerability", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["owasp-a3"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:XSS_REQUEST_PARAMETER_TO_SEND_ERROR", "name": "Security - Servlet reflected cross site scripting vulnerability in error page", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["owasp-a3"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:WEAK_MESSAGE_DIGEST_SHA1", "name": "Security - SHA-1 is a weak hash function", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SMTP_HEADER_INJECTION", "name": "Security - SMTP Header Injection", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SPRING_CSRF_PROTECTION_DISABLED", "name": "Security - Spring CSRF protection disabled", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SPRING_FILE_DISCLOSURE", "name": "Security - Spring File Disclosure", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SPRING_UNVALIDATED_REDIRECT", "name": "Security - Spring Unvalidated Redirect", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:STATIC_IV", "name": "Security - Static IV", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:STRUTS_FILE_DISCLOSURE", "name": "Security - Struts File Disclosure", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:STRUTS_FORM_VALIDATION", "name": "Security - Struts Form without input validation", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:FILE_UPLOAD_FILENAME", "name": "Security - Tainted filename read", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a4", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:DESERIALIZATION_GADGET", "name": "Security - This class could be used as deserialization gadget", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:TRUST_BOUNDARY_VIOLATION", "name": "Security - Trust Boundary Violation", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:WEAK_TRUST_MANAGER", "name": "Security - TrustManager that accept any certificates", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:UNENCRYPTED_SERVER_SOCKET", "name": "Security - Unencrypted Server Socket", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:UNENCRYPTED_SOCKET", "name": "Security - Unencrypted Socket", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:UNSAFE_HASH_EQUALS", "name": "Security - Unsafe hash equals", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "cwe", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:JACKSON_UNSAFE_DESERIALIZATION", "name": "Security - Unsafe Jackson deserialization configuration", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SERVLET_CONTENT_TYPE", "name": "Security - Untrusted Content-Type header", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SERVLET_SERVER_NAME", "name": "Security - Untrusted Hostname header", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SERVLET_QUERY_STRING", "name": "Security - Untrusted query string", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SERVLET_HEADER_REFERER", "name": "Security - Untrusted Referer header", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SERVLET_PARAMETER", "name": "Security - Untrusted servlet parameter", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "injection", "owasp-a1"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SERVLET_SESSION_ID", "name": "Security - Untrusted session cookie value", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SERVLET_HEADER_USER_AGENT", "name": "Security - Untrusted User-Agent header", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}], "actives": {"java:S3751": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S5301": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MINOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S3066": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MINOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S2976": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S3510": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S4601": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S2254": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S2258": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S4347": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S2115": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S4432": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S4434": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:JUA_DONT_ASSERT_INSTANCEOF_IN_TESTS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:CO_ABSTRACT_SELF": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EQ_ABSTRACT_SELF": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_ENTRY_SETS_MAY_REUSE_ENTRY_OBJECTS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SW_SWING_METHODS_INVOKED_IN_SWING_THREAD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:BIT_SIGNED_CHECK": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:CN_IMPLEMENTS_CLONE_BUT_NOT_CLONEABLE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:EQ_COMPARETO_USE_OBJECT_EQUALS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:HE_EQUALS_USE_HASHCODE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:HE_EQUALS_NO_HASHCODE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:HE_HASHCODE_USE_OBJECT_EQUALS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:HE_HASHCODE_NO_EQUALS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:CN_IDIOM": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:HE_INHERITS_EQUALS_USE_HASHCODE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SE_NO_SUITABLE_CONSTRUCTOR_FOR_EXTERNALIZATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NM_CLASS_NOT_EXCEPTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SE_NO_SUITABLE_CONSTRUCTOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SE_NO_SERIALVERSIONID": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NM_CLASS_NAMING_CONVENTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NM_SAME_SIMPLE_NAME_AS_INTERFACE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NM_SAME_SIMPLE_NAME_AS_SUPERCLASS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:CN_IDIOM_NO_SUPER_CALL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_CLONE_COULD_RETURN_NULL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SE_COMPARATOR_SHOULD_BE_SERIALIZABLE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:CO_COMPARETO_INCORRECT_FLOATING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:CO_COMPARETO_RESULTS_MIN_VALUE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:ES_COMPARING_STRINGS_WITH_EQ": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:ES_COMPARING_PARAMETER_STRING_WITH_EQ": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NM_CONFUSING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:CO_SELF_NO_OBJECT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EQ_SELF_NO_OBJECT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:AM_CREATES_EMPTY_JAR_FILE_ENTRY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:AM_CREATES_EMPTY_ZIP_FILE_ENTRY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:PZ_DONT_REUSE_ENTRY_OBJECTS_IN_ITERATORS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_USING_REMOVEALL_TO_CLEAR_COLLECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IMSE_DONT_CATCH_IMSE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:FI_EMPTY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:ME_MUTABLE_ENUM_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:EQ_CHECK_FOR_OPERAND_NOT_COMPATIBLE_WITH_THIS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EQ_GETCLASS_AND_CLASS_CONSTANT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:BC_EQUALS_METHOD_SHOULD_WORK_FOR_ALL_OBJECTS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_EQUALS_SHOULD_HANDLE_NULL_ARGUMENT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:FI_EXPLICIT_INVOCATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:NM_FIELD_NAMING_CONVENTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:JCIP_FIELD_ISNT_FINAL_IN_IMMUTABLE_CLASS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:FI_MISSING_SUPER_CALL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:FI_USELESS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:FI_NULLIFY_SUPER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:FI_FINALIZER_NULLS_FIELDS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:FI_FINALIZER_ONLY_NULLS_FIELDS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:VA_FORMAT_STRING_USES_NEWLINE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IT_NO_SUCH_ELEMENT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NM_WRONG_PACKAGE_INTENTIONAL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RV_RETURN_VALUE_IGNORED_BAD_PRACTICE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RR_NOT_CHECKED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SR_NOT_CHECKED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:THROWS_METHOD_THROWS_RUNTIMEEXCEPTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DM_RUN_FINALIZERS_ON_EXIT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DM_EXIT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:THROWS_METHOD_THROWS_CLAUSE_BASIC_EXCEPTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:THROWS_METHOD_THROWS_CLAUSE_THROWABLE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:ODR_OPEN_DATABASE_RESOURCE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:ODR_OPEN_DATABASE_RESOURCE_EXCEPTION_PATH": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:OS_OPEN_STREAM": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:OS_OPEN_STREAM_EXCEPTION_PATH": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DE_MIGHT_DROP": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DE_MIGHT_IGNORE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:NM_METHOD_NAMING_CONVENTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_BOOLEAN_RETURN_NULL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:ISC_INSTANTIATE_STATIC_CLASS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RV_NEGATING_RESULT_OF_COMPARETO": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SE_BAD_FIELD_INNER_CLASS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SE_BAD_FIELD_STORE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SE_BAD_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:ME_ENUM_FIELD_SETTER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_RANDOM_USED_ONLY_ONCE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:CNT_ROUGH_CONSTANT_VALUE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SE_INNER_CLASS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SE_NONFINAL_SERIALVERSIONID": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:SE_NONLONG_SERIALVERSIONID": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SE_NONSTATIC_SERIALVERSIONID": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SI_INSTANCE_BEFORE_FINALS_ASSIGNED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:J2EE_STORE_OF_NON_SERIALIZABLE_OBJECT_INTO_SESSION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IC_SUPERCLASS_USES_SUBCLASS_DURING_INITIALIZATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RC_REF_COMPARISON_BAD_PRACTICE_BOOLEAN": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RC_REF_COMPARISON_BAD_PRACTICE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SE_READ_RESOLVE_MUST_RETURN_OBJECT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_TOSTRING_COULD_RETURN_NULL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:SE_TRANSIENT_FIELD_NOT_RESTORED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:GC_UNCHECKED_TYPE_IN_GENERIC_CALL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:UI_INHERITANCE_UNSAFE_GETRESOURCE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NM_FUTURE_KEYWORD_USED_AS_IDENTIFIER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NM_FUTURE_KEYWORD_USED_AS_MEMBER_IDENTIFIER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NM_VERY_CONFUSING_INTENTIONAL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "java:S2647": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S5547": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S3329": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S2658": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:RE_POSSIBLE_UNINTENDED_PATTERN": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:ICAST_BAD_SHIFT_AMOUNT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IL_CONTAINER_ADDED_TO_ITSELF": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BC_NULL_INSTANCEOF": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:NP_NULL_INSTANCEOF": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IP_PARAMETER_IS_DEAD_BUT_OVERWRITTEN": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IL_INFINITE_LOOP": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IL_INFINITE_RECURSIVE_LOOP": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NM_METHOD_CONSTRUCTOR_CONFUSION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RANGE_ARRAY_INDEX": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:RANGE_ARRAY_LENGTH": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RANGE_ARRAY_OFFSET": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:BAC_BAD_APPLET_CONSTRUCTOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RV_ABSOLUTE_VALUE_OF_HASHCODE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RV_ABSOLUTE_VALUE_OF_RANDOM_INT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:INT_BAD_COMPARISON_WITH_INT_VALUE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:INT_BAD_COMPARISON_WITH_NONNEGATIVE_VALUE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:INT_BAD_COMPARISON_WITH_SIGNED_BYTE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DMI_BAD_MONTH": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_BIGDECIMAL_CONSTRUCTED_FROM_DOUBLE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:BIT_ADD_OF_SIGNED_BYTE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BIT_IOR_OF_SIGNED_BYTE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EC_UNRELATED_INTERFACES": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EC_UNRELATED_TYPES": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:EC_UNRELATED_CLASS_AND_INTERFACE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EC_NULL_ARG": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_ANNOTATION_IS_NOT_VISIBLE_TO_REFLECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BIT_SIGNED_CHECK_HIGH_BIT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:BIT_AND_ZZ": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:NM_BAD_EQUAL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:MF_CLASS_MASKS_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NM_LCASE_HASHCODE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:NM_LCASE_TOSTRING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:VR_UNRESOLVABLE_REFERENCE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BOA_BADLY_OVERRIDDEN_ADAPTER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_CLOSING_NULL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RV_CHECK_COMPARETO_FOR_SPECIFIC_RETURN_VALUE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_COLLECTIONS_SHOULD_NOT_CONTAIN_THEMSELVES": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:TQ_COMPARING_VALUES_WITH_INCOMPATIBLE_TYPE_QUALIFIERS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:EQ_DONT_DEFINE_EQUALS_FOR_ENUM": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:EQ_SELF_USE_OBJECT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DMI_SCHEDULED_THREAD_POOL_EXECUTOR_WITH_ZERO_CORE_THREADS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DMI_DOH": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EOS_BAD_END_OF_STREAM_CHECK": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SF_DEAD_STORE_DUE_TO_SWITCH_FALLTHROUGH": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:SF_DEAD_STORE_DUE_TO_SWITCH_FALLTHROUGH_TO_THROW": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DLS_DEAD_STORE_OF_CLASS_LITERAL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SIC_THREADLOCAL_DEADLY_EMBRACE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:FL_FLOATS_AS_LOOP_COUNTERS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:IO_APPENDING_TO_OBJECT_OUTPUT_STREAM": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:FE_TEST_IF_EQUAL_TO_NOT_A_NUMBER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_LONG_BITS_TO_DOUBLE_INVOKED_ON_INT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EQ_ALWAYS_FALSE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EQ_ALWAYS_TRUE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EQ_COMPARING_CLASS_NAMES": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EQ_OVERRIDING_EQUALS_NOT_SYMMETRIC": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EQ_OTHER_NO_OBJECT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EQ_OTHER_USE_OBJECT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EC_ARRAY_AND_NONARRAY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EC_INCOMPATIBLE_ARRAY_COMPARE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RV_EXCEPTION_NOT_THROWN": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:UWF_NULL_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:RE_CANT_USE_FILE_SEPARATOR_AS_REGULAR_EXPRESSION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DMI_FUTILE_ATTEMPT_TO_CHANGE_MAXPOOL_SIZE_OF_SCHEDULED_THREAD_POOL_EXECUTOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DMI_CALLING_NEXT_FROM_HASNEXT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BC_IMPOSSIBLE_CAST": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BC_IMPOSSIBLE_CAST_PRIMITIVE_ARRAY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:BC_IMPOSSIBLE_DOWNCAST": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:BC_IMPOSSIBLE_DOWNCAST_OF_TOARRAY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BIT_IOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BIT_AND": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DM_INVALID_MIN_MAX": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BC_IMPOSSIBLE_INSTANCEOF": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:ICAST_INT_CAST_TO_FLOAT_PASSED_TO_ROUND": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:ICAST_INT_2_LONG_AS_INSTANT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:IM_MULTIPLYING_RESULT_OF_IREM": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:ICAST_INT_CAST_TO_DOUBLE_PASSED_TO_CEIL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RE_BAD_SYNTAX_FOR_REGULAR_EXPRESSION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EC_BAD_ARRAY_COMPARE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_INVOKING_HASHCODE_ON_ARRAY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_INVOKING_TOSTRING_ON_ARRAY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DMI_INVOKING_TOSTRING_ON_ANONYMOUS_ARRAY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IJU_ASSERT_METHOD_INVOKED_FROM_RUN_METHOD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:QBA_QUESTIONABLE_BOOLEAN_ASSIGNMENT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SQL_BAD_PREPARED_STATEMENT_ACCESS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BRSA_BAD_RESULTSET_ACCESS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SQL_BAD_RESULTSET_ACCESS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_NULL_PARAM_DEREF": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_NULL_PARAM_DEREF_ALL_TARGETS_DANGEROUS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:NP_NONNULL_PARAM_VIOLATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:MF_METHOD_MASKS_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:NP_ARGUMENT_MIGHT_BE_NULL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:NM_WRONG_PACKAGE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:RV_RETURN_VALUE_IGNORED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_NONNULL_RETURN_VIOLATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:SE_METHOD_MUST_BE_PRIVATE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "fb-contrib:USFW_UNSYNCHRONIZED_SINGLETON_FIELD_WRITES": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:FL_MATH_USING_FLOAT_PRECISION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:NP_OPTIONAL_RETURN_NULL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:FB_MISSING_EXPECTED_WARNING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:GC_UNRELATED_TYPES": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_NONNULL_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_NULL_PARAM_DEREF_NONVIRTUAL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SA_FIELD_SELF_COMPUTATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SA_LOCAL_SELF_COMPUTATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_ALWAYS_NULL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_ALWAYS_NULL_EXCEPTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:NP_GUARANTEED_DEREF": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RCN_REDUNDANT_NULLCHECK_WOULD_HAVE_BEEN_A_NPE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DLS_OVERWRITTEN_INCREMENT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BSHIFT_WRONG_ADD_PRIORITY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_NULL_ON_SOME_PATH": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:NP_NULL_ON_SOME_PATH_EXCEPTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:CAA_COVARIANT_ARRAY_ELEMENT_STORE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:VA_PRIMITIVE_ARRAY_PASSED_TO_OBJECT_VARARG": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:RV_01_TO_INT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_UNWRITTEN_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RpC_REPEATED_CONDITIONAL_TEST": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DMI_ARGUMENTS_WRONG_ORDER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SA_FIELD_SELF_ASSIGNMENT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SA_LOCAL_SELF_ASSIGNMENT_INSTEAD_OF_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SA_FIELD_SELF_COMPARISON": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SA_LOCAL_SELF_COMPARISON": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:HE_SIGNATURE_DECLARES_HASHING_OF_UNHASHABLE_CLASS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:STI_INTERRUPTED_ON_UNKNOWNTHREAD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_STORE_INTO_NONNULL_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RANGE_STRING_INDEX": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:OVERRIDING_METHODS_MUST_INVOKE_SUPER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RC_REF_COMPARISON": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:IJU_BAD_SUITE_METHOD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:IJU_SETUP_NO_SUPER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IJU_TEARDOWN_NO_SUPER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IJU_NO_TESTS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IJU_SUITE_NOT_STATIC": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:SE_READ_RESOLVE_IS_STATIC": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:UMAC_UNCALLABLE_METHOD_OF_ANONYMOUS_CLASS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:FB_UNEXPECTED_WARNING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:UR_UNINIT_READ": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:UR_UNINIT_READ_CALLED_FROM_SUPER_CONSTRUCTOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:SIO_SUPERFLUOUS_INSTANCEOF": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:STI_INTERRUPTED_ON_CURRENTTHREAD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:UWF_UNWRITTEN_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:HE_USE_OF_UNHASHABLE_CLASS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DLS_DEAD_LOCAL_INCREMENT_IN_RETURN": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_VACUOUS_CALL_TO_EASYMOCK_METHOD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EC_UNRELATED_TYPES_USING_POINTER_EQUALITY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_VACUOUS_SELF_COLLECTION_CALL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:TQ_ALWAYS_VALUE_USED_WHERE_NEVER_REQUIRED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:TQ_NEVER_VALUE_USED_WHERE_ALWAYS_REQUIRED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_GUARANTEED_DEREF_ON_EXCEPTION_PATH": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:TQ_MAYBE_SOURCE_VALUE_REACHES_NEVER_SINK": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:TQ_MAYBE_SOURCE_VALUE_REACHES_ALWAYS_SINK": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:TQ_UNKNOWN_VALUE_USED_WHERE_ALWAYS_STRICTLY_REQUIRED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NM_VERY_CONFUSING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "java:S4426": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S2277": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S3281": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S3355": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S5542": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S1989": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MINOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S4275": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S2089": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:DM_CONVERT_CASE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DM_DEFAULT_ENCODING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "java:S4433": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:MC_OVERRIDABLE_METHOD_CALL_IN_CONSTRUCTOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:MC_OVERRIDABLE_METHOD_CALL_IN_CLONE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DP_CREATE_CLASSLOADER_INSIDE_DO_PRIVILEGED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:PERM_SUPER_NOT_CALLED_IN_GETPERMISSIONS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:MS_MUTABLE_ARRAY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:MS_MUTABLE_COLLECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:MS_MUTABLE_COLLECTION_PKGPROTECT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:MS_MUTABLE_HASHTABLE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:MS_CANNOT_BE_FINAL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:MS_SHOULD_BE_FINAL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:MS_SHOULD_BE_REFACTORED_TO_BE_FINAL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:MS_FINAL_PKGPROTECT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:MS_OOI_PKGPROTECT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:MS_PKGPROTECT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:FI_PUBLIC_SHOULD_BE_PROTECTED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EI_EXPOSE_BUF2": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EI_EXPOSE_REP2": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EI_EXPOSE_BUF": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:MS_EXPOSE_BUF": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EI_EXPOSE_REP": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EI_EXPOSE_STATIC_BUF2": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:EI_EXPOSE_STATIC_REP2": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DP_DO_INSIDE_DO_PRIVILEGED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:USC_POTENTIAL_SECURITY_CHECK_BASED_ON_UNTRUSTED_SOURCE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:REFLC_REFLECTION_MAY_INCREASE_ACCESSIBILITY_OF_CLASS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:REFLF_REFLECTION_MAY_INCREASE_ACCESSIBILITY_OF_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:MS_EXPOSE_REP": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "java:S2039": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MINOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:DM_USELESS_THREAD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:VO_VOLATILE_REFERENCE_TO_ARRAY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:VO_VOLATILE_INCREMENT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:STCAL_INVOKE_ON_STATIC_CALENDAR_INSTANCE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:STCAL_INVOKE_ON_STATIC_DATE_FORMAT_INSTANCE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RS_READOBJECT_SYNC": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:WS_WRITEOBJECT_SYNC": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:WA_AWAIT_NOT_IN_LOOP": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SC_START_IN_CTOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:ESync_EMPTY_SYNC": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IS_FIELD_NOT_GUARDED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:IS_INCONSISTENT_SYNC": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IS2_INCONSISTENT_SYNC": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:LI_LAZY_INIT_UPDATE_STATIC": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:LI_LAZY_INIT_INSTANCE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:LI_LAZY_INIT_STATIC": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SSD_DO_NOT_USE_INSTANCE_LOCK_ON_SHARED_STATIC_DATA": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RU_INVOKE_RUN": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:SWL_SLEEP_WITH_LOCK_HELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:UL_UNRELEASED_LOCK_EXCEPTION_PATH": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:UL_UNRELEASED_LOCK": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SP_SPIN_ON_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:ML_SYNC_ON_UPDATED_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:MWN_MISMATCHED_NOTIFY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:MWN_MISMATCHED_WAIT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DM_MONITOR_WAIT_ON_CONDITION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:MSF_MUTABLE_SERVLET_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NN_NAKED_NOTIFY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:TLW_TWO_LOCK_NOTIFY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DC_DOUBLECHECK": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DC_PARTIALLY_CONSTRUCTED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RV_RETURN_VALUE_OF_PUTIFABSENT_IGNORED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:AT_OPERATION_SEQUENCE_ON_CONCURRENT_ABSTRACTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:STCAL_STATIC_CALENDAR_INSTANCE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:STCAL_STATIC_SIMPLE_DATE_FORMAT_INSTANCE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DL_SYNCHRONIZATION_ON_BOOLEAN": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DL_SYNCHRONIZATION_ON_BOXED_PRIMITIVE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DL_SYNCHRONIZATION_ON_UNSHARED_BOXED_PRIMITIVE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:ML_SYNC_ON_FIELD_TO_GUARD_CHANGING_THAT_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:WL_USING_GETCLASS_RATHER_THAN_CLASS_LITERAL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DL_SYNCHRONIZATION_ON_SHARED_CONSTANT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:JLM_JSR166_LOCK_MONITORENTER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:JLM_JSR166_UTILCONCURRENT_MONITORENTER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_SYNC_AND_NULL_CHECK_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:UW_UNCOND_WAIT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:UG_SYNC_SET_UNSYNC_GET": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:JML_JSR166_CALLING_WAIT_RATHER_THAN_AWAIT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NO_NOTIFY_NOT_NOTIFYALL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:WA_NOT_IN_LOOP": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:TLW_TWO_LOCK_WAIT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "java:S2386": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MINOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S2278": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S5679": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S5344": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:BX_UNBOXING_IMMEDIATELY_REBOXED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DM_BOXED_PRIMITIVE_FOR_COMPARE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DM_BOXED_PRIMITIVE_FOR_PARSING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SIC_INNER_SHOULD_BE_STATIC_ANON": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SIC_INNER_SHOULD_BE_STATIC_NEEDS_THIS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DM_GC": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:HSC_HUGE_SHARED_STRING_CONSTANT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:WMI_WRONG_MAP_ITERATOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IIO_INEFFICIENT_INDEX_OF": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IIO_INEFFICIENT_LAST_INDEX_OF": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_COLLECTION_OF_URLS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:IMA_INEFFICIENT_MEMBER_ACCESS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DM_BOXED_PRIMITIVE_TOSTRING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DM_NEW_FOR_GETCLASS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:IIL_PATTERN_COMPILE_IN_LOOP": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IIL_PREPARE_STATEMENT_IN_LOOP": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:UM_UNNECESSARY_MATH": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IIL_PATTERN_COMPILE_IN_LOOP_INDIRECT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:SBSC_USE_STRINGBUFFER_CONCATENATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DM_BOOLEAN_CTOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DM_FP_NUMBER_CTOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DM_STRING_VOID_CTOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DM_STRING_CTOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DM_NUMBER_CTOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DM_STRING_TOSTRING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "fb-contrib:SEO_SUBOPTIMAL_EXPRESSION_ORDER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:ITA_INEFFICIENT_TO_ARRAY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IIL_ELEMENTS_GET_LENGTH_IN_LOOP": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BX_BOXING_IMMEDIATELY_UNBOXED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BX_BOXING_IMMEDIATELY_UNBOXED_TO_PERFORM_COERCION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:BX_UNBOXED_AND_COERCED_FOR_TERNARY_OPERATOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:UPM_UNCALLED_PRIVATE_METHOD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:SIC_INNER_SHOULD_BE_STATIC": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_BLOCKING_METHODS_ON_URL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:URF_UNREAD_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:SS_SHOULD_BE_STATIC": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:UUF_UNUSED_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DM_NEXTINT_VIA_NEXTDOUBLE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "java:S4684": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S2275": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S899": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MINOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:MALICIOUS_XSLT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:SQL_PREPARED_STATEMENT_GENERATED_FROM_NONCONSTANT_STRING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:PT_ABSOLUTE_PATH_TRAVERSAL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findsecbugs:LDAP_ANONYMOUS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:AWS_QUERY_INJECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:BAD_HEXA_CONVERSION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:BLOWFISH_KEY_SIZE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:ANDROID_BROADCAST": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:PADDING_ORACLE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:CIPHER_INTEGRITY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:HTTPONLY_COOKIE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:INSECURE_COOKIE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:DEFAULT_HTTP_CLIENT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:DES_USAGE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:TDES_USAGE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:WICKET_XSS1": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:ECB_MODE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:DMI_EMPTY_DB_PASSWORD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findsecbugs:RPC_ENABLED_EXTENSIONS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:ANDROID_EXTERNAL_FILE_ACCESS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:WEAK_FILENAMEUTILS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:FORMAT_STRING_MANIPULATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:JAXRS_ENDPOINT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:JAXWS_ENDPOINT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SPRING_ENDPOINT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:STRUTS1_ENDPOINT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:STRUTS2_ENDPOINT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:TAPESTRY_ENDPOINT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:WICKET_ENDPOINT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:HARD_CODE_KEY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:HARD_CODE_PASSWORD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:DMI_CONSTANT_DB_PASSWORD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findsecbugs:HAZELCAST_SYMMETRIC_ENCRYPTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:WEAK_HOSTNAME_VERIFIER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:HRS_REQUEST_PARAMETER_TO_COOKIE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findsecbugs:SERVLET_HEADER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:HTTP_PARAMETER_POLLUTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:HRS_REQUEST_PARAMETER_TO_HTTP_HEADER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findsecbugs:INFORMATION_EXPOSURE_THROUGH_AN_ERROR_MESSAGE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:INSECURE_SMTP_SSL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:BEAN_PROPERTY_INJECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:LDAP_ENTRY_POISONING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:WEAK_MESSAGE_DIGEST_MD5": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:CUSTOM_MESSAGE_DIGEST": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:SQL_NONCONSTANT_STRING_PASSED_TO_EXECUTE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findsecbugs:NULL_CIPHER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:OBJECT_DESERIALIZATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:PERMISSIVE_CORS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:COOKIE_PERSISTENT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SQL_INJECTION_ANDROID": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SEAM_LOG_INJECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:EL_INJECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:OGNL_INJECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SCRIPT_ENGINE_INJECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SPEL_INJECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:COMMAND_INJECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:CRLF_INJECTION_LOGS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:EXTERNAL_CONFIG_CONTROL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:HTTP_RESPONSE_SPLITTING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SQL_INJECTION_JDBC": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SQL_INJECTION_SPRING_JDBC": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:LDAP_INJECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SQL_INJECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SQL_INJECTION_TURBINE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SQL_INJECTION_HIBERNATE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SQL_INJECTION_JDO": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SQL_INJECTION_JPA": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:TEMPLATE_INJECTION_FREEMARKER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:TEMPLATE_INJECTION_VELOCITY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:XPATH_INJECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:XSS_SERVLET": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:COOKIE_USAGE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:REDOS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:PT_RELATIVE_PATH_TRAVERSAL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findsecbugs:REQUESTDISPATCHER_FILE_DISCLOSURE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:RSA_KEY_SIZE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:RSA_NO_PADDING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:XSS_REQUEST_PARAMETER_TO_SERVLET_WRITER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:XSS_REQUEST_PARAMETER_TO_SEND_ERROR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findsecbugs:WEAK_MESSAGE_DIGEST_SHA1": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SMTP_HEADER_INJECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SPRING_CSRF_PROTECTION_DISABLED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SPRING_FILE_DISCLOSURE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SPRING_UNVALIDATED_REDIRECT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:STATIC_IV": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:STRUTS_FILE_DISCLOSURE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:STRUTS_FORM_VALIDATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:FILE_UPLOAD_FILENAME": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:DESERIALIZATION_GADGET": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:TRUST_BOUNDARY_VIOLATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:WEAK_TRUST_MANAGER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:UNENCRYPTED_SERVER_SOCKET": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:UNENCRYPTED_SOCKET": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:UNSAFE_HASH_EQUALS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:JACKSON_UNSAFE_DESERIALIZATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SERVLET_CONTENT_TYPE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SERVLET_SERVER_NAME": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SERVLET_QUERY_STRING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SERVLET_HEADER_REFERER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SERVLET_PARAMETER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SERVLET_SESSION_ID": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SERVLET_HEADER_USER_AGENT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}]}, "qProfiles": {"AYWlNIi1NIXMz9ZdzqWb": {"name": "tp-sonar", "lang": "java", "langName": "Java", "parent": "AYWlFw9PNIXMz9Zdzpfn"}, "AYWlFw9PNIXMz9Zdzpfn": {"name": "FindBugs", "lang": "java", "langName": "Java"}}}