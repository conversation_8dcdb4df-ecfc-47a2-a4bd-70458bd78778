name,type,severity,status,sysTags
"""@RequestMapping"" methods should be ""public""",CODE_SMELL,MAJOR,READY,owasp-a6;spring
"""ActiveMQConnectionFactory"" should not be vulnerable to malicious code deserialization",VULNERABILITY,MINOR,READY,cwe;owasp-a8
"""HttpServletRequest.getRequestedSessionId()"" should not be used",VULNERABILITY,CRITICAL,READY,cwe;owasp-a2;sans-top25-porous
A secure password should be used when connecting to a database,VULNERABILITY,BLOCKER,READY,cwe;owasp-a2;owasp-a3
Allowing deserialization of LDAP objects is security-sensitive,SECURITY_HOTSPOT,MAJOR,READY,cwe;owasp-a8
Bad practice -  Asserting value of instanceof in tests is not recommended.,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Abstract class defines covariant compareTo() method,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Abstract class defines covariant equals() method,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Adding elements of an entry set may fail due to reuse of Entry objects,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Certain swing methods need to be invoked in Swing thread,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Check for sign of bitwise operation,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Class defines clone() but doesn't implement Cloneable,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Class defines compareTo(...) and uses Object.equals(),CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Class defines equals() and uses Object.hashCode(),CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Class defines equals() but not hashCode(),CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Class defines hashCode() and uses Object.equals(),CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Class defines hashCode() but not equals(),CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Class implements Cloneable but does not define or use clone method,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Class inherits equals() and uses Object.hashCode(),CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Class is Externalizable but doesn't define a void constructor,CODE_SMELL,MAJOR,READY,bad-practice
"Bad practice - Class is not derived from an Exception, even though it is named as such",CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Class is Serializable but its superclass doesn't define a void constructor,CODE_SMELL,MAJOR,READY,bad-practice
"Bad practice - Class is Serializable, but doesn't define serialVersionUID",CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Class names should start with an upper case letter,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Class names shouldn't shadow simple name of implemented interface,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Class names shouldn't shadow simple name of superclass,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - clone method does not call super.clone(),CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Clone method may return null,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Comparator doesn't implement Serializable,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - compareTo()/compare() incorrectly handles float or double value,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - compareTo()/compare() returns Integer.MIN_VALUE,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Comparison of String objects using == or !=,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Comparison of String parameter using == or !=,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Confusing method names,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Covariant compareTo() method defined,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Covariant equals() method defined,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Creates an empty jar file entry,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Creates an empty zip file entry,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Don't reuse entry objects in iterators,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Don't use removeAll to clear a collection,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Dubious catching of IllegalMonitorStateException,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Empty finalizer should be deleted,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Enum field is public and mutable,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Equals checks for incompatible operand,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - equals method fails for subtypes,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Equals method should not assume anything about the type of its argument,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - equals() method does not check for null argument,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Explicit invocation of finalizer,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Field names should start with a lower case letter,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Fields of immutable classes should be final,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Finalizer does not call superclass finalizer,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Finalizer does nothing but call superclass finalizer,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Finalizer nullifies superclass finalizer,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Finalizer nulls fields,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Finalizer only nulls fields,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Format string should use %n rather than \n,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Iterator next() method cannot throw NoSuchElementException,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method doesn't override method in superclass due to wrong package for parameter,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method ignores exceptional return value,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method ignores results of InputStream.read(),CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method ignores results of InputStream.skip(),CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method intentionally throws RuntimeException.,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method invokes dangerous method runFinalizersOnExit,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method invokes System.exit(...),CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method lists Exception in its throws clause.,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method lists Throwable in its throws clause.,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method may fail to close database resource,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method may fail to close database resource on exception,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method may fail to close stream,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method may fail to close stream on exception,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method might drop exception,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method might ignore exception,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method names should start with a lower case letter,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Method with Boolean return type returns explicit null,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Needless instantiation of class that only supplies static methods,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Negating the result of compareTo()/compare(),CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Non-serializable class has a serializable inner class,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Non-serializable value stored into instance field of a serializable class,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Non-transient non-serializable instance field in serializable class,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Public enum method unconditionally sets its field,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Random object created and used only once,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Rough value of known constant found,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Serializable inner class,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - serialVersionUID isn't final,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - serialVersionUID isn't long,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - serialVersionUID isn't static,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Static initializer creates instance before all static final fields assigned,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Store of non serializable object into HttpSession,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Superclass uses subclass during initialization,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Suspicious reference comparison of Boolean values,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Suspicious reference comparison to constant,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - The readResolve method must be declared with a return type of Object.,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - toString method may return null,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Transient field that isn't set by deserialization.,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Unchecked type in generic call,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Usage of GetResource may be unsafe if class is extended,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Use of identifier that is a keyword in later versions of Java,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Use of identifier that is a keyword in later versions of Java,CODE_SMELL,MAJOR,READY,bad-practice
Bad practice - Very confusing method names (but perhaps intentional),CODE_SMELL,MAJOR,READY,bad-practice
Basic authentication should not be used,VULNERABILITY,CRITICAL,READY,cwe;owasp-a3;sans-top25-porous
Cipher algorithms should be robust,VULNERABILITY,CRITICAL,READY,cert;cwe;owasp-a3;owasp-a6;privacy;sans-top25-porous
Cipher Block Chaining IV's should be unpredictable,VULNERABILITY,CRITICAL,READY,cwe;owasp-a6
Classes should not be loaded dynamically,VULNERABILITY,CRITICAL,READY,
"Correctness - ""."" or ""|"" used for regular expression",BUG,MAJOR,READY,correctness
Correctness - 32 bit int shifted by an amount not in the range -31..31,BUG,MAJOR,READY,correctness
Correctness - A collection is added to itself,BUG,MAJOR,READY,correctness
Correctness - A known null value is checked to see if it is an instance of a type,BUG,MAJOR,DEPRECATED,correctness
Correctness - A known null value is checked to see if it is an instance of a type,BUG,MAJOR,READY,correctness
Correctness - A parameter is dead upon entry to a method but overwritten,BUG,MAJOR,READY,correctness
Correctness - An apparent infinite loop,BUG,MAJOR,READY,correctness
Correctness - An apparent infinite recursive loop,BUG,MAJOR,READY,correctness
Correctness - Apparent method/constructor confusion,BUG,MAJOR,READY,correctness
Correctness - Array index is out of bounds,BUG,CRITICAL,READY,correctness
Correctness - Array length is out of bounds,BUG,CRITICAL,READY,correctness
Correctness - Array offset is out of bounds,BUG,CRITICAL,READY,correctness
Correctness - Bad Applet Constructor relies on uninitialized AppletStub,BUG,MAJOR,READY,correctness
Correctness - Bad attempt to compute absolute value of signed 32-bit hashcode,BUG,MAJOR,READY,correctness
Correctness - Bad attempt to compute absolute value of signed random integer,BUG,MAJOR,READY,correctness
Correctness - Bad comparison of int value with long constant,BUG,MAJOR,READY,correctness
Correctness - Bad comparison of nonnegative value with negative constant or zero,BUG,MAJOR,READY,correctness
Correctness - Bad comparison of signed byte,BUG,MAJOR,READY,correctness
Correctness - Bad constant value for month,BUG,MAJOR,READY,correctness
Correctness - BigDecimal constructed from double that isn't represented precisely,BUG,MAJOR,READY,correctness
Correctness - Bitwise add of signed byte value,BUG,MAJOR,READY,correctness
Correctness - Bitwise OR of signed byte value,BUG,MAJOR,READY,correctness
Correctness - Call to equals() comparing different interface types,BUG,MAJOR,READY,correctness
Correctness - Call to equals() comparing different types,BUG,MAJOR,READY,correctness
Correctness - Call to equals() comparing unrelated class and interface,BUG,MAJOR,READY,correctness
Correctness - Call to equals(null),BUG,MAJOR,READY,correctness
Correctness - Cannot use reflection to check for presence of annotation without runtime retention,BUG,MAJOR,READY,correctness
Correctness - Check for sign of bitwise operation involving negative number,BUG,MAJOR,READY,correctness
Correctness - Check to see if ((...) & 0) == 0,BUG,MAJOR,READY,correctness
Correctness - Class defines equal(Object); should it be equals(Object)?,BUG,MAJOR,READY,correctness
Correctness - Class defines field that masks a superclass field,BUG,MAJOR,READY,correctness
Correctness - Class defines hashcode(); should it be hashCode()?,BUG,MAJOR,READY,correctness
Correctness - Class defines tostring(); should it be toString()?,BUG,MAJOR,READY,correctness
Correctness - Class makes reference to unresolvable class or method,BUG,MAJOR,READY,correctness
Correctness - Class overrides a method implemented in super class Adapter wrongly,BUG,MAJOR,READY,correctness
Correctness - close() invoked on a value that is always null,BUG,MAJOR,READY,correctness
Correctness - Code checks for specific values returned by compareTo,BUG,MAJOR,READY,correctness
Correctness - Collections should not contain themselves,BUG,MAJOR,READY,correctness
Correctness - Comparing values with incompatible type qualifiers,BUG,MAJOR,READY,correctness
Correctness - Covariant equals() method defined for enum,BUG,MAJOR,READY,correctness
"Correctness - Covariant equals() method defined, Object.equals(Object) inherited",BUG,MAJOR,READY,correctness
Correctness - Creation of ScheduledThreadPoolExecutor with zero core threads,BUG,MAJOR,READY,correctness
Correctness - D'oh! A nonsensical method invocation,BUG,MAJOR,READY,correctness
Correctness - Data read is converted before comparison to -1,BUG,MAJOR,READY,correctness
Correctness - Dead store due to switch statement fall through,BUG,MAJOR,READY,correctness
Correctness - Dead store due to switch statement fall through to throw,BUG,MAJOR,READY,correctness
Correctness - Dead store of class literal,BUG,MAJOR,READY,correctness
Correctness - Deadly embrace of non-static inner class and thread local,BUG,MAJOR,READY,correctness
Correctness - Do not use floating-point variables as loop counters,BUG,MAJOR,READY,correctness
Correctness - Doomed attempt to append to an object output stream,BUG,MAJOR,READY,correctness
Correctness - Doomed test for equality to NaN,BUG,MAJOR,READY,correctness
Correctness - Double.longBitsToDouble invoked on an int,BUG,MAJOR,READY,correctness
Correctness - equals method always returns false,BUG,MAJOR,READY,correctness
Correctness - equals method always returns true,BUG,MAJOR,READY,correctness
Correctness - equals method compares class names rather than class objects,BUG,MAJOR,READY,correctness
Correctness - equals method overrides equals in superclass and may not be symmetric,BUG,MAJOR,READY,correctness
Correctness - equals() method defined that doesn't override equals(Object),BUG,MAJOR,READY,correctness
Correctness - equals() method defined that doesn't override Object.equals(Object),BUG,MAJOR,READY,correctness
Correctness - equals() used to compare array and nonarray,BUG,MAJOR,READY,correctness
Correctness - equals(...) used to compare incompatible arrays,BUG,MAJOR,READY,correctness
Correctness - Exception created and dropped rather than thrown,BUG,MAJOR,READY,correctness
Correctness - Field only ever set to null,BUG,MAJOR,READY,correctness
Correctness - File.separator used for regular expression,BUG,MAJOR,READY,correctness
Correctness - Futile attempt to change max pool size of ScheduledThreadPoolExecutor,BUG,MAJOR,READY,correctness
Correctness - hasNext method invokes next,BUG,MAJOR,READY,correctness
Correctness - Impossible cast,BUG,CRITICAL,READY,correctness
Correctness - Impossible cast involving primitive array,BUG,CRITICAL,READY,correctness
Correctness - Impossible downcast,BUG,CRITICAL,READY,correctness
Correctness - Impossible downcast of toArray() result,BUG,CRITICAL,READY,correctness
Correctness - Incompatible bit masks,BUG,MAJOR,READY,correctness
Correctness - Incompatible bit masks,BUG,MAJOR,READY,correctness
Correctness - Incorrect combination of Math.max and Math.min,BUG,MAJOR,READY,correctness
Correctness - instanceof will always return false,BUG,CRITICAL,READY,correctness
Correctness - int value cast to float and then passed to Math.round,BUG,MAJOR,READY,correctness
Correctness - int value converted to long and used as absolute time,BUG,MAJOR,READY,correctness
Correctness - Integer multiply of result of integer remainder,BUG,MAJOR,READY,correctness
Correctness - Integral value cast to double and then passed to Math.ceil,BUG,MAJOR,READY,correctness
Correctness - Invalid syntax for regular expression,BUG,MAJOR,READY,correctness
"Correctness - Invocation of equals() on an array, which is equivalent to ==",BUG,MAJOR,READY,correctness
Correctness - Invocation of hashCode on an array,BUG,MAJOR,READY,correctness
Correctness - Invocation of toString on an array,BUG,MAJOR,READY,correctness
Correctness - Invocation of toString on an unnamed array,BUG,MAJOR,READY,correctness
Correctness - JUnit assertion in run method will not be noticed by JUnit,BUG,MAJOR,READY,correctness
Correctness - Method assigns boolean literal in boolean expression,BUG,MAJOR,READY,correctness
Correctness - Method attempts to access a prepared statement parameter with index 0,BUG,MAJOR,READY,correctness
Correctness - Method attempts to access a result set field with index 0,BUG,MAJOR,DEPRECATED,correctness
Correctness - Method attempts to access a result set field with index 0,BUG,MAJOR,READY,correctness
Correctness - Method call passes null for non-null parameter,BUG,MAJOR,READY,correctness
Correctness - Method call passes null for non-null parameter,BUG,MAJOR,READY,correctness
Correctness - Method call passes null to a non-null parameter,BUG,MAJOR,READY,correctness
Correctness - Method defines a variable that obscures a field,BUG,MAJOR,READY,correctness
Correctness - Method does not check for null argument,BUG,MAJOR,READY,correctness
Correctness - Method doesn't override method in superclass due to wrong package for parameter,BUG,MAJOR,READY,correctness
Correctness - Method encodes String bytes without specifying the character encoding,BUG,MAJOR,READY,correctness
Correctness - Method ignores return value,BUG,MAJOR,READY,correctness
"Correctness - Method may return null, but is declared @Nonnull",BUG,MAJOR,READY,correctness
Correctness - Method must be private in order for serialization to work,BUG,MAJOR,READY,correctness
Correctness - Method performs math using floating point precision,BUG,MAJOR,READY,correctness
Correctness - Method with Optional return type returns explicit null,BUG,MAJOR,READY,correctness
Correctness - Missing expected or desired warning from SpotBugs,BUG,MAJOR,READY,correctness
Correctness - No relationship between generic parameter and method argument,BUG,MAJOR,READY,correctness
Correctness - Non-null field is not initialized,BUG,MAJOR,READY,correctness
Correctness - Non-virtual method call passes null for non-null parameter,BUG,MAJOR,READY,correctness
"Correctness - Nonsensical self computation involving a field (e.g., x & x)",BUG,MAJOR,READY,correctness
"Correctness - Nonsensical self computation involving a variable (e.g., x & x)",BUG,MAJOR,READY,correctness
Correctness - Null pointer dereference,BUG,MAJOR,READY,correctness
Correctness - Null pointer dereference in method on exception path,BUG,MAJOR,READY,correctness
Correctness - Null value is guaranteed to be dereferenced,BUG,MAJOR,READY,correctness
Correctness - Nullcheck of value previously dereferenced,BUG,MAJOR,READY,correctness
Correctness - Overwritten increment,BUG,MAJOR,READY,correctness
Correctness - Possible bad parsing of shift operation,BUG,MAJOR,READY,correctness
Correctness - Possible null pointer dereference,BUG,MAJOR,READY,correctness
Correctness - Possible null pointer dereference in method on exception path,BUG,MAJOR,READY,correctness
Correctness - Possibly incompatible element is stored in covariant array,BUG,MAJOR,READY,correctness
Correctness - Primitive array passed to function expecting a variable number of object arguments,BUG,MAJOR,READY,correctness
Correctness - Random value from 0 to 1 is coerced to the integer 0,BUG,MAJOR,READY,correctness
Correctness - Read of unwritten field,BUG,MAJOR,READY,correctness
Correctness - Repeated conditional tests,BUG,MAJOR,READY,correctness
Correctness - Reversed method arguments,BUG,MAJOR,READY,correctness
Correctness - Self assignment of field,BUG,MAJOR,READY,correctness
Correctness - Self assignment of local rather than assignment to field,BUG,MAJOR,READY,correctness
Correctness - Self comparison of field with itself,BUG,MAJOR,READY,correctness
Correctness - Self comparison of value with itself,BUG,MAJOR,READY,correctness
Correctness - Signature declares use of unhashable class in hashed construct,BUG,MAJOR,READY,correctness
Correctness - Static Thread.interrupted() method invoked on thread instance,BUG,MAJOR,READY,correctness
Correctness - Store of null value into field annotated @Nonnull,BUG,MAJOR,READY,correctness
Correctness - String index is out of bounds,BUG,CRITICAL,READY,correctness
"Correctness - Super method is annotated with @OverridingMethodsMustInvokeSuper, but the overriding method isn't calling the super method.",BUG,MAJOR,READY,correctness
Correctness - Suspicious reference comparison,BUG,MAJOR,READY,correctness
Correctness - TestCase declares a bad suite method,BUG,MAJOR,READY,correctness
Correctness - TestCase defines setUp that doesn't call super.setUp(),BUG,MAJOR,READY,correctness
Correctness - TestCase defines tearDown that doesn't call super.tearDown(),BUG,MAJOR,READY,correctness
Correctness - TestCase has no tests,BUG,MAJOR,READY,correctness
Correctness - TestCase implements a non-static suite method,BUG,MAJOR,READY,correctness
Correctness - The readResolve method must not be declared as a static method.,BUG,MAJOR,READY,correctness
Correctness - Uncallable method defined in anonymous class,BUG,MAJOR,READY,correctness
Correctness - Unexpected/undesired warning from SpotBugs,BUG,MAJOR,READY,correctness
Correctness - Uninitialized read of field in constructor,BUG,MAJOR,READY,correctness
Correctness - Uninitialized read of field method called from constructor of superclass,BUG,MAJOR,READY,correctness
Correctness - Unnecessary type check done using instanceof operator,BUG,MAJOR,READY,correctness
"Correctness - Unneeded use of currentThread() call, to call interrupted()",BUG,MAJOR,READY,correctness
Correctness - Unwritten field,BUG,MAJOR,READY,correctness
Correctness - Use of class without a hashCode() method in a hashed data structure,BUG,MAJOR,READY,correctness
Correctness - Useless increment in return statement,BUG,MAJOR,READY,correctness
Correctness - Useless/vacuous call to EasyMock method,BUG,MAJOR,READY,correctness
Correctness - Using pointer equality to compare different types,BUG,MAJOR,READY,correctness
Correctness - Vacuous call to collections,BUG,MAJOR,READY,correctness
Correctness - Value annotated as carrying a type qualifier used where a value that must not carry that qualifier is required,BUG,MAJOR,READY,correctness
Correctness - Value annotated as never carrying a type qualifier used where value carrying that qualifier is required,BUG,MAJOR,READY,correctness
Correctness - Value is null and guaranteed to be dereferenced on exception path,BUG,MAJOR,READY,correctness
Correctness - Value that might carry a type qualifier is always used in a way prohibits it from having that type qualifier,BUG,MAJOR,READY,correctness
Correctness - Value that might not carry a type qualifier is always used in a way requires that type qualifier,BUG,MAJOR,READY,correctness
Correctness - Value without a type qualifier used where a value is required to have that qualifier,BUG,MAJOR,READY,correctness
Correctness - Very confusing method names,BUG,MAJOR,READY,correctness
Cryptographic keys should be robust,VULNERABILITY,CRITICAL,READY,cwe;owasp-a3;owasp-a6;privacy;rules
Defined filters should be used,VULNERABILITY,CRITICAL,READY,owasp-a6
Encryption algorithms should be used with secure mode and padding scheme,VULNERABILITY,CRITICAL,READY,cert;cwe;owasp-a3;owasp-a6;privacy;sans-top25-porous
Exceptions should not be thrown from servlet methods,VULNERABILITY,MINOR,READY,cert;cwe;error-handling;owasp-a3
I18n - Consider using Locale parameterized version of invoked method,CODE_SMELL,INFO,READY,i18n
I18n - Reliance on default encoding,CODE_SMELL,INFO,READY,i18n
LDAP connections should be authenticated,VULNERABILITY,CRITICAL,READY,cwe;owasp-a2
Malicious code - An overridable method is called from a constructor,CODE_SMELL,INFO,READY,malicious-code
Malicious code - An overridable method is called from the clone() method.,CODE_SMELL,INFO,READY,malicious-code
Malicious code - Classloaders should only be created inside doPrivileged block,CODE_SMELL,INFO,READY,malicious-code
Malicious code - Custom class loader does not call its superclass's getPermissions(),CODE_SMELL,INFO,READY,malicious-code
Malicious code - Field is a mutable array,CODE_SMELL,INFO,READY,malicious-code
Malicious code - Field is a mutable collection,CODE_SMELL,INFO,READY,malicious-code
Malicious code - Field is a mutable collection which should be package protected,CODE_SMELL,INFO,READY,malicious-code
Malicious code - Field is a mutable Hashtable,CODE_SMELL,INFO,READY,malicious-code
Malicious code - Field isn't final and cannot be protected from malicious code,CODE_SMELL,INFO,READY,malicious-code
Malicious code - Field isn't final but should be,CODE_SMELL,INFO,READY,malicious-code
Malicious code - Field isn't final but should be refactored to be so,CODE_SMELL,INFO,READY,malicious-code
Malicious code - Field should be both final and package protected,CODE_SMELL,INFO,READY,malicious-code
Malicious code - Field should be moved out of an interface and made package protected,CODE_SMELL,INFO,READY,malicious-code
Malicious code - Field should be package protected,CODE_SMELL,INFO,READY,malicious-code
"Malicious code - Finalizer should be protected, not public",CODE_SMELL,INFO,READY,malicious-code
Malicious code - May expose internal representation by creating a buffer which incorporates reference to array,CODE_SMELL,INFO,READY,malicious-code
Malicious code - May expose internal representation by incorporating reference to mutable object,CODE_SMELL,INFO,READY,malicious-code
Malicious code - May expose internal representation by returning a buffer sharing non-public data,CODE_SMELL,INFO,READY,malicious-code
Malicious code - May expose internal representation by returning a buffer sharing non-public data,CODE_SMELL,INFO,READY,malicious-code
Malicious code - May expose internal representation by returning reference to mutable object,CODE_SMELL,INFO,READY,malicious-code
Malicious code - May expose internal static state by creating a buffer which stores an external array into a static field,CODE_SMELL,INFO,READY,malicious-code
Malicious code - May expose internal static state by storing a mutable object into a static field,CODE_SMELL,INFO,READY,malicious-code
Malicious code - Method invoked that should be only be invoked inside a doPrivileged block,CODE_SMELL,INFO,READY,malicious-code
Malicious code - Potential security check based on untrusted source.,CODE_SMELL,INFO,READY,malicious-code
Malicious code - Public method uses reflection to create a class it gets in its parameter which could increase the accessibility of any class,CODE_SMELL,INFO,READY,malicious-code
Malicious code - Public method uses reflection to modify a field it gets in its parameter which could increase the accessibility of any class,CODE_SMELL,INFO,READY,malicious-code
Malicious code - Public static method may expose internal representation by returning array,CODE_SMELL,INFO,READY,malicious-code
Multi-threading - A thread was created using the default empty run method,BUG,MAJOR,READY,multi-threading
Multi-threading - A volatile reference to an array doesn't treat the array elements as volatile,BUG,MAJOR,READY,multi-threading
Multi-threading - An increment to a volatile field isn't atomic,BUG,MAJOR,READY,multi-threading
Multi-threading - Call to static Calendar,BUG,MAJOR,READY,multi-threading
Multi-threading - Call to static DateFormat,BUG,MAJOR,READY,multi-threading
Multi-threading - Class's readObject() method is synchronized,BUG,MAJOR,READY,multi-threading
Multi-threading - Class's writeObject() method is synchronized but nothing else is,BUG,MAJOR,READY,multi-threading
Multi-threading - Condition.await() not in loop,BUG,MAJOR,READY,multi-threading
Multi-threading - Constructor invokes Thread.start(),BUG,MAJOR,READY,multi-threading
Multi-threading - Empty synchronized block,BUG,MAJOR,READY,multi-threading
Multi-threading - Field not guarded against concurrent access,BUG,MAJOR,READY,multi-threading
Multi-threading - Inconsistent synchronization,BUG,MAJOR,READY,multi-threading
Multi-threading - Inconsistent synchronization,BUG,MAJOR,READY,multi-threading
Multi-threading - Incorrect lazy initialization and update of static field,BUG,MAJOR,READY,multi-threading
Multi-threading - Incorrect lazy initialization of instance field,BUG,MAJOR,DEPRECATED,multi-threading
Multi-threading - Incorrect lazy initialization of static field,BUG,MAJOR,READY,multi-threading
Multi-threading - Instance level lock was used on a shared static data,BUG,MAJOR,READY,multi-threading
Multi-threading - Invokes run on a thread (did you mean to start it instead?),BUG,MAJOR,READY,multi-threading
Multi-threading - Method calls Thread.sleep() with a lock held,BUG,MAJOR,READY,multi-threading
Multi-threading - Method does not release lock on all exception paths,BUG,MAJOR,READY,multi-threading
Multi-threading - Method does not release lock on all paths,BUG,MAJOR,READY,multi-threading
Multi-threading - Method spins on field,BUG,MAJOR,READY,multi-threading
Multi-threading - Method synchronizes on an updated field,BUG,MAJOR,READY,multi-threading
Multi-threading - Mismatched notify(),BUG,MAJOR,READY,multi-threading
Multi-threading - Mismatched wait(),BUG,MAJOR,READY,multi-threading
Multi-threading - Monitor wait() called on Condition,BUG,MAJOR,READY,multi-threading
Multi-threading - Mutable servlet field,BUG,MAJOR,READY,multi-threading
Multi-threading - Naked notify,BUG,MAJOR,READY,multi-threading
Multi-threading - Notify with two locks held,BUG,MAJOR,DEPRECATED,multi-threading
Multi-threading - Possible double-check of field,BUG,MAJOR,READY,multi-threading
Multi-threading - Possible exposure of partially initialized object,BUG,MAJOR,READY,multi-threading
"Multi-threading - Return value of putIfAbsent ignored, value passed to putIfAbsent reused",BUG,MAJOR,READY,multi-threading
Multi-threading - Sequence of calls to concurrent abstraction may not be atomic,BUG,MAJOR,READY,multi-threading
Multi-threading - Static Calendar field,BUG,MAJOR,READY,multi-threading
Multi-threading - Static DateFormat,BUG,MAJOR,READY,multi-threading
Multi-threading - Synchronization on Boolean,BUG,MAJOR,READY,multi-threading
Multi-threading - Synchronization on boxed primitive,BUG,MAJOR,READY,multi-threading
Multi-threading - Synchronization on boxed primitive values,BUG,MAJOR,READY,multi-threading
Multi-threading - Synchronization on field in futile attempt to guard that field,BUG,MAJOR,READY,multi-threading
Multi-threading - Synchronization on getClass rather than class literal,BUG,MAJOR,READY,multi-threading
Multi-threading - Synchronization on interned String,BUG,MAJOR,READY,multi-threading
Multi-threading - Synchronization performed on Lock,BUG,MAJOR,READY,multi-threading
Multi-threading - Synchronization performed on util.concurrent instance,BUG,MAJOR,READY,multi-threading
Multi-threading - Synchronize and null check on the same field.,BUG,MAJOR,READY,multi-threading
Multi-threading - Unconditional wait,BUG,MAJOR,READY,multi-threading
"Multi-threading - Unsynchronized get method, synchronized set method",BUG,MAJOR,READY,multi-threading
Multi-threading - Using monitor style wait methods on util.concurrent abstraction,BUG,MAJOR,READY,multi-threading
Multi-threading - Using notify() rather than notifyAll(),BUG,MAJOR,READY,multi-threading
Multi-threading - Wait not in loop,BUG,MAJOR,READY,multi-threading
Multi-threading - Wait with two locks held,BUG,MAJOR,READY,multi-threading
null dereference,BUG,CRITICAL,READY,
OpenSAML2 should be configured to prevent authentication bypass,VULNERABILITY,MAJOR,READY,owasp-a2;owasp-a9;spring
Passwords should not be stored in plain-text or with a fast hashing algorithm,VULNERABILITY,CRITICAL,READY,cwe;owasp-a2;owasp-a3;owasp-a6;sans-top25-porous;spring
Performance - Boxed value is unboxed and then immediately reboxed,BUG,MAJOR,READY,performance
Performance - Boxing a primitive to compare,BUG,MAJOR,READY,performance
Performance - Boxing/unboxing to parse a primitive,BUG,MAJOR,READY,performance
Performance - Could be refactored into a named static inner class,BUG,MAJOR,READY,performance
Performance - Could be refactored into a static inner class,BUG,MAJOR,READY,performance
Performance - Explicit garbage collection; extremely dubious except in benchmarking code,BUG,MAJOR,READY,performance
Performance - Huge string constants is duplicated across multiple class files,BUG,MAJOR,READY,performance
Performance - Inefficient use of keySet iterator instead of entrySet iterator,BUG,MAJOR,READY,performance
Performance - Inefficient use of String.indexOf(String),BUG,MAJOR,READY,performance
Performance - Inefficient use of String.lastIndexOf(String),BUG,MAJOR,READY,performance
Performance - Maps and sets of URLs can be performance hogs,BUG,MAJOR,READY,performance
Performance - Method accesses a private member variable of owning class,BUG,MAJOR,READY,performance
Performance - Method allocates a boxed primitive just to call toString,BUG,MAJOR,READY,performance
"Performance - Method allocates an object, only to get the class object",BUG,MAJOR,READY,performance
Performance - Method calls Pattern.compile in a loop,BUG,MAJOR,READY,performance
Performance - Method calls prepareStatement in a loop,BUG,MAJOR,READY,performance
Performance - Method calls static Math class method on a constant value,BUG,MAJOR,READY,performance
Performance - Method compiles the regular expression in a loop,BUG,MAJOR,READY,performance
Performance - Method concatenates strings using + in a loop,BUG,MAJOR,READY,performance
Performance - Method invokes inefficient Boolean constructor; use Boolean.valueOf(...) instead,BUG,MAJOR,READY,performance
Performance - Method invokes inefficient floating-point Number constructor; use static valueOf instead,BUG,MAJOR,READY,performance
Performance - Method invokes inefficient new String() constructor,BUG,MAJOR,READY,performance
Performance - Method invokes inefficient new String(String) constructor,BUG,MAJOR,READY,performance
Performance - Method invokes inefficient Number constructor; use static valueOf instead,BUG,MAJOR,READY,performance
Performance - Method invokes toString() method on a String,BUG,MAJOR,READY,performance
Performance - Method uses toArray() with zero-length array argument,BUG,MAJOR,READY,performance
Performance - NodeList.getLength() called in a loop,BUG,MAJOR,READY,performance
Performance - Primitive value is boxed and then immediately unboxed,BUG,MAJOR,READY,performance
Performance - Primitive value is boxed then unboxed to perform primitive coercion,BUG,MAJOR,READY,performance
Performance - Primitive value is unboxed and coerced for ternary operator,BUG,MAJOR,READY,performance
Performance - Private method is never called,BUG,MAJOR,READY,performance
Performance - Should be a static inner class,BUG,MAJOR,READY,performance
Performance - The equals and hashCode methods of URL are blocking,BUG,MAJOR,READY,performance
Performance - Unread field,BUG,MAJOR,READY,performance
Performance - Unread field: should this field be static?,BUG,MAJOR,READY,performance
Performance - Unused field,BUG,MAJOR,READY,performance
Performance - Use the nextInt method of Random rather than nextDouble to generate a random integer,BUG,MAJOR,READY,performance
Security - A prepared statement is generated from a nonconstant String,VULNERABILITY,MAJOR,READY,injection;owasp-a1
Security - Absolute path traversal in servlet,VULNERABILITY,MAJOR,READY,cwe
Security - Cipher is susceptible to Padding Oracle,VULNERABILITY,MAJOR,READY,cryptography;cwe;owasp-a6
Security - Cipher with no integrity,VULNERABILITY,MAJOR,READY,cryptography;cwe;owasp-a6
Security - Cookie without the HttpOnly flag,VULNERABILITY,MAJOR,READY,
Security - Cookie without the secure flag,VULNERABILITY,MAJOR,READY,cwe
Security - DefaultHttpClient with default constructor is not compatible with TLS 1.2,VULNERABILITY,MAJOR,READY,cryptography;owasp-a6
Security - DES is insecure,VULNERABILITY,MAJOR,READY,cryptography;cwe;owasp-a6
Security - DESede is insecure,VULNERABILITY,MAJOR,READY,cryptography;cwe;owasp-a6
Security - ECB mode is insecure,VULNERABILITY,MAJOR,READY,cryptography;owasp-a6
Security - Empty database password,VULNERABILITY,MAJOR,READY,
Security - External file access (Android),VULNERABILITY,MAJOR,READY,android;cwe
Security - Hard coded key,VULNERABILITY,MAJOR,READY,cwe
Security - Hard coded password,VULNERABILITY,MAJOR,READY,cryptography;cwe;owasp-a6
Security - Hardcoded constant database password,VULNERABILITY,MAJOR,READY,
Security - HostnameVerifier that accept any signed certificates,VULNERABILITY,MAJOR,READY,cryptography;cwe;owasp-a6;wasc
Security - HTTP cookie formed from untrusted input,VULNERABILITY,MAJOR,READY,
Security - HTTP Parameter Pollution,VULNERABILITY,MAJOR,READY,
Security - HTTP Response splitting vulnerability,VULNERABILITY,MAJOR,READY,
Security - JavaBeans Property Injection,VULNERABILITY,CRITICAL,READY,cwe
"Security - MD2, MD4 and MD5 are weak hash functions",VULNERABILITY,MAJOR,READY,cryptography;cwe;owasp-a6
Security - Nonconstant string passed to execute or addBatch method on an SQL statement,VULNERABILITY,MAJOR,READY,injection;owasp-a1
Security - Overly permissive CORS policy,VULNERABILITY,MAJOR,READY,
Security - Persistent Cookie Usage,VULNERABILITY,MAJOR,READY,cwe
Security - Potential Command Injection,VULNERABILITY,CRITICAL,READY,cwe;injection;owasp-a1
Security - Potential HTTP Response Splitting,VULNERABILITY,INFO,READY,cwe;injection;owasp-a1
Security - Potential JDBC Injection,VULNERABILITY,CRITICAL,READY,cwe;injection;owasp-a1;wasc
Security - Potential JDBC Injection (Spring JDBC),VULNERABILITY,CRITICAL,READY,cwe;injection;owasp-a1;wasc
Security - Predictable pseudorandom number generator,VULNERABILITY,MAJOR,READY,cwe
Security - Relative path traversal in servlet,VULNERABILITY,MAJOR,READY,cwe
Security - Servlet reflected cross site scripting vulnerability,VULNERABILITY,MAJOR,READY,owasp-a3
Security - Servlet reflected cross site scripting vulnerability in error page,VULNERABILITY,MAJOR,READY,owasp-a3
Security - Spring CSRF unrestricted RequestMapping,VULNERABILITY,MAJOR,READY,cwe
Server certificates should be verified during SSL/TLS connections,VULNERABILITY,CRITICAL,READY,cert;cwe;owasp-a3;owasp-a6;privacy;ssl
Server hostnames should be verified during SSL/TLS connections,VULNERABILITY,CRITICAL,READY,cwe;owasp-a3;owasp-a6;privacy;ssl
Style - Abstract Method is already defined in implemented interface,CODE_SMELL,INFO,READY,style
Style - Call to unsupported method,CODE_SMELL,INFO,READY,style
Style - Check for oddness that won't work for negative numbers,CODE_SMELL,INFO,READY,style
Style - Class doesn't override equals in superclass,CODE_SMELL,INFO,READY,style
Style - Class exposes synchronization and semaphores in its public interface,CODE_SMELL,INFO,READY,style
Style - Class extends Servlet class and uses instance variables,CODE_SMELL,INFO,READY,style
Style - Class extends Struts Action class and uses instance variables,CODE_SMELL,INFO,READY,style
Style - Class implements same interface as superclass,CODE_SMELL,INFO,READY,style
Style - Class is final but declares protected field,CODE_SMELL,INFO,READY,style
Style - Code contains a hard coded reference to an absolute pathname,CODE_SMELL,INFO,READY,style
"Style - Complicated, subtle or wrong increment in for-loop",CODE_SMELL,INFO,READY,style
Style - Computation of average could overflow,CODE_SMELL,INFO,READY,style
Style - Condition has no effect,CODE_SMELL,INFO,READY,style
Style - Condition has no effect due to the variable type,CODE_SMELL,INFO,READY,style
Style - Consider returning a zero length array rather than null,CODE_SMELL,INFO,READY,style
Style - Covariant array assignment to a field,CODE_SMELL,INFO,READY,style
Style - Covariant array assignment to a local variable,CODE_SMELL,INFO,READY,style
Style - Covariant array is returned from the method,CODE_SMELL,INFO,READY,style
Style - Dead store of null to local variable,CODE_SMELL,INFO,READY,style
Style - Dead store to local variable,CODE_SMELL,INFO,READY,style
Style - Dead store to local variable that shadows field,CODE_SMELL,INFO,READY,style
Style - Dereference of the result of readLine() without nullcheck,CODE_SMELL,INFO,READY,style
Style - Double assignment of field,CODE_SMELL,INFO,READY,style
Style - Double assignment of local variable,CODE_SMELL,INFO,READY,style
Style - Exception is caught when Exception is not thrown,CODE_SMELL,INFO,READY,style
Style - Field not initialized in constructor but dereferenced without null check,CODE_SMELL,INFO,READY,style
Style - Immediate dereference of the result of readLine(),CODE_SMELL,INFO,READY,style
Style - Initialization circularity,CODE_SMELL,INFO,READY,style
Style - instanceof will always return true,CODE_SMELL,INFO,READY,style
Style - Integer remainder modulo 1,CODE_SMELL,INFO,READY,style
Style - Integral division result cast to double or float,CODE_SMELL,INFO,READY,style
"Style - Invocation of substring(0), which returns the original value",CODE_SMELL,INFO,READY,style
Style - Load of known null value,CODE_SMELL,INFO,READY,style
Style - Method checks to see if result of String.indexOf is positive,CODE_SMELL,INFO,READY,style
Style - Method directly allocates a specific implementation of xml interfaces,CODE_SMELL,INFO,READY,style
Style - Method discards result of readLine after checking if it is non-null,CODE_SMELL,INFO,READY,style
"Style - Method ignores return value, is this OK?",CODE_SMELL,INFO,READY,style
Style - Method relaxes nullness annotation on return value,CODE_SMELL,INFO,READY,style
Style - Method superfluously delegates to parent class method,CODE_SMELL,INFO,READY,style
Style - Method tightens nullness annotation on parameter,CODE_SMELL,INFO,READY,style
Style - Method tightens nullness annotation on parameter,CODE_SMELL,INFO,READY,style
Style - Method uses the same code for two branches,CODE_SMELL,INFO,READY,style
Style - Method uses the same code for two switch clauses,CODE_SMELL,INFO,READY,style
Style - Non serializable object written to ObjectOutput,CODE_SMELL,INFO,READY,style
Style - NullPointerException caught,CODE_SMELL,INFO,READY,style
Style - Parameter must be non-null but is marked as nullable,CODE_SMELL,INFO,READY,style
Style - Possible null pointer dereference due to return value of called method,CODE_SMELL,INFO,READY,style
Style - Possible null pointer dereference on branch that might be infeasible,CODE_SMELL,INFO,READY,style
Style - Potentially ambiguous invocation of either an inherited or outer method,CODE_SMELL,INFO,READY,style
Style - Potentially dangerous use of non-short-circuit logic,CODE_SMELL,INFO,READY,style
Style - Private readResolve method not inherited by subclasses,CODE_SMELL,INFO,READY,style
Style - Questionable cast to abstract collection,CODE_SMELL,INFO,READY,style
Style - Questionable cast to concrete collection,CODE_SMELL,INFO,READY,style
Style - Questionable use of non-short-circuit logic,CODE_SMELL,INFO,READY,style
Style - Read of unwritten public or protected field,CODE_SMELL,INFO,READY,style
Style - Redundant comparison of non-null value to null,CODE_SMELL,INFO,READY,style
Style - Redundant comparison of two null values,CODE_SMELL,INFO,READY,style
Style - Redundant nullcheck of value known to be non-null,CODE_SMELL,INFO,READY,style
Style - Redundant nullcheck of value known to be null,CODE_SMELL,INFO,READY,style
Style - Remainder of 32-bit signed random integer,CODE_SMELL,INFO,READY,style
Style - Remainder of hashCode could be negative,CODE_SMELL,INFO,READY,style
Style - Result of integer multiplication cast to long,CODE_SMELL,INFO,READY,style
Style - Return value of method without side effect is ignored,CODE_SMELL,INFO,READY,style
Style - Self assignment of local variable,CODE_SMELL,INFO,READY,style
Style - Switch statement found where default case is missing,CODE_SMELL,INFO,READY,style
Style - Switch statement found where one case falls through to the next case,CODE_SMELL,INFO,READY,style
Style - Test for circular dependencies among classes,CODE_SMELL,INFO,READY,style
Style - Test for floating point equality,CODE_SMELL,INFO,READY,style
Style - Thread passed where Runnable expected,CODE_SMELL,INFO,READY,style
Style - Transient field of class that isn't Serializable.,CODE_SMELL,INFO,READY,style
Style - Unchecked/unconfirmed cast,CODE_SMELL,INFO,READY,style
Style - Unchecked/unconfirmed cast of return value from method,CODE_SMELL,INFO,READY,style
Style - Unread public/protected field,CODE_SMELL,INFO,READY,style
Style - Unsigned right shift cast to short/byte,CODE_SMELL,INFO,READY,style
Style - Unused public or protected field,CODE_SMELL,INFO,READY,style
Style - Unusual equals method,CODE_SMELL,INFO,READY,style
Style - Unwritten public or protected field,CODE_SMELL,INFO,READY,style
Style - Useless assignment in return statement,CODE_SMELL,INFO,READY,style
Style - Useless control flow,CODE_SMELL,INFO,READY,style
Style - Useless control flow to next line,CODE_SMELL,INFO,READY,style
Style - Useless non-empty void method,CODE_SMELL,INFO,READY,style
Style - Useless object created,CODE_SMELL,INFO,READY,style
Style - Useless object created on stack,CODE_SMELL,INFO,READY,style
Style - Vacuous bit mask operation on integer value,CODE_SMELL,INFO,READY,style
