#!/usr/bin/env python3
"""
Sonar规则转CSV工具
将sonar规则JSON文件转换为CSV格式
"""

import json
import csv
import sys
import argparse
from pathlib import Path


def load_sonar_json(file_path):
    """加载Sonar JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print(f"错误: 文件 '{file_path}' 不存在")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"错误: JSON文件格式错误 - {e}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: 读取文件时发生错误 - {e}")
        sys.exit(1)


def convert_to_csv(sonar_data, output_file):
    """将Sonar规则数据转换为CSV文件"""

    # 检查数据结构
    if 'rules' not in sonar_data:
        print("错误: JSON文件中没有找到 'rules' 字段")
        sys.exit(1)

    rules = sonar_data['rules']
    if not rules:
        print("警告: 没有找到任何规则数据")
        return

    # 过滤掉status不为READY的规则
    ready_rules = [rule for rule in rules if rule.get('status') == 'READY']
    filtered_count = len(rules) - len(ready_rules)

    if filtered_count > 0:
        print(f"过滤掉 {filtered_count} 条status不为READY的规则")

    if not ready_rules:
        print("警告: 过滤后没有READY状态的规则")
        return

    # 定义CSV列名 - 添加key列
    fieldnames = ['key', 'name', 'type', 'severity', 'status', 'sysTags']

    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # 写入表头
            writer.writeheader()

            # 写入数据行
            for rule in ready_rules:
                # 处理sysTags字段 - 将数组转换为分号分隔的字符串
                sys_tags = rule.get('sysTags', [])
                sys_tags_str = ';'.join(sys_tags) if sys_tags else ''

                # 构建行数据 - 添加key字段
                row = {
                    'key': rule.get('key', ''),
                    'name': rule.get('name', ''),
                    'type': rule.get('type', ''),
                    'severity': rule.get('severity', ''),
                    'status': rule.get('status', ''),
                    'sysTags': sys_tags_str
                }

                writer.writerow(row)

        print(f"成功转换 {len(ready_rules)} 条READY状态的规则到 '{output_file}'")

    except Exception as e:
        print(f"错误: 写入CSV文件时发生错误 - {e}")
        sys.exit(1)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='将Sonar规则JSON文件转换为CSV格式',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python sonar_to_csv.py input.json
  python sonar_to_csv.py input.json -o output.csv
  python sonar_to_csv.py .cursor/doc/sonar/cgi-sonar.json -o sonar_rules.csv
        """
    )
    
    parser.add_argument('input_file', 
                       help='输入的Sonar JSON文件路径')
    parser.add_argument('-o', '--output', 
                       help='输出的CSV文件路径 (默认: 基于输入文件名生成)')
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    input_path = Path(args.input_file)
    if not input_path.exists():
        print(f"错误: 输入文件 '{args.input_file}' 不存在")
        sys.exit(1)
    
    # 确定输出文件名
    if args.output:
        output_file = args.output
    else:
        # 基于输入文件名生成输出文件名
        output_file = input_path.stem + '_rules.csv'
    
    print(f"输入文件: {args.input_file}")
    print(f"输出文件: {output_file}")
    print("开始转换...")
    
    # 加载JSON数据
    sonar_data = load_sonar_json(args.input_file)
    
    # 转换为CSV
    convert_to_csv(sonar_data, output_file)
    
    print("转换完成!")


if __name__ == '__main__':
    main()
