{"total": 618, "p": 2, "ps": 500, "rules": [{"key": "findsecbugs:UNVALIDATED_REDIRECT", "name": "Security - Unvalidated Redirect", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:PLAY_UNVALIDATED_REDIRECT", "name": "Security - Unvalidated Redirect (Play Framework)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:URL_REWRITING", "name": "Security - URL rewriting method", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:URLCONNECTION_SSRF_FD", "name": "Security - URLConnection Server-Side Request Forgery (SSRF) and File Disclosure", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:ESAPI_ENCRYPTOR", "name": "Security - Use of ESAPI Encryptor", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:SSL_CONTEXT", "name": "Security - Weak SSLContext", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cryptography", "owasp-a6"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:ANDROID_GEOLOCATION", "name": "Security - WebView with geolocation activated (Android)", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["android"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:ANDROID_WEB_VIEW_JAVASCRIPT", "name": "Security - WebView with JavaScript enabled (Android)", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["android", "cwe", "owasp-a3", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:ANDROID_WEB_VIEW_JAVASCRIPT_INTERFACE", "name": "Security - WebView with JavaScript interface (Android)", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["android", "cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:ANDROID_WORLD_WRITABLE", "name": "Security - World writable file (Android)", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["android", "cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:XXE_DOCUMENT", "name": "Security - XML parsing vulnerable to XXE (DocumentBuilder)", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:XXE_SAXPARSER", "name": "Security - XML parsing vulnerable to XXE (SAXParser)", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:XXE_DTD_TRANSFORM_FACTORY", "name": "Security - XML parsing vulnerable to XXE (TransformerFactory)", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:XXE_XMLREADER", "name": "Security - XML parsing vulnerable to XXE (XMLReader)", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:XXE_XMLSTREAMREADER", "name": "Security - XML parsing vulnerable to XXE (XMLStreamReader)", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:XXE_XPATH", "name": "Security - XML parsing vulnerable to XXE (XPathExpression)", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:XML_DECODER", "name": "Security - XMLDecoder usage", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:XXE_XSLT_TRANSFORM_FACTORY", "name": "Security - XSLT parsing vulnerable to XXE (TransformerFactory)", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findsecbugs:XSS_REQUEST_WRAPPER", "name": "Security - XSSRequestWrapper is a weak XSS protection", "severity": "MAJOR", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a3", "wasc"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S3369", "name": "Security constraints should be defined", "severity": "BLOCKER", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S4830", "name": "Server certificates should be verified during SSL/TLS connections", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cert", "cwe", "owasp-a3", "owasp-a6", "privacy", "ssl"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S5527", "name": "Server hostnames should be verified during SSL/TLS connections", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a3", "owasp-a6", "privacy", "ssl"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S2070", "name": "SHA-1 and Message-Digest hash algorithms should not be used in secure contexts", "severity": "CRITICAL", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S4499", "name": "SMTP SSL connection should check server identity", "severity": "CRITICAL", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S3374", "name": "Struts validation forms should have unique names", "severity": "BLOCKER", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "struts"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "findbugs:USM_USELESS_ABSTRACT_METHOD", "name": "Style - Abstract Method is already defined in implemented interface", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DMI_UNSUPPORTED_METHOD", "name": "Style - Call to unsupported method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:IM_BAD_CHECK_FOR_ODD", "name": "Style - Check for oddness that won't work for negative numbers", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EQ_DOESNT_OVERRIDE_EQUALS", "name": "Style - Class doesn't override equals in superclass", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:PS_PUBLIC_SEMAPHORES", "name": "Style - Class exposes synchronization and semaphores in its public interface", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MTIA_SUSPECT_SERVLET_INSTANCE_FIELD", "name": "Style - Class extends Servlet class and uses instance variables", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:MTIA_SUSPECT_STRUTS_INSTANCE_FIELD", "name": "Style - Class extends Struts Action class and uses instance variables", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RI_REDUNDANT_INTERFACES", "name": "Style - Class implements same interface as superclass", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CI_CONFUSED_INHERITANCE", "name": "Style - Class is final but declares protected field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DMI_HARDCODED_ABSOLUTE_FILENAME", "name": "Style - Code contains a hard coded reference to an absolute pathname", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:QF_QUESTIONABLE_FOR_LOOP", "name": "Style - Complicated, subtle or wrong increment in for-loop", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:IM_AVERAGE_COMPUTATION_COULD_OVERFLOW", "name": "Style - Computation of average could overflow", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UC_USELESS_CONDITION", "name": "Style - Condition has no effect", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UC_USELESS_CONDITION_TYPE", "name": "Style - Condition has no effect due to the variable type", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:PZLA_PREFER_ZERO_LENGTH_ARRAYS", "name": "Style - Consider returning a zero length array rather than null", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CAA_COVARIANT_ARRAY_FIELD", "name": "Style - Covariant array assignment to a field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CAA_COVARIANT_ARRAY_LOCAL", "name": "Style - Covariant array assignment to a local variable", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CAA_COVARIANT_ARRAY_RETURN", "name": "Style - Covariant array is returned from the method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DLS_DEAD_LOCAL_STORE_OF_NULL", "name": "Style - Dead store of null to local variable", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DLS_DEAD_LOCAL_STORE", "name": "Style - Dead store to local variable", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DLS_DEAD_LOCAL_STORE_SHADOWS_FIELD", "name": "Style - Dead store to local variable that shadows field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_DEREFERENCE_OF_READLINE_VALUE", "name": "Style - Dereference of the result of readLine() without nullcheck", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SA_FIELD_DOUBLE_ASSIGNMENT", "name": "Style - Double assignment of field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SA_LOCAL_DOUBLE_ASSIGNMENT", "name": "Style - Double assignment of local variable", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:REC_CATCH_EXCEPTION", "name": "Style - Exception is caught when Except<PERSON> is not thrown", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UWF_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR", "name": "Style - Field not initialized in constructor but dereferenced without null check", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_IMMEDIATE_DEREFERENCE_OF_READLINE", "name": "Style - Immediate dereference of the result of readLine()", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:IC_INIT_CIRCULARITY", "name": "Style - Initialization circularity", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:BC_VACUOUS_INSTANCEOF", "name": "Style - instanceof will always return true", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:INT_BAD_REM_BY_1", "name": "Style - Integer remainder modulo 1", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ICAST_IDIV_CAST_TO_DOUBLE", "name": "Style - Integral division result cast to double or float", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DMI_USELESS_SUBSTRING", "name": "Style - Invocation of substring(0), which returns the original value", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_LOAD_OF_KNOWN_NULL_VALUE", "name": "Style - Load of known null value", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RV_CHECK_FOR_POSITIVE_INDEXOF", "name": "Style - Method checks to see if result of String.indexOf is positive", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:XFB_XML_FACTORY_BYPASS", "name": "Style - Method directly allocates a specific implementation of xml interfaces", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RV_DONT_JUST_NULL_CHECK_READLINE", "name": "Style - Method discards result of readLine after checking if it is non-null", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RV_RETURN_VALUE_IGNORED_INFERRED", "name": "Style - Method ignores return value, is this OK?", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_METHOD_RETURN_RELAXING_ANNOTATION", "name": "Style - Method relaxes nullness annotation on return value", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:USM_USELESS_SUBCLASS_METHOD", "name": "Style - Method superfluously delegates to parent class method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_METHOD_PARAMETER_TIGHTENS_ANNOTATION", "name": "Style - Method tightens nullness annotation on parameter", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_METHOD_PARAMETER_RELAXING_ANNOTATION", "name": "Style - Method tightens nullness annotation on parameter", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DB_DUPLICATE_BRANCHES", "name": "Style - Method uses the same code for two branches", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DB_DUPLICATE_SWITCH_CLAUSES", "name": "Style - Method uses the same code for two switch clauses", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DMI_NONSERIALIZABLE_OBJECT_WRITTEN", "name": "Style - Non serializable object written to ObjectOutput", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DCN_NULLPOINTER_EXCEPTION", "name": "Style - <PERSON><PERSON><PERSON><PERSON>er<PERSON>x<PERSON> caught", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE", "name": "Style - Parameter must be non-null but is marked as nullable", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE", "name": "Style - Possible null pointer dereference due to return value of called method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_NULL_ON_SOME_PATH_MIGHT_BE_INFEASIBLE", "name": "Style - Possible null pointer dereference on branch that might be infeasible", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:IA_AMBIGUOUS_INVOCATION_OF_INHERITED_OR_OUTER_METHOD", "name": "Style - Potentially ambiguous invocation of either an inherited or outer method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NS_DANGEROUS_NON_SHORT_CIRCUIT", "name": "Style - Potentially dangerous use of non-short-circuit logic", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_PRIVATE_READ_RESOLVE_NOT_INHERITED", "name": "Style - Private readResolve method not inherited by subclasses", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:BC_BAD_CAST_TO_ABSTRACT_COLLECTION", "name": "Style - Questionable cast to abstract collection", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:BC_BAD_CAST_TO_CONCRETE_COLLECTION", "name": "Style - Questionable cast to concrete collection", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NS_NON_SHORT_CIRCUIT", "name": "Style - Questionable use of non-short-circuit logic", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:NP_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD", "name": "Style - Read of unwritten public or protected field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RCN_REDUNDANT_COMPARISON_OF_NULL_AND_NONNULL_VALUE", "name": "Style - Redundant comparison of non-null value to null", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RCN_REDUNDANT_COMPARISON_TWO_NULL_VALUES", "name": "Style - Redundant comparison of two null values", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RCN_REDUNDANT_NULLCHECK_OF_NONNULL_VALUE", "name": "Style - Redundant nullcheck of value known to be non-null", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RCN_REDUNDANT_NULLCHECK_OF_NULL_VALUE", "name": "Style - Redundant nullcheck of value known to be null", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RV_REM_OF_RANDOM_INT", "name": "Style - Remainder of 32-bit signed random integer", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RV_REM_OF_HASHCODE", "name": "Style - Re<PERSON>inder of hashCode could be negative", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ICAST_INTEGER_MULTIPLY_CAST_TO_LONG", "name": "Style - Result of integer multiplication cast to long", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:RV_RETURN_VALUE_IGNORED_NO_SIDE_EFFECT", "name": "Style - Return value of method without side effect is ignored", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SA_LOCAL_SELF_ASSIGNMENT", "name": "Style - Self assignment of local variable", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SF_SWITCH_NO_DEFAULT", "name": "Style - Switch statement found where default case is missing", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SF_SWITCH_FALLTHROUGH", "name": "Style - Switch statement found where one case falls through to the next case", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:CD_CIRCULAR_DEPENDENCY", "name": "Style - Test for circular dependencies among classes", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:FE_FLOATING_POINT_EQUALITY", "name": "Style - Test for floating point equality", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DMI_THREAD_PASSED_WHERE_RUNNABLE_EXPECTED", "name": "Style - <PERSON>hr<PERSON> passed where <PERSON><PERSON><PERSON> expected", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:SE_TRANSIENT_FIELD_OF_NONSERIALIZABLE_CLASS", "name": "Style - Transient field of class that isn't Serializable.", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:BC_UNCONFIRMED_CAST", "name": "Style - Unchecked/unconfirmed cast", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:BC_UNCONFIRMED_CAST_OF_RETURN_VALUE", "name": "Style - Unchecked/unconfirmed cast of return value from method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:URF_UNREAD_PUBLIC_OR_PROTECTED_FIELD", "name": "Style - Unread public/protected field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ICAST_QUESTIONABLE_UNSIGNED_RIGHT_SHIFT", "name": "Style - Unsigned right shift cast to short/byte", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UUF_UNUSED_PUBLIC_OR_PROTECTED_FIELD", "name": "Style - Unused public or protected field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:EQ_UNUSUAL", "name": "Style - Unusual equals method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UWF_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD", "name": "Style - Unwritten public or protected field", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:DLS_DEAD_LOCAL_STORE_IN_RETURN", "name": "Style - Useless assignment in return statement", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UCF_USELESS_CONTROL_FLOW", "name": "Style - Useless control flow", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UCF_USELESS_CONTROL_FLOW_NEXT_LINE", "name": "Style - Useless control flow to next line", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UC_USELESS_VOID_METHOD", "name": "Style - Useless non-empty void method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UC_USELESS_OBJECT", "name": "Style - Useless object created", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:UC_USELESS_OBJECT_STACK", "name": "Style - Useless object created on stack", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:INT_VACUOUS_BIT_OPERATION", "name": "Style - Vacuous bit mask operation on integer value", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:INT_VACUOUS_COMPARISON", "name": "Style - Vacuous comparison of integer value", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:TQ_EXPLICIT_UNKNOWN_SOURCE_VALUE_REACHES_ALWAYS_SINK", "name": "Style - Value required to have type qualifier, but marked as unknown", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:TQ_EXPLICIT_UNKNOWN_SOURCE_VALUE_REACHES_NEVER_SINK", "name": "Style - Value required to not have type qualifier, but marked as unknown", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ST_WRITE_TO_STATIC_FROM_INSTANCE_METHOD", "name": "Style - Write to static field from instance method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "java:S1148", "name": "Throwable.printStackTrace(...) should not be called", "severity": "MINOR", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S4423", "name": "Weak SSL/TLS protocols should not be used", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a3", "owasp-a6", "privacy", "sans-top25-porous"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S2653", "name": "Web applications should not have a \"main\" method", "severity": "CRITICAL", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S2755", "name": "XML parsers should not be vulnerable to XXE attacks", "severity": "BLOCKER", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a4"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S4435", "name": "XML transformers should be secured", "severity": "CRITICAL", "status": "DEPRECATED", "isTemplate": false, "tags": [], "sysTags": [], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}], "actives": {"findsecbugs:UNVALIDATED_REDIRECT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:PLAY_UNVALIDATED_REDIRECT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:URL_REWRITING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:URLCONNECTION_SSRF_FD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:ESAPI_ENCRYPTOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:SSL_CONTEXT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:ANDROID_GEOLOCATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:ANDROID_WEB_VIEW_JAVASCRIPT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:ANDROID_WEB_VIEW_JAVASCRIPT_INTERFACE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "INFO", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:ANDROID_WORLD_WRITABLE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:XXE_DOCUMENT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:XXE_SAXPARSER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:XXE_DTD_TRANSFORM_FACTORY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:XXE_XMLREADER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:XXE_XMLSTREAMREADER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:XXE_XPATH": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:XML_DECODER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:XXE_XSLT_TRANSFORM_FACTORY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findsecbugs:XSS_REQUEST_WRAPPER": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S3369": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S4830": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S5527": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S2070": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S4499": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S3374": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "findbugs:USM_USELESS_ABSTRACT_METHOD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_UNSUPPORTED_METHOD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:IM_BAD_CHECK_FOR_ODD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:EQ_DOESNT_OVERRIDE_EQUALS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:PS_PUBLIC_SEMAPHORES": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:MTIA_SUSPECT_SERVLET_INSTANCE_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:MTIA_SUSPECT_STRUTS_INSTANCE_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RI_REDUNDANT_INTERFACES": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:CI_CONFUSED_INHERITANCE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_HARDCODED_ABSOLUTE_FILENAME": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:QF_QUESTIONABLE_FOR_LOOP": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:IM_AVERAGE_COMPUTATION_COULD_OVERFLOW": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:UC_USELESS_CONDITION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:UC_USELESS_CONDITION_TYPE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:PZLA_PREFER_ZERO_LENGTH_ARRAYS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:CAA_COVARIANT_ARRAY_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:CAA_COVARIANT_ARRAY_LOCAL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:CAA_COVARIANT_ARRAY_RETURN": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DLS_DEAD_LOCAL_STORE_OF_NULL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DLS_DEAD_LOCAL_STORE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DLS_DEAD_LOCAL_STORE_SHADOWS_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_DEREFERENCE_OF_READLINE_VALUE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SA_FIELD_DOUBLE_ASSIGNMENT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:SA_LOCAL_DOUBLE_ASSIGNMENT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:REC_CATCH_EXCEPTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:UWF_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_IMMEDIATE_DEREFERENCE_OF_READLINE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:IC_INIT_CIRCULARITY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BC_VACUOUS_INSTANCEOF": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:INT_BAD_REM_BY_1": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:ICAST_IDIV_CAST_TO_DOUBLE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_USELESS_SUBSTRING": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_LOAD_OF_KNOWN_NULL_VALUE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RV_CHECK_FOR_POSITIVE_INDEXOF": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:XFB_XML_FACTORY_BYPASS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RV_DONT_JUST_NULL_CHECK_READLINE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:RV_RETURN_VALUE_IGNORED_INFERRED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_METHOD_RETURN_RELAXING_ANNOTATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:USM_USELESS_SUBCLASS_METHOD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:NP_METHOD_PARAMETER_TIGHTENS_ANNOTATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_METHOD_PARAMETER_RELAXING_ANNOTATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DB_DUPLICATE_BRANCHES": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DB_DUPLICATE_SWITCH_CLAUSES": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DMI_NONSERIALIZABLE_OBJECT_WRITTEN": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DCN_NULLPOINTER_EXCEPTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_NULL_ON_SOME_PATH_MIGHT_BE_INFEASIBLE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:IA_AMBIGUOUS_INVOCATION_OF_INHERITED_OR_OUTER_METHOD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NS_DANGEROUS_NON_SHORT_CIRCUIT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:SE_PRIVATE_READ_RESOLVE_NOT_INHERITED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BC_BAD_CAST_TO_ABSTRACT_COLLECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BC_BAD_CAST_TO_CONCRETE_COLLECTION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NS_NON_SHORT_CIRCUIT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:NP_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RCN_REDUNDANT_COMPARISON_OF_NULL_AND_NONNULL_VALUE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RCN_REDUNDANT_COMPARISON_TWO_NULL_VALUES": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:RCN_REDUNDANT_NULLCHECK_OF_NONNULL_VALUE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RCN_REDUNDANT_NULLCHECK_OF_NULL_VALUE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:RV_REM_OF_RANDOM_INT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RV_REM_OF_HASHCODE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:ICAST_INTEGER_MULTIPLY_CAST_TO_LONG": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:RV_RETURN_VALUE_IGNORED_NO_SIDE_EFFECT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:SA_LOCAL_SELF_ASSIGNMENT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:SF_SWITCH_NO_DEFAULT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SF_SWITCH_FALLTHROUGH": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:CD_CIRCULAR_DEPENDENCY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:FE_FLOATING_POINT_EQUALITY": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:DMI_THREAD_PASSED_WHERE_RUNNABLE_EXPECTED": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:SE_TRANSIENT_FIELD_OF_NONSERIALIZABLE_CLASS": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:BC_UNCONFIRMED_CAST": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:BC_UNCONFIRMED_CAST_OF_RETURN_VALUE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:URF_UNREAD_PUBLIC_OR_PROTECTED_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:ICAST_QUESTIONABLE_UNSIGNED_RIGHT_SHIFT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:UUF_UNUSED_PUBLIC_OR_PROTECTED_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:EQ_UNUSUAL": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:UWF_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:DLS_DEAD_LOCAL_STORE_IN_RETURN": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:UCF_USELESS_CONTROL_FLOW": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:UCF_USELESS_CONTROL_FLOW_NEXT_LINE": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:UC_USELESS_VOID_METHOD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:UC_USELESS_OBJECT": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:UC_USELESS_OBJECT_STACK": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:INT_VACUOUS_BIT_OPERATION": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:22+0800", "updatedAt": "2023-01-12T16:59:22+0800"}], "findbugs:INT_VACUOUS_COMPARISON": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:TQ_EXPLICIT_UNKNOWN_SOURCE_VALUE_REACHES_ALWAYS_SINK": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:TQ_EXPLICIT_UNKNOWN_SOURCE_VALUE_REACHES_NEVER_SINK": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "findbugs:ST_WRITE_TO_STATIC_FROM_INSTANCE_METHOD": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T16:59:21+0800", "updatedAt": "2023-01-12T16:59:21+0800"}], "java:S1148": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MINOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S4423": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "MAJOR", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S2653": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S2755": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}], "java:S4435": [{"qProfile": "AYWlNIi1NIXMz9ZdzqWb", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T17:02:54+0800", "updatedAt": "2023-01-12T17:02:54+0800"}]}, "qProfiles": {"AYWlNIi1NIXMz9ZdzqWb": {"name": "tp-sonar", "lang": "java", "langName": "Java", "parent": "AYWlFw9PNIXMz9Zdzpfn"}, "AYWlFw9PNIXMz9Zdzpfn": {"name": "FindBugs", "lang": "java", "langName": "Java"}}}