{"total": 504, "p": 2, "ps": 500, "rules": [{"key": "findbugs:TQ_EXPLICIT_UNKNOWN_SOURCE_VALUE_REACHES_NEVER_SINK", "name": "Style - Value required to not have type qualifier, but marked as unknown", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "findbugs:ST_WRITE_TO_STATIC_FROM_INSTANCE_METHOD", "name": "Style - Write to static field from instance method", "severity": "INFO", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["style"], "lang": "java", "langName": "Java", "params": [], "type": "CODE_SMELL"}, {"key": "java:S4423", "name": "Weak SSL/TLS protocols should not be used", "severity": "CRITICAL", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a3", "owasp-a6", "privacy", "sans-top25-porous"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}, {"key": "java:S2755", "name": "XML parsers should not be vulnerable to XXE attacks", "severity": "BLOCKER", "status": "READY", "isTemplate": false, "tags": [], "sysTags": ["cwe", "owasp-a4"], "lang": "java", "langName": "Java", "params": [], "type": "VULNERABILITY"}], "actives": {"findbugs:TQ_EXPLICIT_UNKNOWN_SOURCE_VALUE_REACHES_NEVER_SINK": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "findbugs:ST_WRITE_TO_STATIC_FROM_INSTANCE_METHOD": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "INHERITED", "severity": "INFO", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:34+0800"}], "java:S4423": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "CRITICAL", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}], "java:S2755": [{"qProfile": "AYWlkrLqNIXMz9ZdzrmE", "inherit": "NONE", "severity": "BLOCKER", "params": [], "createdAt": "2023-01-12T18:42:13+0800", "updatedAt": "2023-01-12T18:42:13+0800"}]}, "qProfiles": {"AYWlkrLqNIXMz9ZdzrmE": {"name": "q-sonar", "lang": "java", "langName": "Java", "parent": "AYWlabzyNIXMz9Zdzq24"}, "AYWlabzyNIXMz9Zdzq24": {"name": "q-sonar-base", "lang": "java", "langName": "Java", "parent": "AYWlFw9PNIXMz9Zdzpfn"}}}