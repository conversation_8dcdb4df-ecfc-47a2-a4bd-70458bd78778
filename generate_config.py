import nacos
import os
import configparser
import logging
import sys
SERVER_ADDRESSES = "hk.nacos.howbuy.com:80"
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class GenerateConfig:
    def __init__(self, project_name, version, env,path):
        """
        生成配置文件
        :param project_name: 项目名称
        :param version: 分支版本
        :param env: 环境名称 dev，test，prod
        """
        self.data_id = project_name+".properties"
        self.version = version
        self.env = env
        self.conf = configparser.ConfigParser()
        self.path = path

    def get_nacos_config(self):
        client = nacos.NacosClient(SERVER_ADDRESSES, namespace=self.env)
        return client.get_config(self.data_id, self.version)

    def create_settings(self):
        #self.conf.read(os.path.join(BASE_DIR, "test.ini"))
        group_list = []
        for i in self.get_nacos_config().split("\n"):
            if i.strip() and not i.strip().startswith("#"):
                i_list = i.strip().split("=")
                g_key = i_list[0]
                value = "=".join(i_list[1:])
                group, key = g_key.split(".")
                if group not in group_list:
                    self.conf.add_section(group)
                    group_list.append(group)
                self.conf.set(group, key, value)
                logging.info(group, key, value)
        with open(os.path.join(self.path, "settings.ini"), "w+") as f:
            self.conf.write(f)
        originpath = os.path.join(self.path, "settings.ini")
        return originpath


if __name__ == "__main__":
    gc = GenerateConfig("mantis", sys.argv[2], sys.argv[3],sys.argv[1])
    # gc = GenerateConfig("mantis", "1.0.0", "test", "D:\\")
    gc.create_settings()
