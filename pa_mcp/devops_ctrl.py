import httpx
from mcp_server.djangomcp import DjangoMCP

mcp_devops_ctrl = DjangoMCP(name="pa_mcp")


@mcp_devops_ctrl.tool()
async def compile_or_deploy_app(app_name: str, branch: str):
    """
    这个方法，是要确定在测试（阶段）环境上，触发指定分支的指定应用进行编译或部署。

    参数:
    app_name: 应用名，例如 "howbuy-qa-info-remote"
    branch: 分支号，例如 "1.5.4"

    返回:
    Json格式的应用部署版本（分支）情况
    """
    # 构建API URL
    url = f"http://spider.howbuy.pa/spider/lib_repo_mgt/get_app_deployed_branch/?"

    # 发送HTTP请求获取接口信息
    async with httpx.AsyncClient() as client:
        try:
            #response = await client.get(url, timeout=30.0)
            #data = response.json()

            return {"status": "success"}

        except Exception as e:
            return {"status": "failed", "error": f"请求异常: {str(e)}"}
