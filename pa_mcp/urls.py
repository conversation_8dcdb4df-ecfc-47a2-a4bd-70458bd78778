from django.urls import path, include
from mcp_server.views import MCPServerStreamableHttpView
from rest_framework.routers import DefaultRouter

from pa_mcp.devops_info import mcp_devops_info
from pa_mcp.devops_ctrl import mcp_devops_ctrl

router = DefaultRouter()

urlpatterns = [
    path("", include(router.urls)),
    path("devops_info", MCPServerStreamableHttpView.as_view(mcp_server=mcp_devops_info)),
    path("devops_ctrl", MCPServerStreamableHttpView.as_view(mcp_server=mcp_devops_ctrl)),
]
