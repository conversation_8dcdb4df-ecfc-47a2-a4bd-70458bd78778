import httpx
from mcp_server.djangomcp import DjangoMCP

mcp_devops_info = DjangoMCP(name="pa_mcp")


@mcp_devops_info.tool()
async def get_app_deployed_branch(suite_code: str):
    """
    这个方法是根据环境名称获取应用部署版本（分支）的列表

    参数:
    suite_code（必填字段）: 环境名称，例如环境名是it50，it62，或bs-prod

    返回:
    Json格式的应用部署版本（分支）情况
    """
    # 构建API URL
    url = f"http://spider.howbuy.pa/spider/lib_repo_mgt/get_app_deployed_branch/?suite_code={suite_code}"

    # 发送HTTP请求获取接口信息
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, timeout=30.0)
            data = response.json()

            return data

        except Exception as e:
            return {"error": f"请求异常: {str(e)}"}

@mcp_devops_info.tool()
async def get_apidoc(app_name: str, branch: str, api_path: str):
    """
    这个方法是根据应用名，分支号（有时候叫版本号）和接口名（关键字）获取接口定义的文档。这个接口文档也可以用于开发前端界面。

    参数:
    app_name（必填字段）: 应用名，例如 "howbuy-qa-info-remote"
    branch（必填字段）: 分支号，例如 "1.5.4"
    api_path（必填字段）: 接口名，例如 "com.howbuy.tms.orders.facade.trade.fund.batchsubsorpur.BatchSubsOrPurTrialFacade"，或最后一段"BatchSubsOrPurTrialFacade"

    返回:
    Json格式的接口文档
    """
    # 构建API URL
    #url_app = f"http://spider.howbuy.pa/spider/app_mgt/app_interface_api?app_name={app_name}&branch_name={branch}"

    url_api = f"http://spider.howbuy.pa/spider/app_mgt/app_interface_api_param_for_mring?need_response_param=1&app_name={app_name}&branch_name={branch}&interface_path={api_path}"

    # 发送HTTP请求获取接口信息
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url_api, timeout=30.0)
            data = response.json()

            # 检查请求是否成功
            if data.get("status") != "success":
                return {"error": f"获取接口信息失败: {data.get('msg', '未知错误')}"}

            return data.get("data")

        except Exception as e:
            return {"error": f"请求异常: {str(e)}"}


@mcp_devops_info.tool()
async def get_app_dev_branch_list(app_name: str):
    """
    这个方法是根据应用名，查询这个应用所有正在开发中的分支，以及最近4个归档的分支（如有）。返回列表中会标明分支是开发中还是已归档

    参数:
    app_name（必填字段）: 应用名例如 "howbuy-qa-info-remote"

    返回:
    Json格式的列表，比如
    [
        {"br_name": "1.0.0", "br_status": "close"},
        {"br_name": "1.0.1", "br_status": "open"},
        {"br_name": "1.0.2", "br_status": "open"},
        ...
    ]
    """
    # 构建API URL
    url = f"http://spider.howbuy.pa/spider/git_iterative/get_app_branch_archive_info/?module_name={app_name}"

    # 发送HTTP请求获取接口信息
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, timeout=30.0)
            data = response.json()

            # 检查请求是否成功
            if data.get("status") != "success":
                return {"error": f"获取接口信息失败: {data.get('msg', '未知错误')}"}

            return data.get("data")

        except Exception as e:
            return {"error": f"请求异常: {str(e)}"}
