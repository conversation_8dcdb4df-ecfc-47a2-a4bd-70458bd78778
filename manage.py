#!/usr/bin/env python
"""Djan<PERSON>'s command-line utility for administrative tasks."""
import os
import sys
import configparser


def main():

    if '--config' in sys.argv:
        index = sys.argv.index('--config')
        config_path = sys.argv[index + 1]
        print('----------manage.config_path:{}-----------'.format(config_path))
        os.environ['CONFIG_PATH'] = config_path
        sys.argv = sys.argv[:index] + sys.argv[index + 2:]

        # 设置默认配置文件路径
        settings_path = os.path.join(config_path, 'settings.ini')
        # 读取配置文件
        config = configparser.ConfigParser()
        config.read(settings_path)

        # 设置端口号和IP地址
        if 'SERVER' in config:
            port = config['SERVER'].get('port', '8080')
            host = config['SERVER'].get('host', '0.0.0.0')
            os.environ['DJANGO_PORT'] = port
            os.environ['DJANGO_HOST'] = host
            print('---------{}---------'.format(sys.argv))
            if len(sys.argv) > 1 and sys.argv[1] == 'runserver':
                if len(sys.argv) == 2:
                    sys.argv.append(f'{host}:{port}')

    """Run administrative tasks."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mantis.settings')
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    main()
