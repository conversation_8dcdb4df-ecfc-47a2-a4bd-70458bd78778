from django.urls import path, include

from test_report import software_quality_report, busi_testing_bugs
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from test_report.test_flow_report import test_flow_report_view
from test_report.test_report_view import ReportInfoView
from test_report.testset.testset_report_view import TestsetReportInfoView

router = DefaultRouter()

router.register(r'iter', ReportInfoView, basename="iter")
router.register(r'testset_report', TestsetReportInfoView, basename="testset_report")
router.register(r'create_test_report', software_quality_report.SoftwareQualityReport, basename="create_test_report")
router.register(r'get_test_report', software_quality_report.SoftwareQualityReportQuery, basename="get_test_report")
router.register(r'get_testing_bug_info', busi_testing_bugs.BusiTestingBugs, basename="get_testing_bug_info")
router.register(r'get_iteration_quality_report_status', software_quality_report.SoftwareQualityReportReviewStatus,
                basename="get_iteration_quality_report_status")
router.register(r'create_test_flow_report', test_flow_report_view.TestFlowReportView,
                basename="create_test_flow_report")
router.register(r'get_test_flow_report_lib', test_flow_report_view.TestFlowReportForSpiderView,
                basename="get_test_flow_report_lib")
router.register(r'get_test_flow_result_by_app_and_branch',
                test_flow_report_view.TestFlowResultByAppAndBranchForSpiderView,
                basename="get_test_flow_result_by_app_and_branch")
router.register(r'get_iteration_quality_report_for_spider', software_quality_report.SoftwareQualityReportForSpiderView,
                basename="get_iteration_quality_report_for_spider")

urlpatterns = [
    path("", include(router.urls))
]
