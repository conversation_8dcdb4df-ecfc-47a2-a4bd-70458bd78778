# This is an auto-generated Django model module.
# You'll have to do the following manually to clean this up:
#   * Rearrange models' order
#   * Make sure each model has one field with primary_key=True
#   * Make sure each ForeignKey and OneToOneField has `on_delete` set to the desired behavior
#   * Remove `managed = False` lines if you wish to allow Django to create, modify, and delete the table
# Feel free to rename the models, but don't rename db_table values or field names.
from django.db import models


class TestsetReportInfo(models.Model):
    id = models.BigAutoField(primary_key=True)
    batch_no = models.Char<PERSON>ield(max_length=100)
    iteration_id = models.CharField(max_length=100, blank=True, null=True)
    module_name = models.CharField(max_length=100, blank=True, null=True)
    test_set_name = models.CharField(max_length=100, blank=True, null=True)
    test_set_id = models.BigIntegerField(blank=True, null=True)
    test_set_report_url = models.CharField(max_length=1000, blank=True, null=True)
    script_pass_percent = models.Char<PERSON>ield(max_length=20, blank=True, null=True)
    create_user = models.CharField(max_length=20)
    update_user = models.Char<PERSON>ield(max_length=20, blank=True, null=True)
    create_time = models.DateTimeField()
    update_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'testset_report_info'
        unique_together = (('batch_no', 'iteration_id', 'module_name', 'test_set_id'),)
