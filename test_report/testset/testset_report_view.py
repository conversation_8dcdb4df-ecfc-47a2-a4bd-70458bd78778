import datetime
import logging

from rest_framework import serializers
from rest_framework.decorators import action
from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from mantis.settings import ApiResult
from test_report.testset.model.testset_report_info import TestsetReportInfo


class TestsetReportInfoView(ViewSet):
    authentication_classes = []

    @action(methods=['post'], detail=False, url_path='create', authentication_classes=[])
    def pong(self, request):
        try:
            if not request.data:
                return Response(ApiResult.failed_dict(msg="请求参数不能为空"))
            # request.data是个数组 利用TestsetReportInfoSerializer 校验参数
            testset_report_arr = []
            for testset_report in request.data:
                testset_report_arr.append(TestsetReportInfo(
                    batch_no=testset_report['batch_no'],
                    iteration_id=testset_report['iteration_id'],
                    module_name=testset_report['module_name'],
                    test_set_name=testset_report['test_set_name'],
                    test_set_id=testset_report['test_set_id'],
                    test_set_report_url=testset_report['test_set_report_url'],
                    script_pass_percent=testset_report['script_pass_percent'],
                    create_user=str(request.user),
                    create_time=datetime.datetime.now()
                ))

            TestsetReportInfo.objects.bulk_create(testset_report_arr)
            data = ApiResult.success_dict(
                data={}, msg="测试报告保存成功")
            data['code'] = 200
            return Response(data=data)
        except Exception as ex:
            logging.error(ex)
        data = ApiResult.success_dict( msg="测试报告保存失败")
        data['code'] = 500
        return Response(data=data)


class TestsetReportInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = TestsetReportInfo
        fields = ('batch_no', 'iteration_id', 'module_name', 'test_set_name', 'test_set_id'
                  , 'test_set_report_url', 'script_pass_percent')
