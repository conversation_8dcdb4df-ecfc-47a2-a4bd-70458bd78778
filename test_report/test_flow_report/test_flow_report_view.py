import datetime
import json
import os
import traceback

from jinja2 import Template
from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from rest_framework import status
from spider_common_utils.external_command.spider_ssh.spider_sftp import SCP
from spider_common_utils.external_command.spider_ssh.ssh_connect import SSHConnectionManager

from mantis.settings import ApiResult, NGINX_LIB_REPO, TEST_REPORT, logger
from measurement.api.measurement_api import BizFlowRunResultView
from measurement.model.models import TestFlowRunRecord, TestFlowAppDeployInfo
from measurement.dao.person_quality_dashborad_dao import get_biz_flow_run_result_by_batch_number, \
    get_run_batch_app_deploy_list
from test_report.models import TestFlowTestReportLibInfo
from test_report.test_flow_report.dao.test_flow_report_dao import get_need_create_report_flow_list, \
    get_case_pass_rate_by_batch_number_list, get_last_test_flow_result
from test_report.test_flow_report.test_flow_report_template import TestFlowReportTemplate


class TestFlowReportView(ViewSet):
    authentication_classes = []

    def list(self, request):
        start_time = (datetime.datetime.now() - datetime.timedelta(hours=24)).strftime("%Y-%m-%d %H:%M:%S")
        run_batch_number_list = get_need_create_report_flow_list(start_time)
        logger.info("需要创建测试报告的批次号列表：{}".format(run_batch_number_list))
        self._create_flow_report(run_batch_number_list)

        return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(msg="编排线自动化测试报告创建成功！"))

    def _create_flow_report(self, run_batch_number_list):
        run_result_list = get_biz_flow_run_result_by_batch_number(run_batch_number_list)

        batch_run_result = {}
        for run_result in run_result_list:
            if run_result["run_batch_number"] not in batch_run_result:
                batch_run_result[run_result["run_batch_number"]] = []
            batch_run_result[run_result["run_batch_number"]].append(run_result)

        bfr = BizFlowRunResultView()
        for batch_number, run_result_list in batch_run_result.items():
            trans_result = bfr.trans_auto_test_result(run_result_list)
            biz_flow_name = run_result_list[0]["biz_flow_name"]
            app_deploy_list = get_run_batch_app_deploy_list(batch_number)
            self._create_report_html(batch_number, biz_flow_name, trans_result, app_deploy_list)

    def _create_report_html(self, batch_number, biz_flow_name, trans_result, app_deploy_list):
        t1 = Template(TestFlowReportTemplate.title_template)
        title_html = t1.render(flow_name=biz_flow_name, run_batch_number=batch_number)
        t2 = Template(TestFlowReportTemplate.table_template)
        table_html = t2.render(data=trans_result)
        t3 = Template(TestFlowReportTemplate.deploy_app_table_template)
        deploy_app_table_html = t3.render(data=app_deploy_list)
        t = Template(TestFlowReportTemplate.report_html_template)
        report = t.render(title_html=title_html, table_html=table_html, deploy_app_table_html=deploy_app_table_html)

        test_report_dir = TEST_REPORT.get("test_report_dir")
        test_flow_dir = NGINX_LIB_REPO["test_flow_dir"]

        for k, v in trans_result.items():
            report_file_name = "test_flow_report_{}.html".format(k)
            run_batch_number = v.get("run_record")[0].get("run_batch_no")
            report_local_cache = os.path.join(test_report_dir, test_flow_dir, run_batch_number)

            if not os.path.exists(report_local_cache):
                cmd = "mkdir -p {}".format(report_local_cache)
                os.system(cmd)

            report_file_path = os.path.join(report_local_cache, report_file_name)
            with open(report_file_path, "w", encoding="utf8") as f:
                f.write(report)

            self._push_to_nginx(report_file_path, run_batch_number, report_file_name)

    def _push_to_nginx(self, src_file, run_batch_number, report_file_name):
        try:
            report_cache_tgt = os.path.join(NGINX_LIB_REPO["root_path"], NGINX_LIB_REPO["test_report_dir"],
                                            NGINX_LIB_REPO["test_flow_dir"], run_batch_number)
            cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            with SSHConnectionManager(NGINX_LIB_REPO["ip"], NGINX_LIB_REPO["username"], NGINX_LIB_REPO["password"]) as ssh:
                scp = SCP(ssh.SFTP)
                
                # 逐级创建目录结构，避免父目录不存在的问题
                self._ensure_remote_directory_exists(scp, report_cache_tgt)
                
                scp.push_file(src_file, os.path.join(report_cache_tgt, report_file_name))

            # 记录制表信息表
            p = TestFlowTestReportLibInfo(run_batch_number=run_batch_number,
                                          report_url=os.path.join(NGINX_LIB_REPO["base_url"],
                                                                  NGINX_LIB_REPO["test_report_dir"],
                                                                  NGINX_LIB_REPO["test_flow_dir"], run_batch_number,
                                                                  report_file_name),
                                          create_time=cur_time,
                                          create_user='howbuyscm')
            p.save()

            test_report_lib_id = p.id

            TestFlowRunRecord.objects.filter(run_batch_number=run_batch_number).update(
                test_report_lib_id=test_report_lib_id)
        except Exception as e:
            # 安全地获取异常字符串表示
            try:
                error_msg = str(e)[:500]
            except RecursionError:
                error_msg = "RecursionError occurred while getting exception string"
            except Exception:
                error_msg = "Unknown error occurred"
            
            # 安全地获取traceback字符串
            try:
                tb_str = traceback.format_exc()[:1000]
            except RecursionError:
                tb_str = "RecursionError occurred while getting traceback"
            except Exception:
                tb_str = "Unable to get traceback"
            
            # 安全地记录错误日志
            try:
                logger.error(f"Error in _push_to_nginx: {type(e).__name__}: {error_msg}")
            except Exception:
                logger.error("Error in _push_to_nginx: Unable to log error details")
            
            try:
                logger.error(f"Traceback: {tb_str}")
            except Exception:
                logger.error("Unable to log traceback")
    
    def _ensure_remote_directory_exists(self, scp, target_path):
        """安全地创建远程目录结构"""
        try:
            # 检查目录是否已存在
            if scp.is_file_found(target_path):
                return
            
            # 获取路径组件
            path_parts = target_path.replace('\\', '/').split('/')
            current_path = ''
            
            for part in path_parts:
                if not part:  # 跳过空字符串（如开头的/）
                    current_path = '/'
                    continue
                    
                if current_path == '/':
                    current_path = '/' + part
                else:
                    current_path = current_path + '/' + part
                
                # 检查当前路径是否存在，不存在则创建
                try:
                    if not scp.is_file_found(current_path):
                        logger.info(f"Creating directory: {current_path}")
                        scp.sftp.mkdir(current_path)
                except Exception as mkdir_e:
                    # 如果目录已存在，mkdir会抛出异常，这是正常的
                    logger.debug(f"Directory creation attempt for {current_path}: {str(mkdir_e)[:200]}")
                    
        except Exception as e:
            logger.error(f"Error ensuring remote directory exists: {str(e)[:300]}")
            # 如果逐级创建失败，尝试使用原来的mkdir_p方法作为备选
            try:
                scp.mkdir_p(target_path)
            except Exception as fallback_e:
                logger.error(f"Fallback mkdir_p also failed: {str(fallback_e)[:300]}")
                raise

class TestFlowReportForSpiderView(ViewSet):
    authentication_classes = []

    def list(self, request) -> Response:
        # 获取并验证参数
        run_batch_number_list = request.GET.getlist('run_batch_number_list')
        if not run_batch_number_list:  # 空列表提前返回
            return Response(
                status=status.HTTP_200_OK,
                data=ApiResult.success_dict(data=[])
            )

        try:
            # 单次查询获取报告URL信息
            report_info = TestFlowTestReportLibInfo.objects.filter(
                run_batch_number__in=run_batch_number_list
            ).values('run_batch_number', 'report_url')

            # 转换为字典提升查询效率
            report_dict = {item['run_batch_number']: item['report_url']
                           for item in report_info}

            # 批量获取通过率
            case_pass_rate_dict = get_case_pass_rate_by_batch_number_list(run_batch_number_list)

            # 单次遍历构建结果
            final_result_list = [
                {
                    "run_batch_number": batch_num,
                    "avg_case_pass_rate": case_pass_rate_dict.get(batch_num, 0),
                    "report_url": report_dict.get(batch_num, "")
                }
                for batch_num in run_batch_number_list
            ]

            return Response(
                status=status.HTTP_200_OK,
                data=ApiResult.success_dict(data=final_result_list)
            )

        except Exception as e:
            return Response(
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=ApiResult.failed_dict(msg=f"发生错误: {str(e)}")
            )


class TestFlowResultByAppAndBranchForSpiderView(ViewSet):
    authentication_classes = []

    def create(self, request) -> Response:
        # 获取并验证参数
        params_list = [
            json.loads(item.replace("'", '"'))
            for item in request.data.getlist('params_data')
        ]

        test_flow_result_list = []
        for params in params_list:
            try:
                app_name_list = params.get('app_name_list')
                biz_code = params.get('biz_code')
                biz_flow_name = params.get('biz_flow_name')
                branch_name = params.get('branch_name')

                # 构建app-branch元组
                if len(app_name_list) == 1:
                    app_branch_tuple = '(("' + app_name_list[0] + '", "' + branch_name + '"))'
                else:
                    app_branch_tuple = tuple((app, branch_name) for app in app_name_list)
                # 获取测试流程结果
                test_flow_result = get_last_test_flow_result(biz_code, biz_flow_name, app_branch_tuple)

                if not test_flow_result:
                    continue

                # 获取部署信息
                deploy_info = TestFlowAppDeployInfo.objects.filter(
                    run_batch_number=test_flow_result['run_batch_number'],
                    app_name__in=app_name_list
                ).values('app_name', 'app_deploy_branch')

                # 构建结果
                test_flow_result['app_deploy_info'] = [
                    {
                        "app_name": item["app_name"],
                        "app_deploy_branch": item["app_deploy_branch"]
                    }
                    for item in deploy_info
                ]

                test_flow_result_list.append(test_flow_result)

            except (KeyError, json.JSONDecodeError) as e:
                # 记录错误日志
                logger.warning(f"Invalid parameter format: {e}")
                continue
        return Response(
            status=status.HTTP_200_OK,
            data=ApiResult.success_dict(data=test_flow_result_list)
        )
