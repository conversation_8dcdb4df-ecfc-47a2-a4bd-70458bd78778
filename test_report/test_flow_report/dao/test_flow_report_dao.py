from django.db import connection
import math

from mantis.settings import logger


def get_need_create_report_flow_list(start_time):
    sql = '''
          SELECT DISTINCT t.run_batch_number, tr.interface_pass_rate, tr.id FROM test_flow_run_record_testset t
          INNER JOIN test_flow_run_record r ON t.run_batch_number = r.run_batch_number
          LEFT JOIN test_flow_run_record_testset_result tr ON t.id = tr.t_id
          WHERE r.test_report_lib_id IS NULL AND r.run_time > '{}'
          '''.format(start_time)

    cursor = connection.cursor()
    cursor.execute(sql)

    need_create = set()
    no_need_create = set()

    # 明确需要创建静态报告是平台拿到了最终的测试结果，即interface_pass_rate有值
    for batch_number, should_create, _ in cursor.fetchall():
        (need_create if should_create else no_need_create).add(batch_number)

    return list(need_create - no_need_create)


def get_case_pass_rate_by_batch_number_list(batch_number_list):
    sql = '''
          SELECT 
                v.run_batch_number, 
                AVG(v.case_pass_rate ) AS avg_pass_rate
            FROM (
                SELECT 
                    r.run_batch_number, 
                    tr.app_name, 
                    COALESCE(NULLIF(REPLACE(tr.case_pass_rate, '%', ''), ''), '0') AS case_pass_rate 
                FROM test_flow_run_record_testset_result tr
                INNER JOIN test_flow_run_record_testset t ON tr.t_id = t.id
                INNER JOIN test_flow_run_record r ON t.run_batch_number = r.run_batch_number
            ) v
            WHERE v.run_batch_number IN ('{}') 
            GROUP BY v.run_batch_number;
          '''.format("','".join(batch_number_list))

    cursor = connection.cursor()
    cursor.execute(sql)
    result_dict = {}
    for item in cursor.fetchall():
        # 保留2位小数，不用四舍五入 20250617 by fwm
        # 使用截断方法避免四舍五入
        truncated_value = math.floor(item[1] * 100) / 100
        result_dict[item[0]] = truncated_value

    return result_dict


def get_last_test_flow_result(biz_code, biz_flow_name, app_branch_tuple):
    sql = '''
          SELECT r.biz_code, r.biz_flow_name, r.run_batch_number, vv.avg_pass_rate, r.run_time, li.report_url
            FROM test_flow_app_deploy_info di
            INNER JOIN test_flow_run_record r ON di.run_batch_number = r.run_batch_number
            INNER JOIN (
                    SELECT 
                    v.run_batch_number, 
                    AVG(v.case_pass_rate ) AS avg_pass_rate
                    FROM (
                    SELECT 
                        r.run_batch_number, 
                        tr.app_name, 
                        COALESCE(NULLIF(REPLACE(tr.case_pass_rate, '%', ''), ''), '0') AS case_pass_rate 
                    FROM test_flow_run_record_testset_result tr
                    INNER JOIN test_flow_run_record_testset t ON tr.t_id = t.id
                    INNER JOIN test_flow_run_record r ON t.run_batch_number = r.run_batch_number
                    ) v    
		        GROUP BY v.run_batch_number ) vv ON vv.run_batch_number = r.run_batch_number
		    LEFT JOIN test_flow_test_report_lib_info li ON r.run_batch_number = li.run_batch_number          
	    WHERE (di.app_name, di.app_deploy_branch) IN {app_branch_tuple} 
            AND r.biz_code = '{biz_code}' AND r.biz_flow_name = '{biz_flow_name}'
            ORDER BY di.run_batch_number DESC LIMIT 1;
        '''.format(biz_code=biz_code, biz_flow_name=biz_flow_name, app_branch_tuple=app_branch_tuple)

    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)

    if cursor.rowcount == 0:
        sql = '''
                 SELECT r.biz_code, r.biz_flow_name, r.run_batch_number, vv.avg_pass_rate, r.run_time, li.report_url 
                    FROM test_flow_app_deploy_info di
                    INNER JOIN test_flow_run_record r ON di.run_batch_number = r.run_batch_number
                    INNER JOIN (
                            SELECT 
                            v.run_batch_number, 
                            AVG(v.case_pass_rate ) AS avg_pass_rate
                            FROM (
                            SELECT 
                                r.run_batch_number, 
                                tr.app_name, 
                                COALESCE(NULLIF(REPLACE(tr.case_pass_rate, '%', ''), ''), '0') AS case_pass_rate 
                            FROM test_flow_run_record_testset_result tr
                            INNER JOIN test_flow_run_record_testset t ON tr.t_id = t.id
                            INNER JOIN test_flow_run_record r ON t.run_batch_number = r.run_batch_number
                            ) v    
                        GROUP BY v.run_batch_number ) vv ON vv.run_batch_number = r.run_batch_number
                    LEFT JOIN test_flow_test_report_lib_info li ON r.run_batch_number = li.run_batch_number          
                WHERE  r.biz_code = '{biz_code}' AND r.biz_flow_name = '{biz_flow_name}'
                    ORDER BY di.run_batch_number DESC LIMIT 1;
              '''.format(biz_code=biz_code, biz_flow_name=biz_flow_name)
        cursor = connection.cursor()
        cursor.execute(sql)
        logger.info(sql)

    if (row := cursor.fetchone()):  # 使用海象运算符简化判断
        # 保留2位小数，不用四舍五入 20250715 by fwm
        truncated_value = math.floor(row[3]* 100) / 100

        return {
            "biz_code": row[0],
            "biz_flow_name": row[1],
            "run_batch_number": row[2],
            "avg_pass_rate": truncated_value,
            "run_time": row[4],
            "report_url": row[5]
        }
    return {}  # 如果没有数据返回空字典
