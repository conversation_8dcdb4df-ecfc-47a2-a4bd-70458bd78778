class TestFlowReportTemplate:
    report_html_template = """<!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Document</title>
        <style>
          body {
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
            font-weight: 300;
            line-height: 1.6;
          }
          .center {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 10vh;
                font-size: 2em;
          }
          .contain {
            height: 100%;
            overflow: auto;
          }
          .warpper {
            padding: 20px;
            min-width: 1400px;
          }
          .title {
            font-weight: bold;
            font-size: 20px;
            margin-bottom: 20px;
          }
          .flex_center {
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .cell_width {
            width: 130px;
            white-space: normal;
            word-break: break-all;
            overflow: hidden;
            text-align: center;
            padding: 0 5px;
          }
          .red_font {
            color: red;
            font-weight: bold;
          }
          .custom_table {
            display: inline-block;
            width: auto;
            border-left: 1px solid #9fa9b4;
            border-top: 1px solid #9fa9b4;
          }
          .table_header {
            display: flex;
            align-items: center;
            background-color: #cfcfe3;
            height: 40px;
            .table_header_key {
              border-right: 1px solid #9fa9b4;
              border-bottom: 1px solid #9fa9b4;
              line-height: 40px;
              text-align: center;
              color: #515a6e;
              font-weight: 700;
            }
          }
          .table_body_row {
            background: #eef5f2 !important;
            display: flex;
          }
          .row_id {
            border-right: 1px solid #9fa9b4;
            border-bottom: 1px solid #9fa9b4;
          }
          .row_item .row_item_app {
            display: flex;
          }
          .row_item_app_name {
            border-right: 1px solid #9fa9b4;
            border-bottom: 1px solid #9fa9b4;
            text-align: center;
          }
          .row_item_app_wrapper {
            display: flex;
            flex-direction: column;
          }
          .row_item_app_item {
            display: flex;
            height: 100%;
          }
          .cell_width {
            text-align: center;
            min-height: 40px;
            border-right: 1px solid #9fa9b4;
            border-bottom: 1px solid #9fa9b4;
          }
          .table_body > .table_body_row:nth-child(even) {
            background: #f1ecee !important;
          }
        </style>
      </head>
      <body>
        <div class="contain">
          <div class="warpper">
            {{ title_html }}
            <!--基础table -->
            <div class="custom_table">
                <div class="table_header">
                    <div class="table_header_key cell_width">
                        应用
                    </div>
                    <div class="table_header_key cell_width">
                        部署版本
                    </div>
                </div>
                {{ deploy_app_table_html }}
            </div>
            <br />
            <br />
            <br />
            <!--树形table -->
            <div class="custom_table">
              <div class="table_header">
                <div class="table_header_key cell_width">编排线名称</div>
                <div class="table_header_key cell_width">执行批次</div>
                <div class="table_header_key cell_width">运行环境</div>
                <div class="table_header_key cell_width">执行顺序-测试集</div>
                <div class="table_header_key cell_width">应用</div>
                <div class="table_header_key cell_width">部署版本</div>
                <div class="table_header_key cell_width">接口版本</div>
                <div class="table_header_key cell_width">接口通过率</div>
                <div class="table_header_key cell_width">用例通过率</div>
                <div class="table_header_key cell_width">开始时间</div>
                <div class="table_header_key cell_width">结束时间</div>
                <div class="table_header_key cell_width">运行时长</div>
                <div class="table_header_key cell_width">结果地址</div>
              </div>
    
              {{table_html}}
            </div>
          </div>
        </div>
      </body>
    </html>
    
    """

    title_template = """
    <div class="center">
        <div class="title">{{flow_name}} {{run_batch_number}} 自动化测试报告</div>
    </div>
    """

    deploy_app_table_template = """
    <div class="table_body">
        {% for app in data %}
            <div class="table_body_row">
                <div class="row_id flex_center cell_width">{{ app['app_name'] }}</div>
                <div class="row_id flex_center cell_width">{{ app['app_deploy_branch'] }}</div>
            </div>
        {% endfor %}
    </div>
    """

    table_template = """
    <div class="table_body">
        {% for key, value in data.items() %}
            {% for run in value['run_record'] %}
                <div class="table_body_row">
                    <!-- 编排线名称 -->
                    <div class="row_id flex_center cell_width">{{ key }}</div>
                    <!-- 执行批次 -->
                    <div class="row_id flex_center cell_width">{{ run['run_batch_no'] }}</div>
                    <!-- 运行环境 -->
                    <div class="row_id flex_center cell_width">{{ run['suite_code'] }}</div>
                    <div class="row_item">
                        {% for result in run['run_result'] %}
                            <div class="row_item_app">
                                <!-- 执行顺序-测试集 -->
                                <div class="row_item_app_name flex_center cell_width">{{ result['testset_id'] }}</div>
                                <div class="row_item_app_wrapper">
                                    {% for app in result['appList'] %}
                                        <div class="row_item_app_item">
                                            <!-- 应用 -->
                                            <div class="flex_center cell_width">{{ app['app_name'] }}</div>
                                            <!-- 部署版本 -->
                                            <div class="flex_center cell_width">{{ app['deploy_branch'] }}</div>
                                            <!-- 接口版本 -->
                                            <div class="flex_center cell_width">{{ app['srcipt_branch'] }}</div>
                                            <!-- 接口通过率 -->
                                            <div
                                                class="flex_center cell_width {% if app['interface_pass_rate'] != '100.00%' %}red_font{% endif %}"
                                            >
                                                {{ app['interface_pass_rate'] }}
                                            </div>
                                            <!-- 用例通过率 -->
                                            <div
                                                class="flex_center cell_width {% if app['case_pass_rate'] != '100.00%' %}red_font{% endif %}"
                                            >
                                                {{ app['case_pass_rate'] }}
                                            </div>
                                            <!-- 开始时间 -->
                                            <div class="flex_center cell_width">{{ app['start_time'] }}</div>
                                            <!-- 结束时间 -->
                                            <div class="flex_center cell_width">{{ app['end_time'] }}</div>
                                            <!-- 运行时长 -->
                                            <div class="flex_center cell_width">{{ app['duration'] }}</div>
                                            <!-- 报告地址 -->
                                            <a class="flex_center cell_width" href="{{ app['result_url'] }}" target="_blank">
                                                测试集结果
                                            </a>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endfor %}
        {% endfor %}
    </div>
    """
