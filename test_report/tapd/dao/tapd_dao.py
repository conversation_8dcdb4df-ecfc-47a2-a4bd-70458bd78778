from django.db import connection
from mantis.settings import logger


def get_iteration_and_test_plan_id_list(workspace_id):
    sql = """
            select p.id as test_plan_id, i.id as iteration_id, p.start_date, p.end_date from tapd_test_plan p 
            inner join tapd_iterations i on p.`name` = i.`name`
            where p.workspace_id = '{}' and p.iteration_id = '0' ;
        """.format(workspace_id)

    logger.info(sql)

    cursor = connection.cursor()
    cursor.execute(sql)
    iteration_and_test_plan_id_list = []
    for row in cursor.fetchall():
        result = {"test_plan_id": row[0], "iteration_id": row[1], "start_date": row[2], "end_date": row[3]}
        iteration_and_test_plan_id_list.append(result)

    return iteration_and_test_plan_id_list
