from common.tapd_curl import CurlRequestsManager
from mantis.settings import TAPD
from task_mgt.tapd_mgt.config.analysis_ini import LoadConfig


class TapdHelper:
    @staticmethod
    def get_count(external_api_name, workspace_id, iteration_id):
        lc = LoadConfig()
        res = lc.loading("external_interface")[external_api_name]
        with CurlRequestsManager() as cr:
            url = TAPD['host_url'] + res.get("request_address") + "?" \
                  + res.get("request_params").format(workspace_id, iteration_id)
            res_code, res_data = cr.curl_get(url, external_api_name)
        count = 0
        if res_code == 200:
            count = res_data.get("data").get("count")
        return count

    @staticmethod
    def exclude_not_exist_fields(keys, ref_dict):
        remove_list = []
        ref_keys = ref_dict.keys();
        for dk in ref_keys:
            found = False
            for k in keys:
                if dk == k:
                    found = True
                    break
            if not found:
                remove_list.append(dk)

        for e in remove_list:
            ref_dict.pop(e)
        return ref_dict

