# Create your views here
from enum import unique, Enum

from rest_framework import status
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from common.tapd_curl import CurlRequestsManager
from mantis import settings
from mantis.settings import TAPD, logger
from tapd_gateway.from_tapd.model.models import TapdEntryLaunchForm
from task_mgt.tapd_mgt.config.analysis_ini import LoadConfig
from test_report.software_quality_report import TestReportUtils
from test_report.tapd.dao.tapd_dao import get_iteration_and_test_plan_id_list
from test_report.tapd.models import TAPDIterationsInfo, TAPDTestPlanInfo, TAPDBugsInfo, TAPDStoriesInfo
from test_report.tapd.tapd_helper import TapdHelper


@unique
class LaunchFormStatusEnum(Enum):
    finished = ('finished', "确认结束")
    signing = ('signing', "待签发")
    initial = ('initial', "初始化")
    auditing = ('auditing', "评审中")
    abandon = ('abandon', "已废弃")
    sign_completed = ('sign_completed', "签发完成")

    def __init__(self, launch_form_status, status_cn_desc):
        self.launch_form_status = launch_form_status
        self.status_cn_desc = status_cn_desc


class TAPDTestPlan(ViewSet):
    """
    tapd测试计划接口
    """

    def __get_test_plan_count(self, workspace_id):
        interface_name = "test_plans_count"
        lc = LoadConfig()
        res = lc.loading("external_interface")[interface_name]
        with CurlRequestsManager() as cr:
            url = TAPD['host_url'] + res.get("request_address") + "?" + res.get("request_params").format(workspace_id)
            res_code, res_data = cr.curl_get(url, interface_name)
        test_plan_count = 0
        if res_code == 200:
            test_plan_count = res_data.get("data").get("count")
        return test_plan_count

    def __update_or_create_test_plan(self, test_plan_list):
        for test_plan_info in test_plan_list:
            test_plan = test_plan_info.get("TestPlan")
            TAPDTestPlanInfo.objects.update_or_create(id=test_plan.get("id"),
                                                      workspace_id=test_plan.get("workspace_id"),
                                                      defaults=test_plan)

    def list(self, request):
        """从tapd拉取测试计划信息"""
        workspace_id = request.query_params.get('workspace_id')
        limit = request.query_params.get('limit')
        page = request.query_params.get('page')

        interface_name = "test_plans"
        lc = LoadConfig()
        res = lc.loading("external_interface")[interface_name]

        with CurlRequestsManager() as cr:
            if not page:
                test_plan_count = self.__get_test_plan_count(workspace_id)
                i = 0
                while i < (int(test_plan_count / int(limit)) + 1):
                    url = TAPD['host_url'] + res.get("request_address") + "?" + res.get("request_params").format(
                        workspace_id, limit, i)
                    res_code, res_data = cr.curl_get(url, interface_name)
                    if res_code == 200:
                        self.__update_or_create_test_plan(res_data.get("data"))
                    else:
                        return Response(status=status.HTTP_200_OK,
                                        data=settings.ApiResult.failed_dict(
                                            '同步TAPD测试计划失败，原因：{}'.format(res_data.get("info"))))
                    i += 1

                return Response(status=status.HTTP_200_OK,
                                data=settings.ApiResult.success_dict(
                                    '同步TAPD测试计划成功，共{}条'.format(test_plan_count)))
            else:
                url = TAPD['host_url'] + res.get("request_address") + "?" + res.get("request_params").format(
                    workspace_id, limit, page)
                res_code, res_data = cr.curl_get(url, interface_name)
                if res_code == 200:
                    self.__update_or_create_test_plan(res_data.get("data"))
                    return Response(status=status.HTTP_200_OK,
                                    data=settings.ApiResult.success_dict(
                                        '同步TAPD测试计划成功，共{}条'.format(len(res_data.get("data")))))

    def put(self, request):
        """更新迭代号到测试计划，后续腾讯会禁用此接口更新迭代id的功能"""
        workspace_id = request.data.get("workspace_id")

        iteration_test_plan_list = get_iteration_and_test_plan_id_list(workspace_id)
        if iteration_test_plan_list:
            with CurlRequestsManager() as cr:
                interface_name = "test_plans_update"
                lc = LoadConfig()
                res = lc.loading("external_interface")[interface_name]
                url = TAPD['host_url'] + res.get("request_address")
                for iteration_test_plan in iteration_test_plan_list:
                    # 此更新接口必须传开始结束时间，否则将时间置空
                    data = {"id": iteration_test_plan.get("test_plan_id"), "workspace_id": workspace_id,
                            "iteration_id": iteration_test_plan.get("iteration_id"),
                            "start_date": iteration_test_plan.get("start_date"),
                            "end_date": iteration_test_plan.get("end_date")}
                    status_code, msg = cr.curl_post(url, data, interface_name)
                    if status_code != 200:
                        return Response(status=status.HTTP_200_OK,
                                        data=settings.ApiResult.failed_dict('更新测试计划的迭代信息失败'))
                if status_code == 200:
                    return Response(status=status.HTTP_200_OK,
                                    data=settings.ApiResult.success_dict(
                                        '更新测试计划的迭代信息成功，共{}条记录'.format(len(iteration_test_plan_list))))
        else:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict('无测试计划需要更新迭代id'))


class TAPDIterations(ViewSet):
    authentication_classes = []

    def __get_iterations_count(self, workspace_id, iteration_id):
        interface_name = "iterations_count"
        return TapdHelper.get_count(interface_name, self.workspace_id, self.iteration_id)

    def __update_or_create_tapd_iterations(self, iterations_list):
        for iterations in iterations_list:
            iteration = iterations.get("Iteration")
            TAPDIterationsInfo.objects.update_or_create(id=iteration.get("id"),
                                                        workspace_id=iteration.get("workspace_id"),
                                                        defaults=iteration)

    def list(self, request):
        """从tapd拉取迭代信息"""
        workspace_id = request.query_params.get('workspace_id')
        iteration_id = request.query_params.get('iteration_id')
        limit = request.query_params.get('limit')
        page = request.query_params.get('page')

        interface_name = "iterations_get"
        lc = LoadConfig()
        res = lc.loading("external_interface")[interface_name]

        with CurlRequestsManager() as cr:
            if not page:
                iterations_count = self.__get_iterations_count(workspace_id, iteration_id)
                i = 0
                while i < (int(iterations_count / int(limit)) + 1):
                    url = TAPD['host_url'] + res.get("request_address") + "?" + res.get("request_params").format(
                        workspace_id, limit, i, iteration_id)
                    res_code, res_data = cr.curl_get(url, interface_name)
                    if res_code == 200:
                        self.__update_or_create_tapd_iterations(res_data.get("data"))
                    else:
                        return Response(status=status.HTTP_200_OK,
                                        data=settings.ApiResult.failed_dict(
                                            '同步TAPD迭代失败，原因：{}'.format(res_data.get("info"))))
                    i += 1

                return Response(status=status.HTTP_200_OK,
                                data=settings.ApiResult.success_dict(
                                    '同步TAPD迭代成功，共{}条'.format(iterations_count)))
            else:
                url = TAPD['host_url'] + res.get("request_address") + "?" + res.get("request_params").format(
                    workspace_id, limit, page, iteration_id)
                res_code, res_data = cr.curl_get(url, interface_name)
                if res_code == 200:
                    self.__update_or_create_tapd_iterations(res_data.get("data"))
                    return Response(status=status.HTTP_200_OK,
                                    data=settings.ApiResult.success_dict(
                                        '同步TAPD迭代成功，共{}条'.format(len(res_data.get("data")))))


class TAPDBug(ViewSet):
    """
    tapd缺陷接口
    """
    authentication_classes = []

    def __get_bug_count(self, workspace_id, iteration_id):
        interface_name = "bugs_count"
        return TapdHelper.get_count(interface_name, self.workspace_id, self.iteration_id)

    def __update_or_create_bugs(self, bugs_list):
        for bug_info in bugs_list:
            bug = bug_info.get("Bug")
            TAPDBugsInfo.objects.update_or_create(id=bug.get("id"),
                                                  workspace_id=bug.get("workspace_id"),
                                                  defaults=bug)

    def list(self, request):
        """从tapd拉取缺陷信息"""
        workspace_id = request.query_params.get('workspace_id')
        iteration_id = request.query_params.get('iteration_id')
        limit = request.query_params.get('limit')
        page = request.query_params.get('page')

        interface_name = "bugs_get"
        lc = LoadConfig()
        res = lc.loading("external_interface")[interface_name]

        with CurlRequestsManager() as cr:
            if not page:
                bugs_count = self.__get_bug_count(workspace_id, iteration_id)
                i = 0
                while i < (int(bugs_count / int(limit)) + 1):
                    url = TAPD['host_url'] + res.get("request_address") + "?" + res.get("request_params").format(
                        workspace_id, limit, i, iteration_id)
                    logger.info("请求url:{}".format(url))
                    res_code, res_data = cr.curl_get(url, interface_name)
                    if res_code == 200:
                        self.__update_or_create_bugs(res_data.get("data"))
                    else:
                        return Response(status=status.HTTP_200_OK,
                                        data=settings.ApiResult.failed_dict(
                                            '同步TAPD测试缺陷失败，原因：{}'.format(res_data.get("info"))))
                    i += 1

                return Response(status=status.HTTP_200_OK,
                                data=settings.ApiResult.success_dict('同步TAPD测试缺陷成功，共{}条'.format(bugs_count)))
            else:
                logger.info(111111)
                url = TAPD['host_url'] + res.get("request_address") + "?" + res.get("request_params").format(
                    workspace_id, limit, page, iteration_id)
                logger.info(url)
                try:
                    res_code, res_data = cr.curl_get(url, interface_name)
                    logger.info(res_code)
                    logger.info(res_data)
                except Exception as e:
                    logger.error(str(e))
                    return Response(status=status.HTTP_200_OK,
                                    data=settings.ApiResult.failed_dict({}))
                if res_code == 200:
                    self.__update_or_create_bugs(res_data.get("data"))
                    return Response(status=status.HTTP_200_OK,
                                    data=settings.ApiResult.success_dict(
                                        '同步TAPD测试缺陷成功，共{}条'.format(len(res_data.get("data")))))


class TAPDStories(ViewSet):
    authentication_classes = []

    def __get_stories_count(self, workspace_id, iteration_id):
        interface_name = "stories_count"
        if not iteration_id:
            iteration_id = ''
        return TapdHelper.get_count(interface_name, self.workspace_id, self.iteration_id)

    def __update_or_create_tapd_stories(self, stories_list):
        for stories in stories_list:
            story = stories.get("Story")
            try:
                TAPDStoriesInfo.objects.update_or_create(id=story.get("id"),
                                                         workspace_id=story.get("workspace_id"),
                                                         defaults=story)
            except Exception as e:
                logger.error("写入数据库失败，原因为：{}".format(str(e)))

    def list(self, request):
        """从tapd拉取需求信息"""
        workspace_id = request.query_params.get('workspace_id')
        iteration_id = request.query_params.get('iteration_id')
        limit = request.query_params.get('limit')
        page = request.query_params.get('page')

        interface_name = "stories_get"
        lc = LoadConfig()
        res = lc.loading("external_interface")[interface_name]

        with CurlRequestsManager() as cr:
            if not page:
                stories_count = self.__get_stories_count(workspace_id, iteration_id)
                i = 0
                while i < (int(stories_count / int(limit)) + 1):
                    url = TAPD['host_url'] + res.get("request_address") + "?" + res.get("request_params").format(
                        workspace_id, limit, i, iteration_id)
                    res_code, res_data = cr.curl_get(url, interface_name)
                    if res_code == 200:
                        self.__update_or_create_tapd_stories(res_data.get("data"))
                    else:
                        return Response(status=status.HTTP_200_OK,
                                        data=settings.ApiResult.failed_dict(
                                            '同步TAPD迭代失败，原因：{}'.format(res_data.get("info"))))
                    i += 1

                return Response(status=status.HTTP_200_OK,
                                data=settings.ApiResult.success_dict('同步TAPD需求成功，共{}条'.format(stories_count)))
            else:
                url = TAPD['host_url'] + res.get("request_address") + "?" + res.get("request_params").format(
                    workspace_id, limit, page, iteration_id)
                res_code, res_data = cr.curl_get(url, interface_name)
                if res_code == 200:
                    self.__update_or_create_tapd_stories(res_data.get("data"))
                    return Response(status=status.HTTP_200_OK,
                                    data=settings.ApiResult.success_dict(
                                        '同步TAPD需求成功，共{}条'.format(len(res_data.get("data")))))


class TAPDLaunchFormsApi(ViewSet):
    authentication_classes = []

    def list(self, request):
        iteration_id = request.query_params.get('iteration_id')
        TestReportUtils.sync_launch_form_from_tapd_to_gateway(iteration_id)

        result = TapdEntryLaunchForm.objects.filter(tapd_launch_form_iteration_id=iteration_id).first()

        if result:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.success_dict(
                                '迭代【{}】的发布评审状态为【{}】'.format(result.tapd_launch_form_title, LaunchFormStatusEnum[
                                    result.tapd_launch_form_status].status_cn_desc)))
        else:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict(
                                '迭代【{}】的发布评审状态查询失败'.format(iteration_id)))
