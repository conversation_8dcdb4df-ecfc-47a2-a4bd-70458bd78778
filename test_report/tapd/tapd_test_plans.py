from dev_effective.service.biz_test_plan_service import BizTestPlanSer
from tapd_gateway.from_tapd.service.tapd_entry_test_plan_ser import TapdEntityTestPlanSer


class TAPDTestPlans:
    def __init__(self, iteration_id_list, project_id):
        self.iteration_id_list = iteration_id_list
        self.project_id = project_id

    def update_test_plan_data(self):
        test_plan_ser = TapdEntityTestPlanSer()
        res_data = test_plan_ser.get_test_plan_by_iteration_ids(self.iteration_id_list)
        if res_data:
            plan_ser = BizTestPlanSer()
            plan_ser.update_or_create_test_plan(res_data, self.project_id)