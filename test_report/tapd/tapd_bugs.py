# Create your views here
import datetime

from common.tapd_curl import CurlRequestsManager
from dev_effective.service.biz_bug_service import BizBugSer
from mantis.settings import TAPD, logger, TEST_REPORT
from tapd_gateway.from_tapd.service.tapd_entry_bug_ser import TapdEntityBugSer
from tapd_gateway.from_tapd.service.tapd_entry_story_ser import TapdEntityStorySer
from tapd_gateway.from_tapd.service.tapd_req_strategy import GetBugTapdStrategy
from task_mgt.tapd_mgt.config.analysis_ini import LoadConfig
from test_report.models import SpiderIterAppInfo


class IterAppChecker:
    def check_iter_app(self, app_list, iter_list):
        for app in app_list:
            result = False
            for iter in iter_list:
                if SpiderIterAppInfo.objects.filter(module_name=app, pipeline_id=iter).values('module_name',
                                                                                              'pipeline_id'):
                    result = True
            if not result:
                return result
        return True


class TAPDBugs:
    def __init__(self, iteration_id_list, workspace_id=None, project_id=None):
        self.workspace_id = workspace_id if workspace_id else TEST_REPORT['workspace_id']
        self.iteration_id_list = iteration_id_list
        self.project_id = project_id

    def _update_or_create_bugs(self, bugs_list):
        bug_ser = BizBugSer()
        bug_ser.update_or_create_bugs(bugs_list, self.project_id)

    def _get_bugs_from_tapd(self):
        """从tapd拉取缺陷信息"""
        if self.iteration_id_list:
            for iteration_id in self.iteration_id_list:
                bug_ser = GetBugTapdStrategy(self.workspace_id, None, iteration_id)
                bug_ser.sync_data_from_tapd()
            return True

    def update_biz_bug_data(self):
        bug_ser = TapdEntityBugSer()
        res_data = bug_ser.get_tapd_entity_bug_by_iteration_ids(self.workspace_id, self.iteration_id_list)
        if res_data:
            self._update_or_create_bugs(res_data)

    def check_bug_iter_app_info(self, iteration_id):
        bug_ser = TapdEntityBugSer()
        iteration_list = []
        iteration_list.append(iteration_id)
        tapd_bugs = bug_ser.get_tapd_entity_bug_by_iteration_ids(self.workspace_id, iteration_list)
        workitem_type_id = TAPD['workitem_type_id']
        story_ser = TapdEntityStorySer()
        tapd_stories = story_ser.get_tapd_entity_story_by_iteration_ids(self.workspace_id, iteration_list,
                                                                        workitem_type_id)
        story = None
        if tapd_stories:
            story = sorted(tapd_stories, key=lambda x: x.get('tapd_entry_id'), reverse=True)[0]

        bug_id_list = []
        result = True
        if tapd_bugs and story:
            app_check = IterAppChecker()
            for bug in tapd_bugs:
                bug_id = bug.get('tapd_entry_id')
                bug_status = bug.get('tapd_bug_status')
                if 'closed' == bug_status or 'resolved' == bug_status:
                    if bug.get('tp_app'):
                        result = app_check.check_iter_app(bug.get('tp_app').split('|'),
                                                          story.get('tapd_story_custom_field_13').split('|'))
                        if not result:
                            bug_id_list.append(int(str(bug_id)[-7:]))
                            continue
                    if bug.get('tms_app'):
                        result = app_check.check_iter_app(bug.get('tms_app').split('|'),
                                                          story.get('tapd_story_custom_field_14').split('|'))
                        if not result:
                            bug_id_list.append(int(str(bug_id)[-7:]))
                            continue
                    if bug.get('fp_app'):
                        result = app_check.check_iter_app(bug.get('fp_app').split('|'),
                                                          story.get('tapd_story_custom_field_16').split('|'))
                        if not result:
                            bug_id_list.append(int(str(bug_id)[-7:]))
                            continue
                    if bug.get('crm_app'):
                        result = app_check.check_iter_app(bug.get('crm_app').split('|'),
                                                          story.get('tapd_story_custom_field_18').split('|'))
                        if not result:
                            bug_id_list.append(int(str(bug_id)[-7:]))
                            continue
                    if bug.get('tms_h5'):
                        result = app_check.check_iter_app(bug.get('tms_h5').split('|'),
                                                          story.get('tapd_story_custom_field_15').split('|'))
                        if not result:
                            bug_id_list.append(int(str(bug_id)[-7:]))
                            continue
                    if bug.get('fp_h5_app'):
                        result = app_check.check_iter_app(bug.get('fp_h5_app').split('|'),
                                                          story.get('tapd_story_custom_field_17').split('|'))
                        if not result:
                            bug_id_list.append(int(str(bug_id)[-7:]))
                            continue
        return result, bug_id_list


class TAPDBugsByStartTime(TAPDBugs):

    def __init__(self):
        self.workspace_id = TEST_REPORT['workspace_id']

    def __get_bug_count(self, start_time_from):
        interface_name = "bugs_count"
        lc = LoadConfig()
        res = lc.loading("external_interface")[interface_name]

        with CurlRequestsManager() as cr:
            url = TAPD['host_url'] + res.get("request_address") + "?" + \
                  res.get("request_params_start_time").format(self.workspace_id, start_time_from)
            res_code, res_data = cr.curl_get(url, interface_name)
        test_bugs_count = 0
        if res_code == 200:
            test_bugs_count = res_data.get("data").get("count")
        return test_bugs_count

    def _get_bugs_iteration_list(self, days):
        start_time_from = (datetime.datetime.now() - datetime.timedelta(days=days)).strftime("%Y-%m-%d")

        limit = 200

        interface_name = "bugs_get"
        lc = LoadConfig()
        res = lc.loading("external_interface")[interface_name]

        with CurlRequestsManager() as cr:
            bugs_count = self.__get_bug_count(start_time_from)
            i = 0
            iteration_list = []
            while i < (int(bugs_count / int(limit)) + 1):
                url = TAPD['host_url'] + res.get("request_address") + "?" + res.get("request_params_start_time").format(
                    self.workspace_id, limit, i + 1, start_time_from)
                logger.info("请求url:{}".format(url))
                res_code, res_data = cr.curl_get(url, interface_name)
                if res_code == 200:
                    self._update_or_create_bugs(res_data.get("data"))
                    for bug_info in res_data.get("data"):
                        iteration_id = bug_info.get("Bug").get("iteration_id")
                        if iteration_id not in iteration_list:
                            iteration_list.append(iteration_id)
                i += 1

            return iteration_list
