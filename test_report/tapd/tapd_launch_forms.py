# Create your views here

from common.tapd_curl import CurlRequestsManager
from dev_effective.service.biz_launch_form_service import Biz<PERSON>aunchFormSer
from mantis.settings import TAPD, logger, TEST_REPORT
from tapd_gateway.from_tapd.service.tapd_entry_launch_form_ser import TapdEntityLaunchFormSer
from tapd_gateway.from_tapd.service.tapd_req_strategy import GetLaunchFormFromTapdStrategy
from task_mgt.tapd_mgt.config.analysis_ini import LoadConfig
from test_report.tapd.models import TAPDLaunchFormsInfo
from test_report.tapd.tapd_helper import TapdHelper


class TAPDLaunchForms:
    def __init__(self, iteration_id_list, project_id=None):
        self.workspace_id = TEST_REPORT['workspace_id']
        self.iteration_id_list = iteration_id_list
        self.project_id = project_id

    def get_launch_forms_from_tapd(self):
        if self.iteration_id_list:
            for iteration_id in self.iteration_id_list:
                story_ser = GetLaunchFormFromTapdStrategy(self.workspace_id, None, iteration_id)
                story_ser.sync_data_from_tapd()
            return True

    def update_test_report_data(self):
        launch_form_ser = TapdEntityLaunchFormSer()
        res_data = launch_form_ser.get_tapd_entity_launch_form_by_iteration_ids(self.workspace_id, self.iteration_id_list)
        if res_data:
            bug_ser = BizLaunchFormSer()
            bug_ser.etl_launch_form(res_data, self.project_id)

    def __get_launch_forms_count(self):
        interface_name = "launch_forms_count"
        lc = LoadConfig()
        res = lc.loading("external_interface")[interface_name]
        with CurlRequestsManager() as cr:
            url = TAPD['host_url'] + res.get("request_address") + "?" \
                  + res.get("request_params").format(self.workspace_id)
            res_code, res_data = cr.curl_get(url, interface_name)
        count = 0
        if res_code == 200:
            count = res_data.get("data").get("count")
        return count

    def __update_or_create_tapd_launch_forms(self, launch_forms_list):
        for launch_forms in launch_forms_list:
            launch_form = launch_forms.get("LaunchForm")

            keys = TAPDLaunchFormsInfo.__dict__.keys()
            launch_form = TapdHelper.exclude_not_exist_fields(keys, launch_form)
            try:
                TAPDLaunchFormsInfo.objects.update_or_create(id=launch_form.get("id"),
                                                             workspace_id=launch_form.get("workspace_id"),
                                                             defaults=launch_form)
            except Exception as e:
                logger.error("写入数据库失败，原因为：{}".format(str(e)))
                raise Exception("写入数据库失败，原因为：{}".format(str(e)))

    def _get_launch_forms_from_tapd(self, iteration_id=None):
        """从tapd拉取发布评审计划"""
        limit = 200

        interface_name = "launch_forms_get"
        lc = LoadConfig()
        res = lc.loading("external_interface")[interface_name]

        with CurlRequestsManager() as cr:
            if not iteration_id:
                launch_forms_count = self.__get_launch_forms_count()
                i = 0
                while i < (int(launch_forms_count / int(limit)) + 1):
                    url = TAPD['host_url'] + res.get("request_address") + "?" + res.get("request_params").format(
                        self.workspace_id, limit, i + 1)
                    res_code, res_data = cr.curl_get(url, interface_name)
                    if res_code == 200:
                        self.__update_or_create_tapd_launch_forms(res_data.get("data"))
                    else:
                        return False
                    i += 1
            else:
                url = TAPD['host_url'] + res.get("request_address") + "?" + res.get("request_params").format(
                    self.workspace_id, limit, 1) + '&iteration_id={}'.format(iteration_id)
                res_code, res_data = cr.curl_get(url, interface_name)
                if res_code == 200:
                    self.__update_or_create_tapd_launch_forms(res_data.get("data"))
                else:
                    return False
        return True
