# Create your views here
from mantis.settings import TEST_REPORT
from test_report.tapd.tapd_helper import TapdHelper


class TAPDIterations:
    def __init__(self, iteration_id_list):
        self.workspace_id = TEST_REPORT['workspace_id']
        self.iteration_id_list = iteration_id_list

    def __get_iterations_count(self):
        interface_name = "iterations_count"
        return TapdHelper.get_count(interface_name, self.workspace_id, self.iteration_id)

