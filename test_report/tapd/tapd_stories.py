# Create your views here

from dev_effective.service.biz_tapd_data_write_strategy import NotNull<PERSON>hecking
from dev_effective.service.biz_story_service import BizStorySer
from mantis.settings import TEST_REPORT, TAPD
from tapd_gateway.from_tapd.model.models import TapdEntryStory
from tapd_gateway.from_tapd.service.tapd_entry_story_ser import TapdEntityStorySer
from tapd_gateway.from_tapd.service.tapd_req_strategy import GetStoryFromTapdStrategy


class TAPDStories:
    def __init__(self, iteration_id_list):
        self.workspace_id = TEST_REPORT['workspace_id']
        self.workitem_type_id = TAPD['workitem_type_id']
        self.iteration_id_list = iteration_id_list

    def __update_or_create_tapd_stories(self, stories_list):
        test_project_ser = BizStorySer()
        test_project_ser.update_or_create_test_projects(stories_list)

    def _get_stories_from_tapd(self):
        """从tapd拉取需求信息"""
        if self.iteration_id_list:
            for iteration_id in self.iteration_id_list:
                story_ser = GetStoryFromTapdStrategy(self.workspace_id, None, iteration_id)
                story_ser.sync_data_from_tapd()

        return True

    def update_project_story_info(self):
        tapd_story_ser = TapdEntityStorySer()
        res_data = tapd_story_ser.get_tapd_entity_story_by_iteration_ids(self.workspace_id, self.iteration_id_list,
                                                                         self.workitem_type_id)
        if res_data:
            self.__update_or_create_tapd_stories(res_data)

    def create_report_for_check_story(self, iteration_id):
        # 1、多人任务时，任务分工比例；2、故事不允许有层级关系
        story_list = TapdEntryStory.objects.filter(tapd_workspace_id=self.workspace_id,
                                                   tapd_story_iteration_id=iteration_id,
                                                   tapd_story_workitem_type_id=TAPD.get("workitem_type_id")
                                                   )
        story_error_list = []
        if story_list:
            not_null_check = NotNullChecking()
            for story in story_list:
                story = story.__dict__
                if story.get("tapd_story_children_id") != "|":
                    story_error_list.append("【" + story.get('tapd_story_name') + "】---> 故事不允许有子故事")

                result = not_null_check.check_task_division_ratio_is_null(story)
                if not result:
                    story_error_list.append("【" + story.get('tapd_story_name') + "】--->【任务分工比例】为空")

            if len(story_error_list) > 0:
                return False, story_error_list
        return True, story_error_list
