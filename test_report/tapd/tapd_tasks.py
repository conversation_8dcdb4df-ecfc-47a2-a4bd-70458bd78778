# Create your views here

from dev_effective.service.biz_task_service import Biz<PERSON>askSer
from dev_effective.dao.dev_effective_dao import TapdWorkspaceModuleConfDao
from mantis.settings import TEST_REPORT
from tapd_gateway.from_tapd.model.models import TapdEntryTask
from tapd_gateway.from_tapd.service.tapd_entry_task_ser import TapdEntityTaskSer
from tapd_gateway.from_tapd.service.tapd_req_strategy import GetTaskFromTapdStrategy


class TAPDTasks:
    def __init__(self, iteration_id_list, project_id=None):
        self.workspace_id = TEST_REPORT['workspace_id']
        self.iteration_id_list = iteration_id_list
        self.project_id = project_id


    def _get_tasks_from_tapd(self):
        """从tapd拉取任务信息"""
        if self.iteration_id_list:
            for iteration_id in self.iteration_id_list:
                task_ser = GetTaskFromTapdStrategy(self.workspace_id, None, iteration_id)
                task_ser.sync_data_from_tapd()
            return True

    def update_biz_task_info(self):
        task_ser = TapdEntityTaskSer()
        res_data = task_ser.get_tapd_entity_task_by_iteration_ids(self.workspace_id, self.iteration_id_list)
        if res_data:
            task_ser = BizTaskSer()
            task_ser.update_or_create_test_tasks(res_data, self.project_id)



    def create_report_for_check_task(self, iteration_id):
        # 1、类型；2、工时（已解决）
        task_list = TapdEntryTask.objects.filter(tapd_workspace_id=self.workspace_id,
                                                tapd_task_iteration_id=iteration_id).exclude(
            entry_status=3)
        task_name_list = []
        if task_list:
            dao = TapdWorkspaceModuleConfDao('任务类型', self.workspace_id, 'task')
            task_type = dao.get_custom_field_by_tapd_name()
            for task in task_list:
                task = task.__dict__
                if task.get('tapd_task_status') != 'done' and not task.get('tapd_task_effort_completed'):
                    task_name_list.append("【" + task.get('tapd_task_name') + "】---> 已解决任务完成工时不可为空")
                if not task.get(task_type):
                    task_name_list.append("【" + task.get('tapd_task_name') + "】---> 任务类型不可为空")
        if task_name_list:
            return False, task_name_list
        return True, task_name_list

