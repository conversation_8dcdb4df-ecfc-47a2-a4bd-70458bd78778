from django.db import models


class TAPDTestPlanInfo(models.Model):
    id = models.IntegerField(verbose_name='计划ID', primary_key=True)
    workspace_id = models.IntegerField(verbose_name='项目ID', blank=True, null=True)
    name = models.CharField(max_length=100, verbose_name='计划名称')
    description = models.TextField(verbose_name='描述')
    iteration_id = models.IntegerField(verbose_name='迭代ID')
    version = models.CharField(max_length=512, verbose_name='版本')
    owner = models.CharField(max_length=512, verbose_name='测试负责人')
    status = models.CharField(max_length=10, verbose_name='计划状态')
    type = models.CharField(max_length=512, verbose_name='测试类型')
    start_date = models.DateTimeField(blank=True, null=True, verbose_name='开始时间')
    end_date = models.DateTimeField(blank=True, null=True, verbose_name='结束时间')
    creator = models.CharField(max_length=50, verbose_name='创建人')
    created = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    modified = models.DateTimeField(blank=True, null=True, verbose_name='修改时间')
    modifier = models.CharField(max_length=50, verbose_name='修改人')
    created_from = models.CharField(max_length=1000, verbose_name='从哪创建')
    custom_field_1 = models.CharField(max_length=1000, verbose_name='custom_field_1')
    custom_field_2 = models.CharField(max_length=1000, verbose_name='custom_field_2')
    custom_field_3 = models.CharField(max_length=1000, verbose_name='custom_field_3')
    custom_field_4 = models.CharField(max_length=1000, verbose_name='custom_field_4')
    custom_field_5 = models.CharField(max_length=1000, verbose_name='custom_field_5')
    custom_field_6 = models.CharField(max_length=1000, verbose_name='custom_field_6')
    custom_field_7 = models.CharField(max_length=1000, verbose_name='custom_field_7')
    custom_field_8 = models.CharField(max_length=1000, verbose_name='custom_field_8')
    custom_field_9 = models.CharField(max_length=1000, verbose_name='custom_field_9')
    custom_field_10 = models.CharField(max_length=1000, verbose_name='custom_field_10')
    custom_field_11 = models.CharField(max_length=1000, verbose_name='custom_field_11')
    custom_field_12 = models.CharField(max_length=1000, verbose_name='custom_field_12')
    custom_field_13 = models.CharField(max_length=1000, verbose_name='custom_field_13')
    custom_field_14 = models.CharField(max_length=1000, verbose_name='custom_field_14')
    custom_field_15 = models.CharField(max_length=1000, verbose_name='custom_field_15')
    custom_field_16 = models.CharField(max_length=1000, verbose_name='custom_field_16')
    custom_field_17 = models.CharField(max_length=1000, verbose_name='custom_field_17')
    custom_field_18 = models.CharField(max_length=1000, verbose_name='custom_field_18')
    custom_field_19 = models.CharField(max_length=1000, verbose_name='custom_field_19')
    custom_field_20 = models.CharField(max_length=1000, verbose_name='custom_field_20')
    custom_field_21 = models.CharField(max_length=1000, verbose_name='custom_field_21')
    custom_field_22 = models.CharField(max_length=1000, verbose_name='custom_field_22')
    custom_field_23 = models.CharField(max_length=1000, verbose_name='custom_field_23')
    custom_field_24 = models.CharField(max_length=1000, verbose_name='custom_field_24')
    custom_field_25 = models.CharField(max_length=1000, verbose_name='custom_field_25')
    custom_field_26 = models.CharField(max_length=1000, verbose_name='custom_field_26')
    custom_field_27 = models.CharField(max_length=1000, verbose_name='custom_field_27')
    custom_field_28 = models.CharField(max_length=1000, verbose_name='custom_field_28')
    custom_field_29 = models.CharField(max_length=1000, verbose_name='custom_field_29')
    custom_field_30 = models.CharField(max_length=1000, verbose_name='custom_field_30')
    custom_field_31 = models.CharField(max_length=1000, verbose_name='custom_field_31')
    custom_field_32 = models.CharField(max_length=1000, verbose_name='custom_field_32')
    custom_field_33 = models.CharField(max_length=1000, verbose_name='custom_field_33')
    custom_field_34 = models.CharField(max_length=1000, verbose_name='custom_field_34')
    custom_field_35 = models.CharField(max_length=1000, verbose_name='custom_field_35')
    custom_field_36 = models.CharField(max_length=1000, verbose_name='custom_field_36')
    custom_field_37 = models.CharField(max_length=1000, verbose_name='custom_field_37')
    custom_field_38 = models.CharField(max_length=1000, verbose_name='custom_field_38')
    custom_field_39 = models.CharField(max_length=1000, verbose_name='custom_field_39')
    custom_field_40 = models.CharField(max_length=1000, verbose_name='custom_field_40')
    custom_field_41 = models.CharField(max_length=1000, verbose_name='custom_field_41')
    custom_field_42 = models.CharField(max_length=1000, verbose_name='custom_field_42')
    custom_field_43 = models.CharField(max_length=1000, verbose_name='custom_field_43')
    custom_field_44 = models.CharField(max_length=1000, verbose_name='custom_field_44')
    custom_field_45 = models.CharField(max_length=1000, verbose_name='custom_field_45')
    custom_field_46 = models.CharField(max_length=1000, verbose_name='custom_field_46')
    custom_field_47 = models.CharField(max_length=1000, verbose_name='custom_field_47')
    custom_field_48 = models.CharField(max_length=1000, verbose_name='custom_field_48')
    custom_field_49 = models.CharField(max_length=1000, verbose_name='custom_field_49')
    custom_field_50 = models.CharField(max_length=1000, verbose_name='custom_field_50')

    class Meta:
        db_table = 'tapd_test_plan'
        verbose_name = 'TAPD测试计划信息表'


class TAPDIterationsInfo(models.Model):
    id = models.IntegerField(verbose_name='计划ID', primary_key=True)
    name = models.CharField(max_length=100, verbose_name='迭代名称')
    workspace_id = models.IntegerField(verbose_name='项目ID', blank=True, null=True)
    startdate = models.DateField(blank=True, null=True, verbose_name='开始时间')
    enddate = models.DateField(blank=True, null=True, verbose_name='结束时间')
    status = models.CharField(max_length=10, verbose_name='迭代状态')
    release_id = models.IntegerField(verbose_name='发布ID')
    description = models.TextField(verbose_name='描述')
    creator = models.CharField(max_length=50, verbose_name='创建人')
    created = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    modified = models.DateTimeField(blank=True, null=True, verbose_name='修改时间')
    modifier = models.CharField(max_length=50, verbose_name='修改人')
    completed = models.CharField(max_length=1000, verbose_name='完成')
    custom_field_1 = models.CharField(max_length=1000, verbose_name='custom_field_1')
    custom_field_2 = models.CharField(max_length=1000, verbose_name='custom_field_2')
    custom_field_3 = models.CharField(max_length=1000, verbose_name='custom_field_3')
    custom_field_4 = models.CharField(max_length=1000, verbose_name='custom_field_4')
    custom_field_5 = models.CharField(max_length=1000, verbose_name='custom_field_5')
    custom_field_6 = models.CharField(max_length=1000, verbose_name='custom_field_6')
    custom_field_7 = models.CharField(max_length=1000, verbose_name='custom_field_7')
    custom_field_8 = models.CharField(max_length=1000, verbose_name='custom_field_8')
    custom_field_9 = models.CharField(max_length=1000, verbose_name='custom_field_9')
    custom_field_10 = models.CharField(max_length=1000, verbose_name='custom_field_10')
    custom_field_11 = models.CharField(max_length=1000, verbose_name='custom_field_11')
    custom_field_12 = models.CharField(max_length=1000, verbose_name='custom_field_12')
    custom_field_13 = models.CharField(max_length=1000, verbose_name='custom_field_13')
    custom_field_14 = models.CharField(max_length=1000, verbose_name='custom_field_14')
    custom_field_15 = models.CharField(max_length=1000, verbose_name='custom_field_15')
    custom_field_16 = models.CharField(max_length=1000, verbose_name='custom_field_16')
    custom_field_17 = models.CharField(max_length=1000, verbose_name='custom_field_17')
    custom_field_18 = models.CharField(max_length=1000, verbose_name='custom_field_18')
    custom_field_19 = models.CharField(max_length=1000, verbose_name='custom_field_19')
    custom_field_20 = models.CharField(max_length=1000, verbose_name='custom_field_20')
    custom_field_21 = models.CharField(max_length=1000, verbose_name='custom_field_21')
    custom_field_22 = models.CharField(max_length=1000, verbose_name='custom_field_22')
    custom_field_23 = models.CharField(max_length=1000, verbose_name='custom_field_23')
    custom_field_24 = models.CharField(max_length=1000, verbose_name='custom_field_24')
    custom_field_25 = models.CharField(max_length=1000, verbose_name='custom_field_25')
    custom_field_26 = models.CharField(max_length=1000, verbose_name='custom_field_26')
    custom_field_27 = models.CharField(max_length=1000, verbose_name='custom_field_27')
    custom_field_28 = models.CharField(max_length=1000, verbose_name='custom_field_28')
    custom_field_29 = models.CharField(max_length=1000, verbose_name='custom_field_29')
    custom_field_30 = models.CharField(max_length=1000, verbose_name='custom_field_30')
    custom_field_31 = models.CharField(max_length=1000, verbose_name='custom_field_31')
    custom_field_32 = models.CharField(max_length=1000, verbose_name='custom_field_32')
    custom_field_33 = models.CharField(max_length=1000, verbose_name='custom_field_33')
    custom_field_34 = models.CharField(max_length=1000, verbose_name='custom_field_34')
    custom_field_35 = models.CharField(max_length=1000, verbose_name='custom_field_35')
    custom_field_36 = models.CharField(max_length=1000, verbose_name='custom_field_36')
    custom_field_37 = models.CharField(max_length=1000, verbose_name='custom_field_37')
    custom_field_38 = models.CharField(max_length=1000, verbose_name='custom_field_38')
    custom_field_39 = models.CharField(max_length=1000, verbose_name='custom_field_39')
    custom_field_40 = models.CharField(max_length=1000, verbose_name='custom_field_40')
    custom_field_41 = models.CharField(max_length=1000, verbose_name='custom_field_41')
    custom_field_42 = models.CharField(max_length=1000, verbose_name='custom_field_42')
    custom_field_43 = models.CharField(max_length=1000, verbose_name='custom_field_43')
    custom_field_44 = models.CharField(max_length=1000, verbose_name='custom_field_44')
    custom_field_45 = models.CharField(max_length=1000, verbose_name='custom_field_45')
    custom_field_46 = models.CharField(max_length=1000, verbose_name='custom_field_46')
    custom_field_47 = models.CharField(max_length=1000, verbose_name='custom_field_47')
    custom_field_48 = models.CharField(max_length=1000, verbose_name='custom_field_48')
    custom_field_49 = models.CharField(max_length=1000, verbose_name='custom_field_49')
    custom_field_50 = models.CharField(max_length=1000, verbose_name='custom_field_50')

    class Meta:
        db_table = 'tapd_iterations'
        verbose_name = 'TAPD迭代信息表'


class TAPDBugsInfo(models.Model):
    id = models.IntegerField(verbose_name='缺陷ID', primary_key=True)
    title = models.CharField(max_length=200, verbose_name='缺陷名称')
    workspace_id = models.IntegerField(verbose_name='项目ID', blank=True, null=True)
    description = models.TextField(verbose_name='描述')
    priority = models.CharField(max_length=10, verbose_name='优先级')
    severity = models.CharField(max_length=10, verbose_name='严重程度')
    module = models.CharField(max_length=100, verbose_name='模块')
    status = models.CharField(max_length=10, verbose_name='状态')
    reporter = models.CharField(max_length=100, verbose_name='创建人')
    created = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    bugtype = models.CharField(max_length=50, verbose_name='缺陷类型')
    resolved = models.CharField(max_length=50, verbose_name='已解决')
    closed = models.CharField(max_length=50, verbose_name='已关闭')
    modified = models.DateTimeField(blank=True, null=True, verbose_name='最后修改时间')
    lastmodify = models.CharField(max_length=50, verbose_name='最后修改人')
    auditer = models.CharField(max_length=50, verbose_name='审核人')
    de = models.CharField(max_length=50, verbose_name='开发人员')
    fixer = models.CharField(max_length=50, verbose_name='修复人')
    version_test = models.CharField(max_length=50, verbose_name='验证版本')
    version_report = models.CharField(max_length=50, verbose_name='发现版本')
    version_close = models.CharField(max_length=50, verbose_name='关闭版本')
    version_fix = models.CharField(max_length=50, verbose_name='合入版本')
    baseline_find = models.CharField(max_length=50, verbose_name='发现基线')
    baseline_join = models.CharField(max_length=50, verbose_name='合入基线')
    baseline_close = models.CharField(max_length=50, verbose_name='关闭版本')
    baseline_test = models.CharField(max_length=50, verbose_name='验证基线')
    sourcephase = models.CharField(max_length=50, verbose_name='引入阶段')
    te = models.CharField(max_length=50, verbose_name='测试人员')
    current_owner = models.CharField(max_length=50, verbose_name='当前处理人')
    iteration_id = models.IntegerField(verbose_name='缺陷ID')
    resolution = models.CharField(max_length=50, verbose_name='解决方法')
    source = models.CharField(max_length=50, verbose_name='缺陷根源')
    originphase = models.CharField(max_length=50, verbose_name='发现阶段')
    confirmer = models.CharField(max_length=50, verbose_name='确认人')
    milestone = models.CharField(max_length=50, verbose_name='里程碑')
    participator = models.CharField(max_length=50, verbose_name='参与人')
    closer = models.CharField(max_length=50, verbose_name='关闭人')
    platform = models.CharField(max_length=50, verbose_name='软件平台')
    os = models.CharField(max_length=50, verbose_name='操作系统')
    testtype = models.CharField(max_length=50, verbose_name='测试类型')
    testphase = models.CharField(max_length=50, verbose_name='测试阶段')
    frequency = models.CharField(max_length=50, verbose_name='重现规律')
    cc = models.CharField(max_length=50, verbose_name='抄送人')
    regression_number = models.CharField(max_length=50, verbose_name='回归数')
    flows = models.CharField(max_length=500, verbose_name='工作流')
    feature = models.CharField(max_length=50, verbose_name='功能')
    testmode = models.CharField(max_length=50, verbose_name='测试方式')
    estimate = models.CharField(max_length=50, verbose_name='估算')
    issue_id = models.CharField(max_length=50, verbose_name='发行ID')
    created_from = models.CharField(max_length=50, verbose_name='创建来源')
    release_id = models.CharField(max_length=50, verbose_name='发布ID')
    verify_time = models.DateTimeField(blank=True, null=True, verbose_name='验证时间')
    reject_time = models.DateTimeField(blank=True, null=True, verbose_name='拒绝时间')
    reopen_time = models.DateTimeField(blank=True, null=True, verbose_name='重开时间')
    audit_time = models.DateTimeField(blank=True, null=True, verbose_name='审核时间')
    suspend_time = models.DateTimeField(blank=True, null=True, verbose_name='挂起时间')
    due = models.CharField(max_length=50, verbose_name='预计')
    begin = models.CharField(max_length=50, verbose_name='预计开始')
    deadline = models.CharField(max_length=50, verbose_name='解决期限')
    in_progress_time = models.DateTimeField(blank=True, null=True, verbose_name='接受处理时间')
    assigned_time = models.DateTimeField(blank=True, null=True, verbose_name='分配时间')
    template_id =models.CharField(max_length=50, verbose_name='模板ID')
    story_id = models.CharField(max_length=50, verbose_name='故事ID')
    label = models.CharField(max_length=50, verbose_name='标签')
    size = models.CharField(max_length=50, verbose_name='大小')
    effort = models.CharField(max_length=50, verbose_name='预估工时')
    effort_completed = models.CharField(max_length=50, verbose_name='完成工时')
    exceed = models.CharField(max_length=50, verbose_name='超出工时')
    remain = models.CharField(max_length=50, verbose_name='剩余工时')
    custom_field_one = models.CharField(max_length=1000, verbose_name='custom_field_1')
    custom_field_two = models.CharField(max_length=1000, verbose_name='custom_field_2')
    custom_field_three = models.CharField(max_length=1000, verbose_name='custom_field_3')
    custom_field_four = models.CharField(max_length=1000, verbose_name='custom_field_4')
    custom_field_five = models.CharField(max_length=1000, verbose_name='custom_field_5')
    custom_field_6 = models.CharField(max_length=1000, verbose_name='custom_field_6')
    custom_field_7 = models.CharField(max_length=1000, verbose_name='custom_field_7')
    custom_field_8 = models.CharField(max_length=1000, verbose_name='custom_field_8')
    custom_field_9 = models.CharField(max_length=1000, verbose_name='custom_field_9')
    custom_field_10 = models.CharField(max_length=1000, verbose_name='custom_field_10')
    custom_field_11 = models.CharField(max_length=1000, verbose_name='custom_field_11')
    custom_field_12 = models.CharField(max_length=1000, verbose_name='custom_field_12')
    custom_field_13 = models.CharField(max_length=1000, verbose_name='custom_field_13')
    custom_field_14 = models.CharField(max_length=1000, verbose_name='custom_field_14')
    custom_field_15 = models.CharField(max_length=1000, verbose_name='custom_field_15')
    custom_field_16 = models.CharField(max_length=1000, verbose_name='custom_field_16')
    custom_field_17 = models.CharField(max_length=1000, verbose_name='custom_field_17')
    custom_field_18 = models.CharField(max_length=1000, verbose_name='custom_field_18')
    custom_field_19 = models.CharField(max_length=1000, verbose_name='custom_field_19')
    custom_field_20 = models.CharField(max_length=1000, verbose_name='custom_field_20')
    custom_field_21 = models.CharField(max_length=1000, verbose_name='custom_field_21')
    custom_field_22 = models.CharField(max_length=1000, verbose_name='custom_field_22')
    custom_field_23 = models.CharField(max_length=1000, verbose_name='custom_field_23')
    custom_field_24 = models.CharField(max_length=1000, verbose_name='custom_field_24')
    custom_field_25 = models.CharField(max_length=1000, verbose_name='custom_field_25')
    custom_field_26 = models.CharField(max_length=1000, verbose_name='custom_field_26')
    custom_field_27 = models.CharField(max_length=1000, verbose_name='custom_field_27')
    custom_field_28 = models.CharField(max_length=1000, verbose_name='custom_field_28')
    custom_field_29 = models.CharField(max_length=1000, verbose_name='custom_field_29')
    custom_field_30 = models.CharField(max_length=1000, verbose_name='custom_field_30')
    custom_field_31 = models.CharField(max_length=1000, verbose_name='custom_field_31')
    custom_field_32 = models.CharField(max_length=1000, verbose_name='custom_field_32')
    custom_field_33 = models.CharField(max_length=1000, verbose_name='custom_field_33')
    custom_field_34 = models.CharField(max_length=1000, verbose_name='custom_field_34')
    custom_field_35 = models.CharField(max_length=1000, verbose_name='custom_field_35')
    custom_field_36 = models.CharField(max_length=1000, verbose_name='custom_field_36')
    custom_field_37 = models.CharField(max_length=1000, verbose_name='custom_field_37')
    custom_field_38 = models.CharField(max_length=1000, verbose_name='custom_field_38')
    custom_field_39 = models.CharField(max_length=1000, verbose_name='custom_field_39')
    custom_field_40 = models.CharField(max_length=1000, verbose_name='custom_field_40')
    custom_field_41 = models.CharField(max_length=1000, verbose_name='custom_field_41')
    custom_field_42 = models.CharField(max_length=1000, verbose_name='custom_field_42')
    custom_field_43 = models.CharField(max_length=1000, verbose_name='custom_field_43')
    custom_field_44 = models.CharField(max_length=1000, verbose_name='custom_field_44')
    custom_field_45 = models.CharField(max_length=1000, verbose_name='custom_field_45')
    custom_field_46 = models.CharField(max_length=1000, verbose_name='custom_field_46')
    custom_field_47 = models.CharField(max_length=1000, verbose_name='custom_field_47')
    custom_field_48 = models.CharField(max_length=1000, verbose_name='custom_field_48')
    custom_field_49 = models.CharField(max_length=1000, verbose_name='custom_field_49')
    custom_field_50 = models.CharField(max_length=1000, verbose_name='custom_field_50')
    custom_field_51 = models.CharField(max_length=1000, verbose_name='custom_field_51')
    custom_field_52 = models.CharField(max_length=1000, verbose_name='custom_field_52')
    custom_field_53 = models.CharField(max_length=1000, verbose_name='custom_field_53')
    custom_field_54 = models.CharField(max_length=1000, verbose_name='custom_field_54')
    custom_field_55 = models.CharField(max_length=1000, verbose_name='custom_field_55')
    custom_field_56 = models.CharField(max_length=1000, verbose_name='custom_field_56')
    custom_field_57 = models.CharField(max_length=1000, verbose_name='custom_field_57')
    custom_field_58 = models.CharField(max_length=1000, verbose_name='custom_field_58')
    custom_field_59 = models.CharField(max_length=1000, verbose_name='custom_field_59')
    custom_field_60 = models.CharField(max_length=1000, verbose_name='custom_field_60')
    custom_field_61 = models.CharField(max_length=1000, verbose_name='custom_field_61')
    custom_field_62 = models.CharField(max_length=1000, verbose_name='custom_field_62')
    custom_field_63 = models.CharField(max_length=1000, verbose_name='custom_field_63')
    custom_field_64 = models.CharField(max_length=1000, verbose_name='custom_field_64')
    custom_field_65 = models.CharField(max_length=1000, verbose_name='custom_field_65')
    custom_field_66 = models.CharField(max_length=1000, verbose_name='custom_field_66')
    custom_field_67 = models.CharField(max_length=1000, verbose_name='custom_field_67')
    custom_field_68 = models.CharField(max_length=1000, verbose_name='custom_field_68')
    custom_field_69 = models.CharField(max_length=1000, verbose_name='custom_field_69')
    custom_field_70 = models.CharField(max_length=1000, verbose_name='custom_field_70')
    custom_field_71 = models.CharField(max_length=1000, verbose_name='custom_field_71')
    custom_field_72 = models.CharField(max_length=1000, verbose_name='custom_field_72')
    custom_field_73 = models.CharField(max_length=1000, verbose_name='custom_field_73')
    custom_field_74 = models.CharField(max_length=1000, verbose_name='custom_field_74')
    custom_field_75 = models.CharField(max_length=1000, verbose_name='custom_field_75')
    custom_field_76 = models.CharField(max_length=1000, verbose_name='custom_field_76')
    custom_field_77 = models.CharField(max_length=1000, verbose_name='custom_field_77')
    custom_field_78 = models.CharField(max_length=1000, verbose_name='custom_field_78')
    custom_field_79 = models.CharField(max_length=1000, verbose_name='custom_field_79')
    custom_field_80 = models.CharField(max_length=1000, verbose_name='custom_field_80')
    custom_field_81 = models.CharField(max_length=1000, verbose_name='custom_field_81')
    custom_field_82 = models.CharField(max_length=1000, verbose_name='custom_field_82')
    custom_field_83 = models.CharField(max_length=1000, verbose_name='custom_field_83')
    custom_field_84 = models.CharField(max_length=1000, verbose_name='custom_field_84')
    custom_field_85 = models.CharField(max_length=1000, verbose_name='custom_field_85')
    custom_field_86 = models.CharField(max_length=1000, verbose_name='custom_field_86')
    custom_field_87 = models.CharField(max_length=1000, verbose_name='custom_field_87')
    custom_field_88 = models.CharField(max_length=1000, verbose_name='custom_field_88')
    custom_field_89 = models.CharField(max_length=1000, verbose_name='custom_field_89')
    custom_field_90 = models.CharField(max_length=1000, verbose_name='custom_field_90')
    custom_field_91 = models.CharField(max_length=1000, verbose_name='custom_field_91')
    custom_field_92 = models.CharField(max_length=1000, verbose_name='custom_field_92')
    custom_field_93 = models.CharField(max_length=1000, verbose_name='custom_field_93')
    custom_field_94 = models.CharField(max_length=1000, verbose_name='custom_field_94')
    custom_field_95 = models.CharField(max_length=1000, verbose_name='custom_field_95')
    custom_field_96 = models.CharField(max_length=1000, verbose_name='custom_field_96')
    custom_field_97 = models.CharField(max_length=1000, verbose_name='custom_field_97')
    custom_field_98 = models.CharField(max_length=1000, verbose_name='custom_field_98')
    custom_field_99 = models.CharField(max_length=1000, verbose_name='custom_field_99')
    custom_field_100 = models.CharField(max_length=1000, verbose_name='custom_field_100')

    class Meta:
        db_table = 'tapd_bug'
        verbose_name = 'TAPD缺陷信息表'


class TAPDStoriesInfo(models.Model):
    id = models.BigIntegerField(verbose_name='ID', primary_key=True)
    workitem_type_id = models.IntegerField(verbose_name='需求类别ID')
    name = models.CharField(max_length=10, verbose_name='需求名称')
    description = models.TextField(verbose_name='描述')
    workspace_id = models.IntegerField(verbose_name='项目ID', blank=True, null=True)
    creator = models.CharField(max_length=20, verbose_name='创建人')
    created = models.DateTimeField(verbose_name='创建时间')
    modified = models.DateTimeField(verbose_name='修改时间')
    status = models.CharField(max_length=20, verbose_name='需求状态')
    owner = models.CharField(max_length=20, verbose_name='处理人')
    cc = models.CharField(max_length=50, verbose_name='抄送人')
    begin = models.DateField(verbose_name='预计开始')
    due = models.DateField(verbose_name='预计结束')
    size = models.CharField(max_length=50, verbose_name='规模')
    priority = models.CharField(max_length=50, verbose_name='优先级')
    developer = models.CharField(max_length=100, verbose_name='开发人员')
    iteration_id = models.BigIntegerField(verbose_name='迭代ID')
    test_focus = models.CharField(max_length=50, verbose_name='测试重点')
    type = models.CharField(max_length=20, verbose_name='类型')
    source = models.CharField(max_length=50, verbose_name='来源')
    module = models.CharField(max_length=50, verbose_name='模块')
    version = models.CharField(max_length=50, verbose_name='版本')
    completed = models.DateTimeField(verbose_name='完成时间')
    category_id = models.CharField(max_length=10, verbose_name='需求分类')
    path = models.CharField(max_length=300, verbose_name='层级')
    parent_id = models.CharField(max_length=100, verbose_name='父需求ID')
    children_id = models.CharField(max_length=100, verbose_name='子需求ID')
    ancestor_id = models.CharField(max_length=100, verbose_name='需求层级关系')
    business_value = models.CharField(max_length=10, verbose_name='业务价值')
    effort = models.CharField(max_length=10, verbose_name='预估工时')
    effort_completed = models.CharField(max_length=10, verbose_name='完成工时')
    exceed = models.CharField(max_length=10, verbose_name='超出工时')
    remain = models.CharField(max_length=10, verbose_name='剩余工时')
    release_id = models.BigIntegerField(verbose_name='发布计划ID')
    confidential = models.CharField(max_length=10, verbose_name='保密级别')
    templated_id = models.BigIntegerField(verbose_name='模板ID')
    created_from = models.CharField(max_length=50, verbose_name='创建来源')
    feature = models.CharField(max_length=50, verbose_name='功能')
    label = models.CharField(max_length=50, verbose_name='标签')
    progress = models.CharField(max_length=50, verbose_name='处理')
    is_archived = models.CharField(max_length=50, verbose_name='是否完成')
    custom_field_one = models.CharField(max_length=1000, verbose_name='custom_field_1')
    custom_field_two = models.CharField(max_length=1000, verbose_name='custom_field_2')
    custom_field_three = models.CharField(max_length=1000, verbose_name='custom_field_3')
    custom_field_four = models.CharField(max_length=1000, verbose_name='custom_field_4')
    custom_field_five = models.CharField(max_length=1000, verbose_name='custom_field_5')
    custom_field_six = models.CharField(max_length=1000, verbose_name='custom_field_6')
    custom_field_seven = models.CharField(max_length=1000, verbose_name='custom_field_7')
    custom_field_eight = models.CharField(max_length=1000, verbose_name='custom_field_8')
    custom_field_9 = models.CharField(max_length=1000, verbose_name='custom_field_9')
    custom_field_10 = models.CharField(max_length=1000, verbose_name='custom_field_10')
    custom_field_11 = models.CharField(max_length=1000, verbose_name='custom_field_11')
    custom_field_12 = models.CharField(max_length=1000, verbose_name='custom_field_12')
    custom_field_13 = models.CharField(max_length=1000, verbose_name='custom_field_13')
    custom_field_14 = models.CharField(max_length=1000, verbose_name='custom_field_14')
    custom_field_15 = models.CharField(max_length=1000, verbose_name='custom_field_15')
    custom_field_16 = models.CharField(max_length=1000, verbose_name='custom_field_16')
    custom_field_17 = models.CharField(max_length=1000, verbose_name='custom_field_17')
    custom_field_18 = models.CharField(max_length=1000, verbose_name='custom_field_18')
    custom_field_19 = models.CharField(max_length=1000, verbose_name='custom_field_19')
    custom_field_20 = models.CharField(max_length=1000, verbose_name='custom_field_20')
    custom_field_21 = models.CharField(max_length=1000, verbose_name='custom_field_21')
    custom_field_22 = models.CharField(max_length=1000, verbose_name='custom_field_22')
    custom_field_23 = models.CharField(max_length=1000, verbose_name='custom_field_23')
    custom_field_24 = models.CharField(max_length=1000, verbose_name='custom_field_24')
    custom_field_25 = models.CharField(max_length=1000, verbose_name='custom_field_25')
    custom_field_26 = models.CharField(max_length=1000, verbose_name='custom_field_26')
    custom_field_27 = models.CharField(max_length=1000, verbose_name='custom_field_27')
    custom_field_28 = models.CharField(max_length=1000, verbose_name='custom_field_28')
    custom_field_29 = models.CharField(max_length=1000, verbose_name='custom_field_29')
    custom_field_30 = models.CharField(max_length=1000, verbose_name='custom_field_30')
    custom_field_31 = models.CharField(max_length=1000, verbose_name='custom_field_31')
    custom_field_32 = models.CharField(max_length=1000, verbose_name='custom_field_32')
    custom_field_33 = models.CharField(max_length=1000, verbose_name='custom_field_33')
    custom_field_34 = models.CharField(max_length=1000, verbose_name='custom_field_34')
    custom_field_35 = models.CharField(max_length=1000, verbose_name='custom_field_35')
    custom_field_36 = models.CharField(max_length=1000, verbose_name='custom_field_36')
    custom_field_37 = models.CharField(max_length=1000, verbose_name='custom_field_37')
    custom_field_38 = models.CharField(max_length=1000, verbose_name='custom_field_38')
    custom_field_39 = models.CharField(max_length=1000, verbose_name='custom_field_39')
    custom_field_40 = models.CharField(max_length=1000, verbose_name='custom_field_40')
    custom_field_41 = models.CharField(max_length=1000, verbose_name='custom_field_41')
    custom_field_42 = models.CharField(max_length=1000, verbose_name='custom_field_42')
    custom_field_43 = models.CharField(max_length=1000, verbose_name='custom_field_43')
    custom_field_44 = models.CharField(max_length=1000, verbose_name='custom_field_44')
    custom_field_45 = models.CharField(max_length=1000, verbose_name='custom_field_45')
    custom_field_46 = models.CharField(max_length=1000, verbose_name='custom_field_46')
    custom_field_47 = models.CharField(max_length=1000, verbose_name='custom_field_47')
    custom_field_48 = models.CharField(max_length=1000, verbose_name='custom_field_48')
    custom_field_49 = models.CharField(max_length=1000, verbose_name='custom_field_49')
    custom_field_50 = models.CharField(max_length=1000, verbose_name='custom_field_50')
    custom_field_51 = models.CharField(max_length=1000, verbose_name='custom_field_51')
    custom_field_52 = models.CharField(max_length=1000, verbose_name='custom_field_52')
    custom_field_53 = models.CharField(max_length=1000, verbose_name='custom_field_53')
    custom_field_54 = models.CharField(max_length=1000, verbose_name='custom_field_54')
    custom_field_55 = models.CharField(max_length=1000, verbose_name='custom_field_55')
    custom_field_56 = models.CharField(max_length=1000, verbose_name='custom_field_56')
    custom_field_57 = models.CharField(max_length=1000, verbose_name='custom_field_57')
    custom_field_58 = models.CharField(max_length=1000, verbose_name='custom_field_58')
    custom_field_59 = models.CharField(max_length=1000, verbose_name='custom_field_59')
    custom_field_60 = models.CharField(max_length=1000, verbose_name='custom_field_60')
    custom_field_61 = models.CharField(max_length=1000, verbose_name='custom_field_61')
    custom_field_62 = models.CharField(max_length=1000, verbose_name='custom_field_62')
    custom_field_63 = models.CharField(max_length=1000, verbose_name='custom_field_63')
    custom_field_64 = models.CharField(max_length=1000, verbose_name='custom_field_64')
    custom_field_65 = models.CharField(max_length=1000, verbose_name='custom_field_65')
    custom_field_66 = models.CharField(max_length=1000, verbose_name='custom_field_66')
    custom_field_67 = models.CharField(max_length=1000, verbose_name='custom_field_67')
    custom_field_68 = models.CharField(max_length=1000, verbose_name='custom_field_68')
    custom_field_69 = models.CharField(max_length=1000, verbose_name='custom_field_69')
    custom_field_70 = models.CharField(max_length=1000, verbose_name='custom_field_70')
    custom_field_71 = models.CharField(max_length=1000, verbose_name='custom_field_71')
    custom_field_72 = models.CharField(max_length=1000, verbose_name='custom_field_72')
    custom_field_73 = models.CharField(max_length=1000, verbose_name='custom_field_73')
    custom_field_74 = models.CharField(max_length=1000, verbose_name='custom_field_74')
    custom_field_75 = models.CharField(max_length=1000, verbose_name='custom_field_75')
    custom_field_76 = models.CharField(max_length=1000, verbose_name='custom_field_76')
    custom_field_77 = models.CharField(max_length=1000, verbose_name='custom_field_77')
    custom_field_78 = models.CharField(max_length=1000, verbose_name='custom_field_78')
    custom_field_79 = models.CharField(max_length=1000, verbose_name='custom_field_79')
    custom_field_80 = models.CharField(max_length=1000, verbose_name='custom_field_80')
    custom_field_81 = models.CharField(max_length=1000, verbose_name='custom_field_81')
    custom_field_82 = models.CharField(max_length=1000, verbose_name='custom_field_82')
    custom_field_83 = models.CharField(max_length=1000, verbose_name='custom_field_83')
    custom_field_84 = models.CharField(max_length=1000, verbose_name='custom_field_84')
    custom_field_85 = models.CharField(max_length=1000, verbose_name='custom_field_85')
    custom_field_86 = models.CharField(max_length=1000, verbose_name='custom_field_86')
    custom_field_87 = models.CharField(max_length=1000, verbose_name='custom_field_87')
    custom_field_88 = models.CharField(max_length=1000, verbose_name='custom_field_88')
    custom_field_89 = models.CharField(max_length=1000, verbose_name='custom_field_89')
    custom_field_90 = models.CharField(max_length=1000, verbose_name='custom_field_90')
    custom_field_91 = models.CharField(max_length=1000, verbose_name='custom_field_91')
    custom_field_92 = models.CharField(max_length=1000, verbose_name='custom_field_92')
    custom_field_93 = models.CharField(max_length=1000, verbose_name='custom_field_93')
    custom_field_94 = models.CharField(max_length=1000, verbose_name='custom_field_94')
    custom_field_95 = models.CharField(max_length=1000, verbose_name='custom_field_95')
    custom_field_96 = models.CharField(max_length=1000, verbose_name='custom_field_96')
    custom_field_97 = models.CharField(max_length=1000, verbose_name='custom_field_97')
    custom_field_98 = models.CharField(max_length=1000, verbose_name='custom_field_98')
    custom_field_99 = models.CharField(max_length=1000, verbose_name='custom_field_99')
    custom_field_100 = models.CharField(max_length=1000, verbose_name='custom_field_100')
    custom_field_101 = models.CharField(max_length=1000, verbose_name='custom_field_101')
    custom_field_102 = models.CharField(max_length=1000, verbose_name='custom_field_102')
    custom_field_103 = models.CharField(max_length=1000, verbose_name='custom_field_103')
    custom_field_104 = models.CharField(max_length=1000, verbose_name='custom_field_104')
    custom_field_105 = models.CharField(max_length=1000, verbose_name='custom_field_105')
    custom_field_106 = models.CharField(max_length=1000, verbose_name='custom_field_106')
    custom_field_107 = models.CharField(max_length=1000, verbose_name='custom_field_107')
    custom_field_108 = models.CharField(max_length=1000, verbose_name='custom_field_108')
    custom_field_109 = models.CharField(max_length=1000, verbose_name='custom_field_109')
    custom_field_110 = models.CharField(max_length=1000, verbose_name='custom_field_110')
    custom_field_111 = models.CharField(max_length=1000, verbose_name='custom_field_111')
    custom_field_112 = models.CharField(max_length=1000, verbose_name='custom_field_112')
    custom_field_113 = models.CharField(max_length=1000, verbose_name='custom_field_113')
    custom_field_114 = models.CharField(max_length=1000, verbose_name='custom_field_114')
    custom_field_115 = models.CharField(max_length=1000, verbose_name='custom_field_115')
    custom_field_116 = models.CharField(max_length=1000, verbose_name='custom_field_116')
    custom_field_117 = models.CharField(max_length=1000, verbose_name='custom_field_117')
    custom_field_118 = models.CharField(max_length=1000, verbose_name='custom_field_118')
    custom_field_119 = models.CharField(max_length=1000, verbose_name='custom_field_119')
    custom_field_120 = models.CharField(max_length=1000, verbose_name='custom_field_120')
    custom_field_121 = models.CharField(max_length=1000, verbose_name='custom_field_121')
    custom_field_122 = models.CharField(max_length=1000, verbose_name='custom_field_122')
    custom_field_123 = models.CharField(max_length=1000, verbose_name='custom_field_123')
    custom_field_124 = models.CharField(max_length=1000, verbose_name='custom_field_124')
    custom_field_125 = models.CharField(max_length=1000, verbose_name='custom_field_125')
    custom_field_126 = models.CharField(max_length=1000, verbose_name='custom_field_126')
    custom_field_127 = models.CharField(max_length=1000, verbose_name='custom_field_127')
    custom_field_128 = models.CharField(max_length=1000, verbose_name='custom_field_128')
    custom_field_129 = models.CharField(max_length=1000, verbose_name='custom_field_129')
    custom_field_130 = models.CharField(max_length=1000, verbose_name='custom_field_130')
    custom_field_131 = models.CharField(max_length=1000, verbose_name='custom_field_131')
    custom_field_132 = models.CharField(max_length=1000, verbose_name='custom_field_132')
    custom_field_133 = models.CharField(max_length=1000, verbose_name='custom_field_133')
    custom_field_134 = models.CharField(max_length=1000, verbose_name='custom_field_134')
    custom_field_135 = models.CharField(max_length=1000, verbose_name='custom_field_135')
    custom_field_136 = models.CharField(max_length=1000, verbose_name='custom_field_136')
    custom_field_137 = models.CharField(max_length=1000, verbose_name='custom_field_137')
    custom_field_138 = models.CharField(max_length=1000, verbose_name='custom_field_138')
    custom_field_139 = models.CharField(max_length=1000, verbose_name='custom_field_139')
    custom_field_140 = models.CharField(max_length=1000, verbose_name='custom_field_140')
    custom_field_141 = models.CharField(max_length=1000, verbose_name='custom_field_141')
    custom_field_142 = models.CharField(max_length=1000, verbose_name='custom_field_142')
    custom_field_143 = models.CharField(max_length=1000, verbose_name='custom_field_143')
    custom_field_144 = models.CharField(max_length=1000, verbose_name='custom_field_144')
    custom_field_145 = models.CharField(max_length=1000, verbose_name='custom_field_145')
    custom_field_146 = models.CharField(max_length=1000, verbose_name='custom_field_146')
    custom_field_147 = models.CharField(max_length=1000, verbose_name='custom_field_147')
    custom_field_148 = models.CharField(max_length=1000, verbose_name='custom_field_148')
    custom_field_149 = models.CharField(max_length=1000, verbose_name='custom_field_149')
    custom_field_150 = models.CharField(max_length=1000, verbose_name='custom_field_150')
    custom_field_151 = models.CharField(max_length=1000, verbose_name='custom_field_151')
    custom_field_152 = models.CharField(max_length=1000, verbose_name='custom_field_152')
    custom_field_153 = models.CharField(max_length=1000, verbose_name='custom_field_153')
    custom_field_154 = models.CharField(max_length=1000, verbose_name='custom_field_154')
    custom_field_155 = models.CharField(max_length=1000, verbose_name='custom_field_155')
    custom_field_156 = models.CharField(max_length=1000, verbose_name='custom_field_156')
    custom_field_157 = models.CharField(max_length=1000, verbose_name='custom_field_157')
    custom_field_158 = models.CharField(max_length=1000, verbose_name='custom_field_158')
    custom_field_159 = models.CharField(max_length=1000, verbose_name='custom_field_159')
    custom_field_160 = models.CharField(max_length=1000, verbose_name='custom_field_160')
    custom_field_161 = models.CharField(max_length=1000, verbose_name='custom_field_161')
    custom_field_162 = models.CharField(max_length=1000, verbose_name='custom_field_162')
    custom_field_163 = models.CharField(max_length=1000, verbose_name='custom_field_163')
    custom_field_164 = models.CharField(max_length=1000, verbose_name='custom_field_164')
    custom_field_165 = models.CharField(max_length=1000, verbose_name='custom_field_165')
    custom_field_166 = models.CharField(max_length=1000, verbose_name='custom_field_166')
    custom_field_167 = models.CharField(max_length=1000, verbose_name='custom_field_167')
    custom_field_168 = models.CharField(max_length=1000, verbose_name='custom_field_168')
    custom_field_169 = models.CharField(max_length=1000, verbose_name='custom_field_169')
    custom_field_170 = models.CharField(max_length=1000, verbose_name='custom_field_170')
    custom_field_171 = models.CharField(max_length=1000, verbose_name='custom_field_171')
    custom_field_172 = models.CharField(max_length=1000, verbose_name='custom_field_172')
    custom_field_173 = models.CharField(max_length=1000, verbose_name='custom_field_173')
    custom_field_174 = models.CharField(max_length=1000, verbose_name='custom_field_174')
    custom_field_175 = models.CharField(max_length=1000, verbose_name='custom_field_175')
    custom_field_176 = models.CharField(max_length=1000, verbose_name='custom_field_176')
    custom_field_177 = models.CharField(max_length=1000, verbose_name='custom_field_177')
    custom_field_178 = models.CharField(max_length=1000, verbose_name='custom_field_178')
    custom_field_179 = models.CharField(max_length=1000, verbose_name='custom_field_179')
    custom_field_180 = models.CharField(max_length=1000, verbose_name='custom_field_180')
    custom_field_181 = models.CharField(max_length=1000, verbose_name='custom_field_181')
    custom_field_182 = models.CharField(max_length=1000, verbose_name='custom_field_182')
    custom_field_183 = models.CharField(max_length=1000, verbose_name='custom_field_183')
    custom_field_184 = models.CharField(max_length=1000, verbose_name='custom_field_184')
    custom_field_185 = models.CharField(max_length=1000, verbose_name='custom_field_185')
    custom_field_186 = models.CharField(max_length=1000, verbose_name='custom_field_186')
    custom_field_187 = models.CharField(max_length=1000, verbose_name='custom_field_187')
    custom_field_188 = models.CharField(max_length=1000, verbose_name='custom_field_188')
    custom_field_189 = models.CharField(max_length=1000, verbose_name='custom_field_189')
    custom_field_190 = models.CharField(max_length=1000, verbose_name='custom_field_190')
    custom_field_191 = models.CharField(max_length=1000, verbose_name='custom_field_191')
    custom_field_192 = models.CharField(max_length=1000, verbose_name='custom_field_192')
    custom_field_193 = models.CharField(max_length=1000, verbose_name='custom_field_193')
    custom_field_194 = models.CharField(max_length=1000, verbose_name='custom_field_194')
    custom_field_195 = models.CharField(max_length=1000, verbose_name='custom_field_195')
    custom_field_196 = models.CharField(max_length=1000, verbose_name='custom_field_196')
    custom_field_197 = models.CharField(max_length=1000, verbose_name='custom_field_197')
    custom_field_198 = models.CharField(max_length=1000, verbose_name='custom_field_198')
    custom_field_199 = models.CharField(max_length=1000, verbose_name='custom_field_199')
    custom_field_200 = models.CharField(max_length=1000, verbose_name='custom_field_200')

    class Meta:
        db_table = 'tapd_stories'
        verbose_name = 'TAPD迭代需求表'


class TAPDLaunchFormsInfo(models.Model):
    id = models.BigIntegerField(verbose_name='ID', primary_key=True)
    title = models.CharField(max_length=100, verbose_name='发布评审计划标题')
    name = models.CharField(max_length=20, verbose_name='发布评审计划名称')
    creator = models.CharField(max_length=50, verbose_name='创建人')
    created = models.DateTimeField(verbose_name='创建时间')
    workspace_id = models.IntegerField(verbose_name='项目ID', blank=True, null=True)
    status = models.CharField(max_length=20, verbose_name='计划状态')
    version_type = models.CharField(max_length=100, verbose_name='版本类型')
    baseline = models.CharField(max_length=100, verbose_name='基线')
    release_model = models.CharField(max_length=100, verbose_name='发布模型')
    roadmap_version = models.CharField(max_length=100, verbose_name='版本')
    release_type = models.CharField(max_length=20, verbose_name='发布类型')
    change_type = models.CharField(max_length=20, verbose_name='变更烈性')
    signed_by = models.CharField(max_length=20, verbose_name='签发人')
    archived_by = models.CharField(max_length=20, verbose_name='发布确认人')
    cc = models.CharField(max_length=50, verbose_name='抄送人')
    change_notifier = models.CharField(max_length=50, verbose_name='变更通知人')
    signed = models.DateTimeField(verbose_name='签发时间')
    archived = models.DateTimeField(verbose_name='发布确认时间')
    signer_result = models.CharField(max_length=100, verbose_name='签发结论')
    signer_comment = models.CharField(max_length=100, verbose_name='签发意见')
    release_result = models.CharField(max_length=100, verbose_name='发布结论')
    release_comment = models.CharField(max_length=100, verbose_name='发布意见')
    test_path = models.CharField(max_length=100, verbose_name='测试路径')
    created_path = models.CharField(max_length=100, verbose_name='归档路径')
    remark = models.CharField(max_length=100, verbose_name='备注')
    participator = models.CharField(max_length=100, verbose_name='参与人')
    template_id = models.BigIntegerField(verbose_name='模板ID')
    iteration_id = models.BigIntegerField(verbose_name='迭代ID')
    release_id = models.BigIntegerField(verbose_name='发布ID')
    flows =  models.CharField(max_length=100, verbose_name='工作流')
    custom_field_one = models.CharField(max_length=1000, verbose_name='custom_field_1')
    custom_field_two = models.CharField(max_length=1000, verbose_name='custom_field_2')
    custom_field_three = models.CharField(max_length=1000, verbose_name='custom_field_3')
    custom_field_four = models.CharField(max_length=1000, verbose_name='custom_field_4')
    custom_field_five = models.CharField(max_length=1000, verbose_name='custom_field_5')
    custom_field_six = models.CharField(max_length=1000, verbose_name='custom_field_6')
    custom_field_seven = models.CharField(max_length=1000, verbose_name='custom_field_7')
    custom_field_eight = models.CharField(max_length=1000, verbose_name='custom_field_8')
    custom_field_9 = models.CharField(max_length=1000, verbose_name='custom_field_9')
    custom_field_10 = models.CharField(max_length=1000, verbose_name='custom_field_10')
    custom_field_11 = models.CharField(max_length=1000, verbose_name='custom_field_11')
    custom_field_12 = models.CharField(max_length=1000, verbose_name='custom_field_12')
    custom_field_13 = models.CharField(max_length=1000, verbose_name='custom_field_13')
    custom_field_14 = models.CharField(max_length=1000, verbose_name='custom_field_14')
    custom_field_15 = models.CharField(max_length=1000, verbose_name='custom_field_15')
    custom_field_16 = models.CharField(max_length=1000, verbose_name='custom_field_16')
    custom_field_17 = models.CharField(max_length=1000, verbose_name='custom_field_17')
    custom_field_18 = models.CharField(max_length=1000, verbose_name='custom_field_18')
    custom_field_19 = models.CharField(max_length=1000, verbose_name='custom_field_19')
    custom_field_20 = models.CharField(max_length=1000, verbose_name='custom_field_20')
    custom_field_21 = models.CharField(max_length=1000, verbose_name='custom_field_21')
    custom_field_22 = models.CharField(max_length=1000, verbose_name='custom_field_22')
    custom_field_23 = models.CharField(max_length=1000, verbose_name='custom_field_23')
    custom_field_24 = models.CharField(max_length=1000, verbose_name='custom_field_24')
    custom_field_25 = models.CharField(max_length=1000, verbose_name='custom_field_25')
    custom_field_26 = models.CharField(max_length=1000, verbose_name='custom_field_26')
    custom_field_27 = models.CharField(max_length=1000, verbose_name='custom_field_27')
    custom_field_28 = models.CharField(max_length=1000, verbose_name='custom_field_28')
    custom_field_29 = models.CharField(max_length=1000, verbose_name='custom_field_29')
    custom_field_30 = models.CharField(max_length=1000, verbose_name='custom_field_30')
    custom_field_31 = models.CharField(max_length=1000, verbose_name='custom_field_31')
    custom_field_32 = models.CharField(max_length=1000, verbose_name='custom_field_32')
    custom_field_33 = models.CharField(max_length=1000, verbose_name='custom_field_33')
    custom_field_34 = models.CharField(max_length=1000, verbose_name='custom_field_34')
    custom_field_35 = models.CharField(max_length=1000, verbose_name='custom_field_35')
    custom_field_36 = models.CharField(max_length=1000, verbose_name='custom_field_36')
    custom_field_37 = models.CharField(max_length=1000, verbose_name='custom_field_37')
    custom_field_38 = models.CharField(max_length=1000, verbose_name='custom_field_38')
    custom_field_39 = models.CharField(max_length=1000, verbose_name='custom_field_39')
    custom_field_40 = models.CharField(max_length=1000, verbose_name='custom_field_40')


    class Meta:
        db_table = 'tapd_launch_forms'
        verbose_name = 'TAPD发布评审表'
