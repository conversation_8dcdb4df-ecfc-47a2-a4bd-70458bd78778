from django.urls import path, include

from test_report.tapd import views
from rest_framework.routers import DefaultRouter

router = DefaultRouter()

router.register(r'get_iteration_from_tapd', views.TAPDIterations, basename="get_iteration_from_tapd")
router.register(r'get_bug_from_tapd', views.TAPDBug, basename="get_bug_from_tapd")
router.register(r'get_stories_from_tapd', views.TAPDStories, basename="get_stories_from_tapd")
router.register(r'get_launch_forms_status', views.TAPDLaunchFormsApi, basename="get_launch_forms_status")


urlpatterns = [
    path("", include(router.urls))
]
