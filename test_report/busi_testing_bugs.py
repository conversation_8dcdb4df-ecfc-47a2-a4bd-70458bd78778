# Create your views here

from rest_framework import status
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from mantis import settings
from test_report.report_data_etl import ReportDataETL
from test_report.tapd.tapd_bugs import TAPDBugsByStartTime
from test_report.tapd.tapd_iterations import TAPDIterations
from test_report.tapd.tapd_stories import TAPDStories


class BusiTestingBugs(ViewSet):
    """
    测试报告接口
    """
    authentication_classes = []

    # get方法
    def list(self, request, *args, **kwargs):
        days = int(request.query_params.get('days'))
        tbb = TAPDBugsByStartTime()
        iteration_list = tbb._get_bugs_iteration_list(days)

        ts = TAPDStories(iteration_list)
        result = ts._get_stories_from_tapd()
        if not result:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict('获取TAPD需求失败！'))

        tt = TAPDIterations(iteration_list)
        result = tt._get_iterations_from_tapd()
        if not result:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict('获取TAPD迭代失败！'))

        for iteration_id in iteration_list:
            rd = ReportDataETL(iteration_id)
            result, msg = rd._report_data_etl()
        if result:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.success_dict('缺陷相关数据处理成功！'))
        else:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict('缺陷相关数据处理是吧，原因为：{}'.format(msg)))
