# Test Report 模块项目说明文档

## 项目概述

`test_report` 模块是 Mantis 系统中的测试报告管理核心模块，负责生成、管理和展示各类测试报告，包括软件质量报告、测试流程报告、业务测试报告和测试集报告等。该模块与 TAPD、measurement 和 tapd_gateway 模块深度集成，提供完整的测试报告解决方案。

### 核心功能

- **软件质量报告生成**：基于 TAPD 数据生成软件质量分析报告
- **测试流程报告**：自动化测试流程执行结果报告生成和展示
- **业务测试报告**：业务测试相关的缺陷和项目报告
- **测试集报告**：测试集执行结果的统计和展示
- **TAPD 数据同步**：与 TAPD 系统进行数据同步和处理
- **报告数据 ETL**：测试数据的提取、转换和加载

## 技术架构

### 架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Test Report 模块架构                      │
├─────────────────────────────────────────────────────────────┤
│  API 层 (Views)                                             │
│  ├── ReportInfoView          # 报告信息视图                  │
│  ├── TestFlowReportView      # 测试流程报告视图              │
│  ├── SoftwareQualityReport   # 软件质量报告                  │
│  ├── BusiTestingBugs         # 业务测试缺陷                  │
│  └── TestsetReportInfoView   # 测试集报告视图                │
├─────────────────────────────────────────────────────────────┤
│  服务层 (Service)                                           │
│  ├── ReportDataETL           # 报告数据 ETL 服务             │
│  ├── TAPDBugs               # TAPD 缺陷处理服务              │
│  ├── TAPDStories            # TAPD 需求处理服务              │
│  ├── TAPDTasks              # TAPD 任务处理服务              │
│  └── TestFlowReportTemplate  # 测试流程报告模板              │
├─────────────────────────────────────────────────────────────┤
│  数据访问层 (DAO)                                           │
│  ├── software_quality_report_dao  # 软件质量报告 DAO        │
│  ├── test_flow_report_dao         # 测试流程报告 DAO        │
│  └── test_report_dao              # 测试报告通用 DAO        │
├─────────────────────────────────────────────────────────────┤
│  数据模型层 (Models)                                        │
│  ├── SoftwareQualityReportModel   # 软件质量报告模型        │
│  ├── BusiTestingProjectModel      # 业务测试项目模型        │
│  ├── TestFlowTestReportLibInfo    # 测试流程报告库信息      │
│  └── TestsetReportInfo            # 测试集报告信息          │
└─────────────────────────────────────────────────────────────┘
```

### 核心模块

#### 1. API 接口层

**主要视图类：**

- `ReportInfoView`：提供报告信息查询和聚合功能
- `TestFlowReportView`：测试流程报告的生成和管理
- `SoftwareQualityReport`：软件质量报告的创建和查询
- `BusiTestingBugs`：业务测试缺陷数据处理
- `TestsetReportInfoView`：测试集报告信息的创建和保存

#### 2. 服务层

**核心服务类：**

- `ReportDataETL`：报告数据的提取、转换和加载处理
- `TAPDBugs`：TAPD 缺陷数据的同步和处理
- `TAPDStories`：TAPD 需求数据的同步和处理
- `TAPDTasks`：TAPD 任务数据的同步和处理

#### 3. 数据访问层

**主要 DAO 类：**

- `software_quality_report_dao`：软件质量报告相关数据查询
- `test_flow_report_dao`：测试流程报告数据查询
- `test_report_dao`：通用测试报告数据访问

#### 4. 数据模型层

**核心模型：**

- `SoftwareQualityReportModel`：软件质量报告数据模型
- `BusiTestingProjectModel`：业务测试项目数据模型
- `TestFlowTestReportLibInfo`：测试流程报告库信息模型
- `TestsetReportInfo`：测试集报告信息模型

## 数据流程

### 报告生成流程

```mermaid
flowchart TD
    A[TAPD 系统] --> B[tapd_gateway 模块]
    B --> C[test_report 数据同步]
    C --> D[ReportDataETL 处理]
    D --> E[报告数据生成]
    E --> F[HTML 报告生成]
    F --> G[Nginx 服务器部署]
    G --> H[报告 URL 返回]
    
    I[measurement 模块] --> J[测试流程数据]
    J --> K[TestFlowReportView]
    K --> L[测试流程报告生成]
    L --> F
```

### 与其他模块的集成关系

#### 与 tapd_gateway 模块的集成

- **数据依赖**：依赖 `tapd_gateway` 提供的 TAPD 数据同步服务
- **模型引用**：使用 `TapdEntryBug`、`TapdEntryStory`、`TapdEntryTask` 等模型
- **服务调用**：调用 `TapdEntityBugSer`、`TapdEntityStorySer` 等服务

#### 与 measurement 模块的集成

- **数据共享**：使用 `TestFlowRunRecord`、`TestFlowAppDeployInfo` 等模型
- **API 调用**：调用 `BizFlowRunResultView` 获取测试流程结果
- **DAO 共享**：使用 `person_quality_dashborad_dao` 获取测试数据

#### 与 dev_effective 模块的集成

- **服务依赖**：使用 `BizBugSer`、`BizStorySer`、`BizTaskSer` 等业务服务
- **模型引用**：使用 `DevEfficTestProject` 等模型

#### 与 utest 模块的集成

**数据流向**:
```
utest (单元测试分析) → test_report (综合报告生成)
```

**集成点**:
- **测试数据共享**: 使用utest分析的单元测试数据生成综合报告
- **质量指标整合**: 将单元测试质量指标纳入软件质量报告
- **报告生成协作**: 结合单元测试结果生成完整的测试报告

**集成示例**:
```python
class SoftwareQualityReport(ViewSet):
    def create(self, request):
        iteration_id = request.data.get('iteration_id')
        
        # 获取utest模块的单元测试分析结果
        utest_results = self.get_utest_analysis_results(iteration_id)
        
        # 结合其他测试数据生成综合报告
        report_data = {
            'unit_test_coverage': utest_results.get('coverage_rate'),
            'unit_test_pass_rate': utest_results.get('pass_rate'),
            'unit_test_case_count': utest_results.get('case_count'),
            # 其他测试数据
        }
        
        # 生成软件质量报告
        return self.generate_quality_report(report_data)
```

#### 与 task_mgt 模块的集成

**任务调用关系**:
```
test_report (报告生成请求) → task_mgt (任务执行) → qa_scripts (报告生成)
```

**集成场景**:
- **报告生成任务**: 通过task_mgt调用qa_scripts生成测试报告
- **批量报告处理**: 大批量测试报告的异步生成
- **定时报告任务**: 定期生成测试报告的定时任务

**任务配置**:
```python
# task_mgt配置中的test_report相关任务
qa_scripts_config = {
    'create_test_report': {
        'description': '创建业务测试报告',
        'script_path': '/data/app/mantis/qa_scripts/job/router/urls.py',
        'log_dir': '/data/logs/mantis/test_report/'
    }
}
```

**调用示例**:
```python
from task_mgt.external_service import ExternalService

class TestFlowReportView(ViewSet):
    def create(self, request):
        # 创建测试报告生成任务
        service = ExternalService(
            srv_name='create_test_report',
            operator=request.user.username,
            params={
                'report_type': 'test_flow',
                'iteration_id': request.data.get('iteration_id')
            }
        )
        
        # 执行异步报告生成任务
        result = service.call_local_service()
        return Response(result)
```

#### 与 user 模块的集成

**认证依赖**:
```
user (认证服务) → test_report (报告管理)
```

**集成方式**:
- **API认证**: 所有API接口都需要用户认证
- **权限控制**: 基于用户角色的报告访问权限
- **操作审计**: 记录用户的报告操作历史

**使用示例**:
```python
class SoftwareQualityReport(ViewSet):
    authentication_classes = [JWTAuthentication, LoginAuthentication]
    permission_classes = [IsAuthenticated]
    
    def list(self, request):
        # 用户认证检查
        if not request.user.is_authenticated:
            return Response({'error': '需要登录'}, status=401)
        
        # 根据用户权限过滤报告
        user_role = getattr(request.user, 'role', 'user')
        if user_role == 'admin':
            # 管理员可以查看所有报告
            reports = self.get_all_reports()
        else:
            # 普通用户只能查看自己相关的报告
            reports = self.get_user_reports(request.user)
        
        return Response(reports)
```

#### 与 quality 模块的集成

**历史集成关系**:
```
quality (已废弃) → test_report (功能整合)
```

**功能整合**:
- **缺陷报告**: 将quality模块的缺陷相关功能整合到test_report
- **质量度量**: 统一的质量度量和报告生成
- **API兼容**: 保持与原quality模块API的兼容性

**迁移说明**:
- quality模块的报告功能已迁移到test_report
- 建议使用test_report的综合报告接口
- 逐步迁移客户端调用到新的API接口

## API 接口

### 主要 API 端点

| 端点 | 方法 | 功能描述 |
|------|------|----------|
| `/test_report/report_info/` | GET | 获取聚合报告信息 |
| `/test_report/testset_report/` | GET/POST | 测试集报告管理 |
| `/test_report/software_quality_report/` | POST | 创建软件质量报告 |
| `/test_report/software_quality_report_query/` | GET | 查询软件质量报告 |
| `/test_report/busi_testing_bugs/` | GET/POST | 业务测试缺陷信息 |
| `/test_report/iteration_quality_report_status/` | GET | 迭代质量报告状态 |
| `/test_report/test_flow_report/` | GET | 测试流程报告 |

### API 使用示例

#### 1. 创建软件质量报告

```python
# POST /test_report/software_quality_report/
{
    "iteration_id": "1001234",
    "workspace_id": "12345"
}
```

#### 2. 获取测试流程报告

```python
# GET /test_report/test_flow_report/
# 自动获取最近24小时需要创建报告的测试流程
```

#### 3. 保存测试集报告信息

```python
# POST /test_report/testset_report/create/
[
    {
        "batch_no": "20241201001",
        "iteration_id": "1001234",
        "module_name": "用户模块",
        "test_set_name": "用户登录测试集",
        "test_set_id": "TS001",
        "test_set_report_url": "http://example.com/report.html",
        "script_pass_percent": 95.5
    }
]
```

## 配置说明

### 主要配置项

在 `mantis/settings.ini` 中的相关配置：

```ini
[TEST_REPORT]
workspace_id = 12345
test_report_dir = /tmp/test_reports

[NGINX_LIB_REPO]
ip = *************
username = nginx_user
password = nginx_pass
root_path = /var/www/html
test_report_dir = test_reports
test_flow_dir = test_flow
base_url = http://*************
```

### 环境变量

- `TEST_REPORT_DIR`：测试报告本地缓存目录
- `NGINX_SERVER`：Nginx 服务器配置信息
- `TAPD_WORKSPACE_ID`：TAPD 工作空间 ID

## 部署指南

### 1. 环境准备

```bash
# 安装依赖
pip install django djangorestframework jinja2

# 创建报告目录
mkdir -p /tmp/test_reports/test_flow
```

### 2. 数据库迁移

```bash
# 执行数据库迁移
python manage.py makemigrations test_report
python manage.py migrate
```

### 3. 配置 Nginx 服务器

```nginx
server {
    listen 80;
    server_name test-reports.example.com;
    
    location /test_reports/ {
        root /var/www/html;
        index index.html;
    }
}
```

### 4. 启动服务

```bash
# 启动 Django 服务
python manage.py runserver 0.0.0.0:8000
```

## 监控和日志

### 日志配置

```python
# settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'test_report_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/mantis/test_report.log',
        },
    },
    'loggers': {
        'test_report': {
            'handlers': ['test_report_file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### 关键监控指标

- **报告生成成功率**：监控报告生成的成功率
- **TAPD 数据同步状态**：监控与 TAPD 的数据同步情况
- **报告访问量**：统计报告的访问情况
- **系统响应时间**：监控 API 响应时间

## 故障排查

### 常见问题

#### 1. 报告生成失败

**问题现象**：测试流程报告生成失败

**排查步骤**：
```bash
# 检查日志
tail -f /var/log/mantis/test_report.log

# 检查 Nginx 服务器连接
ping nginx_server_ip

# 检查报告目录权限
ls -la /tmp/test_reports/
```

#### 2. TAPD 数据同步异常

**问题现象**：TAPD 数据无法正常同步

**排查步骤**：
```bash
# 检查 TAPD 配置
grep -r "TAPD" mantis/settings.ini

# 检查网络连接
curl -I https://api.tapd.cn

# 检查数据库连接
python manage.py dbshell
```

#### 3. 报告模板渲染错误

**问题现象**：HTML 报告模板渲染失败

**解决方案**：
- 检查 Jinja2 模板语法
- 验证数据格式是否正确
- 检查模板文件路径

## 扩展开发

### 添加新的报告类型

1. **创建新的视图类**：

```python
class CustomReportView(ViewSet):
    def list(self, request):
        # 实现自定义报告逻辑
        pass
```

2. **添加 URL 路由**：

```python
# urls.py
router.register(r'custom_report', CustomReportView, basename='custom_report')
```

3. **创建数据模型**：

```python
class CustomReportModel(models.Model):
    name = models.CharField(max_length=100)
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
```

### 集成新的数据源

1. **创建数据同步服务**：

```python
class NewDataSourceSync:
    def sync_data(self):
        # 实现数据同步逻辑
        pass
```

2. **添加到 ETL 流程**：

```python
class ReportDataETL:
    def _sync_new_data_source(self):
        sync_service = NewDataSourceSync()
        sync_service.sync_data()
```

## 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0.0 | 2024-01-01 | 初始版本，基础报告功能 |
| v1.1.0 | 2024-03-01 | 添加测试流程报告功能 |
| v1.2.0 | 2024-06-01 | 优化 TAPD 数据同步 |
| v1.3.0 | 2024-09-01 | 增加业务测试报告 |
| v1.4.0 | 2024-12-01 | 完善错误处理和日志 |

## 模块集成关系

### 与 qa_scripts 模块的集成

`test_report` 模块与 `qa_scripts` 模块存在密切的协作关系：

#### 数据流向
```
test_report (数据源) → qa_scripts (报告生成)
     ↓                        ↓
  TAPD数据同步              软件质量报告
  测试执行数据              可视化图表
  缺陷管理数据              HTML报告
```

#### 共享数据和模型
- **缺陷数据**: `test_report` 通过 TAPD 同步的缺陷数据被 `qa_scripts` 用于生成缺陷分析报告
- **项目数据**: `dev_effic_test_project` 表的数据为 `qa_scripts` 提供项目基础信息
- **测试数据**: 测试执行结果数据用于质量分析和趋势统计

#### 业务协作
1. **数据准备阶段**: `test_report` 负责从 TAPD 同步最新的项目数据、缺陷数据、任务数据
2. **报告生成阶段**: `qa_scripts` 基于 `test_report` 提供的数据生成软件质量报告
3. **报告展示阶段**: 两个模块共同使用 Nginx 服务器进行报告文件的存储和访问

#### 技术集成点
- **数据库共享**: 两个模块使用相同的数据库连接和数据表
- **配置共享**: 共享 TAPD 配置、Nginx 服务器配置等
- **工具链集成**: 都使用 PyECharts 进行图表生成，使用相同的 HTML 模板技术

## 联系信息

- **开发团队**：Mantis 开发团队
- **技术支持**：<EMAIL>
- **文档维护**：<EMAIL>

---

*本文档最后更新时间：2024年12月*