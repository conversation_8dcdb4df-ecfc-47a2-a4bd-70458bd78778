import json

import traceback
from enum import Enum

from django.db.models import Max
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.viewsets import ViewSet
from rest_framework.response import Response

from code_quality.dao.model import CodeQualityScanLogService
from mantis.settings import ApiResult, logger
from test_report.models import SoftwareQualityReportModel
from test_report.dao.software_quality_report_dao import get_tapd_iteration_id
from test_report.testset.model.testset_report_info import TestsetReportInfo
import inspect
import sys


class ReportInfoView(ViewSet):
    authentication_classes = []

    @action(methods=['post'], detail=False, url_path='get_agg_report', authentication_classes=[])
    def get_agg_report(self, request):
        try:
            iteration_id = request.data.get('iteration_id', None)
            if not iteration_id:
                return Response(ApiResult.failed_dict(msg="iteration_id不能为空"))
            chain = TestReportChain()
            # 获取当前目录下的所有TestExecutor的实现类, 添加到责任链中
            for name, obj in inspect.getmembers(sys.modules[__name__]):
                if inspect.isclass(obj) and issubclass(obj, ReportExecutor) and obj != ReportExecutor:
                    reportExecutor = obj(iteration_id=iteration_id)
                    if reportExecutor.report_type:
                        chain.add_executor(reportExecutor)
            report_list: list[ReportInfoResult] = chain.execute_test()
            report_list_json = [f.__dict__ for f in report_list]
            data = ApiResult.success_dict(
                data=report_list_json, msg="查询报告成功")
            data['code'] = 200
            return Response(data=data)
        except Exception as ex:
            traceback.print_exc()
            data = ApiResult.success_dict(msg=str(ex))
            data['code'] = 500
            return Response(data=data)

    @action(methods=['post'], detail=False, url_path='get_sq_report_url', authentication_classes=[])
    def get_sq_report_url(self, request, *args, **kwargs):
        iteration_id = request.data.get('iteration_id', None)

        tapd_iteration_id_list = get_tapd_iteration_id(iteration_id)

        if not tapd_iteration_id_list:
            return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            data=ApiResult.failed_dict(
                                '没找到迭代{}对应的质量报告！请联系相关测试同学或者平台'.format(iteration_id)))

        lastReport = SoftwareQualityReportModel.objects.filter(iteration_id__in=tapd_iteration_id_list).order_by(
            '-create_time'). \
            last()
        if lastReport:
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.success_dict(data=lastReport.report_url))
        else:
            return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            data=ApiResult.failed_dict('未查到测试报告!'))


class ReportType(Enum):
    CCN = ('ccn', 'CCN报告')
    P3C = ('p3c', 'P3C报告')
    DUPLICATED = ('DUPLICATED', '重复行报告')
    CODE_SMELL = ('CODE_SMELL', 'BUG报告')
    BUG = ('BUG', 'BUG报告')
    VULNERABILITY = ('VULNERABILITY', '安全漏洞报告')
    UNIT_TEST = ('UNIT_TEST', '单元测试报告')
    TEST_SET = ('testset', '测试集报告')

    def __init__(self, report_type, report_tite):
        self.report_type = report_type
        self.report_tite = report_tite


class ReportInfoResult:
    def __init__(self, **kwargs):
        self.report_type = None
        self.module_name = None
        # 迭代ID
        self.iteration_id = None
        # 状态
        self.status = None
        # 通过率
        self.pass_rate = None
        # 报告名称
        self.report_title = None
        # 报告地址
        self.report_url = None
        # 报告扩展描述
        self.report_ext_desc = None

        for key, value in kwargs.items():
            setattr(self, key, value)

    def __str__(self):
        attribute_list = [attr for attr in dir(self) if not attr.startswith('_')]
        attribute_str_list = [f"{attr}: {getattr(self, attr)!r}" for attr in attribute_list]
        return ', '.join(attribute_str_list)


class ReportExecutor:
    report_type = None
    report_title = None
    iteration_id = None

    def __init__(self, iteration_id, report_type=None, report_title=None):
        self.iteration_id = iteration_id
        self.report_type = report_type
        self.report_title = report_title

    def execute_test(self) -> list[ReportInfoResult]:
        raise NotImplementedError

    def generate_report_details(self):
        raise NotImplementedError


class CodeQualityReportExecutor(ReportExecutor):

    def __init__(self, iteration_id, report_type=None, report_title=None):
        super().__init__(iteration_id, report_type, report_title)

    def execute_test(self) -> list[ReportInfoResult]:
        latest_app_type_scan_time = CodeQualityScanLogService.objects.filter(iteration_id=self.iteration_id,
                                                                             scan_type=self.report_type) \
            .values('app_name') \
            .annotate(latest_scan_time=Max('scan_time')) \
            .order_by('-latest_scan_time')
        results: list[ReportInfoResult] = []
        for item in latest_app_type_scan_time:
            latest_log = CodeQualityScanLogService.objects.filter(iteration_id=self.iteration_id,
                                                                  scan_type=self.report_type,
                                                                  app_name=item.get('app_name'),
                                                                  scan_time=item.get('latest_scan_time')).first()
            url_info = latest_log.scan_result.split(',')
            report_ext_desc_arr = [info.strip() for info in url_info if info.startswith('本次报告url:')]
            report_ext_desc = ''
            report_url = ''
            if report_ext_desc_arr:
                report_ext_desc = report_ext_desc_arr[0]
                report_url = report_ext_desc.split('本次报告url:')[1]
                report_ext_desc = latest_log.scan_result.replace(report_ext_desc, '')
            scan_status, pass_rate = self.transform_status(latest_log.scan_status)
            results.append(ReportInfoResult(
                report_type=self.report_type,
                module_name=latest_log.app_name,
                iteration_id=self.iteration_id,
                status=scan_status,
                pass_rate=pass_rate,
                report_title=self.report_title,
                report_url=report_url,
                report_ext_desc=report_ext_desc
            ))

        return results

    @staticmethod
    def transform_status(scan_status):
        logger.info(f'scan_status:{scan_status}')
        logger.info(f'type:{type(scan_status)}')
        if scan_status == '1':
            return '通过', '100%'
        else:
            return '不通过', '0%'


class CCNReportExecutor(CodeQualityReportExecutor):

    def __init__(self, iteration_id, report_type=ReportType.CCN.report_type, report_title=ReportType.CCN.report_tite):
        super().__init__(iteration_id, report_type, report_title)


class P3CReportExecutor(CodeQualityReportExecutor):

    def __init__(self, iteration_id, report_type=ReportType.P3C.report_type, report_title=ReportType.P3C.report_tite):
        super().__init__(iteration_id, report_type, report_title)


class VULNERABILITYReportExecutor(CodeQualityReportExecutor):

    def __init__(self, iteration_id, report_type=ReportType.VULNERABILITY.report_type,
                 report_title=ReportType.VULNERABILITY.report_tite):
        super().__init__(iteration_id, report_type, report_title)


class BUGReportExecutor(CodeQualityReportExecutor):

    def __init__(self, iteration_id, report_type=ReportType.BUG.report_type, report_title=ReportType.BUG.report_tite):
        super().__init__(iteration_id, report_type, report_title)


class CODESMELLReportExecutor(CodeQualityReportExecutor):

    def __init__(self, iteration_id, report_type=ReportType.CODE_SMELL.report_type,
                 report_title=ReportType.CODE_SMELL.report_tite):
        super().__init__(iteration_id, report_type, report_title)


class DUPLICATEDReportExecutor(CodeQualityReportExecutor):

    def __init__(self, iteration_id, report_type=ReportType.DUPLICATED.report_type,
                 report_title=ReportType.DUPLICATED.report_tite):
        super().__init__(iteration_id, report_type, report_title)


class UnitReportExecutor(CodeQualityReportExecutor):

    def __init__(self, iteration_id, report_type=ReportType.UNIT_TEST.report_type,
                 report_title=ReportType.UNIT_TEST.report_tite):
        super().__init__(iteration_id, report_type, report_title)


class TESTSETReportExecutor(ReportExecutor):

    def __init__(self, iteration_id, report_type=ReportType.TEST_SET.report_type,
                 report_title=ReportType.TEST_SET.report_tite):
        super().__init__(iteration_id, report_type, report_title)

    def execute_test(self):
        latest_test_set_report_infos = TestsetReportInfo.objects.filter(iteration_id=self.iteration_id) \
            .filter(iteration_id=self.iteration_id) \
            .values('module_name') \
            .annotate(latest_scan_time=Max('create_time')) \
            .order_by('-latest_scan_time')

        results: list[ReportInfoResult] = []
        for item in latest_test_set_report_infos:
            latest_test_set_report = TestsetReportInfo.objects.filter(iteration_id=self.iteration_id,
                                                                      module_name=item.get('module_name'),
                                                                      create_time=item.get('latest_scan_time')).first()
            scan_status = self.transform_status(latest_test_set_report.script_pass_percent)
            results.append(ReportInfoResult(
                report_type=self.report_type,
                module_name=latest_test_set_report.module_name,
                iteration_id=self.iteration_id,
                status=scan_status,
                pass_rate=latest_test_set_report.script_pass_percent,
                report_title=latest_test_set_report.test_set_name,
                report_url=latest_test_set_report.test_set_report_url,
                report_ext_desc=None
            ))
        return results

    @staticmethod
    def transform_status(script_pass_percent):
        if script_pass_percent.split(".")[0] == '100' or script_pass_percent == '100%':
            return '通过'
        else:
            return '不通过'


class TestReportChain:
    def __init__(self):
        self.executors = []

    def add_executor(self, executor):
        self.executors.append(executor)

    def execute_test(self):
        results: list[ReportInfoResult] = []
        for executor in self.executors:
            result_list = executor.execute_test()
            logger.info('报告:{},数量:{}'.format(executor.report_type, len(result_list)))
            results.extend(result_list)
        return results
