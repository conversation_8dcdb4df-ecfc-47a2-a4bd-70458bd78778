from django.db import models


class SoftwareQualityReportModel(models.Model):

    workspace_id = models.BigIntegerField(verbose_name='tapd工程ID')
    iteration_id = models.BigIntegerField(verbose_name='迭代ID')
    report_url = models.CharField(verbose_name='报告地址', max_length=256)
    create_time = models.DateTimeField(verbose_name='创建时间')
    status = models.IntegerField(verbose_name='审核状态')
    launch_form_id = models.IntegerField(verbose_name='发布评审ID')

    class Meta:
        db_table = 'software_quality_report_info'
        verbose_name = '软件质量报告信息表'


class BusiTestingProjectModel(models.Model):

    extend_id = models.BigIntegerField(verbose_name='外部项目ID')
    name = models.CharField(verbose_name='迭代名称', max_length=100)
    start_date = models.DateField(verbose_name='开始日期')
    end_date = models.DateField(verbose_name='结束日期')
    status = models.CharField(verbose_name='迭代状态', max_length=10)
    creator = models.CharField(verbose_name='创建人', max_length=50)
    create_time = models.DateTimeField(verbose_name='创建时间')
    team = models.CharField(verbose_name='团队', max_length=10)
    complete_time = models.DateTimeField(verbose_name='完成时间')
    data_source = models.CharField(verbose_name='数据来源', max_length=10)
    update_time = models.DateTimeField(verbose_name='更新时间')

    class Meta:
        db_table = 'busi_testing_project'
        verbose_name = '业务测试项目表'


class BusiTestingScheduleModel(models.Model):
    __tablename__ = 'busi_testing_schedule'

    project_id = models.BigIntegerField(verbose_name='业务测试项目表ID')
    extend_id = models.BigIntegerField(verbose_name='外部计划ID')
    name = models.CharField(verbose_name='需求名称', max_length=100)
    owner = models.CharField(verbose_name='当前处理人', max_length=20)
    expected_start = models.DateField(verbose_name='预计开始日期')
    expected_end = models.DateField(verbose_name='预计结束日期')
    actual_start = models.DateField(verbose_name='实际开始日期')
    actual_end = models.DateField(verbose_name='实际结束日期')
    effort = models.CharField(verbose_name='预计工时', max_length=5)
    work_hours = models.CharField(verbose_name='实际工时', max_length=5)
    status = models.CharField(verbose_name='状态', max_length=5)
    creator = models.CharField(verbose_name='创建人', max_length=5)
    create_time = models.DateTimeField(verbose_name='创建时间')
    complete_time = models.DateTimeField(verbose_name='完成时间')
    team = models.CharField(verbose_name='团队', max_length=10)
    delay_days = models.CharField(verbose_name='延期天数', max_length=10)
    delay_description = models.TextField(verbose_name='延期说明')
    compatibility_description = models.CharField(verbose_name='兼容性测试描述', max_length=500)
    test_summary = models.TextField(verbose_name='测试总结')
    tp_iters = models.CharField(verbose_name='藏经阁迭代', max_length=200)
    tms_iters = models.CharField(verbose_name='达摩院服务端迭代', max_length=200)
    tms_h5_iters = models.CharField(verbose_name='达摩院h5迭代', max_length=200)
    fp_iters = models.CharField(verbose_name='爱码仕服务端迭代', max_length=200)
    fp_h5_app_iters = models.CharField(verbose_name='爱码仕h5&客户端迭代', max_length=200)
    crm_iters = models.CharField(verbose_name='六扇门迭代', max_length=200)
    data_source = models.CharField(verbose_name='数据来源', max_length=10)
    update_time = models.DateTimeField(verbose_name='更新时间')
    dev2test_date = models.CharField(verbose_name='开发提测日期', max_length=50)
    business_system = models.CharField(verbose_name='业务系统', max_length=50)

    class Meta:
        db_table = 'busi_testing_schedule'
        verbose_name = '业务测试计划表'


class BusiTestingBugsModel(models.Model):

    project_id = models.BigIntegerField(verbose_name='业务测试项目表ID')
    extend_id = models.BigIntegerField(verbose_name='外部缺陷ID')
    title = models.CharField(verbose_name='缺陷名称', max_length=200)
    severity = models.CharField(verbose_name='严重程度', max_length=10)
    status = models.CharField(verbose_name='缺陷状态', max_length=10)
    bug_type = models.CharField(verbose_name='缺陷类型', max_length=50)
    reporter = models.CharField(verbose_name='创建人', max_length=100)
    create_time = models.DateTimeField(verbose_name='创建时间')
    fixer = models.CharField(verbose_name='修复人', max_length=100)
    resolve_time = models.DateTimeField(verbose_name='修复时间')
    close_time = models.DateTimeField(verbose_name='关闭时间')
    flows = models.CharField(verbose_name='工作流', max_length=500)
    tp_app = models.CharField(verbose_name='藏经阁全应用', max_length=100)
    tms_app = models.CharField(verbose_name='达摩院服务端应用', max_length=100)
    fp_app = models.CharField(verbose_name='爱码仕服务端应用', max_length=100)
    crm_app = models.CharField(verbose_name='六扇门全应用', max_length=100)
    tms_h5 = models.CharField(verbose_name='达摩院H5应用', max_length=100)
    fp_h5_app = models.CharField(verbose_name='爱码仕H5&客户端应用', max_length=100)
    attribution_analysis = models.CharField(verbose_name='根因分析', max_length=100)
    data_source = models.CharField(verbose_name='数据来源', max_length=10)
    update_time = models.DateTimeField(verbose_name='更新时间')

    class Meta:
        db_table = 'busi_testing_bugs'
        verbose_name = '业务测试缺陷表'


class SpiderIterAppInfo(models.Model):

    module_name = models.CharField(verbose_name='应用名称', max_length=50)
    pipeline_id = models.CharField(verbose_name='迭代名称', max_length=100)

    class Meta:
        db_table = 'spider_iter_app_info'
        verbose_name = 'spider迭代应用关系表'


class TestFlowTestReportLibInfo(models.Model):
    run_batch_number = models.CharField(verbose_name='批次号', max_length=255)
    report_url = models.CharField(verbose_name='测试报告地址', max_length=500)
    update_time = models.DateTimeField(verbose_name='更新时间')
    create_time = models.DateTimeField(verbose_name='创建时间')
    create_user = models.CharField(verbose_name='创建人', max_length=100)
    update_user = models.CharField(verbose_name='更新人', max_length=100)

    class Meta:
        db_table = 'test_flow_test_report_lib_info'
        verbose_name = '编排线测试报告制品表'
