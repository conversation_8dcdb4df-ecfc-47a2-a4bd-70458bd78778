from dev_effective.model.models import DevEfficTestProject
from mantis.settings import logger
from test_report.tapd.tapd_bugs import TAPDBugs
from test_report.tapd.tapd_launch_forms import TAPDLaunchForms
from test_report.tapd.tapd_stories import TAPDStories
from test_report.tapd.tapd_tasks import TAPDTasks
from test_report.tapd.tapd_test_plans import TAPDTestPlans


class ReportDataETL:
    """
    报告数据转换工具类
    """

    def __init__(self, iteration_id):
        self.iteration_id = iteration_id
        self.project_id = ''

    def set_project_id(self, project_id):
        self.project_id = project_id

    def _report_data_etl(self, is_check=False):
        result, msg, project_id = self.__update_project_story_data(is_check=is_check)
        if result:
            self.set_project_id(project_id)
            result, msg = self.__update_test_report()
            if result:
                result, msg = self.__update_bugs()
                if result:
                    result, msg = self.__update_test_dev_submit()
                    if result:
                        result, msg = self.__update_tasks()
        return result, msg

    def __update_project_story_data(self, is_check=None):
        # 转换计划数据
        try:
            # 待讨论 这里为什么要更新story数据 ，外层已经更新过一次了 20240506 by fwm
            ts = TAPDStories([self.iteration_id])
            ts.update_project_story_info()

            obj = DevEfficTestProject.objects.filter(tapd_iteration_id=self.iteration_id).values('id',
                                                                                                 'name',
                                                                                                 'owner',
                                                                                                 'expected_start',
                                                                                                 'expected_end',
                                                                                                 'actual_start',
                                                                                                 'actual_end').first()
            if is_check:
                result, msg = self.__tapd_story_info_check(obj)

                if not result:
                    return result, msg, None

            return True, '故事数据转换成功', obj.get("id")
        except Exception as err:
            logger.error('故事数据转换失败'.format(self.iteration_id))
            logger.error(err)
            return False, err

    def __update_tasks(self):
        try:
            task = TAPDTasks([self.iteration_id], self.project_id)
            task.update_biz_task_info()
            return True, '任务数据转换成功'
        except Exception as err:
            logger.error('任务数据转换失败'.format(self.iteration_id))
            logger.error(err)
            return False, err

    def __update_bugs(self):
        # 转换缺陷数据
        try:
            tb = TAPDBugs([self.iteration_id], None, self.project_id)
            tb.update_biz_bug_data()
            return True, '缺陷数据转换成功'
        except Exception as err:
            logger.error('缺陷数据转换失败'.format(self.iteration_id))
            logger.error(err)
            return False, err

    def __update_test_report(self):
        logger.info('发布评审')
        tb = TAPDLaunchForms([self.iteration_id], self.project_id)
        tb.update_test_report_data()
        return True, '发布评审数据转换成功'

    def __update_test_dev_submit(self):
        logger.info('开发提测计划')
        tb = TAPDTestPlans([self.iteration_id], self.project_id)
        tb.update_test_plan_data()
        return True, '开发提测计划数据转换成功'

    def __tapd_story_info_check(self, story_info):
        result = True
        msg = 'TAPD测试需求中的【{}】字段不能为空，请填写完整后再尝试！'
        return_msg = ''
        if not story_info:
            return False, '需求不合规，请检查需求名称、处理人、预计开始时间、预计结束时间、实际开始时间、实际结束时间，或联系PA'
        if not story_info.get("name"):
            result = False
            return_msg = msg.format("需求名称")
        if not story_info.get("owner"):
            result = False
            return_msg = msg.format("需求处理人")
        if not story_info.get("expected_start"):
            result = False
            return_msg = msg.format("预计开始时间")
        if not story_info.get("expected_end"):
            result = False
            return_msg = msg.format("预计结束时间")
        if not story_info.get("actual_start"):
            result = False
            return_msg = msg.format("实际开始时间")
        if not story_info.get("actual_end"):
            result = False
            return_msg = msg.format("实际结束时间")
        return result, return_msg
