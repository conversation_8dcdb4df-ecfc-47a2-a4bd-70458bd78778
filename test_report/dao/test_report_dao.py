import datetime

from sqlalchemy.sql import text

from mantis.pool import instance_db_session, InstanceDbSession
from mantis.settings import TEST_REPORT
from test_report.test_report_model import SoftwareQualityReportModel, TapdIterationsModel, TapdStoriesModel, \
    BusiTestingScheduleModel, TapdBugModel, BusiTestingBugsModel


class SoftwareQualityReportDao:
    __query_test_report_sql = '''
            SELECT * FROM software_quality_report_info 
            where worksapce_id = '{}'
            ORDER BY id DESC
            '''

    __query_test_report_by_iteration_id_sql = '''
                                                SELECT * FROM software_quality_report_info 
                                                where worksapce_id = '{}' and iteration_id = '{}'
                                                ORDER BY id DESC
                                            '''
    __query_latest_report_info = '''
                                    SELECT * FROM software_quality_report_info 
                                    WHERE id = (SELECT MAX(id) FROM software_quality_report_info 
                                                WHERE workspace_id = '{}' 
                                                AND iteration_id = '{}')
                                '''

    def __init__(self, iteration_id=None, worksapce_id=None):
        self.__iteration_id = iteration_id
        self.__worksapce_id = worksapce_id if worksapce_id else TEST_REPORT['workspace_id']

    def query_current_info(self):
        db_session = instance_db_session()
        if self.__iteration_id:
            result = db_session.query(SoftwareQualityReportModel).from_statement(
                text(self.__query_test_report_by_iteration_id_sql.format(self.__worksapce_id))).all()
        else:
            result = db_session.query(SoftwareQualityReportModel).from_statement(
                text(self.__query_test_report_sql.format(self.__worksapce_id, self.__iteration_id))).all()
        db_session.close()
        return result

    def query_the_latest_report_info(self):
        db_session = instance_db_session()
        result = db_session.query(SoftwareQualityReportModel).from_statement(
            text(self.__query_latest_report_info.format(self.__worksapce_id, self.__iteration_id))).all()
        db_session.close()
        return result


class TapdIterationsDao:
    __query_tapd_iteration_sql = '''
                                select * from tapd_iterations
                                where workspace_id = '{}' 
                                AND id = '{}';
                                '''

    def __init__(self, iteration_id=None, worksapce_id=None):
        self.__iteration_id = iteration_id
        self.__worksapce_id = worksapce_id if worksapce_id else TEST_REPORT['workspace_id']

    def query_tapd_iteration_info(self):
        db_session = instance_db_session()
        result = db_session.query(TapdIterationsModel).from_statement(
            text(self.__query_tapd_iteration_sql.format(self.__worksapce_id, self.__iteration_id))).all()
        db_session.close()
        return result


class DevEfficTestProjectDao:

    def __init__(self, workspace_id=None, iteration_id=None):
        self.__workspace_id = workspace_id
        self.__iteration_id = iteration_id

    def get_project(self):
        query_sql = '''
                        SELECT * 
                        FROM dev_effic_test_project t
                        WHERE t.tapd_iteration_id = {} limit 1;
                    '''.format(self.__iteration_id)
        project_id = None
        with InstanceDbSession() as db:
            for project in db.execute(query_sql).fetchall():
                project_id = project.id
        return project_id


class DevEfficTestReportDao:

    def __init__(self, project_id):
        self.__project_id = project_id

    def get_report(self):
        query_sql = '''
                        SELECT * 
                        FROM dev_effic_test_report t
                        WHERE t.project_id = {} limit 1;
                    '''.format(self.__project_id)
        report_id = None
        with InstanceDbSession() as db:
            for report in db.execute(query_sql).fetchall():
                report_id = report.id
        return report_id


class TapdStoriesDao:
    __query_tapd_stories_sql = '''
                                select * from tapd_stories
                                where workspace_id = '{}' 
                                AND iteration_id = '{}';
                                '''

    def __init__(self, iteration_id=None, worksapce_id=None):
        self.__iteration_id = iteration_id
        self.__worksapce_id = worksapce_id if worksapce_id else TEST_REPORT['workspace_id']

    def query_tapd_story_info(self):
        db_session = instance_db_session()
        result = db_session.query(TapdStoriesModel).from_statement(
            text(self.__query_tapd_stories_sql.format(self.__worksapce_id, self.__iteration_id))).all()
        db_session.close()
        return result


class BusiTestingScheduleDao:

    def update_or_insert_schedule(self, tapd_stories_info_list, project_id):
        cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        try:
            db_session = instance_db_session()

            for tapd_stories_info in tapd_stories_info_list:

                result, msg = self.__tapd_story_info_check(tapd_stories_info)
                if not result:
                    return result, msg
                obj = db_session.query(BusiTestingScheduleModel).filter_by(project_id=project_id,
                                                                           extend_id=tapd_stories_info.id).first()
                if obj:
                    schedule_id = obj.id
                    date = {"name": tapd_stories_info.name, "owner": tapd_stories_info.owner,
                            "expected_start": tapd_stories_info.begin, "expected_end": tapd_stories_info.due,
                            "actual_start": tapd_stories_info.custom_field_10,
                            "actual_end": tapd_stories_info.custom_field_11,
                            "work_hours": tapd_stories_info.custom_field_six, "status": tapd_stories_info.status,
                            "creator": tapd_stories_info.creator, "create_time": tapd_stories_info.created,
                            "complete_time": tapd_stories_info.completed, "team": tapd_stories_info.custom_field_two,
                            "delay_days": tapd_stories_info.custom_field_four,
                            "delay_description": tapd_stories_info.custom_field_eight,
                            "test_summary": tapd_stories_info.custom_field_9, "data_source": "TAPD",
                            "update_time": cur_time}
                    db_session.query(BusiTestingScheduleModel).filter_by(id=schedule_id).update(date)
                    db_session.commit()
                else:
                    bts = BusiTestingScheduleModel(project_id=project_id, extend_id=tapd_stories_info.id,
                                                   name=tapd_stories_info.name, owner=tapd_stories_info.owner,
                                                   expected_start=tapd_stories_info.begin,
                                                   expected_end=tapd_stories_info.due,
                                                   actual_start=tapd_stories_info.custom_field_10 if tapd_stories_info.custom_field_10 else None,
                                                   actual_end=tapd_stories_info.custom_field_11 if tapd_stories_info.custom_field_10 else None,
                                                   work_hours=tapd_stories_info.custom_field_six,
                                                   status=tapd_stories_info.status,
                                                   creator=tapd_stories_info.creator,
                                                   create_time=tapd_stories_info.created,
                                                   complete_time=tapd_stories_info.completed,
                                                   team=tapd_stories_info.custom_field_two,
                                                   delay_days=tapd_stories_info.custom_field_four,
                                                   delay_description=tapd_stories_info.custom_field_eight,
                                                   test_summary=tapd_stories_info.custom_field_9, data_source="TAPD",
                                                   update_time=cur_time)
                    db_session.add(bts)
                    db_session.commit()
                return True, ''

        except Exception as err:
            raise (err)
        finally:
            db_session.close()

    def __tapd_story_info_check(self, story_info):
        result = True
        msg = '{}不能为空，请填写完整后再尝试！'
        return_msg = ''
        if not story_info.name:
            result = False
            return_msg = msg.format("需求名称")
        if not story_info.owner:
            result = False
            return_msg = msg.format("需求处理人")
        if not story_info.begin:
            result = False
            return_msg = msg.format("预计开始时间")
        if not story_info.due:
            result = False
            return_msg = msg.format("预计结束时间")
        if not story_info.custom_field_10:
            result = False
            return_msg = msg.format("实际开始时间")
        if not story_info.custom_field_11:
            result = False
            return_msg = msg.format("实际结束时间")
        if not story_info.custom_field_six:
            result = False
            return_msg = msg.format("工时")
        return result, return_msg


class TapdBugDao:
    __query_tapd_Bug_sql = '''
                            select * from tapd_bug
                            where workspace_id = '{}' 
                            AND iteration_id = '{}';
                            '''

    def __init__(self, iteration_id=None, worksapce_id=None):
        self.__iteration_id = iteration_id
        self.__worksapce_id = worksapce_id if worksapce_id else TEST_REPORT['workspace_id']

    def query_tapd_bug_info(self):
        db_session = instance_db_session()
        result = db_session.query(TapdBugModel).from_statement(
            text(self.__query_tapd_Bug_sql.format(self.__worksapce_id, self.__iteration_id))).all()
        db_session.close()
        return result


class BusiTestingBugsDao:
    def update_or_insert_bug(self, tapd_bug_list, project_id):
        cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        try:
            db_session = instance_db_session()

            for tapd_bug in tapd_bug_list:
                obj = db_session.query(BusiTestingBugsModel).filter_by(project_id=project_id,
                                                                       extend_id=tapd_bug.id).first()
                if obj:
                    bug_id = obj.id
                    date = {"title": tapd_bug.title, "severity": tapd_bug.severity,
                            "status": tapd_bug.status, "bug_type": tapd_bug.bugtype,
                            "reporter": tapd_bug.reporter,
                            "create_time": tapd_bug.created,
                            "fixer": tapd_bug.fixer, "resolve_time": tapd_bug.resolved,
                            "close_time": tapd_bug.closed, "flows": tapd_bug.flows,
                            "tp_iter_app": tapd_bug.custom_field_one, "tms_iter_app": tapd_bug.custom_field_two,
                            "fp_iter_app": tapd_bug.custom_field_three,
                            "crm_iter_app": tapd_bug.custom_field_four,
                            "h5_iter_app": tapd_bug.custom_field_five, "attribution_analysis": tapd_bug.custom_field_7,
                            "data_source": "TAPD",
                            "update_time": cur_time}
                    db_session.query(BusiTestingBugsModel).filter_by(id=bug_id).update(date)
                    db_session.commit()
                else:
                    btb = BusiTestingBugsModel(project_id=project_id, extend_id=tapd_bug.id,
                                               title=tapd_bug.title, severity=tapd_bug.severity,
                                               status=tapd_bug.status, bug_type=tapd_bug.bugtype,
                                               reporter=tapd_bug.reporter,
                                               create_time=tapd_bug.created,
                                               fixer=tapd_bug.fixer, resolve_time=tapd_bug.resolved,
                                               close_time=tapd_bug.closed, flows=tapd_bug.flows,
                                               tp_iter_app=tapd_bug.custom_field_one,
                                               tms_iter_app=tapd_bug.custom_field_two,
                                               fp_iter_app=tapd_bug.custom_field_three,
                                               crm_iter_app=tapd_bug.custom_field_four,
                                               h5_iter_app=tapd_bug.custom_field_five,
                                               attribution_analysis=tapd_bug.custom_field_7, data_source="TAPD",
                                               update_time=cur_time)
                    db_session.add(btb)
                    db_session.commit()
        except Exception as err:
            raise (err)
        finally:
            db_session.close()
