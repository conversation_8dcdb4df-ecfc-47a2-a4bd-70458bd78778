# Create your views here

from django.db import connection
from mantis.settings import logger


def get_tapd_iteration_id(iteration_id):
    sql = '''
            select distinct ts.tapd_iteration_id, ts.tp_iters FROM dev_effic_test_project ts
            WHERE CONCAT('|', ts.tp_iters, '|' ) LIKE '%|{iteration_id}|%' 
            OR CONCAT('|', ts.tms_iters, '|' ) LIKE '%|{iteration_id}|%' 
            OR CONCAT('|', ts.tms_h5_iters, '|' ) LIKE '%|{iteration_id}|%' 
            OR CONCAT('|', ts.fp_iters, '|' ) LIKE '%|{iteration_id}|%'
            OR CONCAT('|', ts.fp_h5_app_iters, '|' ) LIKE '%|{iteration_id}|%' 
            OR CONCAT('|', ts.crm_iters, '|' ) LIKE '%|{iteration_id}|%';
          '''.format(iteration_id=iteration_id)

    logger.info(sql)

    cursor = connection.cursor()
    cursor.execute(sql)

    tapd_iteration_id_list = []
    for row in cursor.fetchall():
        tapd_iteration_id_list.append(row[0])

    return tapd_iteration_id_list
