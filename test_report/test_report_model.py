from sqlalchemy import Column, Integer, String, DateTime, Date, Text
from mantis.pool import Base


class SoftwareQualityReportModel(Base, object):
    __tablename__ = 'software_quality_report_info'

    id = Column(Integer(), primary_key=True, nullable=False)
    workspace_id = Column(Integer())
    iteration_id = Column(Integer())
    report_url = Column(String(256))
    create_time = Column(DateTime())
    status = Column(Integer())


class TapdIterationsModel(Base, object):
    __tablename__ = 'tapd_iterations'

    id = Column(Integer(), primary_key=True, nullable=False)
    name = Column(String(100))
    startdate = Column(Date())
    enddate = Column(Date())
    status = Column(String(10))
    creator = Column(String(50))
    created = Column(DateTime())
    custom_field_1 = Column(Text)
    completed = Column(DateTime())


class BusiTestingProjectModel(Base, object):
    __tablename__ = 'busi_testing_project'

    id = Column(Integer(), primary_key=True, nullable=False)
    extend_id = Column(Integer())
    name = Column(String(100))
    start_date = Column(Date())
    end_date = Column(Date())
    status = Column(String(10))
    creator = Column(String(50))
    create_time = Column(DateTime())
    team = Column(String(10))
    complete_time = Column(DateTime())
    data_source = Column(String(10))
    update_time = Column(DateTime())


class TapdStoriesModel(Base, object):
    __tablename__ = 'tapd_stories'

    id = Column(Integer(), primary_key=True, nullable=False)
    name = Column(String(100))
    owner = Column(String(20))
    begin = Column(Date())
    due = Column(Date())
    custom_field_10 = Column(Text)
    custom_field_11 = Column(Text)
    custom_field_six = Column(Text)
    status = Column(String(20))
    creator = Column(String(20))
    created = Column(DateTime())
    completed = Column(DateTime())
    custom_field_two = Column(Text)
    custom_field_four = Column(Text)
    custom_field_eight = Column(Text)
    custom_field_9 = Column(Text)


class BusiTestingScheduleModel(Base, object):
    __tablename__ = 'busi_testing_schedule'

    id = Column(Integer(), primary_key=True, nullable=False)
    project_id = Column(Integer())
    extend_id = Column(Integer())
    name = Column(String(100))
    owner = Column(String(20))
    expected_start = Column(Date())
    expected_end = Column(Date())
    actual_start = Column(Date())
    actual_end = Column(Date())
    work_hours = Column(String(5))
    status = Column(String(20))
    creator = Column(String(20))
    create_time = Column(DateTime())
    complete_time = Column(DateTime())
    team = Column(String(10))
    delay_days = Column(String(10))
    delay_description = Column(Text)
    test_summary = Column(Text)
    data_source = Column(String(10))
    update_time = Column(DateTime())
    dev2test_date = Column(String(50))


class TapdBugModel(Base, object):
    __tablename__ = 'tapd_bug'

    id = Column(Integer(), primary_key=True, nullable=False)
    title = Column(String(200))
    severity = Column(String(10))
    status = Column(String(10))
    bugtype = Column(String(50))
    reporter = Column(String(100))
    created = Column(DateTime())
    fixer = Column(String(50))
    resolved = Column(DateTime())
    closed = Column(DateTime())
    flows = Column(String(500))
    custom_field_one = Column(Text)
    custom_field_two = Column(Text)
    custom_field_three = Column(Text)
    custom_field_four = Column(Text)
    custom_field_five = Column(Text)
    custom_field_7 = Column(Text)


class BusiTestingBugsModel(Base, object):
    __tablename__ = 'busi_testing_bugs'

    id = Column(Integer(), primary_key=True, nullable=False)
    project_id = Column(Integer())
    extend_id = Column(Integer())
    title = Column(String(200))
    severity = Column(String(10))
    status = Column(String(10))
    bug_type = Column(String(50))
    reporter = Column(String(100))
    create_time = Column(DateTime())
    fixer = Column(String(50))
    resolve_time = Column(DateTime())
    close_time = Column(DateTime())
    flows = Column(String(500))
    tp_iter_app = Column(String(100))
    tms_iter_app = Column(String(100))
    fp_iter_app = Column(String(100))
    crm_iter_app = Column(String(100))
    h5_iter_app = Column(String(100))
    attribution_analysis = Column(String(100))
    data_source = Column(String(10))
    update_time = Column(DateTime())