# Create your views here
import datetime
from time import sleep

from django.http import HttpResponseRedirect
from rest_framework import status
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from dev_effective.model.models import DevEfficTestReport, DevEfficTestProject, DevEfficTestReportLibInfoDjango
from dev_effective.service.biz_launch_form_service import TestReportStatusEnum
from dev_effective.service.biz_tapd_data_write_strategy import TestLaunchFormSyncStrategy, BizDataSyncStrategyContext
from mantis import settings
from mantis.settings import TEST_REPORT, logger
from measurement.model.models import DevopsCheckRecordInfo
from measurement.service.app_auto_test_check_service import Check<PERSON>ontentEnum
from tapd_gateway.from_tapd.api.views import BizType
from tapd_gateway.from_tapd.service.tapd_entry_launch_form_ser import TapdEntityLaunchFormSer
from tapd_gateway.from_tapd.service.tapd_req_strategy import GetLaunchFormFromTapdStrategy, StrategyContext
from tapd_gateway.from_tapd.service.tapd_sync_data_success_log import TapdEntryDataSyncLogSer, TapdFunctionLogType
from task_mgt import external_service
from test_report.report_data_etl import ReportDataETL
from test_report.dao.software_quality_report_dao import get_tapd_iteration_id
from test_report.tapd.tapd_bugs import TAPDBugs
from test_report.tapd.tapd_launch_forms import TAPDLaunchForms
from test_report.tapd.tapd_stories import TAPDStories
from test_report.tapd.tapd_tasks import TAPDTasks


class SoftwareQualityReport(ViewSet):
    """
    测试报告接口
    """
    authentication_classes = []

    # get方法
    def list(self, request, *args, **kwargs):
        iteration_id = request.query_params.get('iteration_id')
        if not iteration_id:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict('迭代ID不能为空！'))

        ts = TAPDStories([iteration_id])
        result = ts._get_stories_from_tapd()
        if not result:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict('获取TAPD需求失败！'))

        task = TAPDTasks([iteration_id])
        result = task._get_tasks_from_tapd()
        if not result:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict('获取TAPD任务失败！'))

        result, story_name_list = ts.create_report_for_check_story(iteration_id)
        if not result:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict(
                                '故事检查不通过：{}'.format(story_name_list)))

        result, task_name_list = task.create_report_for_check_task(iteration_id)
        if not result:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict('任务检查不通过：{}'.format(task_name_list)))

        tb = TAPDBugs([iteration_id])
        result = tb._get_bugs_from_tapd()
        if not result:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict('获取TAPD缺陷失败！'))

        result, bug_id_list = tb.check_bug_iter_app_info(iteration_id)
        if not result:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict(
                                '缺陷的应用与迭代信息不匹配，缺陷列表为：{}'.format(bug_id_list)))

        tb = TAPDLaunchForms([iteration_id])
        result = tb.get_launch_forms_from_tapd()
        if not result:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict('获取TAPD发布评审失败！'))

        rd = ReportDataETL(iteration_id)
        result, msg = rd._report_data_etl(is_check=True)

        if not result:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict('报告数据转换失败：{}'.format(msg)))

        business_name = "create_test_report"
        params = {"business_name": business_name, "iteration_id": iteration_id}

        user = "mantis"

        stat, sid = external_service.ExternalService(business_name, user, params).call_local_service()

        if stat == 0:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.success_dict('测试报告调用成功！'))
        else:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict('服务异常！请联系平台排查！'))


class SoftwareQualityReportQuery(ViewSet):
    authentication_classes = []

    def list(self, request, *args, **kwargs):
        iteration_id = request.query_params.get('iteration_id')
        obj = DevEfficTestReportLibInfoDjango.objects.filter(report_id__in=DevEfficTestReport.objects.filter(
            project_id=DevEfficTestProject.objects.filter(tapd_iteration_id=iteration_id).first().id)).last()

        if obj:
            return HttpResponseRedirect(obj.report_url)
        return Response(status=status.HTTP_200_OK,
                        data=settings.ApiResult.failed_dict('未查到测试报告URL!'))


class SoftwareQualityReportReviewStatus(ViewSet):
    authentication_classes = []

    def list(self, request):
        iteration_id = request.query_params.get('iteration_id')
        business_name = request.query_params.get('business_name')

        tapd_iteration_id_list = get_tapd_iteration_id(iteration_id)

        if not tapd_iteration_id_list:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict(
                                '没找到迭代{}对应的质量报告！请联系相关测试同学或者平台'.format(iteration_id)))

        result = ''
        for tapd_iteration_id in tapd_iteration_id_list:

            obj = DevEfficTestReport.objects.filter(
                project_id=DevEfficTestProject.objects.filter(tapd_iteration_id=tapd_iteration_id).first().id).order_by(
                'id').last()
            if not obj:
                return Response(status=status.HTTP_200_OK, data=settings.ApiResult.failed_dict(
                    '没找到tapd迭代id为{}对应的质量报告！请联系相关测试同学或者平台'.format(tapd_iteration_id)))
            if obj.status == TestReportStatusEnum.SUCCESS.value:
                self.__create_devops_check_record(iteration_id, business_name, obj.id)
                continue
            else:
                # 风险检测来的请求不去同步tapd信息，产线申请的请求去同步tapd信息 20241205 by fwm
                if business_name != 'risk_check':
                    TestReportUtils.sync_launch_form_from_tapd_to_gateway(tapd_iteration_id)
                    # 经常出现只从tapd同步到gateway，没有从gateway同步到dev_effic的情况
                    # 接口都是同步处理，没看出来问题在哪
                    # 先等待1s观察一下， 20241205 by fwm
                    sleep(1)
                    TestReportUtils.sync_launch_form_from_gateway_to_dev_effic()

                obj = DevEfficTestReport.objects.filter(
                    project_id=DevEfficTestProject.objects.filter(
                        tapd_iteration_id=tapd_iteration_id).first().id).first()
                if obj:
                    self.__create_devops_check_record(iteration_id, business_name, obj.id)
                if obj.status == TestReportStatusEnum.SUCCESS.value:
                    continue
                else:
                    result += '【{}】测试报告状态为：{}！'.format(obj.title, obj.status)

        if not result:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.success_dict('迭代测试报告审核通过！'))
        else:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.failed_dict(
                                '测试报告状态异常！详情：{}'.format(result)))

    def __create_devops_check_record(self, iteration_id, business_name, report_id):
        try:
            dftr = DevEfficTestReportLibInfoDjango.objects.filter(report_id=report_id).last()
            if dftr:
                report_lib_id = dftr.id

                dcri = DevopsCheckRecordInfo.objects.filter(iteration_id=iteration_id,
                                                            check_content=CheckContentEnum.MANUAL.content,
                                                            business_name=business_name)
                if dcri:
                    dcri.update(report_lib_id=report_lib_id, update_time=datetime.datetime.now(),
                                update_user='be-script')
                else:
                    DevopsCheckRecordInfo.objects.create(iteration_id=iteration_id,
                                                         check_content=CheckContentEnum.MANUAL.content,
                                                         business_name=business_name,
                                                         report_lib_id=report_lib_id,
                                                         create_time=datetime.datetime.now(),
                                                         create_user='be-script')
        except Exception as e:
            logger.error("写入数据库失败，原因为：{}".format(str(e)))
            raise Exception("写入数据库失败，原因为：{}".format(str(e)))


class SoftwareQualityReportForSpiderView(ViewSet):
    authentication_classes = []

    def list(self, request):
        iteration_id = request.query_params.get('iteration_id')

        tapd_iteration_id_list = get_tapd_iteration_id(iteration_id)

        result = []
        for tapd_iteration_id in tapd_iteration_id_list:
            obj = DevEfficTestReport.objects.filter(
                project_id=DevEfficTestProject.objects.filter(tapd_iteration_id=tapd_iteration_id).first().id).order_by(
                'id').last()

            result.append({"report_url": TapdEntityLaunchFormSer().get_report_url(tapd_iteration_id),
                           "report_status": obj.status if obj else ""})

        return Response(status=status.HTTP_200_OK,
                        data=settings.ApiResult.success_dict(data=result, msg="获取测试报告成功！"))


class TestReportUtils:
    @classmethod
    def sync_launch_form_from_tapd_to_gateway(cls, iteration_id):
        iteration_id = iteration_id
        biz_type = BizType.LAUNCH_FORM.value
        workspace_id = TEST_REPORT['workspace_id']

        current_time = datetime.datetime.now()
        # 统一逻辑，去日志表里拿一次上次成功时间，实际传了迭代id，后面不用按照modified去范围查
        data_sync_ser = TapdEntryDataSyncLogSer()
        modified = data_sync_ser.get_modified(workspace_id, biz_type, TapdFunctionLogType.DATA_TO_GATEWAY.value,
                                              current_time)

        tapd_strategy = GetLaunchFormFromTapdStrategy(workspace_id, modified, iteration_id)

        context = StrategyContext(tapd_strategy)
        ins_batch_size, upd_batch_size, del_rows = context.execute_strategy()
        logger.info('按迭代{}同步TAPD发布评审数据到网关，新增条数：{}，更新条数：{}，删除条数：{}'.format(iteration_id,
                                                                                                    ins_batch_size,
                                                                                                    upd_batch_size,
                                                                                                    del_rows))

    @classmethod
    def sync_launch_form_from_gateway_to_dev_effic(cls):
        sync_strategy = TestLaunchFormSyncStrategy()
        context = BizDataSyncStrategyContext(sync_strategy)
        context.execute_strategy()
