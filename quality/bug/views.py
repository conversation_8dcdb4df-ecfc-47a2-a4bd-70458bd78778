from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from rest_framework import status
from mantis.settings import Api<PERSON><PERSON>ult, logger
from tapd_gateway.from_tapd.model.models import TapdEntryBug
from tapd_gateway.to_tapd.model.bug_model import BizBugCreate
class GetBugCreateStatus(ViewSet):
    authentication_classes = []

    def create(self, request):
        # bug_id_info = {}
        # entry_id_list = []
        # statu_info = {}
        # logger.info(request.data)
        # uuid_list = request.data['uuid_list']
        # uuid_list = uuid_list.split(',')
        # objs = BizBugCreate.objects.filter(bug_create_uuid__in=uuid_list)
        # for obj in objs:
        #     bug_id_info[obj.tapd_bug_id] = {'bug_create_uuid': obj.bug_create_uuid, 'tapd_bug_id': obj.tapd_bug_id}
        #     entry_id_list.append(obj.tapd_bug_id)
        # entry_bug_objs = TapdEntryBug.objects.filter(tapd_entry_id__in=entry_id_list)
        # for entry_bug_obj in entry_bug_objs:
        #     bug_id_info[entry_bug_obj.tapd_entry_id]['tapd_bug_status'] = entry_bug_obj.entry_status
        #     statu_info[bug_id_info[entry_bug_obj.tapd_entry_id]['bug_create_uuid']] = bug_id_info[entry_bug_obj.tapd_entry_id]
        # return Response(status=status.HTTP_200_OK,
        #                 data=ApiResult.success_dict(data=statu_info, msg="获取tapd bug状态成功"))
        return Response(status=status.HTTP_200_OK, data=ApiResult.failed_dict(msg="接口已废弃"))
