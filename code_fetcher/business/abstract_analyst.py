import json
from concurrent.futures import ThreadPoolExecutor, as_completed

import requests
from mantis.settings import logger

class AnaContentEntry:
    entry = None
    content = ""
    analysis_result = ""
    def __init__(self, entry, content):
        self.entry = entry
        self.content = content


class AbstractAnalyst:


    def analysing_brief(self, segments=[]):
        """
        segments: 描述的片段
        returns: 分析描述，总是返回一个str
        """
        pass

    def analysing_content_list(self, segments=[]):
        """
        segments: 条件的片段
        returns: 分析内容列表，总是返回一个ContentEntry的[]，或None
        """
        pass

    def generate_analysis_report(self, analysis_results):
        """
        analysis_results: 分析结果列表，[]或None
        returns: 可以是报告的地址，可以是分析结果本身
        """
        pass

class AnalysisMachine():

    def __init__(self, analyst: AbstractAnalyst):
        self.analyst = analyst

    def running(self):
        self.analyst.analysing_content_list()

    def analysis(self, brief_segments=[], content_segments=[]):
        conditions = [self.analyst.analysing_brief(brief_segments)]
        ana_content_entries = self.analyst.analysing_content_list(content_segments)
        if ana_content_entries is None:
            return None

        # 使用 ThreadPoolExecutor 并发执行
        with ThreadPoolExecutor(max_workers=3) as executor:
            # 提交任务到线程池
            future_to_index = {
                executor.submit(self.__ai_analysis_single, conditions, ana_content_entry.content): index
                for index, ana_content_entry in enumerate(ana_content_entries)
            }

            # 处理完成的任务
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    analysis_result = future.result()
                    if analysis_result is not None:
                        ana_content_entries[index].analysis_result = analysis_result
                except Exception as e:
                    print(f"Error processing entry {index}: {e}")

        analysis_result = self.analyst.generate_analysis_report(ana_content_entries)
        return analysis_result

    def __ai_analysis_single(self, conditions, content):
        full_content = """需要分析的内容：\n\n""" + content

        # 构建请求的数据结构
        payload = {
            "conditions": conditions,
            "question": full_content
        }

        # 设置请求头（根据API需求可能需要调整）
        headers = {
            'Content-Type': 'application/json'
        }

        # 发送POST请求
        try:
            response = requests.post(
                "http://mantis.howbuy.pa/mantis/assistant/hunyuan_t1/ask/",
                headers=headers,
                data=json.dumps(payload)
            )

            # 检查响应状态码
            if response.status_code == 200:
                # 解析JSON响应
                result = response.json()

                return result['answer']
            else:
                # 处理非200状态码的情况
                logger.error(f"请求失败，状态码：{response.status_code}")
                logger.error(f"响应内容：{response.text}")
                return None
        except requests.RequestException as e:
            # 处理请求异常
            logger.error(f"请求过程中发生错误：{e}")
            return None
