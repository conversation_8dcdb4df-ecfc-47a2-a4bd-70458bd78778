import json
import os

import pandas as pd
import sqlalchemy

from code_fetcher.business.abstract_analyst import AbstractAnalyst, AnaContentEntry
from code_fetcher.dao.code_repo_gitlab_file_dao import CodeRepoGitlabFileDao
from mantis.pool import SustainablePool
from openpyxl import load_workbook
from openpyxl.utils.dataframe import dataframe_to_rows

from mantis.settings import DISK_CACHE, logger


class RmqSenderProblemsAnalyst(AbstractAnalyst):
    pool = SustainablePool(10)
    code_repo_gitlab_file_dao = CodeRepoGitlabFileDao(pool)
    output_path = os.path.join(DISK_CACHE["code_repos"], "analyst")

    def analysing_brief(self, segments=[]):
        conditions = """
com.howbuy.message.MessageService的send()方法定义是：String send(String channel, SimpleMessage message)
在发送成功时方法会返回一个64位的字符串

推荐的正确写法是：
String messageId = MessageService.getInstance().send(channel, message);
然后根据messageId的值来判断是否确实发送成功了；messageId也可以用于未来定位问题

现在需要根据给出的内容，判断：
1. 是否用到了com.howbuy.message.MessageService.send()
2. 如果用到了，是否按照推荐的写法

作如下回答：
- 如果代码中用的是其他send方法，而不是MessageService的send方法，那么回答：{"type": "NO_USE", "function": 使用send方法的函数, "statement": "用的send方法不是MQ的方法。"}
- 如果代码中使用了，并且采取了推荐的方式，那么回答：{"type": "GOOD_USE", "function": 使用send方法的函数, "statement": "用法符合推荐方式。"}
- 如果代码中使用了，但没有采取推荐的方式，那么回答：{"type": "BAD_USE", "function": 使用send方法的函数, "statement": 描述问题}

回答内容限定格式：{"type": , "function": , "statement": }
只用限定格式回答，不允许在回答内容上增加其他额外字符，包括“json”、“换行”等
            """

        return conditions

    def analysing_content_list(self, segments=[]):
        ana_content_entries = []
        files = self.code_repo_gitlab_file_dao.select_gitlab_file_models_with_keyword(segments)
        for file in files:
            ana_content_entry = AnaContentEntry(file, file.file_content)
            ana_content_entries.append(ana_content_entry)
        return ana_content_entries

    def generate_analysis_report(self, ana_content_entries):
        if not os.path.exists(self.output_path):
            os.makedirs(self.output_path)
        output_file = os.path.join(self.output_path, "rmq_sender_problems_analyst.xlsx")

        # 创建一个空的DataFrame来存储结果
        data = []

        # 遍历所有AnaContentEntry对象
        for entry in ana_content_entries:
            # 获取entry中的project_id
            project_name = entry.entry.path_with_namespace
            file_name = entry.entry.file_name

            # 解析analysis_result JSON字符串
            try:
                s = repr(entry.analysis_result)[1:-1].replace('\\"', '\"')
                analysis_result = json.loads(s)
            except json.JSONDecodeError as err:
                # 如果解析失败，使用空值填充
                logger.error(f"转json过程中发生问题：{err} \n {entry.analysis_result}")
                analysis_result = {"type": "", "function": "", "statement": ""}

            # 将数据添加到列表中
            data.append({
                "project_name": project_name,
                "file_name": file_name,
                "type": analysis_result.get("type", ""),
                "function": analysis_result.get("function", ""),
                "statement": analysis_result.get("statement", "")
            })

        # 将数据转换为DataFrame
        df = pd.DataFrame(data)
        # 将DataFrame写入Excel文件
        df.to_excel(output_file, index=False)
        # 使用openpyxl加载刚刚保存的Excel文件
        workbook = load_workbook(output_file)
        sheet = workbook.active

        # 设置每一列的宽度
        column_widths = [35, 35, 15, 30, 100]
        for i, width in enumerate(column_widths, start=1):
            sheet.column_dimensions[chr(64 + i)].width = width

        # 保存修改后的Excel文件
        workbook.save(output_file)