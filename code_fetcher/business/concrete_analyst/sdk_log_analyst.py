import json
import os

import pandas as pd
import sqlalchemy

from code_fetcher.business.abstract_analyst import AbstractAnalyst, AnaContentEntry
from code_fetcher.dao.code_repo_gitlab_file_dao import CodeRepoGitlabFileDao
from mantis.pool import SustainablePool
from openpyxl import load_workbook
from openpyxl.utils.dataframe import dataframe_to_rows

from mantis.settings import DISK_CACHE, logger


class SdkLogAnalyst(AbstractAnalyst):
    pool = SustainablePool(10)
    code_repo_gitlab_file_dao = CodeRepoGitlabFileDao(pool)
    output_path = os.path.join(DISK_CACHE["code_repos"], "analyst")

    def analysing_brief(self, segments=[]):
        conditions = """
如果代码中有打印info日志，就输出如下格式，有几处记录几处：
[
{"fragment": "打印日志的上面5行和下面3行", "intention": "是用来记录什么的"}
]
如果没有打印info日志，就输出如下格式：
[{"fragment": "NULL", "intention": "NULL"}]
            """

        return conditions

    def analysing_content_list(self, segments=[]):
        ana_content_entries = []
        files = self.code_repo_gitlab_file_dao.select_gitlab_file_models_by_project_ids(["1517"])
        for file in files:
            ana_content_entry = AnaContentEntry(file, file.file_content)
            ana_content_entries.append(ana_content_entry)
        return ana_content_entries

    def generate_analysis_report(self, ana_content_entries):
        if not os.path.exists(self.output_path):
            os.makedirs(self.output_path)
        output_file = os.path.join(self.output_path, "sdk_log_analyst.xlsx")

        # 创建一个空的DataFrame来存储结果
        data = []

        # 遍历所有AnaContentEntry对象
        for entry in ana_content_entries:
            # 获取entry中的project_id
            project_name = entry.entry.path_with_namespace
            file_name = entry.entry.file_name

            # 解析analysis_result JSON字符串
            try:
                #s = repr(entry.analysis_result)[1:-1].replace('\\"', '\"')
                #analysis_result = json.loads(s)
                analysis_result = entry.analysis_result
            except json.JSONDecodeError as err:
                # 如果解析失败，使用空值填充
                logger.error(f"转json过程中发生问题：{err} \n {entry.analysis_result}")
                analysis_result = [{"fragment": "NULL", "intention": "NULL"}]

            # 将数据添加到列表中
            data.append({
                "project_name": project_name,
                "file_name": file_name,
                "detail": analysis_result
            })

        # 将数据转换为DataFrame
        df = pd.DataFrame(data)
        # 将DataFrame写入Excel文件
        df.to_excel(output_file, index=False)
        # 使用openpyxl加载刚刚保存的Excel文件
        workbook = load_workbook(output_file)
        sheet = workbook.active

        # 设置每一列的宽度
        column_widths = [35, 35, 200]
        for i, width in enumerate(column_widths, start=1):
            sheet.column_dimensions[chr(64 + i)].width = width

        # 保存修改后的Excel文件
        workbook.save(output_file)