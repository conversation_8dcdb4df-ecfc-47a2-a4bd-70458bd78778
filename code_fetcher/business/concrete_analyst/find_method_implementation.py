import json
import os
import re

from code_fetcher.business.abstract_analyst import AbstractAnalyst, AnaContentEntry
from code_fetcher.dao.code_repo_gitlab_file_dao import CodeRepoGitlabFileDao
from mantis.pool import SustainablePool

from mantis.settings import DISK_CACHE, logger


class FindMethodImplementation(AbstractAnalyst):
    pool = SustainablePool(10)
    code_repo_gitlab_file_dao = CodeRepoGitlabFileDao(pool)
    output_path = os.path.join(DISK_CACHE["code_repos"], "analyst")

    def analysing_brief(self, segments=[]):
        """
        segments只要传入一个方法名称
        """
        if segments and segments[0]:
            segment = segments[0]
        else:
            segment = ""

        conditions = """
你的工作是在给出的代码片段中，找出是否有%s的实现方法。作如下回答：
- 但凡代码中有%s的实现方法，就回答：{"type": "Implementation", "function_code": 实现这个方法的代码, "calling_methods": [{"package_of_method": 被调方法的包路径, "class_of_method": 被调方法的类, "method_name": 被调方法的名称}]}
注释：calling_methods是上述的实现中调用的其他方法。有几个就列几个，不要遗漏。如果没有，就给一个空的[]
- 如果代码中没有%s的实现方法，只有%s的调用，而非实现，那么回答：{"type": "Call", "function_code": 调用这个方法的代码}
- 如果代码中既没有实现%s的实现方法，也没有%s的调用，那么回答：{"type": "None", "function_code": "None"}

回答内容限定格式：{"type": , "function_code": }。只用限定格式回答，不允许在回答内容上增加其他额外字符，包括“json”、“换行”等
            """ % (segment, segment, segment, segment, segment, segment)

        return conditions

    def analysing_content_list(self, segments=[]):
        """
        segments传入尽可能（一定）包含这个方法的类，比如包路径，类名，方法名等
        """
        ana_content_entries = []
        files = self.code_repo_gitlab_file_dao.select_gitlab_file_models_with_keyword(segments)
        for file in files:
            ana_content_entry = AnaContentEntry(file, file.file_content)
            ana_content_entries.append(ana_content_entry)
        return ana_content_entries

    def generate_analysis_report(self, ana_content_entries):
        # 遍历所有AnaContentEntry对象
        for entry in ana_content_entries:
            # 获取entry中的project_id
            project_name = entry.entry.path_with_namespace
            file_name = entry.entry.file_name
            full_code = entry.content

            # 解析analysis_result JSON字符串
            try:
                s = repr(str.strip(entry.analysis_result))[1:-1].replace('\\"', '\"')
                analysis_result = json.loads(s)
                if analysis_result.get("type") == "Implementation":
                    res = {
                        "project_name": project_name,
                        "file_name": file_name,
                        "function_code": analysis_result.get("function_code"),
                        "full_code": full_code
                    }

                    return res
            except json.JSONDecodeError as err:
                # 如果解析失败，使用空值填充
                logger.error(f"转json过程中发生问题：{err} \n {entry.analysis_result}")
                analysis_result = {"type": "", "function_code": ""}

        return "没有找到代码"
