import datetime as datetime_tool
import hashlib
import json
from concurrent.futures.thread import Thread<PERSON>oolExecutor

from code_fetcher.dao.git_commits_diff_dao import GitCommitsDiffDao
from code_fetcher.model.git_commits_diff_model import GitCommitsDiffModel
from code_fetcher.business.git_http_request import GitHttpRequest
from mantis.settings import logger


class GitUserCommitsProcessor:

    def __init__(self, userId, fromDateStr, toDataStr, pool):
        self.user_id = userId
        self.fromDateStr = fromDateStr
        self.toDataStr = toDataStr
        self.fromDate = datetime_tool.datetime.strptime(fromDateStr, '%Y-%m-%d').date()
        self.toDate = datetime_tool.datetime.strptime(toDataStr, '%Y-%m-%d').date() if toDataStr else None
        self.git_commits_diff_dao = GitCommitsDiffDao(pool)

    def run(self):
        self.process()

    def process(self):
        logger.info("开始 获取作者【%s】的代码" % self.user_id)
        try:
            commitsDiffMap = {}
            res = self._fetchAndInsertCommitsStatisticsList(commitsDiffMap)
            logger.info("结束 获取作者【%s】的代码" % self.user_id)
            return res
        except Exception as err:
            logger.error(err)
            return {"result": False, "msg": err}

    def _fetchAndInsertCommitsStatisticsList(self, commitsDiffMap):

        if commitsDiffMap is None:
            return

        pushedList = self._fetchPushedListAfterDate()

        count = 0
        f_count = 0
        commitCount = 0
        processed = 0
        futures = []

        total = len(pushedList) if pushedList else 0
        if total > 0:
            with ThreadPoolExecutor(max_workers=20) as executor:
                for e in pushedList:
                    try:
                        processed += 1
                        future = executor.submit(self._on_process_a_commit_diff, e, processed, total)
                        futures.append(future)
                        # self._on_process_a_commit_diff(e, processed, total)
                    except Exception as err:
                        print(3333)

            for future in futures:
                res = future.result()
                commitCount += res["commitCount"]
                count += res["c"]
                f_count += res["f"]

            msg = "总共 处理作者【%s】，commit条数【%d】，写入条数【%d】，失败条数【%d】" % (
                self.user_id, commitCount, count, f_count)
            logger.info(msg)
            return {"result": True, "msg": msg}
        else:
            return {"result": True, "msg": "没有任何提交！"}

    def _on_process_a_commit_diff(self, e, processed, total):
        try:
            commitsDiffList = self._fetchCompareListByCommit(e['project_id'], e['commit_from'], e['commit_to'],
                                                             e['p_datetime'], e['title'])
            commitCount = len(commitsDiffList)
            c, f = self.insertCommitsDiffList(e['action'], e['author_name'], e['ref'], commitsDiffList)
            if processed % 100 == 1:
                logger.info("还在 处理作者【%s】【%d/%d】" % (self.user_id, processed, total))
            return {"commitCount": commitCount, "c": c, "f": f}
        except Exception as err:
            return {"commitCount": 0, "c": 0, "f": 0}

    def insertCommitsDiffList(self, action, username, ref, commitsDiffList):
        count = 0
        f_count = 0
        for e in commitsDiffList:
            po = GitCommitsDiffModel()
            po.set(e)
            po.user_id = self.user_id
            po.action = action
            po.ref = ref
            po.username = username
            po.file_type = ".{}".format(po.new_path.split(".")[-1]) if "." in po.new_path else "nofix"
            diffStat = self._get_diff_statistics(po.diff)
            t_diff = po.diff  # '%s%s' % (po.diff, po.title)
            po.diff_md5 = hashlib.md5(t_diff.encode('utf-8')).hexdigest()
            po.add_counts = diffStat[0]
            po.del_counts = diffStat[1]
            po.add_counts_effect = self._get_add_statistics_effect(po.diff)

            try:

                if self.git_commits_diff_dao.insert(po) < 0:
                    count += 1
                else:
                    f_count += 1
            except Exception as err:
                f_count += 1

        return count, f_count

    def _fetchCompareListByCommit(self, projectId, commitFrom, commitTo, p_datetime, title, c_datetime=None):
        list = []
        url = None
        committerName = ""
        commits = []
        currentDiffs = []
        objList = []

        try:
            if commitFrom is None or commitFrom == "null":

                # if True:
                list = []
                # print("没有from")
                next = '1'
                while True:
                    url = "%s/api/v4/projects/%s/repository/commits/%s/diff?per_page=100&page=%s" % (
                        GitHttpRequest.host(), projectId, commitTo, next)
                    response = GitHttpRequest.get(url)
                    next = response.headers.get('X-Next-Page')
                    objList = json.loads(response.text)
                    current = self._convertGitCompareListEffect(objList, projectId, commitTo, p_datetime, title,
                                                                c_datetime)
                    list.extend(current)
                    if next is None or next == '':
                        break
                    else:
                        continue
                # print("没有from 完成")
            else:
                # print("有from")
                url = "%s/api/v4/projects/%s/repository/compare?from=%s&to=%s" % (
                    GitHttpRequest.host(), projectId, commitFrom, commitTo)
                response = GitHttpRequest.get(url)
                obj = json.loads(response.text)

                commits = obj['commits']
                # currentDiffs = obj['diffs']
                for e in commits:
                    commitToE = e['id']
                    titleE = e['title']
                    c_datetime = datetime_tool.datetime.strptime(e['committed_date'], '%Y-%m-%dT%H:%M:%S.%f%z')
                    list.extend(
                        self._fetchCompareListByCommit(projectId, None, commitToE, p_datetime, titleE, c_datetime))

                # print("有from 完成")

        except Exception as err:
            print("通过提交id获取提交代码失败！【%s】" % url)
            logger.error(err)
            # raise ex

        return list

    def _convertGitCompareListEffect(self, objList, projectId, commit, p_datetime, title, c_datetime=None):
        list = []

        for e in objList:
            try:
                model = {
                    'project_id': projectId,
                    'commit': commit,
                    'old_path': e['old_path'],
                    'new_path': e['new_path'],
                    'diff': e['diff'],
                    'p_datetime': p_datetime,
                    'c_datetime': c_datetime if c_datetime is not None else p_datetime,
                    'new_file': e['new_file'],
                    'title': title,
                    'is_merge': 'y' if (
                            title and ('merge' in title.lower() or '回合' in title or '合并' in title)) else 'n'
                }
                list.append(model)
            except:
                print('_convertGitCompareListEffect 错误')

        return list

    def _fetchPushedListAfterDate(self):
        urlRoot = "%s/api/v4/users/%s/events?per_page=100" % (GitHttpRequest.host(), self.user_id)

        pushedList = []
        page = 1
        cycle = 0

        while cycle < 200:
            cycle += 1
            try:
                url = urlRoot + "&page=%s" % page
                response = GitHttpRequest.get(url)
                objList = json.loads(response.text)

                x_page = response.headers['X-Page']
                x_total_pages = response.headers['X-Total-Pages']
                print("正在 获取作者【%s】，页码【%s/%s】" % (self.user_id, x_page, x_total_pages))

                if not self._refGitPushedListAfterDate(objList, pushedList) or page == int(x_total_pages):
                    break

                page += 1

            except Exception as ex:
                print("获取参与者【%s】提交代码失败" % self.user_id)
                break

        print("正在 获取作者【%s】，pushed条数【%d】" % (self.user_id, len(pushedList)))

        return pushedList

    def _refGitPushedListAfterDate(self, objList, refGitPushedList):

        try:
            for e in objList:

                if 'action_name' in e and 'pushed' in e['action_name']:
                    createdAt = datetime_tool.datetime.strptime(e['created_at'].split('+')[0],
                                                                '%Y-%m-%dT%H:%M:%S.%f').date()

                    if createdAt < self.fromDate:
                        return False

                    if self.toDate and createdAt > self.toDate:
                        continue

                    model = {
                        'action': e['action_name'],
                        'author_name': e['author']['name'],
                        'commit_from': e['push_data']['commit_from'],
                        'commit_to': e['push_data']['commit_to'],
                        'p_datetime': datetime_tool.datetime.strptime(e['created_at'], '%Y-%m-%dT%H:%M:%S.%f%z'),
                        'project_id': e['project_id'],
                        'ref': e['push_data']['ref'],
                        'title': e['push_data']['commit_title']
                    }
                    refGitPushedList.append(model)

        except Exception as err:
            logger.error('_refGitPushedListAfterDate {}'.format(err))

        return True

    def _get_diff_statistics(self, diff):
        adds = 0
        dels = 0
        diff_stat = []
        try:
            lines = diff.split("\n")
            for line in lines:
                if line.startswith("-"):
                    dels += 1
                elif line.startswith("+"):
                    adds += 1
        except Exception as ex:
            print(str(ex))  # TODO: handle exception

        diff_stat.append(adds)
        diff_stat.append(dels)
        return diff_stat

    def _get_add_statistics_effect(self, diff):
        adds = 0

        try:
            lines = diff.split("\n")
            for line in lines:
                if line.startswith("+"):
                    line = line[1:]
                    if line.strip().startswith("*") or line.strip().startswith("/") or line.strip().startswith(
                            "<!") or len(line.strip()) == 0 or line.strip().startswith(
                        "import") or line.strip().startswith("#"):
                        pass
                    else:
                        adds += 1
        except Exception as ex:
            a = str(ex)

        return adds


"""
    def _convertGitCommitsDiffListEffect(self, objList, projectId, commit, datetime):
        list = []

        for e in objList:
            try:
                if e.get('new_path') and e.get('diff'):
                    model = {
                        'project_id': projectId,
                        'commit': commit,
                        'old_path': e['old_path'],
                        'new_path': e['new_path'],
                        'diff': e['diff'],
                        'datetime': datetime
                    }
                    list.append(model)
            except Exception as err:
                logger.error('_convertGitCommitsDiffListEffect {}'.format(err))

        return list
        

    def _processCommitsOfCommit(self, projectId, committerName, initFrom, initTo, commits, currentDiffs, datetime,
                                title,
                                refList):
        if len(commits) == 0:
            return
        if len(commits) == 1:
            refList.extend(self._convertGitCompareListEffect(currentDiffs, projectId, initTo, datetime, title))
            return

        for commit in commits:
            try:
                froms = commit['parent_ids']
                if len(froms) != 1:
                    continue
                if committerName != commit['committer_name']:
                    continue

                if len(froms) == 1:
                    fromVal = froms[0]
                    toVal = commit['id']

                    url = "%s/api/v4/projects/%s/repository/compare?from=%s&to=%s" % (
                        GitHttpRequest.host(), projectId, fromVal, toVal)
                    response = GitHttpRequest.get(url)
                    obj = json.loads(response.text)

                    intCurrentDiffs = obj['diffs']
                    intTitle = obj['commit']['title'] if 'commit' in obj and 'title' in obj['commit'] else None
                    intCommitterName = obj['commit']['committer_name'] if 'commit' in obj and 'committer_name' in \
                                                                          obj[
                                                                              'commit'] else None

                    if committerName != intCommitterName:
                        continue
                    refList.extend(
                        self._convertGitCompareListEffect(intCurrentDiffs, projectId, toVal, datetime, intTitle))

            except:
                logger.error("_processCommitsOfCommit 错误")
"""
