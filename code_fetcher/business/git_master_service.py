import datetime as datetime_tool
import json
import os
import subprocess
import threading

import requests
from concurrent.futures import ThreadPoolExecutor, as_completed

import sqlalchemy

from assistant.service.base.hunyuan import HunyuanLite, DeepseekV3, HunyuanTurbo
from code_fetcher.business.git_http_request import GitHttpRequest
from code_fetcher.dao.code_repo_gitlab_file_dao import CodeRepoGitlabFileDao
from code_fetcher.dao.code_repo_gitlab_project_dao import CodeRepoGitlabProjectDao
from code_fetcher.model.code_repo_gitlab_file_model import GitlabFileModel
from code_fetcher.model.code_repo_gitlab_project_model import GitlabProjectModel
from mantis.pool import SustainablePool
from rest_framework.response import Response
from rest_framework import status
from mantis.settings import DISK_CACHE


class GitMasterService:

    pool = SustainablePool(10)
    days_before = 5
    lock = threading.Lock()

    def __init__(self):
        self.code_repo_gitlab_project_dao = CodeRepoGitlabProjectDao(self.pool)
        self.code_repo_gitlab_file_dao = CodeRepoGitlabFileDao(self.pool)
        self.base_dir = DISK_CACHE["code_repos"]

    projects_url = "{gitlab_http}/api/v4/projects?per_page=100&page={page}"
    #trees_url = "{gitlab_http}/api/v4/projects/{project_id}/repository/tree?ref={branch}&recursive=true&per_page=100&page={page}"
    #file_content_url = "{gitlab_http}/api/v4/projects/{project_id}/repository/files/{path}/raw?ref={branch}"
    #file_blame_url = "{gitlab_http}/api/v4/projects/{project_id}/repository/files/{path}/blame?ref={branch}"

    def refresh_all_project(self):

        projects_gitlab_list = self.__get_gitlab_content_all("projects")
        current_list = []
        for i in range(len(projects_gitlab_list)):
            current_list.append(self.__convert_project(projects_gitlab_list[i]))

        self.code_repo_gitlab_project_dao.save_or_update_gitlab_project_models(current_list)

        return Response(status=status.HTTP_200_OK, data={"result": True, "message": len(current_list)})

    def __process_repository(self, project):
        """
        克隆或更新单个Git仓库到指定目录。

        :param project: 包含项目信息的对象，需有ssh_url_to_repo和project_id属性
        :return: 字典包含项目ID和操作状态
        """
        repo_dir = os.path.join(self.base_dir, str(project.project_id))

        with self.lock:
            if not os.path.exists(self.base_dir):
                os.makedirs(self.base_dir)

        # 尝试执行 git pull, 目录不存在，直接执行 git clone
        if os.path.exists(repo_dir):

            p_result, git_output = self.__pull_repository(project, repo_dir)
        else:
            p_result, git_output = self.__clone_repository(project, repo_dir)

        if p_result:
            p_result, db_output = self.__refresh_project_files(project)
        else:
            db_output = "没有执行"

        return {"project_id": project.project_id, "status": p_result, "git_output": git_output, "db_output": db_output}

    def __pull_repository(self, project, repo_dir):
        try:
            result = subprocess.run(
                ["git", "-C", repo_dir, "pull"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True  # 如果命令返回非零退出状态，会引发CalledProcessError
            )

            return True, result.stdout #{"project_id": project.project_id, "status": "pulled", "output": result.stdout}
        except subprocess.CalledProcessError as pull_error:
            # git pull 失败，记录错误并尝试 git clone
            try:
                # 删除现有目录以便重新克隆
                subprocess.run(["rm", "-rf", repo_dir], check=True)
            except subprocess.CalledProcessError as remove_error:
                return False, remove_error.stderr

            self.__clone_repository(project, repo_dir)

    def __clone_repository(self, project, repo_dir):
        try:
            result = subprocess.run(
                ["git", "clone", project.ssh_url_to_repo, repo_dir],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )
            return True, result.stdout#{"project_id": project.project_id, "status": "cloned", "output": result.stdout}
        except subprocess.CalledProcessError as e:
            return False, e.stderr#{"project_id": project.project_id, "status": "failed2clone", "error": e.stderr}

    def download_all_project(self, days_before):
        """
        下载所有活跃的项目到本地目录，使用线程池并发执行。
        :param days_before: 项目活跃天数，超过该天数的项目将被下载
        :return: DRF Response对象
        """

        self.days_before = days_before

        activity_projects = self.__get_validated_projects(days_before)
        if not activity_projects:
            return Response(
                status=status.HTTP_200_OK,
                data={"result": True, "message": "没有找到活跃的项目。"}
            )

        results = []
        # 定义线程池大小为5
        with ThreadPoolExecutor(max_workers=3) as executor:
            # 提交所有克隆任务
            future_to_project = {executor.submit(self.__process_repository, project): project for project in activity_projects}

            for future in as_completed(future_to_project):
                project = future_to_project[future]
                try:
                    data = future.result()
                    results.append(data)
                except Exception as exc:
                    # 捕获未预见的异常
                    results.append({
                        "project_id": project.project_id,
                        "status": "error",
                        "error": str(exc)
                    })

        return Response(
            status=status.HTTP_200_OK,
            data={"result": True, "message": results}
        )

    def __refresh_project_files(self, project):
        repo_dir = os.path.join(self.base_dir, str(project.project_id))
        path_entries = []
        is_java_project = False

        try:

            for root, dirs, files in os.walk(repo_dir):
                if root.startswith(os.path.join(repo_dir, '.git')) or root == repo_dir:
                    continue
                # 处理当前目录中的所有子目录
                for dir_name in dirs:
                    dir_path = os.path.join(root, dir_name)
                    relative_path = os.path.relpath(dir_path, repo_dir)
                    # 添加目录信息到数据库
                    directory_entry = GitlabFileModel(
                        project_id=project.project_id,
                        path_name=relative_path,
                        path_type='directory',
                        file_name=None,
                        file_type=None,
                        file_content_b=None
                    )
                    path_entries.append(directory_entry)

                # 处理当前目录中的所有文件
                for file_name in files:
                    file_path = os.path.join(root, file_name)
                    relative_path = os.path.relpath(file_path, repo_dir)
                    # 获取文件扩展名作为文件类型
                    _, file_extension = os.path.splitext(file_name)
                    file_type = file_extension.lstrip('.') if file_extension else 'unknown'
                    if file_type == 'java':
                        is_java_project = True

                    # 读取文件内容
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                    except (UnicodeDecodeError, IOError) as e:
                        # 如果无法读取文件内容，可以记录为空或处理异常
                        content = None

                    # 添加文件信息到数据库
                    file_entry = GitlabFileModel(
                        project_id=project.project_id,
                        path_name=relative_path,
                        path_type='file',
                        file_name=file_name,
                        file_type=file_type,
                        file_content_b=sqlalchemy.func.compress(content)
                    )

                    path_entries.append(file_entry)

            if is_java_project:
                with self.lock:
                    saved = self.code_repo_gitlab_file_dao.save_gitlab_file_models(project.project_id, path_entries)
                output = "保存成功" if saved else "保存失败"
            else:
                saved = False
                output = "不是Java项目"
        except Exception as e:
            saved = False
            output = "保存失败，原因：{}".format(str(e))

        return saved, output

    def __ask_for_need_check(self, project_path, content):
        if ".send(" in content:

            conditions = [
                """用于判断用户给出代码中是否包含如下这样的调用，只是用了Date()构造函数的情形：\
                Date [具体变量] = new Date([某个时间])
    
                回答内容限定格式：{"result": , "segments": }
                - 如果存在这样的调用，就回答：{"result": True, "segments": [包含的某一个代码片段]}
                - 不存在，就回答：{"result": False, "segments": None"}
                不允许增加任何额外的字符，包括“json”、“换行”等
                """
            ]

            conditions = [
                """
com.howbuy.message.MessageService.send()方法定义是：String send(String channel, SimpleMessage message)
在发送成功时方法会返回一个64位的字符串

推荐的正确写法是：
String messageId = MessageService.getInstance().send(channel, message);
然后根据messageId的值来判断是否确实发送成功了；messageId也可以用于未来定位问题

现在需要判断，给出的代码是否用到了com.howbuy.message.MessageService.send()，用到了是否按照推荐的写法。回答内容限定格式：{"type": , "class": , "function": , "statement": }
- 如果代码中完全没有用到，那么回答：{"type": "No_USE", "class": None , "function": None, "statement": "没有使用send方法。"}
- 如果代码中使用了，并且采取了推荐的方式，那么回答：{"type": "GOOD_USE", "class": 使用send方法的class, "function": 使用send方法的函数, "statement": "用法符合推荐方式。"}
- 如果代码中使用了，但没有采取推荐的方式，那么回答：{"type": "BAD_USE", "class": 使用send方法的class, "function": 使用send方法的函数, "statement": 问题描述}

只用限定格式回答，不允许在回答内容上增加其他额外字符，包括“json”、“换行”等
                """
            ]

            # content拼接一句话
            full_content = """需要判断的代码如下：\n\n""" + content

            # 构建请求的数据结构
            payload = {
                "conditions": conditions,
                "question": full_content
            }

            # 设置请求头（根据API需求可能需要调整）
            headers = {
                'Content-Type': 'application/json'
            }

            # 发送POST请求
            try:
                response = requests.post(
                    "http://mantis.howbuy.pa/mantis/assistant/hunyuan_turbo/ask/",
                    headers=headers,
                    data=json.dumps(payload)
                )

                # 检查响应状态码
                if response.status_code == 200:
                    # 解析JSON响应
                    result = response.json()
                    details = json.loads(result['answer'])

                    # 提取各个字段
                    type = details.get("type", "")
                    class_name = details.get("class", "")
                    function = details.get("function", "")
                    statement = details.get("statement", "")

                    print(
                        f"| {project_path} | {type} | {class_name} | {function} | {statement} |")

                    return result
                else:
                    # 处理非200状态码的情况
                    print(f"请求失败，状态码：{response.status_code}")
                    print(f"响应内容：{response.text}")
                    return None
            except requests.RequestException as e:
                # 处理请求异常
                print(f"请求过程中发生错误：{e}")
                return None

    def __get_validated_projects(self, n=1):
        # commited_project_ids = [945, 193, 51] # 调试用
        activity_projects = self.code_repo_gitlab_project_dao.select_projects_actived_last_n_days(n)
        #valid_projects = [
        #    project for project in activity_projects
        #    if project.project_id in commited_project_ids
        #]
        valid_projects = activity_projects

        return valid_projects


    def __get_gitlab_content_all(self, type, *args):
        '''

        @return: dict
        '''
        all = []
        page = 1
        while page:
            page, content = self.__get_content_by_page(type, page, *args)
            all.extend(content)

        return all

    def __get_content_by_page(self, type, page, *args):
        '''

        @param page:
        @return: 下一页, 本页所有
        '''
        url = ""
        if type == "projects":
            url = self.projects_url.format(gitlab_http=GitHttpRequest.host(), page=page)
        elif type == "trees":
            url = self.trees_url.format(gitlab_http=GitHttpRequest.host(), project_id=args[0], branch=args[1], page=page)

        response = GitHttpRequest.get(url)

        try:
            next_page = response.headers["X-Next-Page"]
        except Exception as err:
            next_page = None

        try:
            res = response.json()
        except Exception as err:
            res = response.content if response.status_code == 200 else []

        return next_page, res

    def __convert_project(self, project_gitlab):
        model = GitlabProjectModel()
        model.project_id = project_gitlab.get("id")
        model.default_branch = project_gitlab.get("default_branch")
        model.visibility = project_gitlab.get("visibility")
        model.path_with_namespace = project_gitlab.get("path_with_namespace")
        model.last_activity_at = datetime_tool.datetime.strptime(project_gitlab.get("last_activity_at"), '%Y-%m-%dT%H:%M:%S.%f%z')
        model.ssh_url_to_repo = project_gitlab.get("ssh_url_to_repo")

        return model