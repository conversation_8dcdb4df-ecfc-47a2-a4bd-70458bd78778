from concurrent.futures import ThreadPoolExecutor

import requests

from code_fetcher.business.git_http_request import GitHttpRequest
from code_fetcher.business.git_user_commits_processor import Git<PERSON>serCommitsProcessor
from rest_framework.response import Response
from rest_framework import status

from code_fetcher.dao.git_commits_diff_dao import G<PERSON><PERSON>ommitsDiff<PERSON><PERSON>
from mantis.pool import SustainablePool
from mantis.settings import SPIDER, logger


class GitCodeService:
    pool = SustainablePool(30)

    def __init__(self):
        self.git_commits_diff_dao = GitCommitsDiffDao(self.pool)

    def cp_stat_data(self):
        self.git_commits_diff_dao.truncat_stat_data()
        project_ids = self.git_commits_diff_dao.select_project_ids()
        futures = []
        success = 0
        failed = 0
        with ThreadPoolExecutor(max_workers=8) as executor:
            for e in project_ids:
                try:
                    future = executor.submit(self.git_commits_diff_dao.cp_stat_data_by_project, e)
                    futures.append(future)
                except Exception as err:
                    print(3333)

        for e in futures:
            if e.result():
                success += 1
            else:
                failed += 1

        return Response(status=status.HTTP_200_OK, data={"result": failed == 0, "message": success})

    def migrate_data(self):
        project_ids = self.git_commits_diff_dao.select_projects()

        futures = []
        with ThreadPoolExecutor(max_workers=10) as executor:
            for e in project_ids:
                try:
                    future = executor.submit(self._migrate_data, e["project_id"], e["username"])
                    futures.append(future)
                except Exception as err:
                    print(3333)

        failed = 0
        for e in futures:
            if not e.result():
                failed += 1

        return Response(status=status.HTTP_200_OK, data={"result": True, "message": failed})

    def _migrate_data(self, project_id, username):
        if self.git_commits_diff_dao.cp_old_to_new(project_id, username):
            logger.info("_migrate_data %s %s 迁移结果 成功" % (project_id, username))
            return True
        else:
            logger.info("_migrate_data %s %s 迁移结果 失败" % (project_id, username))
            return False

    def _threading_unneed_exceeds(self, project_ids, method):
        futures = []
        success = 0
        failed = 0

        with ThreadPoolExecutor(max_workers=5) as executor:
            for e in project_ids:
                try:
                    future = executor.submit(self._threading_unneed_by_projects, method, e)
                    futures.append(future)
                except Exception as err:
                    print(3333)

        for e in futures:
            if e.result():
                success += 1
            else:
                failed += 1

        logger.info("_threading_unneed_exceeds %s 实际失效的id批次 成功 %s 失败 %s" % (method, success, failed))

        return failed == 0

    def unneed_once_exceeeds(self):
        project_ids = self.git_commits_diff_dao.select_project_ids()

        if self._threading_unneed_exceeds(project_ids, 'select_unneed_onetime'):
            if self._threading_unneed_exceeds(project_ids, 'select_unneed_onecommit'):
                return Response(status=status.HTTP_200_OK, data={"result": True, "msg": "成功！"})
            else:
                return Response(status=status.HTTP_200_OK, data={"result": False, "msg": "出现失败的！"})
        else:
            return Response(status=status.HTTP_200_OK, data={"result": False, "msg": "出现失败的！"})

    def fetch_code_by_user(self, user_id, from_date_str, to_date_str=None):
        if user_id == 97:
            return Response(status=status.HTTP_200_OK, data={"result": False, "msg": "howbuyscm"})
        p = GitUserCommitsProcessor(str(user_id), from_date_str, to_date_str, self.pool)
        data = p.process()
        return Response(status=status.HTTP_200_OK, data=data)

    def fetch_code(self):
        userlist = self._get_user_list()
        for u in userlist:
            p = GitUserCommitsProcessor(str(u), '2023-10-01', self.pool)
            p.run()

        return Response(status=status.HTTP_200_OK, data={"result": True, "message": len(userlist)})

    def _get_user_list(self):
        url = '%s/api/v4/users?per_page=1&page=1' % GitHttpRequest.host()
        response = GitHttpRequest.get(url)
        users = response.json()
        first_user_id = users[0]['id']

        user_list = []
        i = 0
        while i < first_user_id:
            i += 1
            user_list.append(i)

        return user_list

    def _select_ids_not_first_time_commit(self, project_id):
        newpaths = self.git_commits_diff_dao.select_project_newpaths(project_id)
        if len(newpaths) == 1 and "state" in newpaths[0]:
            logger.error("_select_ids_not_first_time_commit 失败 %s %s" % (project_id, newpaths[0]["state"]))
        futures = []
        ids = []
        workers = 8 if len(newpaths) > 1000 else 3
        with ThreadPoolExecutor(max_workers=workers) as executor:
            for f in newpaths:
                try:
                    future = executor.submit(self.git_commits_diff_dao.select_ids_not_first_time_commit, project_id, f)
                    futures.append(future)
                except Exception as err:
                    print(3333)

        for e in futures:
            ids.extend(e.result())

        logger.info("_select_ids_not_first_time_commit %s 执行过的path数 %s 要处理的id数量 %s" % (
            project_id, len(futures), len(ids)))

        return ids

    def _select_ids_not_first_id_commit(self, project_id):
        newpaths = self.git_commits_diff_dao.select_project_newpaths(project_id)
        if len(newpaths) == 1 and "state" in newpaths[0]:
            logger.error("_select_ids_not_first_id_commit 失败 %s %s" % (project_id, newpaths[0]["state"]))
        futures = []
        ids = []
        workers = 8 if len(newpaths) > 1000 else 3
        with ThreadPoolExecutor(max_workers=workers) as executor:
            for f in newpaths:
                try:
                    future = executor.submit(self.git_commits_diff_dao.select_ids_not_first_id_commit, project_id, f)
                    futures.append(future)
                except Exception as err:
                    print(3333)

        for e in futures:
            ids.extend(e.result())

        logger.info("_select_ids_not_first_id_commit %s 执行过的path数 %s 要处理的id数量 %s" % (
            project_id, len(futures), len(ids)))

        return ids

    def _delete_by_ids(self, ids, batches):
        if self.git_commits_diff_dao.delete_by_ids(ids):
            logger.info("_delete_by_ids 成功 批次号 %s %s" % (batches, len(ids)))
            return True
        else:
            logger.info("_delete_by_ids 失败 批次号 %s %s" % (batches, len(ids)))
            return False

    def _threading_unneed_by_projects(self, method, project_id):

        ids = self.git_commits_diff_dao.select_unneed_onetime(
            project_id) if method == 'select_unneed_onetime' else self.git_commits_diff_dao.select_unneed_onecommit(
            project_id)

        result = self.git_commits_diff_dao.unneed_by_ids(ids)
        logger.info("_threading_unneed_by_projects 状态%s %s %s %s条" % (result, method, project_id, len(ids)))
        return result

    def _threading_unneed_by_ids(self, futures):
        '''
        futures is a future's list of 'a id's list'
        '''
        success = 0
        failed = 0
        unneeds = []
        ids_list_list = []  # 最终完整list

        for e in futures:
            ids_list_list.append(e.result())

        with ThreadPoolExecutor(max_workers=5) as executor:
            for e in ids_list_list:
                future = executor.submit(self.git_commits_diff_dao.unneed_by_ids, e)
                unneeds.append(future)

        for e in unneeds:
            if e.result():
                success += 1
            else:
                failed += 1

        logger.info("_threading_unneed_by_ids 实际失效的id批次 成功 %s 失败 %s" % (success, failed))

        return failed == 0

    def _threading_delete_by_ids(self, futures):
        '''
        futures is a future's list of 'a id's list'
        '''
        success = 0
        failed = 0
        deletes = []
        ids_list = []  # 最终完整list

        for e in futures:
            ids_list.extend(e.result())

        batches = 0

        with ThreadPoolExecutor(max_workers=5) as executor:
            idx = 0
            ids = []
            for e in ids_list:
                idx += 1
                ids.append(e)
                if idx >= 1000:
                    batches += 1
                    future = executor.submit(self._delete_by_ids, ids, batches)
                    deletes.append(future)
                    idx = 0
                    ids = []

            batches += 1
            future = executor.submit(self._delete_by_ids, ids, batches)
            deletes.append(future)

        for e in deletes:
            if e.result():
                success += 1
            else:
                failed += 1

        logger.info("_threading_delele_by_ids 实际删除的批次数量 %s %s" % (batches, success))

        return success, failed

    def remove_not_first_commit(self):
        self.git_commits_diff_dao.delele_howbuyscm()

        project_ids = self.git_commits_diff_dao.select_project_ids()
        if len(project_ids) == 1 and "state" in project_ids[0]:
            logger.error("remove_not_first_commit 失败 %s" % project_ids[0]["state"])

        futures = []
        with ThreadPoolExecutor(max_workers=5) as executor:
            for e in project_ids:
                try:
                    future = executor.submit(self._select_ids_not_first_time_commit, e)
                    futures.append(future)
                except Exception as err:
                    print(3333)

        success, failed = self._threading_delete_by_ids(futures)
        old = """
        if failed == 0:
            futures = []
            with ThreadPoolExecutor(max_workers=5) as executor:
                for e in project_ids:
                    try:
                        future = executor.submit(self._select_ids_not_first_id_commit, e)
                        futures.append(future)
                    except Exception as err:
                        print(3333)

            success, failed = self._threading_delete_by_ids(futures)
            """

        if failed > 0:
            return Response(status=status.HTTP_200_OK, data={"result": False, "message": failed})
        else:
            return Response(status=status.HTTP_200_OK, data={"result": True, "message": success})

    def change_to_cn_name(self):

        url = '{}user/get_cn_name/'.format(SPIDER["url"])
        response = requests.get(url)
        names = response.json()

        need_changes = self.git_commits_diff_dao.get_need_changes_by_name_models(names)

        names_queue = {row['username']: row for row in names}

        for need_change in need_changes:
            if need_change in names_queue:
                logger.info('%s %s' % (need_change, names_queue[need_change]["cn_name"]))
                self.git_commits_diff_dao.update_by_need_changes(need_change, names_queue[need_change]["cn_name"])

        return Response(status=status.HTTP_200_OK, data={"result": True, "message": len(need_changes)})


"""
#调试main函数
if __name__ == '__main__':
    git_code_service = GitCodeService()
    git_code_service.fetch_code_by_user(338, '2024-07-15' )
    # git_code_service.fetch_code()
    # git_code_service.remove_not_first_commit()
    # git_code_service.change_to_cn_name()

if __name__ == '__main__':
    success = 0
    failed = 0
    deletes = []
    ids_list = []  # 最终完整list
    i = 0
    # 写一个循环，将小于23212的数字写入到ids_list中
    while i < 23251:
        i += 1
        ids_list.append(i)

    with ThreadPoolExecutor(max_workers=5) as executor:
        idx = 0
        ids = []
        row = 0
        for e in ids_list:
            idx += 1
            ids.append(e)
            if idx >= 1000:
                row+=1
                print("{} {}".format(row, len(ids)))
                idx = 0
                ids = []

        print("{} {}".format(row, len(ids)))
"""
