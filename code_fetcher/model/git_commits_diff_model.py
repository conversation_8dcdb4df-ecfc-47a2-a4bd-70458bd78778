from sqlalchemy import Column, Integer, String, DateTime, Text, func, BINARY
from sqlalchemy.orm import session

from mantis.pool import Base


class GitCommitsDiffModel(Base, object):
    __tablename__ = 'code_stat_git_commits_diff'

    id = Column(Integer(), primary_key=True, nullable=False)
    project_id = Column(Integer())
    commit = Column(String(200))
    old_path = Column(String(500))
    new_path = Column(String(500))
    diff = Column(BINARY())
    p_datetime = Column(DateTime())
    c_datetime = Column(DateTime())
    user_id = Column(String(25))
    username = Column(String(50))
    action = Column(String(200))
    ref = Column(String(200))
    new_file = Column(String(100))
    message = Column(String(2000))
    file_type = Column(String(100))
    add_counts = Column(Integer())
    del_counts = Column(Integer())
    add_counts_effect = Column(Integer())
    title = Column(String(100))
    is_merge = Column(String(5))
    diff_md5 = Column(String(200))

    def set(self, arg_dict):
        for key, value in arg_dict.items():
            setattr(self, key, value)

    def __repr__(self):
        return '<GitCommitsDiffModel %r>' % self.project_id
