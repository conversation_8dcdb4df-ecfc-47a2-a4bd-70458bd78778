from sqlalchemy import Column, Integer, String, DateTime

from mantis.pool import Base


class GitlabProjectModel(Base, object):
    __tablename__ = 'code_repo_gitlab_projects'

    project_id = Column(Integer,primary_key=True, nullable=False)
    path_with_namespace = Column(String(200))
    visibility = Column(String(100))
    default_branch = Column(String(200))
    last_activity_at = Column(DateTime())
    ssh_url_to_repo = Column(String(500))
