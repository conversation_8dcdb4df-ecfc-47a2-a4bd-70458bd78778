from sqlalchemy import Column, Integer, String, DateTime, Text, BINARY, func, cast
from sqlalchemy.orm import column_property
from mantis.pool import Base


class GitlabFileModel(Base, object):
    __tablename__ = 'code_repo_gitlab_files'

    project_id = Column(Integer,primary_key=True, nullable=False)
    ref = Column(String(100), default="master")
    path_name = Column(String(300))
    path_type = Column(String(100)) # tree or file
    file_name = Column(String(100))
    file_type = Column(String(50)) #.java, .xml and so on
    file_content_b = Column(BINARY)
    file_content = column_property(cast(func.uncompress(file_content_b), Text))
