import datetime

from code_fetcher.model.code_repo_gitlab_project_model import GitlabProjectModel
from mantis.settings import logger


class CodeRepoGitlabProjectDao:

    def __init__(self, pool):
        self.pool = pool

    def save_or_update_gitlab_project_models(self, models):
        """
        :param models: a list of GitlabProjectModel
        """
        db_session = self.pool.get_db_session()
        try:
            for model in models:
                db_session.merge(model)

            db_session.commit()
            db_session.close()
            return True
        except Exception as err:
            logger.error("save_or_update_gitlab_project_models {}".format(err))
            db_session.close()
            return False


    def select_projects_actived_last_n_days(self, n):
        db_session = self.pool.get_db_session()
        three_days_ago = datetime.datetime.now().replace(hour=23, minute=59, second=59, microsecond=0) - datetime.timedelta(days=n)
        query = (db_session.query(GitlabProjectModel).filter(GitlabProjectModel.last_activity_at > three_days_ago)
                 .order_by(GitlabProjectModel.last_activity_at.desc()).distinct())
        projects = query.all()
        return projects

    def select_project_ids_last_n_days(self, n):
        db_session = self.pool.get_db_session()
        three_days_ago = datetime.datetime.now().replace(hour=23, minute=59, second=59, microsecond=0) - datetime.timedelta(days=n)
        sql = """SELECT distinct project_id from code_stat_git_commits_diff cd where p_datetime > '{}' and ref = 'master' and file_type = '.java'""".format(three_days_ago)

        res = []

        try:
            result = db_session.execute(sql)

            for row in result:
                res.append(row["project_id"])

        except Exception as err:
            res = [{"state": err}]
        finally:
            db_session.close()

        return res

        """
        db_session = self.pool.get_db_session()
        now = datetime.datetime.now()

        # 查询project_id，条件是p_datetime和当前时间差在3天内
        three_days_ago = now - datetime.timedelta(days=3)
        query = db_session.query(GitCommitsDiffModel.project_id).filter(GitCommitsDiffModel.p_datetime >= three_days_ago).distinct()
        project_ids = query.all()
        print(project_ids)
        return project_ids
        """


"""
if __name__ == '__main__':
    sql_ids = '0'
    ids = ['123','465', '4324']
    for id in ids:
        sql_ids = '{},{}'.format(sql_ids, id)
    sql = 'delete from code_stat_git_commits_diff where id in ({})'.format(sql_ids)
    print(sql)
"""
