from sqlalchemy import func

from code_fetcher.model.git_commits_diff_model import GitCommitsDiffModel
from mantis.settings import logger


class GitCommitsDiffDao:

    def __init__(self, pool):
        self.pool = pool

    def truncat_stat_data(self):
        sql = "truncate code_stat_git_commits_diff_stat"

        db_session = self.pool.get_db_session()

        try:
            db_session.execute(sql)
        except Exception as err:
            return False
        finally:
            db_session.close()

        return True

    def cp_stat_data_by_project(self, project_id):
        sql = """
            insert into code_stat_git_commits_diff_stat
            SELECT
                id,
                project_id,
                commit,
                old_path,
                new_path,
                c_datetime,
                p_datetime,
                user_id,
                username,
            create_date,
            update_date,
                action,
                ref,
                new_file,
                message,
                file_type,
                add_counts,
                del_counts,
                add_counts_effect,
                title,
                is_merge,
                diff_md5,
                need_stat,
                first_commit_id
            FROM
                code_stat_git_commits_diff cd
            where project_id = {}
            and is_merge = 'n'
            and need_stat = 1
            and first_commit_id is null
            and
            (
            cd.file_type in ('.java')
            or cd.file_type in ('.xml', '.bundle/_CodeSignature/CodeResources')
            or cd.file_type in ('.jsp', '.html', '.htm', '.vue', '.js', '.jsx', '.less', '.ftl', '.ts')
            or cd.file_type in ('.json', '.json5')
            or cd.file_type in ('.m', '.h')
            or cd.file_type in ('.xib', '.storyboard', '.nib')
            or cd.file_type in ('.py', '.rs', '.toml')
            or cd.file_type in ('.sql', '.groovy', '.sh', '.yaml', '.yml', '.conf')
            or cd.file_type in ('.css', '.scss', '.styl', '.svg')
            or cd.file_type in ('.kt', '.ets')
            or (cd.file_type = 'nofix' and cd.new_path like '%Dockerfile')
            )
            and cd.add_counts_effect > 0
            and cd.project_id not in ('173', '248', '253', '317', '357', '382', '514', '515', '517', '605', '713', '747', '794', '837', '1070', '1123', '1183', '1259', '1265', '1270', '1274', '1277', '1308', '1315', '1320', '1327', '1334', '1337', '1372', '1376', '1377', '1432')
            and
            (
                (cd.new_path not like 'HowbuyFund/Pods%' and cd.project_id = '367')
                or (cd.new_path not like 'HBPublicFundModules/Pods%' and cd.project_id = '369')
                or (cd.new_path not like 'PiggyBank/Pods%' and  cd.project_id = '370')
                or (cd.new_path not like 'HBAccountCenterModules/Pods%' and cd.project_id = '374')
                or (cd.new_path not like 'HBPrivateFundModules/Pods%' and cd.project_id = '478')
                or (cd.new_path not like 'Pods%' and cd.project_id = '1263')
                or (cd.new_path not like 'docs/%' and cd.project_id = '1190')
                or cd.project_id not in ('367', '369', '370', '374', '478', '1263', '1190')
            )
            and new_path not like '.idea/%'
            and new_path not in ('pnpm-lock.yaml')
        """.format(project_id)

        db_session = self.pool.get_db_session()

        try:
            db_session.execute(sql)
            db_session.commit()

        except Exception as err:
            db_session.rollback()
            logger.error('cp_stat_data_by_project 失败 %s %s' % (project_id, err))
            return False
        finally:
            db_session.close()

        logger.info('cp_stat_data_by_project 成功 %s' % project_id)
        return True

    def insert(self, model):
        res = -1
        db_session = self.pool.get_db_session()
        if model and isinstance(model, GitCommitsDiffModel):
            model.diff = func.compress(model.diff)
            try:
                db_session.add(model)
                db_session.commit()
            except Exception as err:
                res_str = "代码提交记录写入失败 %s" % err
                if "Duplicate entry" not in res_str:
                    logger.error(res_str)
                db_session.rollback()
                res = model.id if model and model.id else 0
            finally:
                db_session.close()

        return res

    def select_project_ids(self):
        db_session = self.pool.get_db_session()
        sql = """SELECT distinct project_id from code_stat_git_commits_diff cd order by project_id"""

        res = []

        try:
            result = db_session.execute(sql)

            for row in result:
                res.append(row["project_id"])

        except Exception as err:
            res = [{"state": err}]
        finally:
            db_session.close()

        return res

    def select_projects(self):
        db_session = self.pool.get_db_session()
        sql = """SELECT distinct project_id, username from code_stat_git_commits_diff cd order by username, project_id"""

        res = []

        try:
            result = db_session.execute(sql)

            for row in result:
                res.append(row)

        except Exception as err:
            res = [{"state": err}]
        finally:
            db_session.close()

        return res

    def select_project_newpaths(self, project_id):
        db_session = self.pool.get_db_session()
        sql = """SELECT distinct new_path from code_stat_git_commits_diff cd where project_id = {}""".format(project_id)

        res = []

        try:
            result = db_session.execute(sql)

            for row in result:
                res.append(row['new_path'])

        except Exception as err:
            res = []
            logger.error('select_project_newpaths %s %s' % (project_id, err))
        finally:
            db_session.close()

        return res

    def select_ids_not_first_time_commit(self, project_id, new_path):
        db_session = self.pool.get_db_session()
        sql = """
            SELECT
                cd.id
            FROM
                code_stat_git_commits_diff cd
                    INNER JOIN
                    (
            SELECT
                cd.project_id,
                cd.new_path,
                cd.diff_md5,
                min(cd.p_datetime) as p_datetime
            FROM
                code_stat_git_commits_diff cd
            where project_id = {} and new_path = '{}'
            GROUP BY
                cd.project_id,
                cd.new_path,
                cd.diff_md5
            HAVING
                count(0)>1 ) co
                    ON
                    cd.project_id = co.project_id AND
                cd.new_path = co.new_path AND
                cd.diff_md5 = co.diff_md5 AND
                cd.p_datetime <> co.p_datetime
            where first_commit_id is null
        """.format(project_id, new_path)

        res = []

        try:
            result = db_session.execute(sql)

            for row in result:
                res.append(row["id"])

        except Exception as err:
            res = []
            logger.error('select_ids_not_first_time_commit %s %s %s' % (project_id, new_path, err))
        finally:
            db_session.close()

        return res

    def select_ids_not_first_id_commit(self, project_id, new_path):
        db_session = self.pool.get_db_session()
        sql = """
            SELECT
                cd.id,
                cd.project_id,
                cd.new_path,
                cd.diff_md5
            FROM
                code_stat_git_commits_diff cd
                    INNER JOIN
                    (
            SELECT
                cd.project_id,
                cd.new_path,
                cd.diff_md5,
                min(id) as id
            FROM
                code_stat_git_commits_diff cd
            where project_id = {} and new_path = '{}' and is_merge = 'n'
            GROUP BY
                cd.project_id,
                cd.new_path,
                cd.diff_md5
            HAVING
                count(0)>1 
                ) co
                on cd.project_id = co.project_id AND
                cd.new_path = co.new_path AND
                cd.diff_md5 = co.diff_md5 AND
                cd.id <> co.id
        where is_merge = 'n'
        """.format(project_id, new_path)

        res = []

        try:
            result = db_session.execute(sql)

            for row in result:
                res.append(row["id"])

        except Exception as err:
            res = []
            logger.error('select_ids_not_first_id_commit %s %s %s' % (project_id, new_path, err))
        finally:
            db_session.close()

        return res

    def get_need_changes_by_name_models(self, name_models):

        db_session = self.pool.get_db_session()

        sql_username = "''"

        for name_model in name_models:
            sql_username = "{},'{}'".format(sql_username, name_model["username"])

        sql = 'SELECT distinct username FROM code_stat_git_commits_diff where username in ({})'.format(
            sql_username)

        res = []
        try:
            result = db_session.execute(sql)

            for row in result:
                res.append(row["username"])

        except Exception as err:
            res = []
            logger.error('get_need_changes_by_name_models 失败 %s %s' % (sql, err))
        finally:
            db_session.close()

        logger.info('get_need_changes_by_name_models 成功 %s req %s act %s' % (sql, result.rowcount, len(res)))
        return res

    def select_unneed_onetime(self, project_id):
        db_session = self.pool.get_db_session()
        sql = """
            select id from
                v_git_commits_diff_effect cd
            inner join
            (
            SELECT
                project_id, p_datetime, username, sum(cd.add_counts_effect) as total
            FROM
                v_git_commits_diff_effect cd
            where project_id = {}
            group by project_id, p_datetime, username having sum(cd.add_counts_effect) > 5000

            ) x
            on x.project_id = cd.project_id  and x.p_datetime = cd.p_datetime and x.username = cd.username
        """.format(project_id)

        res = []

        try:
            result = db_session.execute(sql)

            for row in result:
                res.append(row["id"])

        except Exception as err:
            res = []
            logger.error('select_unneed_onetime %s %s' % (project_id, err))
        finally:
            db_session.close()

        logger.info('select_unneed_onetime %s 获取数据 %s 条' % (project_id, len(res)))
        return res

    def select_unneed_onecommit(self, project_id):
        db_session = self.pool.get_db_session()
        sql = """

            select distinct cd.id from
            v_git_commits_diff_effect cd
            inner join
            (
            select project_id, commit, username, total from
            (
            SELECT
                project_id, commit, username, sum(cd.add_counts_effect) as total, 
                group_concat(distinct case when cd.file_type = '.vue' or cd.file_type = '%.css' then '.aaa' else '.bbb' end) 
                as file_types
            FROM
                v_git_commits_diff_effect cd
            where project_id = {}
            group by project_id, commit, username having sum(cd.add_counts_effect) > 2000
            ) a
            where file_types not like '%.aaa%'
            
            union all
            
            select project_id, commit, username, total from
            (
            SELECT
                project_id, commit, username, sum(cd.add_counts_effect) as total
            FROM
                v_git_commits_diff_effect cd
            where project_id = {}
            group by project_id, commit, username having sum(cd.add_counts_effect) > 4500
            ) a
            
            ) x
            
            on x.project_id = cd.project_id  and x.commit = cd.commit  and x.username = cd.username
        """.format(project_id, project_id)

        res = []

        try:
            result = db_session.execute(sql)

            for row in result:
                res.append(row["id"])

        except Exception as err:
            res = []
            logger.error('select_unneed_onecommit %s %s' % (project_id, err))
        finally:
            db_session.close()

        logger.info('select_unneed_onecommit %s 获取数据 %s 条' % (project_id, len(res)))
        return res

    def unneed_by_ids(self, ids):
        if ids is None or len(ids) == 0:
            return True

        sql_ids = '0'
        for id in ids:
            sql_ids = '{},{}'.format(sql_ids, id)

        sql = 'update code_stat_git_commits_diff set need_stat = 0, update_date = now() where id in ({})'.format(
            sql_ids)

        db_session = self.pool.get_db_session()

        try:
            db_session.execute(sql)
            db_session.commit()

        except Exception as err:
            db_session.rollback()
            logger.error('unneed_by_ids 失败 %s %s' % (err, sql))
            return False
        finally:
            db_session.close()

        logger.info('unneed_by_ids 成功 %s 条' % (len(ids)))
        return True

    def delete_by_ids(self, ids):
        if ids is None or len(ids) == 0:
            return True

        sql_ids = '0'
        for id in ids:
            sql_ids = '{},{}'.format(sql_ids, id)

        sql = 'update code_stat_git_commits_diff set first_commit_id = 0 where id in ({})'.format(sql_ids)

        db_session = self.pool.get_db_session()

        try:
            db_session.execute(sql)
            db_session.commit()

        except Exception as err:
            db_session.rollback()
            logger.error('delele_by_ids %s %s' % (err, sql))
            return False
        finally:
            db_session.close()

        return True

    def delele_howbuyscm(self):
        sql = " delete from code_stat_git_commits_diff where username = 'howbuyscm' "

        db_session = self.pool.get_db_session()

        try:
            db_session.execute(sql)
            db_session.commit()

        except Exception as err:
            db_session.rollback()
            return False
        finally:
            db_session.close()

        return True

    def cp_old_to_new(self, projcet_id, username):
        sql = """
        INSERT INTO code_stat_git_commits_diff_o 
        (
        	id,
	project_id,
	`commit`,
	old_path,
	new_path,
	diff, 
	datetime,
	user_id,
	username,
create_date,
update_date,
	`action`,
	`ref`,
	new_file,
	message,
	file_type,
	add_counts,
	del_counts,
	add_counts_effect,
	title,
	is_merge,
	diff_md5
        )-- 替换列名，将需要插入的列名填入括号内
        SELECT
                	id,
	project_id,
	`commit`,
	old_path,
	new_path,
	diff, 
	datetime,
	user_id,
	username,
create_date,
update_date,
	`action`,
	`ref`,
	new_file,
	message,
	file_type,
	add_counts,
	del_counts,
	add_counts_effect,
	title,
	is_merge,
	diff_md5
        FROM code_stat_git_commits_diff
        WHERE project_id = {} and username = '{}'
        """.format(projcet_id, username)

        db_session = self.pool.get_db_session()

        try:
            db_session.execute(sql)
            db_session.commit()

        except Exception as err:
            db_session.rollback()
            return False
        finally:
            db_session.close()

        return True

    def update_by_need_changes(self, username, cn_name):

        sql = "update code_stat_git_commits_diff set username = '{}', update_date = now() where username = '{}'".format(
            cn_name, username)

        db_session = self.pool.get_db_session()

        try:
            db_session.execute(sql)
            db_session.commit()

        except Exception as err:
            db_session.rollback()
            return False
        finally:
            db_session.close()

        return True


"""
if __name__ == '__main__':
    sql_ids = '0'
    ids = ['123','465', '4324']
    for id in ids:
        sql_ids = '{},{}'.format(sql_ids, id)
    sql = 'delete from code_stat_git_commits_diff where id in ({})'.format(sql_ids)
    print(sql)
"""
