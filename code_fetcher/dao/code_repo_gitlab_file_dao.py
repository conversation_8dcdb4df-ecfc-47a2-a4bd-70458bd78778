import datetime
from importlib.resources import contents

import sqlalchemy
from sqlalchemy import or_, and_
from code_fetcher.model.code_repo_gitlab_file_model import GitlabFileModel
from code_fetcher.model.code_repo_gitlab_project_model import GitlabProjectModel
from mantis.settings import logger


class CodeRepoGitlabFileDao:

    def __init__(self, pool):
        self.pool = pool

    def save_gitlab_file_models(self, project_id, models):
        """
        :param models: a list of GitlabProjectModel
        """
        db_session = self.pool.get_db_session()

        try:
            delete_count = db_session.query(GitlabFileModel).filter(
                GitlabFileModel.project_id == project_id
            ).delete(synchronize_session=False)

            for model in models:
                db_session.add(model)

            db_session.commit()
            db_session.close()

            return True
        except Exception as err:
            logger.error("save_gitlab_file_models {} {}".format(project_id, err))
            db_session.close()
            return False

    def select_gitlab_file_models_with_keyword(self, keywords):
        db_session = self.pool.get_db_session()
        try:
            like_conditions = [sqlalchemy.func.uncompress(GitlabFileModel.file_content_b).like(f'%{kw}%') for kw in
                               keywords]
            combined_condition = and_(*like_conditions)

            # 构建查询
            query = db_session.query(
                GitlabProjectModel.path_with_namespace,
                GitlabFileModel.file_name,
                GitlabFileModel.file_content
            ).join(
                GitlabProjectModel,
                GitlabFileModel.project_id == GitlabProjectModel.project_id
            ).filter(
                GitlabFileModel.file_type == 'java',
                GitlabProjectModel.path_with_namespace.notlike('%pa-%'),
                combined_condition
            )
            files = query.all()
            db_session.close()
            return files
        except Exception as err:
            logger.error("select_gitlab_file_models_with_keyword {}".format(err))
            db_session.close()
            return None


    def select_gitlab_file_models_by_project_ids(self, project_ids):
        """
        根据项目ID列表查询GitLab文件
        :param project_ids: 项目ID列表
        :return: 查询结果列表，每个元素包含项目路径、文件名和文件内容
        """
        db_session = self.pool.get_db_session()
        try:
            keywords = ["log", "info"]
            like_conditions = [sqlalchemy.func.uncompress(GitlabFileModel.file_content_b).like(f'%{kw}%') for kw in
                               keywords]
            combined_condition = and_(*like_conditions)

            # 构建查询
            query = db_session.query(
                GitlabProjectModel.path_with_namespace,
                GitlabFileModel.file_name,
                GitlabFileModel.file_content
            ).join(
                GitlabProjectModel,
                GitlabFileModel.project_id == GitlabProjectModel.project_id
            ).filter(
                GitlabFileModel.project_id.in_(project_ids),
                GitlabFileModel.file_type == 'java',
                combined_condition
            )
            files = query.all()
            db_session.close()
            return files
        except Exception as err:
            logger.error("select_gitlab_file_models_by_project_ids error: {}".format(err))
            db_session.close()
            return None

"""
if __name__ == '__main__':
    sql_ids = '0'
    ids = ['123','465', '4324']
    for id in ids:
        sql_ids = '{},{}'.format(sql_ids, id)
    sql = 'delete from code_stat_git_commits_diff where id in ({})'.format(sql_ids)
    print(sql)
"""
