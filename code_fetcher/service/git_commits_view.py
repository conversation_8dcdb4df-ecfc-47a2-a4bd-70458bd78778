from rest_framework.decorators import action
from rest_framework.viewsets import ViewSet

from code_fetcher.business.git_code_service import GitCodeService


class GitCommitsView(ViewSet):
    git_code_service = GitCodeService()

    @action(methods=['GET'], detail=False, url_path='fetch_code_by_user')
    def fetch_code_by_user(self, request):
        return self.git_code_service.fetch_code_by_user(request.data["user_id"], request.data["from_date_str"])

    @action(methods=['GET'], detail=False, url_path='fetch_code_by_user_ft')
    def fetch_code_by_user_ft(self, request):
        return self.git_code_service.fetch_code_by_user(request.data["user_id"], request.data["from_date_str"],
                                                        request.data["to_date_str"])

    @action(methods=['GET'], detail=False, url_path='fetch_code')
    def fetch_code(self, request, *args, **kwargs):
        return self.git_code_service.fetch_code()

    @action(methods=['GET'], detail=False, url_path='remove_not_first_commit')
    def remove_not_first_commit(self, request, *args, **kwargs):
        return self.git_code_service.remove_not_first_commit()

    @action(methods=['GET'], detail=False, url_path='change_to_cn_name')
    def change_to_cn_name(self, request, *args, **kwargs):
        return self.git_code_service.change_to_cn_name()

    @action(methods=['GET'], detail=False, url_path='unneed_once_exceeeds')
    def unneed_once_exceeeds(self, request, *args, **kwargs):
        return self.git_code_service.unneed_once_exceeeds()

    @action(methods=['GET'], detail=False, url_path='migrate_data')
    def migrate_data(self, request, *args, **kwargs):
        return self.git_code_service.migrate_data()

    @action(methods=['GET'], detail=False, url_path='cp_stat_data')
    def cp_stat_data(self, request, *args, **kwargs):
        return self.git_code_service.cp_stat_data()


"""
pipeline {
 agent {
    label 'vmx'
}
    
    stages {


        stage('获取代码') {
            steps {
                sh label: '', script: '''python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/gitlab_code/user_commit.py 'fetch_code' '15' '***************:8011' '''
            }
        }


        stage('中文人名') {
            steps {
                script {
                    def response = httpRequest(url: 'http://***************:8031/mantis/code_fetcher/git_commits_view/change_to_cn_name/', acceptType: 'APPLICATION_JSON')
                    def json = readJSON(text: response.content)
                    if (json.result) {
                        echo json.message.toString()
                    } else {
                        error '失败'
                    }
                }
            }
        }


        
        stage('剔除非首次提') {
            steps {
                script {
                    def response = httpRequest(url: 'http://***************:8031/mantis/code_fetcher/git_commits_view/remove_not_first_commit/', acceptType: 'APPLICATION_JSON')
                    def json = readJSON(text: response.content)
                    if (json.result) {
                        echo json.message.toString()
                    } else {
                        echo response.content
                        error '失败'
                    }
                }
            }
        }

        stage('去过多提交') {
            steps {
                script {
                    def response = httpRequest(url: 'http://***************:8031/mantis/code_fetcher/git_commits_view/unneed_once_exceeeds/', acceptType: 'APPLICATION_JSON')
                    def json = readJSON(text: response.content)
                    if (json.result) {
                        echo json.message.toString()
                    } else {
                        echo response.content
                        error '失败'
                    }
                }
            }
        }

        stage('转移新的数据') {
            steps {
                script {
                    def response = httpRequest(url: 'http://***************:8031/mantis/code_fetcher/git_commits_view/cp_stat_data/', acceptType: 'APPLICATION_JSON')
                    def json = readJSON(text: response.content)
                    if (json.result) {
                        echo json.message.toString()
                    } else {
                        echo response.content
                        error '失败'
                    }
                }
            }
        }

    }
}

"""
