from rest_framework.decorators import action
from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from rest_framework import status

from code_fetcher.business.abstract_analyst import AnalysisMachine
from code_fetcher.business.concrete_analyst.find_method_implementation import FindMethodImplementation
from code_fetcher.business.concrete_analyst.rmq_sender_problems_analyst import RmqSenderProblemsAnalyst
from code_fetcher.business.concrete_analyst.sdk_log_analyst import SdkLogAnalyst


class GitAnalystView(ViewSet):
    rmq_sender_problems_machine = AnalysisMachine(RmqSenderProblemsAnalyst())
    find_method_implementation_machine = AnalysisMachine(FindMethodImplementation())
    sdk_log_analyst_machine = AnalysisMachine(SdkLogAnalyst())

    @action(methods=['GET'], detail=False, url_path='sdk_log_analyst')
    def sdk_log_analyst(self, request, *args, **kwargs):
        res = self.sdk_log_analyst_machine.analysis([], [])
        return Response(status=status.HTTP_200_OK, data={"result": True, "message": res})

    @action(methods=['GET'], detail=False, url_path='rmq_sender_problems_analyst')
    def rmq_sender_problems_analyst(self, request, *args, **kwargs):
        res = self.rmq_sender_problems_machine.analysis([], [".send(", "MessageService", "com.howbuy.message"])
        return Response(status=status.HTTP_200_OK, data={"result": True, "message": res})

    @action(methods=['GET'], detail=False, url_path='find_method_implementation')
    def find_method_implementation(self, request, *args, **kwargs):
        try:
            package_name_of_method = request.data["package_name_of_method"]
            class_name_of_method = request.data["class_name_of_method"]
            method_name = request.data["method_name"]

            if "app_name" in request.data:
                app_name = request.data["app_name"]
            if "branch" in request.data:
                branch = request.data["branch"]

            brief_segments = [class_name_of_method + "." + method_name + "()"]
            content_segments = [package_name_of_method, class_name_of_method, method_name + "("]

        except Exception as e:
            return Response(status=status.HTTP_400_BAD_REQUEST, data={"result": False, "message": "参数错误"})

        res = self.find_method_implementation_machine.analysis(brief_segments, content_segments)
        return Response(status=status.HTTP_200_OK, data={"result": True, "message": res})

"""
pipeline {
 agent {
    label 'vmx'
}
    
    stages {

        stage('刷新项目状态') {
            steps {
                script {
                    def response = httpRequest(url: 'http://***************:8031/mantis/code_fetcher/git_master_view/refresh_all_project/', acceptType: 'APPLICATION_JSON')
                    def json = readJSON(text: response.content)
                    if (json.result) {
                        echo json.message.toString()
                    } else {
                        error '失败'
                    }
                }
            }
        }

        stage('更新项目文件') {
            steps {
                script {
                    def response = httpRequest(url: 'http://***************:8031/mantis/code_fetcher/git_master_view/download_all_project/', acceptType: 'APPLICATION_JSON')
                    def json = readJSON(text: response.content)
                    if (json.result) {
                        echo json.message.toString()
                    } else {
                        error '失败'
                    }
                }
            }
        }

    }
}

"""
