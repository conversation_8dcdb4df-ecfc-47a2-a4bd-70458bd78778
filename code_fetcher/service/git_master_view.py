from rest_framework.decorators import action
from rest_framework.viewsets import ViewSet

from code_fetcher.business.git_master_service import GitMasterService


class GitMasterView(ViewSet):
    git_master_service = GitMasterService()

    @action(methods=['GET'], detail=False, url_path='refresh_all_project')
    def refresh_all_project(self, request, *args, **kwargs):
        return self.git_master_service.refresh_all_project()

    @action(methods=['GET'], detail=False, url_path='download_all_project')
    def download_all_project(self, request, *args, **kwargs):
        days_before = int(request.GET.get("days_before"))
        return self.git_master_service.download_all_project(days_before)

"""
pipeline {
 agent {
    label 'vmx'
}
    
    stages {

        stage('刷新项目状态') {
            steps {
                script {
                    def response = httpRequest(url: 'http://***************:8031/mantis/code_fetcher/git_master_view/refresh_all_project/', acceptType: 'APPLICATION_JSON')
                    def json = readJSON(text: response.content)
                    if (json.result) {
                        echo json.message.toString()
                    } else {
                        error '失败'
                    }
                }
            }
        }

        stage('更新项目文件') {
            steps {
                script {
                    def response = httpRequest(url: 'http://***************:8031/mantis/code_fetcher/git_master_view/download_all_project/', acceptType: 'APPLICATION_JSON')
                    def json = readJSON(text: response.content)
                    if (json.result) {
                        echo json.message.toString()
                    } else {
                        error '失败'
                    }
                }
            }
        }

    }
}

"""
