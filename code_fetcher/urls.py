from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from code_fetcher.service import git_commits_view, git_master_view, git_analyst_view

router = DefaultRouter()

router.register(r'git_commits_view', git_commits_view.GitCommitsView, basename="git_commits_view")
router.register(r'git_master_view', git_master_view.GitMasterView, basename="git_master_view")
router.register(r'git_analyst_view', git_analyst_view.GitAnalystView, basename="git_analyst_view")

urlpatterns = [
    path("", include(router.urls))
]
