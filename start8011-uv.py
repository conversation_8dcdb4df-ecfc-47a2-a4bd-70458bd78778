#!/usr/bin/python310

import os
import subprocess


class Driver:
    log_path = "/data/logs/mantis/mantis-web8011.log"
    mantis_path = "/data/app/mantis"

    def start(self):
        os.system('pwd')
        if os.path.isfile(self.log_path):
            os.system("cat /dev/null > {}".format(self.log_path))
        runserver_cmd = "nohup uv run {}/manage.py runserver 0.0.0.0:8011 > {} 2>&1 &".format(
            self.mantis_path,
            self.log_path)
        print(">>>> runserver_cmd: {}".format(runserver_cmd))
        os.system(runserver_cmd)
        # subprocess.Popen(runserver_cmd, shell=True)
    def stop(self):
        os.system('pkill -9 -f "manage.py runserver 0.0.0.0:8011"')

    def restart(self):
        self.stop()
        self.start()


if __name__ == "__main__":
    dr = Driver()
    dr.restart()
