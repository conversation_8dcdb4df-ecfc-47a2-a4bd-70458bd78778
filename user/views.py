# Create your views here.
import json
from django.http import HttpResponse
from rest_framework.viewsets import ViewSet
from django.contrib.auth import authenticate, login
from rest_framework import status
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.views import TokenObtainPairView


class LoginView(TokenObtainPairView):
    authentication_classes = []

    def post(self, request, *args, **kwargs):
        username = request.data.get('username')
        password = request.data.get('password')
        user = authenticate(username=username, password=password)
        if user:
            response = super().post(request, *args, **kwargs)
            # 触发django内置认证器
            login(request, user=user)

            json_data = json.dumps(
                {"data": {"username": username, "token": "Bearer "+response.data.get("access"),
                          "refreshToken": "Bearer "+response.data.get("refresh")
                          },
                 "code": status.HTTP_200_OK,
                 "msg": '登录成功'})
            return HttpResponse(json_data, content_type='application/json')
        else:
            json_data = json.dumps(
                {"code": status.HTTP_401_UNAUTHORIZED,
                 "msg": "用户名或密码错误",
                 "error": {"msg": "用户名或密码错误"}})
            return HttpResponse(json_data, content_type='application/json')


class UserInfo(ViewSet):
    key_username = 'username'
    key_password = 'password'
    authentication_classes = [JWTAuthentication]

    def list(self, request, *args, **kwargs):
        json_data = json.dumps(
            {"data": {"userId": 1, "userName": str(request.user), "userRole": "admin"},
             "code": status.HTTP_200_OK,
             "msg": '查询成功'})
        return HttpResponse(json_data, content_type='application/json')
