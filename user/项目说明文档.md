# User 模块项目说明文档

## 项目概述

`user` 模块是 Mantis 系统中的用户认证和授权核心模块，负责用户身份验证、会话管理、权限控制和用户信息管理。该模块为整个 Mantis 系统提供了统一的用户认证服务，支持JWT令牌和会话双重认证机制，确保系统的安全性和用户体验。

### 核心功能

- **用户认证**：基于用户名密码的身份验证
- **JWT令牌管理**：生成和验证JWT访问令牌
- **会话管理**：基于Django会话的状态管理
- **用户信息服务**：提供用户基本信息查询
- **权限验证**：统一的权限检查机制
- **认证中间件**：自定义认证处理逻辑

## 技术架构

```
┌─────────────────────────────────────────────────────────────┐
│                     User 认证模块                          │
├─────────────────────────────────────────────────────────────┤
│  API接口层 (views.py)                                       │
│  ├── LoginView                 # JWT登录认证接口            │
│  │   ├── TokenObtainPairView    # 继承JWT视图               │
│  │   ├── 用户名密码验证         # authenticate()             │
│  │   └── JWT令牌生成           # get_tokens_for_user()      │
│  └── UserInfo                  # 用户信息查询接口           │
│      ├── 用户基本信息           # ID, username, role         │
│      └── 认证状态验证           # 登录状态检查               │
├─────────────────────────────────────────────────────────────┤
│  认证中间件层 (authentication.py)                          │
│  ├── LoginAuthentication       # 自定义认证类               │
│  │   ├── BaseAuthentication     # 继承DRF基础认证           │
│  │   ├── 会话Cookie检查         # sessionid验证             │
│  │   ├── 用户登录状态验证       # is_authenticated          │
│  │   └── 异常处理               # NotAuthenticated          │
│  └── 认证流程管理               # authenticate()方法        │
├─────────────────────────────────────────────────────────────┤
│  路由配置层 (urls.py)                                       │
│  ├── /login/                   # 登录接口路由               │
│  ├── /user_info/               # 用户信息接口路由           │
│  └── DefaultRouter             # DRF路由器配置              │
└─────────────────────────────────────────────────────────────┘
```

## 核心模块

### 1. 用户认证视图 (LoginView)

#### 继承关系
```python
class LoginView(TokenObtainPairView):
    # 继承自 rest_framework_simplejwt.views.TokenObtainPairView
    # 提供JWT令牌生成功能
```

#### 主要功能

- **用户身份验证**
  ```python
  def post(self, request, *args, **kwargs):
      # 1. 获取用户名和密码
      username = request.data.get('username')
      password = request.data.get('password')
      
      # 2. 用户认证
      user = authenticate(username=username, password=password)
      
      # 3. 生成JWT令牌
      if user:
          refresh = RefreshToken.for_user(user)
          return Response({
              'refresh': str(refresh),
              'access': str(refresh.access_token),
          })
  ```

- **令牌响应格式**
  ```json
  {
      "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
      "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
  ```

#### 认证流程

```
1. 客户端提交 → username + password
2. 服务端验证 → Django authenticate()
3. 用户验证成功 → 生成JWT令牌对
4. 返回响应 → access_token + refresh_token
5. 客户端存储 → 后续请求携带access_token
```

### 2. 用户信息视图 (UserInfo)

#### 视图类型
```python
class UserInfo(ViewSet):
    # 继承自 rest_framework.viewsets.ViewSet
    # 提供用户信息查询功能
```

#### 主要功能

- **用户信息查询**
  ```python
  def list(self, request):
      # 获取当前认证用户信息
      user = request.user
      
      # 返回用户基本信息
      return Response({
          'id': user.id,
          'username': user.username,
          'role': getattr(user, 'role', 'user'),  # 用户角色
          'is_authenticated': user.is_authenticated
      })
  ```

- **响应数据格式**
  ```json
  {
      "id": 1,
      "username": "admin",
      "role": "admin",
      "is_authenticated": true
  }
  ```

#### 权限要求
- 需要用户已认证（通过JWT或会话）
- 只能查询当前用户自己的信息
- 支持跨域请求（如配置）

### 3. 自定义认证类 (LoginAuthentication)

#### 继承关系
```python
class LoginAuthentication(BaseAuthentication):
    # 继承自 rest_framework.authentication.BaseAuthentication
    # 实现自定义认证逻辑
```

#### 核心方法

- **认证入口方法**
  ```python
  def authenticate(self, request):
      # 1. 检查会话Cookie
      sessionid = request.COOKIES.get('sessionid')
      if not sessionid:
          return None  # 无会话信息，跳过此认证
      
      # 2. 验证会话有效性
      session = SessionStore(session_key=sessionid)
      if not session.exists(sessionid):
          raise NotAuthenticated('会话已过期')
      
      # 3. 获取用户信息
      user_id = session.get('_auth_user_id')
      if user_id:
          user = User.objects.get(id=user_id)
          if user.is_authenticated:
              return (user, None)
      
      # 4. 认证失败
      raise NotAuthenticated('用户未认证')
  ```

#### 认证逻辑

```
1. Cookie检查 → 获取sessionid
2. 会话验证 → 检查会话是否存在和有效
3. 用户提取 → 从会话中获取用户ID
4. 用户验证 → 检查用户状态
5. 认证结果 → 返回(user, auth)或抛出异常
```

#### 异常处理

- **NotAuthenticated异常**
  ```python
  from rest_framework.exceptions import NotAuthenticated
  
  # 会话不存在
  raise NotAuthenticated('会话不存在')
  
  # 会话过期
  raise NotAuthenticated('会话已过期')
  
  # 用户未认证
  raise NotAuthenticated('用户未认证')
  ```

### 4. URL路由配置

#### 路由定义
```python
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import LoginView, UserInfo

# 创建路由器
router = DefaultRouter()
router.register(r'user_info', UserInfo, basename='user_info')

# URL模式
urlpatterns = [
    path('login/', LoginView.as_view(), name='login'),
    path('', include(router.urls)),
]
```

#### 完整URL映射

| URL路径 | 视图类 | HTTP方法 | 功能描述 |
|---------|--------|----------|----------|
| `/user/login/` | LoginView | POST | 用户登录认证 |
| `/user/user_info/` | UserInfo | GET | 获取用户信息 |

## API接口文档

### 1. 用户登录接口

**接口地址**: `POST /user/login/`

**请求参数**:
```json
{
    "username": "用户名",
    "password": "密码"
}
```

**成功响应** (200):
```json
{
    "refresh": "刷新令牌",
    "access": "访问令牌"
}
```

**失败响应** (401):
```json
{
    "detail": "用户名或密码错误"
}
```

**使用示例**:
```bash
curl -X POST http://localhost:8088/user/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "password123"
  }'
```

### 2. 用户信息查询接口

**接口地址**: `GET /user/user_info/`

**请求头**:
```
Authorization: Bearer <access_token>
# 或者
Cookie: sessionid=<session_id>
```

**成功响应** (200):
```json
{
    "id": 1,
    "username": "admin",
    "role": "admin",
    "is_authenticated": true
}
```

**失败响应** (401):
```json
{
    "detail": "用户未认证"
}
```

**使用示例**:
```bash
# 使用JWT令牌
curl -X GET http://localhost:8088/user/user_info/ \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."

# 使用会话Cookie
curl -X GET http://localhost:8088/user/user_info/ \
  -H "Cookie: sessionid=abc123def456"
```

## 认证机制

### 1. JWT令牌认证

#### 令牌类型
- **Access Token**: 短期访问令牌（默认5分钟）
- **Refresh Token**: 长期刷新令牌（默认1天）

#### 令牌使用流程
```
1. 用户登录 → 获取令牌对
2. API请求 → 携带Access Token
3. 令牌验证 → 服务端验证签名和有效期
4. 令牌过期 → 使用Refresh Token获取新令牌
5. 刷新失败 → 重新登录
```

#### 令牌配置
```python
# settings.py
from datetime import timedelta

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=5),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
}
```

### 2. 会话认证

#### 会话机制
- **Session Store**: Django默认会话存储
- **Session Cookie**: 浏览器会话Cookie
- **Session Data**: 服务端会话数据

#### 会话配置
```python
# settings.py
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
SESSION_COOKIE_AGE = 3600  # 1小时
SESSION_COOKIE_SECURE = True  # HTTPS环境
SESSION_COOKIE_HTTPONLY = True  # 防止XSS
```

### 3. 双重认证支持

#### 认证优先级
```
1. JWT认证 → DRF默认JWT认证器
2. 会话认证 → 自定义LoginAuthentication
3. 匿名访问 → 允许部分接口匿名访问
```

#### 认证配置
```python
# settings.py
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'user.authentication.LoginAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
}
```

## 与其他模块的集成

### 1. 与所有业务模块集成

**认证依赖关系**:
```
user (认证服务) ← 所有业务模块 (认证依赖)
```

**集成方式**:
- **API认证**: 所有API接口都依赖user模块的认证
- **权限控制**: 基于用户角色的权限验证
- **用户信息**: 获取当前操作用户信息

### 2. 与 dev_effective 模块集成

**认证场景**:
```python
# dev_effective/views.py
class DemandView(ViewSet):
    authentication_classes = [JWTAuthentication, LoginAuthentication]
    permission_classes = [IsAuthenticated]
    
    def create(self, request):
        # 获取当前用户
        operator = request.user.username
        # 业务逻辑处理
```

### 3. 与 utest 模块集成

**认证场景**:
```python
# utest/views.py
class ReportAnalysisView(ViewSet):
    def list(self, request):
        # 用户认证检查
        if not request.user.is_authenticated:
            return Response({'error': '需要登录'}, status=401)
        
        # 获取用户相关的测试报告
        reports = TestReport.objects.filter(creator=request.user)
```

### 4. 与 task_mgt 模块集成

**认证场景**:
```python
# task_mgt/external_service.py
class ExternalService:
    def __init__(self, srv_name, operator, params):
        self.operator = operator  # 来自request.user.username
        
    def call_local_service(self):
        # 记录操作人员信息
        ServiceResults.objects.create(
            operator=self.operator,
            # 其他字段
        )
```

### 5. 与前端系统集成

**认证流程**:
```javascript
// 前端登录
const login = async (username, password) => {
    const response = await fetch('/user/login/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password })
    });
    
    const data = await response.json();
    
    // 存储令牌
    localStorage.setItem('access_token', data.access);
    localStorage.setItem('refresh_token', data.refresh);
};

// API请求拦截器
axios.interceptors.request.use(config => {
    const token = localStorage.getItem('access_token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});
```

## 安全特性

### 1. 密码安全

- **密码哈希**: Django默认PBKDF2算法
- **盐值加密**: 自动生成随机盐值
- **密码策略**: 可配置密码复杂度要求

```python
# settings.py
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 8,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]
```

### 2. 令牌安全

- **签名验证**: HMAC-SHA256签名算法
- **有效期控制**: 短期访问令牌，定期刷新
- **令牌轮换**: 刷新时生成新的令牌对
- **黑名单机制**: 撤销的令牌加入黑名单

### 3. 会话安全

- **HttpOnly Cookie**: 防止XSS攻击
- **Secure Cookie**: HTTPS传输
- **SameSite属性**: 防止CSRF攻击
- **会话过期**: 自动清理过期会话

```python
# settings.py
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
CSRF_COOKIE_SECURE = True
```

### 4. API安全

- **CORS配置**: 跨域请求控制
- **速率限制**: API调用频率限制
- **输入验证**: 请求参数验证
- **异常处理**: 安全的错误信息返回

```python
# settings.py
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "https://mantis.example.com",
]

REST_FRAMEWORK = {
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle'
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '100/hour',
        'user': '1000/hour'
    }
}
```

## 配置说明

### JWT配置

```python
# settings.py
from datetime import timedelta

SIMPLE_JWT = {
    # 访问令牌有效期
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=5),
    
    # 刷新令牌有效期
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),
    
    # 是否轮换刷新令牌
    'ROTATE_REFRESH_TOKENS': True,
    
    # 轮换后是否将旧令牌加入黑名单
    'BLACKLIST_AFTER_ROTATION': True,
    
    # 算法
    'ALGORITHM': 'HS256',
    
    # 签名密钥
    'SIGNING_KEY': SECRET_KEY,
    
    # 令牌类型
    'AUTH_HEADER_TYPES': ('Bearer',),
    
    # 用户ID字段
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
}
```

### 认证配置

```python
# settings.py
REST_FRAMEWORK = {
    # 默认认证类
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'user.authentication.LoginAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ],
    
    # 默认权限类
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    
    # 异常处理
    'EXCEPTION_HANDLER': 'rest_framework.views.exception_handler',
}
```

### 会话配置

```python
# settings.py

# 会话引擎
SESSION_ENGINE = 'django.contrib.sessions.backends.db'

# 会话有效期（秒）
SESSION_COOKIE_AGE = 3600

# 会话Cookie名称
SESSION_COOKIE_NAME = 'sessionid'

# 会话Cookie域
SESSION_COOKIE_DOMAIN = None

# 会话Cookie路径
SESSION_COOKIE_PATH = '/'

# HTTPS传输
SESSION_COOKIE_SECURE = True

# HttpOnly属性
SESSION_COOKIE_HTTPONLY = True

# SameSite属性
SESSION_COOKIE_SAMESITE = 'Lax'

# 浏览器关闭时过期
SESSION_EXPIRE_AT_BROWSER_CLOSE = False

# 每次请求保存会话
SESSION_SAVE_EVERY_REQUEST = False
```

## 部署指南

### 环境要求
- Python 3.8+
- Django 3.2+
- Django REST Framework 3.12+
- djangorestframework-simplejwt 4.8+

### 安装步骤

1. **依赖安装**
   ```bash
   pip install djangorestframework
   pip install djangorestframework-simplejwt
   pip install django-cors-headers  # 如需CORS支持
   ```

2. **Django配置**
   ```python
   # settings.py
   INSTALLED_APPS = [
       'django.contrib.auth',
       'django.contrib.sessions',
       'rest_framework',
       'rest_framework_simplejwt',
       'corsheaders',  # 可选
       'user',
   ]
   
   MIDDLEWARE = [
       'corsheaders.middleware.CorsMiddleware',  # 可选
       'django.middleware.security.SecurityMiddleware',
       'django.contrib.sessions.middleware.SessionMiddleware',
       'django.middleware.common.CommonMiddleware',
       'django.middleware.csrf.CsrfViewMiddleware',
       'django.contrib.auth.middleware.AuthenticationMiddleware',
       'django.contrib.messages.middleware.MessageMiddleware',
   ]
   ```

3. **URL配置**
   ```python
   # mantis/urls.py
   urlpatterns = [
       path('admin/', admin.site.urls),
       path('user/', include('user.urls')),
       # 其他URL配置
   ]
   ```

4. **数据库迁移**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

5. **创建超级用户**
   ```bash
   python manage.py createsuperuser
   ```

### 生产环境配置

1. **HTTPS配置**
   ```python
   # settings.py
   SECURE_SSL_REDIRECT = True
   SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
   SESSION_COOKIE_SECURE = True
   CSRF_COOKIE_SECURE = True
   ```

2. **安全密钥**
   ```python
   # settings.py
   import os
   SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY')
   ```

3. **数据库配置**
   ```python
   # settings.py
   DATABASES = {
       'default': {
           'ENGINE': 'django.db.backends.mysql',
           'NAME': os.environ.get('DB_NAME'),
           'USER': os.environ.get('DB_USER'),
           'PASSWORD': os.environ.get('DB_PASSWORD'),
           'HOST': os.environ.get('DB_HOST'),
           'PORT': os.environ.get('DB_PORT'),
       }
   }
   ```

4. **缓存配置**
   ```python
   # settings.py
   CACHES = {
       'default': {
           'BACKEND': 'django.core.cache.backends.redis.RedisCache',
           'LOCATION': 'redis://127.0.0.1:6379/1',
       }
   }
   ```

## 监控和日志

### 日志配置

```python
# settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/data/logs/mantis/user.log',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'user': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### 关键监控指标

- **登录成功率**: 监控用户登录成功的比例
- **令牌刷新频率**: 监控JWT令牌的刷新频率
- **会话活跃度**: 监控活跃会话数量
- **认证失败次数**: 监控认证失败的频率
- **API响应时间**: 监控认证接口的响应时间

### 告警配置

```python
# 自定义监控中间件
class AuthenticationMonitoringMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        start_time = time.time()
        response = self.get_response(request)
        
        # 记录认证相关指标
        if request.path.startswith('/user/'):
            duration = time.time() - start_time
            logger.info(f"Auth API: {request.path}, Status: {response.status_code}, Duration: {duration}")
        
        return response
```

## 常见问题排查

### 1. JWT令牌验证失败

**现象**: 返回401 Unauthorized
**排查步骤**:
1. 检查令牌格式是否正确
   ```bash
   # 正确格式
   Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
   ```
2. 验证令牌是否过期
3. 检查签名密钥是否一致
4. 确认令牌是否在黑名单中

### 2. 会话认证失败

**现象**: 会话Cookie存在但认证失败
**排查步骤**:
1. 检查会话是否过期
   ```python
   from django.contrib.sessions.models import Session
   Session.objects.filter(expire_date__lt=timezone.now()).delete()
   ```
2. 验证Cookie域和路径设置
3. 检查会话数据完整性
4. 确认用户状态是否正常

### 3. CORS跨域问题

**现象**: 浏览器报CORS错误
**排查步骤**:
1. 检查CORS配置
   ```python
   CORS_ALLOWED_ORIGINS = [
       "http://localhost:3000",
   ]
   ```
2. 验证预检请求处理
3. 检查请求头设置
4. 确认中间件顺序

### 4. 密码验证失败

**现象**: 正确密码无法登录
**排查步骤**:
1. 检查用户是否存在且激活
   ```python
   user = User.objects.get(username='admin')
   print(user.is_active)
   ```
2. 验证密码哈希算法
3. 检查密码验证器配置
4. 确认数据库连接正常

## 性能优化

### 1. 缓存优化

```python
# 用户信息缓存
from django.core.cache import cache

class UserInfo(ViewSet):
    def list(self, request):
        cache_key = f"user_info_{request.user.id}"
        user_info = cache.get(cache_key)
        
        if not user_info:
            user_info = {
                'id': request.user.id,
                'username': request.user.username,
                'role': getattr(request.user, 'role', 'user'),
            }
            cache.set(cache_key, user_info, timeout=300)  # 5分钟缓存
        
        return Response(user_info)
```

### 2. 数据库优化

```python
# 会话清理任务
from django.core.management.base import BaseCommand
from django.contrib.sessions.models import Session
from django.utils import timezone

class Command(BaseCommand):
    def handle(self, *args, **options):
        # 清理过期会话
        expired_sessions = Session.objects.filter(
            expire_date__lt=timezone.now()
        )
        count = expired_sessions.count()
        expired_sessions.delete()
        self.stdout.write(f"Deleted {count} expired sessions")
```

### 3. 令牌优化

```python
# 自定义JWT载荷
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer

class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        
        # 添加自定义声明
        token['username'] = user.username
        token['role'] = getattr(user, 'role', 'user')
        
        return token
```

## 扩展开发

### 添加新的认证方式

1. **API Key认证**
   ```python
   from rest_framework.authentication import BaseAuthentication
   
   class APIKeyAuthentication(BaseAuthentication):
       def authenticate(self, request):
           api_key = request.META.get('HTTP_X_API_KEY')
           if not api_key:
               return None
           
           try:
               user = User.objects.get(api_key=api_key)
               return (user, None)
           except User.DoesNotExist:
               raise AuthenticationFailed('Invalid API key')
   ```

2. **OAuth2集成**
   ```python
   # 使用django-oauth-toolkit
   pip install django-oauth-toolkit
   
   # settings.py
   INSTALLED_APPS = [
       'oauth2_provider',
   ]
   
   REST_FRAMEWORK = {
       'DEFAULT_AUTHENTICATION_CLASSES': [
           'oauth2_provider.contrib.rest_framework.OAuth2Authentication',
       ],
   }
   ```

### 添加用户角色管理

1. **扩展用户模型**
   ```python
   from django.contrib.auth.models import AbstractUser
   
   class CustomUser(AbstractUser):
       ROLE_CHOICES = [
           ('admin', 'Administrator'),
           ('manager', 'Manager'),
           ('user', 'User'),
       ]
       role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='user')
       department = models.CharField(max_length=50, blank=True)
   ```

2. **基于角色的权限**
   ```python
   from rest_framework.permissions import BasePermission
   
   class IsAdminOrReadOnly(BasePermission):
       def has_permission(self, request, view):
           if request.method in ['GET', 'HEAD', 'OPTIONS']:
               return request.user.is_authenticated
           return request.user.is_authenticated and request.user.role == 'admin'
   ```

### 添加多因素认证

1. **TOTP支持**
   ```python
   import pyotp
   
   class TOTPAuthentication(BaseAuthentication):
       def authenticate(self, request):
           username = request.data.get('username')
           password = request.data.get('password')
           totp_code = request.data.get('totp_code')
           
           user = authenticate(username=username, password=password)
           if user and self.verify_totp(user, totp_code):
               return (user, None)
           
           raise AuthenticationFailed('Invalid credentials or TOTP code')
       
       def verify_totp(self, user, code):
           totp = pyotp.TOTP(user.totp_secret)
           return totp.verify(code)
   ```

## 版本历史

### v1.0.0 (当前版本)
- 基础用户认证功能
- JWT令牌支持
- 会话认证支持
- 用户信息查询
- 自定义认证中间件

### 后续规划
- 增加OAuth2支持
- 实现多因素认证
- 增强用户角色管理
- 支持API Key认证
- 增加用户行为审计
- 实现单点登录(SSO)

## 联系信息

- **项目负责人**: 基础架构团队
- **技术支持**: 通过内部工单系统提交
- **文档更新**: 2024年12月

---

*本文档随项目版本更新，请以最新版本为准。*