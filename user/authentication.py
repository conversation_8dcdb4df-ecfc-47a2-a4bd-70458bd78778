import json

from django.contrib.sessions.backends.db import SessionS<PERSON>
from rest_framework.authentication import BaseAuthentication
from rest_framework import exceptions
from rest_framework.exceptions import AuthenticationFailed
from datetime import datetime, timedelta

from mantis.settings import SESSION_COOKIE_NAME, ConsSessionKeys, AUTH


class LoginAuthentication(BaseAuthentication):
    """
    All authentication classes should extend BaseAuthentication.
    """

    def authenticate(self, request):
        """
       Authenticate the request and return a two-tuple of (user, token).
       """

        cookies = request.COOKIES
        session = SessionStore()
        is_login = False
        if SESSION_COOKIE_NAME in cookies and session.exists(cookies[SESSION_COOKIE_NAME]):
            session = SessionStore(cookies[SESSION_COOKIE_NAME])
            session.load()
            session_items = session.items()
            if session_items:
                for row in session_items:
                    if row[0] == ConsSessionKeys.username:
                        user = row[1]
                    if row[0] == ConsSessionKeys.is_login:
                        is_login = row[1]
                if is_login:
                    return (user, None)
                else:
                    raise exceptions.NotAuthenticated('未登入')
            else:
                raise exceptions.NotAuthenticated('session过期')
        else:
            raise exceptions.NotAuthenticated('未登入')

    def authenticate_header(self, request):
        """
        Return a string to be used as the value of the `WWW-Authenticate`
        header in a `401 Unauthenticated` response, or `None` if the
        authentication scheme should return `403 Permission Denied` responses.
        """
        pass
