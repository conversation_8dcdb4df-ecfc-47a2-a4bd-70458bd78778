# Create your views here
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet
from rest_framework import status
from datetime import date, timedelta
from mantis import settings
from task_mgt import external_service
from utest.data.utest_report_suites_dao import ReportSuitesDao
from utest.utest_ser import get_utest_result, get_batch_num, get_batch_app_report_info, get_utest_report_info, \
    get_utest_result_by_report_id


class UnitTestReport(ViewSet):
    """
    单元测试报告分析接口
    """

    # post方法
    def create(self, request, *args, **kwargs):
        business_name = request.data.get('business_name')
        app_name = request.data.get('app_name')
        iteration_id = request.data.get('iteration_id')
        lib_repo_url = request.data.get('lib_repo_url')
        batch_num = request.data.get('batch_num', 0)

        params = {"business_name": business_name, "app_name": app_name,
                  "iteration_id": iteration_id, "lib_repo_url": lib_repo_url}
        if batch_num > 0:
            params.update({"batch_num": batch_num})

        user = str(request.user)

        stat, sid = external_service.ExternalService(business_name, user, params, 'sync').call_local_service()

        if stat == 0:
            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.success_dict('单元测试结果数据分析成功！详细结果可查看日志，日志id为：{}'.format(sid)))
        else:
            return Response(status=status.HTTP_200_OK, data=settings.ApiResult.failed_dict('服务异常！请联系PA排查！'))

    # get方法
    def list(self, request, *args, **kwargs):
        pass


class UnitTestResult(ViewSet):
    """
    单元测试结果检测接口
    """
    authentication_classes = []

    def list(self, request):
        app_name = request.GET.get('app_name')
        iteration_id = request.GET.get('iteration_id')
        lib_repo_url = request.GET.get('lib_repo_url')

        report_result = get_utest_report_info(app_name, iteration_id, lib_repo_url)

        for item in report_result:
            unit_report_id = item[0]
            result = get_utest_result_by_report_id(unit_report_id)

            for row in result:
                # 通过规则：总数=通过数+跳过数
                if row[5] + row[6] + row[7] + row[8] + row[9] == row[7] + row[8]:
                    return Response(status=status.HTTP_200_OK,
                                    data=settings.ApiResult.success_dict(
                                        '迭代{}的应用{}单元测试结果检测通过！'.format(iteration_id, app_name)))
                else:
                    return Response(status=status.HTTP_200_OK,
                                    data=settings.ApiResult.failed_dict(
                                        '迭代{}的应用{}单元测试结果检测不通过！'.format(iteration_id, app_name)))

            return Response(status=status.HTTP_200_OK,
                            data=settings.ApiResult.success_dict(
                                '迭代{}的应用{}单元测试结果通过，测试案例为0！'.format(iteration_id, app_name)))

        return Response(status=status.HTTP_200_OK,
                        data=settings.ApiResult.failed_dict(
                            '迭代{}的应用{}一次单元测试都没做过，检测结果不通过！'.format(iteration_id, app_name)))

    @action(methods=['get'], detail=False, url_name='query_current_passed_api_list')
    def query_current_passed_api_list(self, request):
        report_suites_dao = ReportSuitesDao()
        suites_list = report_suites_dao.query_current_suites()
        result = []
        for i, val in enumerate(suites_list):
            row = {}
            row["test_class"] = val.test_class
            row["module_name"] = val.module_name
            row["team_alias"] = val.team_alias
            result.append(row)
        return Response(status=status.HTTP_200_OK, data=result)


class BatchUnitTestReport(ViewSet):
    """
    单元测试结果批量分析接口
    """

    def create(self, request):
        business_name = request.data.get('business_name')
        batch_num = request.data.get('batch_num')

        if not batch_num:
            batch_num = get_batch_num()

        from_date = (date.today() + timedelta(days=-15)).strftime("%Y-%m-%d")

        batch_iteration_id = "unit0.0.0"

        batch_app_report_info = get_batch_app_report_info(batch_iteration_id, from_date, batch_num)

        params = {"business_name": business_name, "batch_app_report_info_list": batch_app_report_info}

        user = str(request.user)

        stat, sid = external_service.ExternalService(business_name, user, params, 'sync').call_local_service()

        if stat == 0:
            return Response(status=status.HTTP_200_OK, data=settings.ApiResult.success_dict('单元测试结果数据批量分析成功！'))
        else:
            return Response(status=status.HTTP_200_OK, data=settings.ApiResult.failed_dict('单元测试结果数据批量分析失败！'))