from django.urls import path, include
from utest import views
from rest_framework.routers import DefaultRouter

router = DefaultRouter()

router.register('analyze_unit_test_data', views.UnitTestReport, basename="analyze_unit_test_data")
router.register('check_unit_test_result', views.UnitTestResult, basename="check_unit_test_result")
router.register('batch_analyze_unit_test_data', views.BatchUnitTestReport, basename="batch_analyze_unit_test_data")

urlpatterns = [
    path("", include(router.urls))
]
