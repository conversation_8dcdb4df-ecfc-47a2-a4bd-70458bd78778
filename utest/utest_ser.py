from django.db import connection
from mantis.settings import logger


def get_utest_result(app_name, iteration_id):
    sql = '''
            select * from utest_report_behaviors where unit_report_id = (
            select max(id) from utest_report_info t 
            where t.module_name = '{app_name}' and t.iteration_id = '{iteration_id}');
        '''.format(iteration_id=iteration_id, app_name=app_name)

    logger.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor.fetchall()


def get_utest_result_by_report_id(report_id):
    sql = '''
            SELECT * FROM utest_report_behaviors WHERE unit_report_id = '{}';
        '''.format(report_id)

    logger.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor.fetchall()


def get_batch_num():
    sql = '''
            select MAX(batch_num) from utest_report_info;
          '''
    cursor = connection.cursor()
    cursor.execute(sql)
    for row in cursor.fetchall():
        return row[0] + 1


def get_batch_app_report_info(iteration_id, from_date, batch_num):
    sql = '''
            select ri.module_name, ri.iteration_id, ri.lib_repo_url, ri.create_time 
            from spider_product_mgt_test_report_info ri where (module_name, iteration_id, create_time) in (
                select t.module_name, iteration_id, max(t.create_time) from spider_product_mgt_test_report_info t 
                where t.iteration_id like '%_{}' and t.create_time > '{}' and t.lib_repo_url is not null
                GROUP BY module_name, iteration_id );
          '''.format(iteration_id, from_date)

    logger.info(sql)

    cursor = connection.cursor()
    cursor.execute(sql)
    app_report_info_list = []
    for row in cursor.fetchall():
        result = {"app_name": row[0], "iteration_id": row[1], "lib_repo_url": row[2], "batch_num": batch_num}
        app_report_info_list.append(result)

    return app_report_info_list


def get_utest_report_info(app_name, iteration_id, lib_repo_url):
    sql = '''
            select * from utest_report_info t 
            where t.iteration_id = '{}' and t.module_name = '{}' and t.lib_repo_url = '{}'
        '''.format(iteration_id, app_name, lib_repo_url)

    cursor = connection.cursor()
    cursor.execute(sql)

    return cursor.fetchall()
