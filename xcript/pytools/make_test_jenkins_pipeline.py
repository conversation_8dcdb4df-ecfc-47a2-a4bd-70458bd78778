def generate_jenkins_stages(num_stages):
    result = []
    for i in range(1, num_stages + 1):
        result.append(f"""        stage('Stage {i}') {{
            parallel {{
                stage('Step {i}-1') {{
                    steps {{
                        echo '执行成功'
                    }}
                }}
                stage('Step {i}-2') {{
                    steps {{
                        echo '执行成功'
                    }}
                }}
                stage('Step {i}-3') {{
                    steps {{
                        echo '执行成功'
                    }}
                }}
            }}
        }}""")
    return "\n".join(result)

if __name__ == "__main__":
    num_stages = 150  # 你可以修改这个数字
    print(generate_jenkins_stages(num_stages))