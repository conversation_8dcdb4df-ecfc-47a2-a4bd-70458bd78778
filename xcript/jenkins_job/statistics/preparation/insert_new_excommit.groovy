"""
        stage('删多-一次提交') {
            steps {
                sh label: '', script: '''python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/dbops/mysql_helper.py '**************' 3306 'admin' '}R38d6w%2MXo' 'mantis' "${insert_new_excommit}" '''
            }
        }
"""


"""
INSERT INTO mantis.code_stat_git_commits_diff_excommit
	(commit, username, total)
SELECT
	commit, username, sum(cd.add_counts_effect) as total
FROM
	mantis.code_stat_git_commits_diff cd
where datetime > date_add(now(), interval - 15 day) and cd.username <> 'howbuyscm' and is_merge = 'n'
and not exists
(
select 1 from mantis.code_stat_git_commits_diff_excommit cde where cde.commit = cd.commit and cde.username = cd.username
)
group by commit, username having sum(cd.add_counts_effect) > 3000
"""
