-- ----------------------------
-- 2-1、tapd故事实体表：tapd_entry_story
-- 说明：
-- ----------------------------
select *
from tapd_entry_story;

DROP TABLE IF EXISTS tapd_entry_story;
CREATE TABLE tapd_entry_story
(
    create_user                     VARCHAR(20) COMMENT '创建人',
    create_time                     DATETIME(0) COMMENT '创建时间',
    update_user                     VARCHAR(20) COMMENT '修改人',
    update_time                     DATETIME(0) COMMENT '修改时间',
    stamp                           BIGINT(20) DEFAULT 0 COMMENT '版本',
    id                              BIGINT(20) AUTO_INCREMENT,
    ins_batch_number                BIGINT(20) COMMENT '插入时批次号',
    upd_batch_number                BIGINT(20) COMMENT '最后一次更新时批次号',
    del_batch_number                BIGINT(20) COMMENT '最后一次删除时批次号',
    del_user                        VARCHAR(20) COMMENT '最后一次删除人',
    del_time                        DATETIME(0) COMMENT '最后一次删除时间',
    rec_batch_number                BIGINT(20) COMMENT '最后一次恢复时批次号',
    rec_user                        VARCHAR(20) COMMENT '最后一次恢复人',
    rec_time                        DATETIME(0) COMMENT '最后一次恢复时间',

    entry_status                    INT(4) COMMENT '数据实体状态：1-新记录、2-更新过、3-已删除',
    entry_desc                      VARCHAR(255) COMMENT '数据实体说明',

    res_json                        JSON COMMENT '请求返回Json',
    tapd_workspace_id               BIGINT(20) COMMENT '项目ID',
    tapd_entry_id                   BIGINT(20) NOT NULL COMMENT '数据实体ID（字符非数字）',

    tapd_story_workitem_type_id     BIGINT(20) COMMENT '故事类别',
    tapd_story_name                 VARCHAR(200) COMMENT '故事标题',
    tapd_story_description          VARCHAR(255) COMMENT '描述（备用）',
    tapd_story_creator              VARCHAR(100) COMMENT '创建人',
    tapd_story_created              DATETIME(0) COMMENT '创建时间',
    tapd_story_modified             DATETIME(0) COMMENT '最后修改时间',
    tapd_story_status               VARCHAR(20) COMMENT '故事状态',
    tapd_story_owner                VARCHAR(100) COMMENT '处理人',
    tapd_story_cc                   VARCHAR(100) COMMENT '抄送人',
    tapd_story_begin                DATE COMMENT '预计开始',
    tapd_story_due                  DATE COMMENT '预计结束',
    tapd_story_size                 INT(4) COMMENT '规模',
    tapd_story_priority             VARCHAR(50) COMMENT '优先级',
    tapd_story_developer            VARCHAR(100) COMMENT '开发人员',
    tapd_story_iteration_id         BIGINT(20) COMMENT '迭代ID',
    tapd_story_test_focus           VARCHAR(100) COMMENT '测试重点',
    tapd_story_type                 VARCHAR(100) COMMENT '需求类型',
    tapd_story_source               VARCHAR(100) COMMENT '需求来源',
    tapd_story_module               VARCHAR(100) COMMENT '模块',
    tapd_story_version              VARCHAR(100) COMMENT '版本',
    tapd_story_completed            DATETIME(0) COMMENT '完成时间',
    tapd_story_category_id          BIGINT(20) COMMENT '需求分类',
    tapd_story_path                 VARCHAR(500) COMMENT '路径',
    tapd_story_parent_id            BIGINT(20) COMMENT '父ID',
    tapd_story_children_id          VARCHAR(500) COMMENT '子ID',
    tapd_story_ancestor_id          BIGINT(20) COMMENT '祖父ID',
    tapd_story_level                VARCHAR(20) COMMENT '层级',
    tapd_story_business_value       INT(4) COMMENT '业务价值',
    tapd_story_effort               VARCHAR(20) COMMENT '预估工时',
    tapd_story_effort_completed     VARCHAR(20) COMMENT '完成工时',
    tapd_story_exceed               FLOAT(6, 2) COMMENT '超出工时',
    tapd_story_remain               FLOAT(6, 2) COMMENT '剩余工时',
    tapd_story_release_id           BIGINT(20) COMMENT '发布计划',
    tapd_story_bug_id               BIGINT(20) COMMENT '缺陷ID',
    tapd_story_templated_id         BIGINT(20) COMMENT '模板ID',
    tapd_story_created_from         VARCHAR(20) COMMENT '需求创建来源',
    tapd_story_feature              VARCHAR(20) COMMENT '特性',
    tapd_story_label                VARCHAR(20) COMMENT '标签查询',
    tapd_story_progress             VARCHAR(20) COMMENT '进步',
    tapd_story_is_archived          TINYINT(1) COMMENT '是否归档',
    tapd_story_tech_risk            VARCHAR(20) COMMENT '技术风险',
    tapd_story_priority_label       VARCHAR(50) COMMENT '优先级标签',

    tapd_story_custom_field_one     VARCHAR(100) COMMENT '自定义字段001',
    tapd_story_custom_field_two     VARCHAR(100) COMMENT '自定义字段002',
    tapd_story_custom_field_three   VARCHAR(100) COMMENT '自定义字段003',
    tapd_story_custom_field_four    VARCHAR(100) COMMENT '自定义字段004',
    tapd_story_custom_field_five    VARCHAR(100) COMMENT '自定义字段005',
    tapd_story_custom_field_six     VARCHAR(100) COMMENT '自定义字段006',
    tapd_story_custom_field_seven   VARCHAR(100) COMMENT '自定义字段007',
    tapd_story_custom_field_eight   VARCHAR(100) COMMENT '自定义字段008',
    tapd_story_custom_field_9       VARCHAR(100) COMMENT '自定义字段009',
    tapd_story_custom_field_10      VARCHAR(100) COMMENT '自定义字段010',
    tapd_story_custom_field_11      VARCHAR(100) COMMENT '自定义字段011',
    tapd_story_custom_field_12      VARCHAR(100) COMMENT '自定义字段012',
    tapd_story_custom_field_13      VARCHAR(100) COMMENT '自定义字段013',
    tapd_story_custom_field_14      VARCHAR(100) COMMENT '自定义字段014',
    tapd_story_custom_field_15      VARCHAR(100) COMMENT '自定义字段015',
    tapd_story_custom_field_16      VARCHAR(100) COMMENT '自定义字段016',
    tapd_story_custom_field_17      VARCHAR(100) COMMENT '自定义字段017',
    tapd_story_custom_field_18      VARCHAR(100) COMMENT '自定义字段018',
    tapd_story_custom_field_19      VARCHAR(100) COMMENT '自定义字段019',
    tapd_story_custom_field_20      VARCHAR(100) COMMENT '自定义字段020',
    tapd_story_custom_field_21      VARCHAR(100) COMMENT '自定义字段021',
    tapd_story_custom_field_22      VARCHAR(100) COMMENT '自定义字段022',
    tapd_story_custom_field_23      VARCHAR(100) COMMENT '自定义字段023',
    tapd_story_custom_field_24      VARCHAR(100) COMMENT '自定义字段024',
    tapd_story_custom_field_25      VARCHAR(100) COMMENT '自定义字段025',
    tapd_story_custom_field_26      VARCHAR(100) COMMENT '自定义字段026',
    tapd_story_custom_field_27      VARCHAR(100) COMMENT '自定义字段027',
    tapd_story_custom_field_28      VARCHAR(100) COMMENT '自定义字段028',
    tapd_story_custom_field_29      VARCHAR(100) COMMENT '自定义字段029',
    tapd_story_custom_field_30      VARCHAR(100) COMMENT '自定义字段030',
    tapd_story_custom_field_31      VARCHAR(100) COMMENT '自定义字段031',
    tapd_story_custom_field_32      VARCHAR(100) COMMENT '自定义字段032',
    tapd_story_custom_field_33      VARCHAR(100) COMMENT '自定义字段033',
    tapd_story_custom_field_34      VARCHAR(100) COMMENT '自定义字段034',
    tapd_story_custom_field_35      VARCHAR(100) COMMENT '自定义字段035',
    tapd_story_custom_field_36      VARCHAR(100) COMMENT '自定义字段036',
    tapd_story_custom_field_37      VARCHAR(100) COMMENT '自定义字段037',
    tapd_story_custom_field_38      VARCHAR(100) COMMENT '自定义字段038',
    tapd_story_custom_field_39      VARCHAR(100) COMMENT '自定义字段039',
    tapd_story_custom_field_40      VARCHAR(100) COMMENT '自定义字段040',
    tapd_story_custom_field_41      VARCHAR(100) COMMENT '自定义字段041',
    tapd_story_custom_field_42      VARCHAR(100) COMMENT '自定义字段042',
    tapd_story_custom_field_43      VARCHAR(100) COMMENT '自定义字段043',
    tapd_story_custom_field_44      VARCHAR(100) COMMENT '自定义字段044',
    tapd_story_custom_field_45      VARCHAR(100) COMMENT '自定义字段045',
    tapd_story_custom_field_46      VARCHAR(100) COMMENT '自定义字段046',
    tapd_story_custom_field_47      VARCHAR(100) COMMENT '自定义字段047',
    tapd_story_custom_field_48      VARCHAR(100) COMMENT '自定义字段048',
    tapd_story_custom_field_49      VARCHAR(100) COMMENT '自定义字段049',
    tapd_story_custom_field_50      VARCHAR(100) COMMENT '自定义字段050',
    tapd_story_custom_field_51      VARCHAR(100) COMMENT '自定义字段051',
    tapd_story_custom_field_52      VARCHAR(100) COMMENT '自定义字段052',
    tapd_story_custom_field_53      VARCHAR(100) COMMENT '自定义字段053',
    tapd_story_custom_field_54      VARCHAR(100) COMMENT '自定义字段054',
    tapd_story_custom_field_55      VARCHAR(100) COMMENT '自定义字段055',
    tapd_story_custom_field_56      VARCHAR(100) COMMENT '自定义字段056',
    tapd_story_custom_field_57      VARCHAR(100) COMMENT '自定义字段057',
    tapd_story_custom_field_58      VARCHAR(100) COMMENT '自定义字段058',
    tapd_story_custom_field_59      VARCHAR(100) COMMENT '自定义字段059',
    tapd_story_custom_field_60      VARCHAR(100) COMMENT '自定义字段060',
    tapd_story_custom_field_61      VARCHAR(100) COMMENT '自定义字段061',
    tapd_story_custom_field_62      VARCHAR(100) COMMENT '自定义字段062',
    tapd_story_custom_field_63      VARCHAR(100) COMMENT '自定义字段063',
    tapd_story_custom_field_64      VARCHAR(100) COMMENT '自定义字段064',
    tapd_story_custom_field_65      VARCHAR(100) COMMENT '自定义字段065',
    tapd_story_custom_field_66      VARCHAR(100) COMMENT '自定义字段066',
    tapd_story_custom_field_67      VARCHAR(100) COMMENT '自定义字段067',
    tapd_story_custom_field_68      VARCHAR(100) COMMENT '自定义字段068',
    tapd_story_custom_field_69      VARCHAR(100) COMMENT '自定义字段069',
    tapd_story_custom_field_70      VARCHAR(100) COMMENT '自定义字段070',
    tapd_story_custom_field_71      VARCHAR(100) COMMENT '自定义字段071',
    tapd_story_custom_field_72      VARCHAR(100) COMMENT '自定义字段072',
    tapd_story_custom_field_73      VARCHAR(100) COMMENT '自定义字段073',
    tapd_story_custom_field_74      VARCHAR(100) COMMENT '自定义字段074',
    tapd_story_custom_field_75      VARCHAR(100) COMMENT '自定义字段075',
    tapd_story_custom_field_76      VARCHAR(100) COMMENT '自定义字段076',
    tapd_story_custom_field_77      VARCHAR(100) COMMENT '自定义字段077',
    tapd_story_custom_field_78      VARCHAR(100) COMMENT '自定义字段078',
    tapd_story_custom_field_79      VARCHAR(100) COMMENT '自定义字段079',
    tapd_story_custom_field_80      VARCHAR(100) COMMENT '自定义字段080',
    tapd_story_custom_field_81      VARCHAR(100) COMMENT '自定义字段081',
    tapd_story_custom_field_82      VARCHAR(100) COMMENT '自定义字段082',
    tapd_story_custom_field_83      VARCHAR(100) COMMENT '自定义字段083',
    tapd_story_custom_field_84      VARCHAR(100) COMMENT '自定义字段084',
    tapd_story_custom_field_85      VARCHAR(100) COMMENT '自定义字段085',
    tapd_story_custom_field_86      VARCHAR(100) COMMENT '自定义字段086',
    tapd_story_custom_field_87      VARCHAR(100) COMMENT '自定义字段087',
    tapd_story_custom_field_88      VARCHAR(100) COMMENT '自定义字段088',
    tapd_story_custom_field_89      VARCHAR(100) COMMENT '自定义字段089',
    tapd_story_custom_field_90      VARCHAR(100) COMMENT '自定义字段090',
    tapd_story_custom_field_91      VARCHAR(100) COMMENT '自定义字段091',
    tapd_story_custom_field_92      VARCHAR(100) COMMENT '自定义字段092',
    tapd_story_custom_field_93      VARCHAR(100) COMMENT '自定义字段093',
    tapd_story_custom_field_94      VARCHAR(100) COMMENT '自定义字段094',
    tapd_story_custom_field_95      VARCHAR(100) COMMENT '自定义字段095',
    tapd_story_custom_field_96      VARCHAR(100) COMMENT '自定义字段096',
    tapd_story_custom_field_97      VARCHAR(100) COMMENT '自定义字段097',
    tapd_story_custom_field_98      VARCHAR(100) COMMENT '自定义字段098',
    tapd_story_custom_field_99      VARCHAR(100) COMMENT '自定义字段099',
    tapd_story_custom_field_100     VARCHAR(100) COMMENT '自定义字段100',
#     tapd_story_custom_field_101     VARCHAR(50) COMMENT '自定义字段101',
#     tapd_story_custom_field_102     VARCHAR(50) COMMENT '自定义字段102',
#     tapd_story_custom_field_103     VARCHAR(50) COMMENT '自定义字段103',
#     tapd_story_custom_field_104     VARCHAR(50) COMMENT '自定义字段104',
#     tapd_story_custom_field_105     VARCHAR(50) COMMENT '自定义字段105',
#     tapd_story_custom_field_106     VARCHAR(50) COMMENT '自定义字段106',
#     tapd_story_custom_field_107     VARCHAR(50) COMMENT '自定义字段107',
#     tapd_story_custom_field_108     VARCHAR(50) COMMENT '自定义字段108',
#     tapd_story_custom_field_109     VARCHAR(50) COMMENT '自定义字段109',
#     tapd_story_custom_field_110     VARCHAR(50) COMMENT '自定义字段110',
#     tapd_story_custom_field_111     VARCHAR(50) COMMENT '自定义字段111',
#     tapd_story_custom_field_112     VARCHAR(50) COMMENT '自定义字段112',
#     tapd_story_custom_field_113     VARCHAR(50) COMMENT '自定义字段113',
#     tapd_story_custom_field_114     VARCHAR(50) COMMENT '自定义字段114',
#     tapd_story_custom_field_115     VARCHAR(50) COMMENT '自定义字段115',
#     tapd_story_custom_field_116     VARCHAR(50) COMMENT '自定义字段116',
#     tapd_story_custom_field_117     VARCHAR(50) COMMENT '自定义字段117',
#     tapd_story_custom_field_118     VARCHAR(50) COMMENT '自定义字段118',
#     tapd_story_custom_field_119     VARCHAR(50) COMMENT '自定义字段119',
#     tapd_story_custom_field_120     VARCHAR(50) COMMENT '自定义字段120',
#     tapd_story_custom_field_121     VARCHAR(50) COMMENT '自定义字段121',
#     tapd_story_custom_field_122     VARCHAR(50) COMMENT '自定义字段122',
#     tapd_story_custom_field_123     VARCHAR(50) COMMENT '自定义字段123',
#     tapd_story_custom_field_124     VARCHAR(50) COMMENT '自定义字段124',
#     tapd_story_custom_field_125     VARCHAR(50) COMMENT '自定义字段125',
#     tapd_story_custom_field_126     VARCHAR(50) COMMENT '自定义字段126',
#     tapd_story_custom_field_127     VARCHAR(50) COMMENT '自定义字段127',
#     tapd_story_custom_field_128     VARCHAR(50) COMMENT '自定义字段128',
#     tapd_story_custom_field_129     VARCHAR(50) COMMENT '自定义字段129',
#     tapd_story_custom_field_130     VARCHAR(50) COMMENT '自定义字段130',
#     tapd_story_custom_field_131     VARCHAR(50) COMMENT '自定义字段131',
#     tapd_story_custom_field_132     VARCHAR(50) COMMENT '自定义字段132',
#     tapd_story_custom_field_133     VARCHAR(50) COMMENT '自定义字段133',
#     tapd_story_custom_field_134     VARCHAR(50) COMMENT '自定义字段134',
#     tapd_story_custom_field_135     VARCHAR(50) COMMENT '自定义字段135',
#     tapd_story_custom_field_136     VARCHAR(50) COMMENT '自定义字段136',
#     tapd_story_custom_field_137     VARCHAR(50) COMMENT '自定义字段137',
#     tapd_story_custom_field_138     VARCHAR(50) COMMENT '自定义字段138',
#     tapd_story_custom_field_139     VARCHAR(50) COMMENT '自定义字段139',
#     tapd_story_custom_field_140     VARCHAR(50) COMMENT '自定义字段140',
#     tapd_story_custom_field_141     VARCHAR(50) COMMENT '自定义字段141',
#     tapd_story_custom_field_142     VARCHAR(50) COMMENT '自定义字段142',
#     tapd_story_custom_field_143     VARCHAR(50) COMMENT '自定义字段143',
#     tapd_story_custom_field_144     VARCHAR(50) COMMENT '自定义字段144',
#     tapd_story_custom_field_145     VARCHAR(50) COMMENT '自定义字段145',
#     tapd_story_custom_field_146     VARCHAR(50) COMMENT '自定义字段146',
#     tapd_story_custom_field_147     VARCHAR(50) COMMENT '自定义字段147',
#     tapd_story_custom_field_148     VARCHAR(50) COMMENT '自定义字段148',
#     tapd_story_custom_field_149     VARCHAR(50) COMMENT '自定义字段149',
#     tapd_story_custom_field_150     VARCHAR(50) COMMENT '自定义字段150',
#     tapd_story_custom_field_151     VARCHAR(50) COMMENT '自定义字段151',
#     tapd_story_custom_field_152     VARCHAR(50) COMMENT '自定义字段152',
#     tapd_story_custom_field_153     VARCHAR(50) COMMENT '自定义字段153',
#     tapd_story_custom_field_154     VARCHAR(50) COMMENT '自定义字段154',
#     tapd_story_custom_field_155     VARCHAR(50) COMMENT '自定义字段155',
#     tapd_story_custom_field_156     VARCHAR(50) COMMENT '自定义字段156',
#     tapd_story_custom_field_157     VARCHAR(50) COMMENT '自定义字段157',
#     tapd_story_custom_field_158     VARCHAR(50) COMMENT '自定义字段158',
#     tapd_story_custom_field_159     VARCHAR(50) COMMENT '自定义字段159',
#     tapd_story_custom_field_160     VARCHAR(50) COMMENT '自定义字段160',
#     tapd_story_custom_field_161     VARCHAR(50) COMMENT '自定义字段161',
#     tapd_story_custom_field_162     VARCHAR(50) COMMENT '自定义字段162',
#     tapd_story_custom_field_163     VARCHAR(50) COMMENT '自定义字段163',
#     tapd_story_custom_field_164     VARCHAR(50) COMMENT '自定义字段164',
#     tapd_story_custom_field_165     VARCHAR(50) COMMENT '自定义字段165',
#     tapd_story_custom_field_166     VARCHAR(50) COMMENT '自定义字段166',
#     tapd_story_custom_field_167     VARCHAR(50) COMMENT '自定义字段167',
#     tapd_story_custom_field_168     VARCHAR(50) COMMENT '自定义字段168',
#     tapd_story_custom_field_169     VARCHAR(50) COMMENT '自定义字段169',
#     tapd_story_custom_field_170     VARCHAR(50) COMMENT '自定义字段170',
#     tapd_story_custom_field_171     VARCHAR(50) COMMENT '自定义字段171',
#     tapd_story_custom_field_172     VARCHAR(50) COMMENT '自定义字段172',
#     tapd_story_custom_field_173     VARCHAR(50) COMMENT '自定义字段173',
#     tapd_story_custom_field_174     VARCHAR(50) COMMENT '自定义字段174',
#     tapd_story_custom_field_175     VARCHAR(50) COMMENT '自定义字段175',
#     tapd_story_custom_field_176     VARCHAR(50) COMMENT '自定义字段176',
#     tapd_story_custom_field_177     VARCHAR(50) COMMENT '自定义字段177',
#     tapd_story_custom_field_178     VARCHAR(50) COMMENT '自定义字段178',
#     tapd_story_custom_field_179     VARCHAR(50) COMMENT '自定义字段179',
#     tapd_story_custom_field_180     VARCHAR(50) COMMENT '自定义字段180',
#     tapd_story_custom_field_181     VARCHAR(50) COMMENT '自定义字段181',
#     tapd_story_custom_field_182     VARCHAR(50) COMMENT '自定义字段182',
#     tapd_story_custom_field_183     VARCHAR(50) COMMENT '自定义字段183',
#     tapd_story_custom_field_184     VARCHAR(50) COMMENT '自定义字段184',
#     tapd_story_custom_field_185     VARCHAR(50) COMMENT '自定义字段185',
#     tapd_story_custom_field_186     VARCHAR(50) COMMENT '自定义字段186',
#     tapd_story_custom_field_187     VARCHAR(50) COMMENT '自定义字段187',
#     tapd_story_custom_field_188     VARCHAR(50) COMMENT '自定义字段188',
#     tapd_story_custom_field_189     VARCHAR(50) COMMENT '自定义字段189',
#     tapd_story_custom_field_190     VARCHAR(50) COMMENT '自定义字段190',
#     tapd_story_custom_field_191     VARCHAR(50) COMMENT '自定义字段191',
#     tapd_story_custom_field_192     VARCHAR(50) COMMENT '自定义字段192',
#     tapd_story_custom_field_193     VARCHAR(50) COMMENT '自定义字段193',
#     tapd_story_custom_field_194     VARCHAR(50) COMMENT '自定义字段194',
#     tapd_story_custom_field_195     VARCHAR(50) COMMENT '自定义字段195',
#     tapd_story_custom_field_196     VARCHAR(50) COMMENT '自定义字段196',
#     tapd_story_custom_field_197     VARCHAR(50) COMMENT '自定义字段197',
#     tapd_story_custom_field_198     VARCHAR(50) COMMENT '自定义字段198',
#     tapd_story_custom_field_199     VARCHAR(50) COMMENT '自定义字段199',
#     tapd_story_custom_field_200     VARCHAR(50) COMMENT '自定义字段200',

    tapd_story_custom_plan_field_1  VARCHAR(100) COMMENT '自定义计划字段01',
    tapd_story_custom_plan_field_2  VARCHAR(100) COMMENT '自定义计划字段02',
    tapd_story_custom_plan_field_3  VARCHAR(100) COMMENT '自定义计划字段03',
    tapd_story_custom_plan_field_4  VARCHAR(100) COMMENT '自定义计划字段04',
    tapd_story_custom_plan_field_5  VARCHAR(100) COMMENT '自定义计划字段05',
    tapd_story_custom_plan_field_6  VARCHAR(100) COMMENT '自定义计划字段06',
    tapd_story_custom_plan_field_7  VARCHAR(100) COMMENT '自定义计划字段07',
    tapd_story_custom_plan_field_8  VARCHAR(100) COMMENT '自定义计划字段08',
    tapd_story_custom_plan_field_9  VARCHAR(100) COMMENT '自定义计划字段09',
    tapd_story_custom_plan_field_10 VARCHAR(100) COMMENT '自定义计划字段10',

    PRIMARY KEY (id),
    UNIQUE KEY udx_entry_id (tapd_entry_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'tapd故事实体表';
