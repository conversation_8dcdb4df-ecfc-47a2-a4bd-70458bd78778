-- ----------------------------
-- 205、tapd发布评审实体表：tapd_entry_launch_form
-- 说明：
-- ----------------------------
select *
from tapd_entry_launch_form;

DROP TABLE IF EXISTS tapd_entry_launch_form;
CREATE TABLE tapd_entry_launch_form
(
    create_user                         VARCHAR(20) COMMENT '创建人',
    create_time                         DATETIME(0) COMMENT '创建时间',
    update_user                         VARCHAR(20) COMMENT '修改人',
    update_time                         DATETIME(0) COMMENT '修改时间',
    stamp                               BIGINT(20) DEFAULT 0 COMMENT '版本',
    id                                  BIGINT(20) AUTO_INCREMENT,
    ins_batch_number                    BIGINT(20) COMMENT '插入时批次号',
    upd_batch_number                    BIGINT(20) COMMENT '最后一次更新时批次号',
    del_batch_number                    BIGINT(20) COMMENT '最后一次删除时批次号',
    del_user                            VARCHAR(20) COMMENT '最后一次删除人',
    del_time                            DATETIME(0) COMMENT '最后一次删除时间',
    rec_batch_number                    BIGINT(20) COMMENT '最后一次恢复时批次号',
    rec_user                            VARCHAR(20) COMMENT '最后一次恢复人',
    rec_time                            DATETIME(0) COMMENT '最后一次恢复时间',

    entry_status                        INT(4) COMMENT '数据实体状态：1-新记录、2-更新过、3-已删除',
    entry_desc                          VARCHAR(255) COMMENT '数据实体说明',

    res_json                            JSON COMMENT '请求返回Json',
    tapd_workspace_id                   BIGINT(20) COMMENT '项目ID',
    tapd_entry_id                       BIGINT(20) NOT NULL COMMENT '数据实体ID（字符非数字）',

    tapd_launch_form_title              VARCHAR(200) COMMENT '发布评审标题',
    tapd_launch_form_description        VARCHAR(255) COMMENT '描述（备用）',
    tapd_launch_form_creator            VARCHAR(20) COMMENT '创建人',
    tapd_launch_form_created            DATETIME(0) COMMENT '创建时间',
    tapd_launch_form_status             VARCHAR(20) COMMENT '发布评审状态',
    tapd_launch_form_version_type       VARCHAR(100) COMMENT '发布评审版本类型',
    tapd_launch_form_baseline           VARCHAR(100) COMMENT '基线',
    tapd_launch_form_release_model      VARCHAR(100) COMMENT '发布模块',
    tapd_launch_form_roadmap_version    VARCHAR(100) COMMENT '测试计划状态',
    tapd_launch_form_release_type       VARCHAR(100) COMMENT '路标版本',
    tapd_launch_form_change_type        VARCHAR(100) COMMENT '变更类型',
    tapd_launch_form_signed_by          VARCHAR(20) COMMENT '签发人',
    tapd_launch_form_archived_by        VARCHAR(20) COMMENT '发布确认人',
    tapd_launch_form_cc                 VARCHAR(100) COMMENT '抄送人',
    tapd_launch_form_change_notifier    VARCHAR(100) COMMENT '变更通知人',
    tapd_launch_form_signed             DATETIME(0) COMMENT '签发时间',
    tapd_launch_form_archived           DATETIME(0) COMMENT '归档时间',
    tapd_launch_form_signer_result      VARCHAR(255) COMMENT '签发结论',
    tapd_launch_form_signer_comment     VARCHAR(255) COMMENT '签发意见',
    tapd_launch_form_release_result     VARCHAR(255) COMMENT '发布结果',
    tapd_launch_form_release_comment    VARCHAR(255) COMMENT '发布意见',
    tapd_launch_form_test_path          VARCHAR(255) COMMENT '测试路径',
    tapd_launch_form_created_path       VARCHAR(255) COMMENT '归档路径',
    tapd_launch_form_remark             VARCHAR(255) COMMENT '备注',
    tapd_launch_form_participator       VARCHAR(100) COMMENT '参与人',
    tapd_launch_form_template_id        BIGINT(20) COMMENT '模板ID',
    tapd_launch_form_iteration_id       BIGINT(20) COMMENT '迭代ID',
    tapd_launch_form_release_id         BIGINT(20) COMMENT '发布计划ID',
    tapd_launch_form_flows              VARCHAR(500) COMMENT '工作流',
    tapd_launch_form_modified           DATETIME(0) COMMENT '最后修改时间',

    tapd_launch_form_custom_field_one   VARCHAR(100) COMMENT '自定义字段001',
    tapd_launch_form_custom_field_two   VARCHAR(100) COMMENT '自定义字段002',
    tapd_launch_form_custom_field_three VARCHAR(100) COMMENT '自定义字段003',
    tapd_launch_form_custom_field_four  VARCHAR(100) COMMENT '自定义字段004',
    tapd_launch_form_custom_field_five  VARCHAR(100) COMMENT '自定义字段005',
    tapd_launch_form_custom_field_six   VARCHAR(100) COMMENT '自定义字段006',
    tapd_launch_form_custom_field_seven VARCHAR(100) COMMENT '自定义字段007',
    tapd_launch_form_custom_field_eight VARCHAR(100) COMMENT '自定义字段008',
    tapd_launch_form_custom_field_9     VARCHAR(100) COMMENT '自定义字段009',
    tapd_launch_form_custom_field_10    VARCHAR(100) COMMENT '自定义字段010',
    tapd_launch_form_custom_field_11    VARCHAR(100) COMMENT '自定义字段011',
    tapd_launch_form_custom_field_12    VARCHAR(100) COMMENT '自定义字段012',
    tapd_launch_form_custom_field_13    VARCHAR(100) COMMENT '自定义字段013',
    tapd_launch_form_custom_field_14    VARCHAR(100) COMMENT '自定义字段014',
    tapd_launch_form_custom_field_15    VARCHAR(100) COMMENT '自定义字段015',
    tapd_launch_form_custom_field_16    VARCHAR(100) COMMENT '自定义字段016',
    tapd_launch_form_custom_field_17    VARCHAR(100) COMMENT '自定义字段017',
    tapd_launch_form_custom_field_18    VARCHAR(100) COMMENT '自定义字段018',
    tapd_launch_form_custom_field_19    VARCHAR(100) COMMENT '自定义字段019',
    tapd_launch_form_custom_field_20    VARCHAR(100) COMMENT '自定义字段020',
    tapd_launch_form_custom_field_21    VARCHAR(100) COMMENT '自定义字段021',
    tapd_launch_form_custom_field_22    VARCHAR(100) COMMENT '自定义字段022',
    tapd_launch_form_custom_field_23    VARCHAR(100) COMMENT '自定义字段023',
    tapd_launch_form_custom_field_24    VARCHAR(100) COMMENT '自定义字段024',
    tapd_launch_form_custom_field_25    VARCHAR(100) COMMENT '自定义字段025',
    tapd_launch_form_custom_field_26    VARCHAR(100) COMMENT '自定义字段026',
    tapd_launch_form_custom_field_27    VARCHAR(100) COMMENT '自定义字段027',
    tapd_launch_form_custom_field_28    VARCHAR(100) COMMENT '自定义字段028',
    tapd_launch_form_custom_field_29    VARCHAR(100) COMMENT '自定义字段029',
    tapd_launch_form_custom_field_30    VARCHAR(100) COMMENT '自定义字段030',
    tapd_launch_form_custom_field_31    VARCHAR(100) COMMENT '自定义字段031',
    tapd_launch_form_custom_field_32    VARCHAR(100) COMMENT '自定义字段032',
    tapd_launch_form_custom_field_33    VARCHAR(100) COMMENT '自定义字段033',
    tapd_launch_form_custom_field_34    VARCHAR(100) COMMENT '自定义字段034',
    tapd_launch_form_custom_field_35    VARCHAR(100) COMMENT '自定义字段035',
    tapd_launch_form_custom_field_36    VARCHAR(100) COMMENT '自定义字段036',
    tapd_launch_form_custom_field_37    VARCHAR(100) COMMENT '自定义字段037',
    tapd_launch_form_custom_field_38    VARCHAR(100) COMMENT '自定义字段038',
    tapd_launch_form_custom_field_39    VARCHAR(100) COMMENT '自定义字段039',
    tapd_launch_form_custom_field_40    VARCHAR(100) COMMENT '自定义字段040',
    tapd_launch_form_custom_field_41    VARCHAR(100) COMMENT '自定义字段041',
    tapd_launch_form_custom_field_42    VARCHAR(100) COMMENT '自定义字段042',
    tapd_launch_form_custom_field_43    VARCHAR(100) COMMENT '自定义字段043',
    tapd_launch_form_custom_field_44    VARCHAR(100) COMMENT '自定义字段044',
    tapd_launch_form_custom_field_45    VARCHAR(100) COMMENT '自定义字段045',
    tapd_launch_form_custom_field_46    VARCHAR(100) COMMENT '自定义字段046',
    tapd_launch_form_custom_field_47    VARCHAR(100) COMMENT '自定义字段047',
    tapd_launch_form_custom_field_48    VARCHAR(100) COMMENT '自定义字段048',
    tapd_launch_form_custom_field_49    VARCHAR(100) COMMENT '自定义字段049',
    tapd_launch_form_custom_field_50    VARCHAR(100) COMMENT '自定义字段050',

    PRIMARY KEY (id),
    UNIQUE KEY udx_entry_id (tapd_entry_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'tapd测试计划实体表';
