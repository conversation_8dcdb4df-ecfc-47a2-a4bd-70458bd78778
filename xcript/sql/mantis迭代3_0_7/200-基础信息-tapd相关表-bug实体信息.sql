-- ----------------------------
-- 2-0、tapd缺陷实体表：tapd_entry_bug
-- 说明：
-- ----------------------------
select *
from tapd_entry_bug;

DROP TABLE IF EXISTS tapd_entry_bug;
CREATE TABLE tapd_entry_bug
(
    create_user                   VARCHAR(20) COMMENT '创建人',
    create_time                   DATETIME(0) COMMENT '创建时间',
    update_user                   VARCHAR(20) COMMENT '修改人',
    update_time                   DATETIME(0) COMMENT '修改时间',
    stamp                         BIGINT(20) DEFAULT 0 COMMENT '版本',
    id                            BIGINT(20) AUTO_INCREMENT,
    ins_batch_number              BIGINT(20) COMMENT '插入时批次号',
    upd_batch_number              BIGINT(20) COMMENT '最后一次更新时批次号',
    del_batch_number              BIGINT(20) COMMENT '最后一次删除时批次号',
    del_user                      VARCHAR(20) COMMENT '最后一次删除人',
    del_time                      DATETIME(0) COMMENT '最后一次删除时间',
    rec_batch_number              BIGINT(20) COMMENT '最后一次恢复时批次号',
    rec_user                      VARCHAR(20) COMMENT '最后一次恢复人',
    rec_time                      DATETIME(0) COMMENT '最后一次恢复时间',

    entry_status                  INT(4) COMMENT '数据实体状态：1-新记录、2-更新过、3-已删除',
    entry_desc                    VARCHAR(255) COMMENT '数据实体说明',

    res_json                      JSON COMMENT '请求返回Json',
    tapd_workspace_id             BIGINT(20) COMMENT '项目ID',
    tapd_entry_id                 BIGINT(20) NOT NULL COMMENT '数据实体ID（字符非数字）',

    tapd_bug_title                VARCHAR(200) COMMENT '缺陷标题',
    tapd_bug_description          VARCHAR(255) COMMENT '描述（备用）',
    tapd_bug_priority             VARCHAR(50) COMMENT '优先级',
    tapd_bug_severity             VARCHAR(10) COMMENT '严重程度',
    tapd_bug_module               VARCHAR(100) COMMENT '模块',
    tapd_bug_status               VARCHAR(20) COMMENT '状态',
    tapd_bug_reporter             VARCHAR(100) COMMENT '创建人',
    tapd_bug_created              DATETIME(0) COMMENT '创建时间',
    tapd_bug_bugtype              VARCHAR(50) COMMENT '缺陷类型',
    tapd_bug_resolved             DATETIME(0) COMMENT '修复时间',
    tapd_bug_closed               DATETIME(0) COMMENT '关闭时间',
    tapd_bug_modified             DATETIME(0) COMMENT '最后修改时间',
    tapd_bug_lastmodify           VARCHAR(50) COMMENT '最后修改人',
    tapd_bug_de                   VARCHAR(50) COMMENT '开发人员',
    tapd_bug_fixer                VARCHAR(50) COMMENT '修复人',
    tapd_bug_version_test         VARCHAR(50) COMMENT '验证版本',
    tapd_bug_version_report       VARCHAR(50) COMMENT '发现版本',
    tapd_bug_version_close        VARCHAR(50) COMMENT '关闭版本',
    tapd_bug_version_fix          VARCHAR(50) COMMENT '合入版本',
    tapd_bug_baseline_find        VARCHAR(50) COMMENT '发现基线',
    tapd_bug_baseline_join        VARCHAR(50) COMMENT '合入基线',
    tapd_bug_baseline_close       VARCHAR(50) COMMENT '关闭版本',
    tapd_bug_baseline_test        VARCHAR(50) COMMENT '验证基线',
    tapd_bug_sourcephase          VARCHAR(50) COMMENT '引入阶段',
    tapd_bug_te                   VARCHAR(50) COMMENT '测试人员',
    tapd_bug_current_owner        VARCHAR(50) COMMENT '当前处理人',
    tapd_bug_iteration_id         BIGINT(20) COMMENT '迭代ID',
    tapd_bug_resolution           VARCHAR(50) COMMENT '解决方法',
    tapd_bug_source               VARCHAR(50) COMMENT '缺陷根源',
    tapd_bug_originphase          VARCHAR(50) COMMENT '发现阶段',
    tapd_bug_confirmer            VARCHAR(50) COMMENT '确认人',
    tapd_bug_milestone            VARCHAR(50) COMMENT '里程碑',
    tapd_bug_participator         VARCHAR(50) COMMENT '参与人',
    tapd_bug_closer               VARCHAR(50) COMMENT '关闭人',
    tapd_bug_platform             VARCHAR(50) COMMENT '软件平台',
    tapd_bug_os                   VARCHAR(50) COMMENT '操作系统',
    tapd_bug_testtype             VARCHAR(50) COMMENT '测试类型',
    tapd_bug_testphase            VARCHAR(50) COMMENT '测试阶段',
    tapd_bug_frequency            VARCHAR(50) COMMENT '重现规律',
    tapd_bug_cc                   VARCHAR(50) COMMENT '抄送人',
    tapd_bug_regression_number    VARCHAR(50) COMMENT '回归数',
    tapd_bug_flows                VARCHAR(500) COMMENT '工作流',
    tapd_bug_feature              VARCHAR(50) COMMENT '功能',
    tapd_bug_testmode             VARCHAR(50) COMMENT '测试方式',
    tapd_bug_estimate             INT(4) COMMENT '预计解决时间',
    tapd_bug_issue_id             BIGINT(20) COMMENT '发行ID',
    tapd_bug_created_from         VARCHAR(50) COMMENT '创建来源',
    tapd_bug_release_id           BIGINT(20) COMMENT '发布计划',
    tapd_bug_verify_time          DATETIME(0) COMMENT '验证时间',
    tapd_bug_reject_time          DATETIME(0) COMMENT '拒绝时间',
    tapd_bug_reopen_time          DATETIME(0) COMMENT '重开时间',
    tapd_bug_audit_time           DATETIME(0) COMMENT '审核时间',
    tapd_bug_suspend_time         DATETIME(0) COMMENT '挂起时间',
    tapd_bug_due                  DATE COMMENT '预计',
    tapd_bug_begin                DATE COMMENT '预计开始',
    tapd_bug_deadline             DATE COMMENT '解决期限',
    tapd_bug_in_progress_time     DATETIME(0) COMMENT '接受处理时间',
    tapd_bug_assigned_time        DATETIME(0) COMMENT '分配时间',
    tapd_bug_template_id          BIGINT(20) COMMENT '模板ID',
    tapd_bug_story_id             BIGINT(20) COMMENT '故事ID',
    tapd_bug_label                VARCHAR(50) COMMENT '标签',
    tapd_bug_size                 VARCHAR(50) COMMENT '大小',
    tapd_bug_effort               INT(4) COMMENT '预估工时',
    tapd_bug_effort_completed     VARCHAR(50) COMMENT '完成工时',
    tapd_bug_exceed               VARCHAR(50) COMMENT '超出工时',
    tapd_bug_remain               VARCHAR(50) COMMENT '剩余工时',
    tapd_bug_priority_label       VARCHAR(50) COMMENT '优先级标签',

    tapd_bug_custom_field_one     VARCHAR(100) COMMENT '自定义字段001',
    tapd_bug_custom_field_two     VARCHAR(100) COMMENT '自定义字段002',
    tapd_bug_custom_field_three   VARCHAR(100) COMMENT '自定义字段003',
    tapd_bug_custom_field_four    VARCHAR(100) COMMENT '自定义字段004',
    tapd_bug_custom_field_five    VARCHAR(100) COMMENT '自定义字段005',
    tapd_bug_custom_field_6       VARCHAR(100) COMMENT '自定义字段006',
    tapd_bug_custom_field_7       VARCHAR(100) COMMENT '自定义字段007',
    tapd_bug_custom_field_8       VARCHAR(100) COMMENT '自定义字段008',
    tapd_bug_custom_field_9       VARCHAR(100) COMMENT '自定义字段009',
    tapd_bug_custom_field_10      VARCHAR(100) COMMENT '自定义字段010',
    tapd_bug_custom_field_11      VARCHAR(100) COMMENT '自定义字段011',
    tapd_bug_custom_field_12      VARCHAR(100) COMMENT '自定义字段012',
    tapd_bug_custom_field_13      VARCHAR(100) COMMENT '自定义字段013',
    tapd_bug_custom_field_14      VARCHAR(100) COMMENT '自定义字段014',
    tapd_bug_custom_field_15      VARCHAR(100) COMMENT '自定义字段015',
    tapd_bug_custom_field_16      VARCHAR(100) COMMENT '自定义字段016',
    tapd_bug_custom_field_17      VARCHAR(100) COMMENT '自定义字段017',
    tapd_bug_custom_field_18      VARCHAR(100) COMMENT '自定义字段018',
    tapd_bug_custom_field_19      VARCHAR(100) COMMENT '自定义字段019',
    tapd_bug_custom_field_20      VARCHAR(100) COMMENT '自定义字段020',
    tapd_bug_custom_field_21      VARCHAR(100) COMMENT '自定义字段021',
    tapd_bug_custom_field_22      VARCHAR(100) COMMENT '自定义字段022',
    tapd_bug_custom_field_23      VARCHAR(100) COMMENT '自定义字段023',
    tapd_bug_custom_field_24      VARCHAR(100) COMMENT '自定义字段024',
    tapd_bug_custom_field_25      VARCHAR(100) COMMENT '自定义字段025',
    tapd_bug_custom_field_26      VARCHAR(100) COMMENT '自定义字段026',
    tapd_bug_custom_field_27      VARCHAR(100) COMMENT '自定义字段027',
    tapd_bug_custom_field_28      VARCHAR(100) COMMENT '自定义字段028',
    tapd_bug_custom_field_29      VARCHAR(100) COMMENT '自定义字段029',
    tapd_bug_custom_field_30      VARCHAR(100) COMMENT '自定义字段030',
    tapd_bug_custom_field_31      VARCHAR(100) COMMENT '自定义字段031',
    tapd_bug_custom_field_32      VARCHAR(100) COMMENT '自定义字段032',
    tapd_bug_custom_field_33      VARCHAR(100) COMMENT '自定义字段033',
    tapd_bug_custom_field_34      VARCHAR(100) COMMENT '自定义字段034',
    tapd_bug_custom_field_35      VARCHAR(100) COMMENT '自定义字段035',
    tapd_bug_custom_field_36      VARCHAR(100) COMMENT '自定义字段036',
    tapd_bug_custom_field_37      VARCHAR(100) COMMENT '自定义字段037',
    tapd_bug_custom_field_38      VARCHAR(100) COMMENT '自定义字段038',
    tapd_bug_custom_field_39      VARCHAR(100) COMMENT '自定义字段039',
    tapd_bug_custom_field_40      VARCHAR(100) COMMENT '自定义字段040',
    tapd_bug_custom_field_41      VARCHAR(100) COMMENT '自定义字段041',
    tapd_bug_custom_field_42      VARCHAR(100) COMMENT '自定义字段042',
    tapd_bug_custom_field_43      VARCHAR(100) COMMENT '自定义字段043',
    tapd_bug_custom_field_44      VARCHAR(100) COMMENT '自定义字段044',
    tapd_bug_custom_field_45      VARCHAR(100) COMMENT '自定义字段045',
    tapd_bug_custom_field_46      VARCHAR(100) COMMENT '自定义字段046',
    tapd_bug_custom_field_47      VARCHAR(100) COMMENT '自定义字段047',
    tapd_bug_custom_field_48      VARCHAR(100) COMMENT '自定义字段048',
    tapd_bug_custom_field_49      VARCHAR(100) COMMENT '自定义字段049',
    tapd_bug_custom_field_50      VARCHAR(100) COMMENT '自定义字段050',
    tapd_bug_custom_field_51      VARCHAR(100) COMMENT '自定义字段051',
    tapd_bug_custom_field_52      VARCHAR(100) COMMENT '自定义字段052',
    tapd_bug_custom_field_53      VARCHAR(100) COMMENT '自定义字段053',
    tapd_bug_custom_field_54      VARCHAR(100) COMMENT '自定义字段054',
    tapd_bug_custom_field_55      VARCHAR(100) COMMENT '自定义字段055',
    tapd_bug_custom_field_56      VARCHAR(100) COMMENT '自定义字段056',
    tapd_bug_custom_field_57      VARCHAR(100) COMMENT '自定义字段057',
    tapd_bug_custom_field_58      VARCHAR(100) COMMENT '自定义字段058',
    tapd_bug_custom_field_59      VARCHAR(100) COMMENT '自定义字段059',
    tapd_bug_custom_field_60      VARCHAR(100) COMMENT '自定义字段060',
    tapd_bug_custom_field_61      VARCHAR(100) COMMENT '自定义字段061',
    tapd_bug_custom_field_62      VARCHAR(100) COMMENT '自定义字段062',
    tapd_bug_custom_field_63      VARCHAR(100) COMMENT '自定义字段063',
    tapd_bug_custom_field_64      VARCHAR(100) COMMENT '自定义字段064',
    tapd_bug_custom_field_65      VARCHAR(100) COMMENT '自定义字段065',
    tapd_bug_custom_field_66      VARCHAR(100) COMMENT '自定义字段066',
    tapd_bug_custom_field_67      VARCHAR(100) COMMENT '自定义字段067',
    tapd_bug_custom_field_68      VARCHAR(100) COMMENT '自定义字段068',
    tapd_bug_custom_field_69      VARCHAR(100) COMMENT '自定义字段069',
    tapd_bug_custom_field_70      VARCHAR(100) COMMENT '自定义字段070',
    tapd_bug_custom_field_71      VARCHAR(100) COMMENT '自定义字段071',
    tapd_bug_custom_field_72      VARCHAR(100) COMMENT '自定义字段072',
    tapd_bug_custom_field_73      VARCHAR(100) COMMENT '自定义字段073',
    tapd_bug_custom_field_74      VARCHAR(100) COMMENT '自定义字段074',
    tapd_bug_custom_field_75      VARCHAR(100) COMMENT '自定义字段075',
    tapd_bug_custom_field_76      VARCHAR(100) COMMENT '自定义字段076',
    tapd_bug_custom_field_77      VARCHAR(100) COMMENT '自定义字段077',
    tapd_bug_custom_field_78      VARCHAR(100) COMMENT '自定义字段078',
    tapd_bug_custom_field_79      VARCHAR(100) COMMENT '自定义字段079',
    tapd_bug_custom_field_80      VARCHAR(100) COMMENT '自定义字段080',
    tapd_bug_custom_field_81      VARCHAR(100) COMMENT '自定义字段081',
    tapd_bug_custom_field_82      VARCHAR(100) COMMENT '自定义字段082',
    tapd_bug_custom_field_83      VARCHAR(100) COMMENT '自定义字段083',
    tapd_bug_custom_field_84      VARCHAR(100) COMMENT '自定义字段084',
    tapd_bug_custom_field_85      VARCHAR(100) COMMENT '自定义字段085',
    tapd_bug_custom_field_86      VARCHAR(100) COMMENT '自定义字段086',
    tapd_bug_custom_field_87      VARCHAR(100) COMMENT '自定义字段087',
    tapd_bug_custom_field_88      VARCHAR(100) COMMENT '自定义字段088',
    tapd_bug_custom_field_89      VARCHAR(100) COMMENT '自定义字段089',
    tapd_bug_custom_field_90      VARCHAR(100) COMMENT '自定义字段090',
    tapd_bug_custom_field_91      VARCHAR(100) COMMENT '自定义字段091',
    tapd_bug_custom_field_92      VARCHAR(100) COMMENT '自定义字段092',
    tapd_bug_custom_field_93      VARCHAR(100) COMMENT '自定义字段093',
    tapd_bug_custom_field_94      VARCHAR(100) COMMENT '自定义字段094',
    tapd_bug_custom_field_95      VARCHAR(100) COMMENT '自定义字段095',
    tapd_bug_custom_field_96      VARCHAR(100) COMMENT '自定义字段096',
    tapd_bug_custom_field_97      VARCHAR(100) COMMENT '自定义字段097',
    tapd_bug_custom_field_98      VARCHAR(100) COMMENT '自定义字段098',
    tapd_bug_custom_field_99      VARCHAR(100) COMMENT '自定义字段099',
    tapd_bug_custom_field_100     VARCHAR(100) COMMENT '自定义字段100',

    tapd_bug_custom_plan_field_1  VARCHAR(100) COMMENT '自定义计划字段01',
    tapd_bug_custom_plan_field_2  VARCHAR(100) COMMENT '自定义计划字段02',
    tapd_bug_custom_plan_field_3  VARCHAR(100) COMMENT '自定义计划字段03',
    tapd_bug_custom_plan_field_4  VARCHAR(100) COMMENT '自定义计划字段04',
    tapd_bug_custom_plan_field_5  VARCHAR(100) COMMENT '自定义计划字段05',
    tapd_bug_custom_plan_field_6  VARCHAR(100) COMMENT '自定义计划字段06',
    tapd_bug_custom_plan_field_7  VARCHAR(100) COMMENT '自定义计划字段07',
    tapd_bug_custom_plan_field_8  VARCHAR(100) COMMENT '自定义计划字段08',
    tapd_bug_custom_plan_field_9  VARCHAR(100) COMMENT '自定义计划字段09',
    tapd_bug_custom_plan_field_10 VARCHAR(100) COMMENT '自定义计划字段10',

    PRIMARY KEY (id),
    UNIQUE KEY udx_entry_id (tapd_entry_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'tapd缺陷实体表';
