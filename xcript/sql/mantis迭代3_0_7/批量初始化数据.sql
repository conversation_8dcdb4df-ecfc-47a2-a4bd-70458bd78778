/*
SQLyog Community v13.1.9 (64 bit)
MySQL - 5.7.29-log
*********************************************************************
*/
/*!40101 SET NAMES utf8 */;
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','40003001','40003','1159626479001000254','story','custom_field_three','select','2024公司项目',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','42203001','42203','1163231183001000253','story','custom_field_three','select','2024公司项目',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','42103001','42103','1140756777001000252','story','custom_field_five','select','2024公司项目',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','41203001','41203','1152223238001000256','story','custom_field_five','select','2024公司项目',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','41303001','41303','1136243514001000283','story','custom_field_one','select','2024公司项目',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','41403001','41403','1164984051001000284','story','custom_field_one','select','2024公司项目',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','41103001','41103','1166914855001000258','story','custom_field_one','select','2024公司项目',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','43003001','43003','1136225275001000255','story','custom_field_one','select','2024公司项目',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);









insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003001','30003','1155014084001000151','story','custom_field_one','select','测试类型',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003002','30003','1155014084001000152','story','custom_field_two','select','所属团队',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003003','30003','1155014084001000153','story','custom_field_three','select','所属小组',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003004','30003','1155014084001000156','story','custom_field_six','text','实际工时(H)',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003005','30003','1155014084001000159','story','custom_field_seven','user_chooser','需求人员',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003006','30003','1155014084001000166','story','custom_field_four','float','延期天数',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003007','30003','1155014084001000167','story','custom_field_eight','textarea','延期说明',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003008','30003','1155014084001000168','story','custom_field_9','textarea','测试总结',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003009','30003','1155014084001000169','story','custom_field_10','dateinput','实际开始',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003010','30003','1155014084001000170','story','custom_field_11','dateinput','实际结束',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003011','30003','1155014084001000171','story','custom_field_12','multi_select','兼容性列表',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003012','30003','1155014084001000177','story','custom_field_13','multi_select','藏经阁-全应用',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003013','30003','1155014084001000178','story','custom_field_14','multi_select','达摩院-服务端',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003014','30003','1155014084001000179','story','custom_field_15','multi_select','达摩院-H5',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003015','30003','1155014084001000180','story','custom_field_16','multi_select','爱码仕-服务端',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003016','30003','1155014084001000181','story','custom_field_17','multi_select','爱码仕-H5&APP',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003017','30003','1155014084001000182','story','custom_field_18','multi_select','六扇门-全应用',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003018','30003','1155014084001000199','story','custom_field_20','dateinput','提测日期',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003019','30003','1155014084001000201','story','custom_field_five','text','需求版本',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003020','30003','1155014084001000202','story','custom_field_21','select','开发归因',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003021','30003','1155014084001000293','story','custom_field_22','text','研发测试计划id',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30003022','30003','1155014084001000294','story','custom_field_23','text','研发测试计划URL',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);






insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30005001','30005','1155014084001000165','bug','custom_field_7','select','技术归因分析',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30005002','30005','1155014084001000172','bug','custom_field_8','textarea','修复问题说明',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30005003','30005','1155014084001000183','bug','custom_field_10','multi_select','藏经阁-全应用',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30005004','30005','1155014084001000187','bug','custom_field_11','multi_select','摩院-服务端',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30005005','30005','1155014084001000188','bug','custom_field_12','multi_select','爱码仕-服务端',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30005006','30005','1155014084001000189','bug','custom_field_13','multi_select','六扇门-全应用',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30005007','30005','1155014084001000191','bug','custom_field_14','multi_select','达摩院-H5',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30005008','30005','1155014084001000193','bug','custom_field_15','multi_select','爱码仕-H5&APP',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30005009','30005','1155014084001000200','bug','custom_field_one','select','是否可自测发现',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30005010','30005','1155014084001000203','bug','custom_field_two','multi_select','开发归因',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30005011','30005','1155014084001000208','bug','custom_field_three','textarea','问题原因说明',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);


insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-01 14:36:50','weimin.feng','2024-03-01 14:36:55','0','30004002','30004','1155014084001000267','task','custom_field_two','select','任务类型',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);
insert into `tapd_workspace_module_conf` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `module_id`, `tapd_id`, `tapd_entry_type`, `tapd_custom_field`, `tapd_type`, `tapd_name`, `tapd_options`, `tapd_sort`, `conf_is_sync`, `conf_table`, `conf_column`, `conf_name`, `conf_type`, `conf_is_active`, `conf_desc`) values('weimin.feng','2024-03-04 14:36:50','weimin.feng','2024-03-04 14:36:55','0','30004001','30004','1155014084001000285','task','custom_field_three','select','研发测试计划',NULL,'0','1',NULL,NULL,NULL,NULL,'1',NULL);



INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','非战略项目','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','投研-海外数据接入','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','投研-完善股权数据库','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','投研-投研系统优化','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','投研-投研能力与服务的系统转化输出','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-研习社专项','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-理财九章专项','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-线上线下课程打通','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-资配报告饼图优化','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-资配/健康度报告纳入收益型保险','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-sop改造配套系统流程重构配合新的T@T流程的系统改造','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-海外签约专项','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-好臻线上化','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-机构持仓专项','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-好买下单流程改造','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-双录功能改造','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-持仓改造3.01.客户界面增加理财分析，收益序列等功能，为年账单，资配等打基础（跑批有问题中）','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-年度账单','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-企微消息自定义发送','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-新规电子问卷支持重做','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-自定义专题','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-企微离职回访','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-小程序整合梳理','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','高端-客户画像全景','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-账户（我的）页面重构及改版','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-首页改动-优化结构、增加投资助手（基金动态）','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-C位研习社及研习社私教课体系（含运营工具、小程序等）','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-收益账单优化','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-任务（激励）体系的搭建完成','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-小程序体系的初步搭建完成','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-运营支持，任务系统','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-资产账单','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-浮动管理费改造','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-定投优化','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-投顾互转','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-T0转投','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-阶梯券，历史活动迁移','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-账户安全，一键登录','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-潜龙新手版','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-投顾算法优化','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-货币赢支付','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-投顾持牌系统建设','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-数据产品报表','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-小程序基础产品功能与技术框架','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-平台接入标准化（云闪付合作）','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-投研/产品资讯内容','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-投顾策略应用','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','零售-消息中心（面向合作伙伴）','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','机构-客户端工具','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','机构-机构客户交易接入-定制化','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','机构-数据服务平台重构升级','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','机构-机构事业线数据报表系统优化','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','机构-机构绩效','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');
INSERT INTO `tapd_company_project` ( `project_year`, `project_name`, `project_is_active`, `create_time`, `create_user`, `update_time`, `update_user`, `stamp`) VALUES('2024','机构-财务系统','1','2024-03-04 15:18:25','weimin.feng',NULL,NULL,'0');


insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('1','1140756777001000926','1140756777001186890','1140756777001000926','2024-03-07 19:04:13');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('2','1140756777001000923','1140756777001179911,1140756777001182561,1140756777001182565,1140756777001182673,1140756777001182675,1140756777001182677,1140756777001182830,1140756777001182841,1140756777001183796,1140756777001183797,1140756777001183800,1140756777001183806,1140756777001183808,1140756777001184434,1140756777001184479,1140756777001184480,1140756777001184954,1140756777001186373','1140756777001000923','2024-03-08 19:23:02');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('3','1140756777001000922','1140756777001179550,1140756777001179795,1140756777001180186,1140756777001180217,1140756777001180219,1140756777001180221,1140756777001180222,1140756777001180224,1140756777001180226,1140756777001180229,1140756777001180982,1140756777001182337,1140756777001182338,1140756777001182764,1140756777001182766,1140756777001183197,1140756777001186890,1140756777001186930','1140756777001000922','2024-03-07 19:04:06');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('4','1140756777001000905','1140756777001179795','1140756777001000905','2024-03-08 19:23:02');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('5','1140756777001000903','1140756777001178266,1140756777001182841,1140756777001184433,1140756777001184480,1140756777001185927,1140756777001185928,1140756777001185931,1140756777001186325,1140756777001186373,1140756777001186384,1140756777001186385,1140756777001186386,1140756777001186387','1140756777001000903','2024-03-08 19:23:02');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('6','1163231183001000924',NULL,'1163231183001000924','2024-03-11 10:13:00');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('7','1163231183001000920',NULL,'1163231183001000920','2024-03-11 09:53:01');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('8','1163231183001000917','1163231183001183563','1163231183001000917','2024-03-11 09:53:01');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('9','1163231183001000916','1163231183001184090','1163231183001000916','2024-03-07 19:04:00');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('10','1163231183001000911','1163231183001183461','1163231183001000911','2024-03-11 09:53:01');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('11','1163231183001000910','1163231183001186169','1163231183001000910','2024-03-11 09:23:00');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('12','1163231183001000909','1163231183001183381','1163231183001000909','2024-03-11 09:53:01');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('13','1163231183001000908','1163231183001180260','1163231183001000908','2024-03-11 09:53:01');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('14','1163231183001000907','1163231183001179171','1163231183001000907','2024-03-07 19:04:02');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('15','1163231183001000906','1163231183001183381','1163231183001000906','2024-03-11 09:53:02');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('16','1163231183001000904','1163231183001183817,1163231183001183819,1163231183001183821,1163231183001183822,1163231183001185403','1163231183001000904','2024-03-11 10:03:00');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('17','1163231183001000902','1163231183001171090,1163231183001173263','1163231183001000902','2024-03-11 09:53:02');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('18','1163231183001000901','1163231183001168779','1163231183001000901','2024-03-11 09:53:02');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('19','1163231183001000899','1163231183001184985','1163231183001000899','2024-03-11 09:53:03');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('20','1163231183001000898','1163231183001176534','1163231183001000898','2024-03-11 10:03:00');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('21','1163231183001000897','1163231183001183395','1163231183001000897','2024-03-11 09:53:03');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('22','1163231183001000892','1163231183001170566,1163231183001181421','1163231183001000892','2024-03-11 09:53:03');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('23','1163231183001000891','1163231183001170870','1163231183001000891','2024-03-11 09:53:03');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('24','1163231183001000890',NULL,'1163231183001000890','2024-03-11 09:53:04');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('25','1152223238001000933','1152223238001187837','1152223238001000933','2024-03-07 19:04:15');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('26','1152223238001000932','1152223238001187689','1152223238001000932','2024-03-07 19:04:14');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('27','1152223238001000930','1152223238001185478','1152223238001000930','2024-03-07 19:04:14');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('28','1152223238001000929',NULL,'1152223238001000929','2024-03-07 19:04:14');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('29','1163231183001000934','1163231183001186095','1163231183001000934','2024-03-11 09:53:00');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('30','1140756777001000921','','1140756777001000921','2024-03-07 19:04:06');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('31','1140756777001000915','','1140756777001000915','2024-03-07 19:04:07');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('32','1164984051001000885','','1164984051001000885','2024-03-07 19:04:08');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('33','1164984051001000406','','1164984051001000406','2024-03-07 19:04:08');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('34','1166914855001000919','','1166914855001000919','2024-03-07 19:04:08');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('35','1159626479001000819','','1159626479001000819','2024-03-07 19:04:09');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('36','1159626479001000155','','1159626479001000155','2024-03-07 19:04:09');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('37','1159626479001000137','','1159626479001000137','2024-03-07 19:04:09');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('38','1159626479001000135','','1159626479001000135','2024-03-07 19:04:10');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('39','1159626479001000134','','1159626479001000134','2024-03-07 19:04:10');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('40','1159626479001000126','','1159626479001000126','2024-03-07 19:04:10');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('41','1159626479001000125','','1159626479001000125','2024-03-07 19:04:11');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('42','1159626479001000124','','1159626479001000124','2024-03-07 19:04:11');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('43','1159626479001000076','','1159626479001000076','2024-03-07 19:04:11');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('44','1159626479001000074','','1159626479001000074','2024-03-07 19:04:12');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('45','1159626479001000055','','1159626479001000055','2024-03-07 19:04:12');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('46','1159626479001000054','','1159626479001000054','2024-03-07 19:04:12');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('47','1159626479001000051','','1159626479001000051','2024-03-07 19:04:13');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('48','1136225275001000012','','1136225275001000012','2024-03-07 19:04:13');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('49','1140756777001000939','1140756777001179868',NULL,'2024-03-08 16:43:00');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('50','1163231183001000936','1163231183001174055,1163231183001183545',NULL,'2024-03-11 09:53:00');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('51','1140756777001000938','','1140756777001000938','2024-03-11 13:26:13');
insert into `tapd_entry_test_plan_bind_story` (`id`, `tapd_test_plan_id`, `relative_dev_story_ids`, `relative_test_story_id`, `update_time`) values('52','1166914855001000941','','1166914855001000941','2024-03-11 13:26:13');


insert into `t` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `workspace_id`, `module_name`, `module_url`, `module_is_active`, `module_desc`) values('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12','0','40007','400','test_plan','test_plans','1','初始化tapd项目模块数据。zt@2024-02-20');
insert into `t` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `workspace_id`, `module_name`, `module_url`, `module_is_active`, `module_desc`) values('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12','0','41007','410','test_plan','test_plans','1','初始化tapd项目模块数据。zt@2024-02-20');
insert into `t` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `workspace_id`, `module_name`, `module_url`, `module_is_active`, `module_desc`) values('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12','0','41107','411','test_plan','test_plans','1','初始化tapd项目模块数据。zt@2024-02-20');
insert into `t` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `workspace_id`, `module_name`, `module_url`, `module_is_active`, `module_desc`) values('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12','0','41207','412','test_plan','test_plans','1','初始化tapd项目模块数据。zt@2024-02-20');
insert into `t` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `workspace_id`, `module_name`, `module_url`, `module_is_active`, `module_desc`) values('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12','0','41307','413','test_plan','test_plans','1','初始化tapd项目模块数据。zt@2024-02-20');
insert into `t` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `workspace_id`, `module_name`, `module_url`, `module_is_active`, `module_desc`) values('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12','0','41407','414','test_plan','test_plans','1','初始化tapd项目模块数据。zt@2024-02-20');
insert into `t` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `workspace_id`, `module_name`, `module_url`, `module_is_active`, `module_desc`) values('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12','0','42007','420','test_plan','test_plans','1','初始化tapd项目模块数据。zt@2024-02-20');
insert into `t` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `workspace_id`, `module_name`, `module_url`, `module_is_active`, `module_desc`) values('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12','0','42107','421','test_plan','test_plans','1','初始化tapd项目模块数据。zt@2024-02-20');
insert into `t` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `workspace_id`, `module_name`, `module_url`, `module_is_active`, `module_desc`) values('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12','0','42207','422','test_plan','test_plans','1','初始化tapd项目模块数据。zt@2024-02-20');
insert into `t` (`create_user`, `create_time`, `update_user`, `update_time`, `stamp`, `id`, `workspace_id`, `module_name`, `module_url`, `module_is_active`, `module_desc`) values('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12','0','43007','430','test_plan','test_plans','1','初始化tapd项目模块数据。zt@2024-02-20');
