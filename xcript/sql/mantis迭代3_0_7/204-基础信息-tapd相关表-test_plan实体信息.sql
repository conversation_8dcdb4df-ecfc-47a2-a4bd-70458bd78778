-- ----------------------------
-- 204、tapd测试计划实体表：tapd_entry_test_plan
-- 说明：
-- ----------------------------
select *
from tapd_entry_test_plan;

DROP TABLE IF EXISTS tapd_entry_test_plan;
CREATE TABLE tapd_entry_test_plan
(
    create_user                    VARCHAR(20) COMMENT '创建人',
    create_time                    DATETIME(0) COMMENT '创建时间',
    update_user                    VARCHAR(20) COMMENT '修改人',
    update_time                    DATETIME(0) COMMENT '修改时间',
    stamp                          BIGINT(20) DEFAULT 0 COMMENT '版本',
    id                             BIGINT(20) AUTO_INCREMENT,
    ins_batch_number               BIGINT(20) COMMENT '插入时批次号',
    upd_batch_number               BIGINT(20) COMMENT '最后一次更新时批次号',
    del_batch_number               BIGINT(20) COMMENT '最后一次删除时批次号',
    del_user                       VARCHAR(20) COMMENT '最后一次删除人',
    del_time                       DATETIME(0) COMMENT '最后一次删除时间',
    rec_batch_number               BIGINT(20) COMMENT '最后一次恢复时批次号',
    rec_user                       VARCHAR(20) COMMENT '最后一次恢复人',
    rec_time                       DATETIME(0) COMMENT '最后一次恢复时间',

    entry_status                   INT(4) COMMENT '数据实体状态：1-新记录、2-更新过、3-已删除',
    entry_desc                     VARCHAR(255) COMMENT '数据实体说明',

    res_json                       JSON COMMENT '请求返回Json',
    tapd_workspace_id              BIGINT(20) COMMENT '项目ID',
    tapd_entry_id                  BIGINT(20) NOT NULL COMMENT '数据实体ID（字符非数字）',

    tapd_test_plan_name            VARCHAR(200) COMMENT '测试计划标题',
    tapd_test_plan_description     VARCHAR(255) COMMENT '描述（备用）',
    tapd_test_plan_iteration_id    BIGINT(20) COMMENT '测试计划迭代ID',
    tapd_test_plan_version         VARCHAR(100) COMMENT '版本',
    tapd_test_plan_owner           VARCHAR(100) COMMENT '测试计划负责人',
    tapd_test_plan_status          VARCHAR(20) COMMENT '测试计划状态',
    tapd_test_plan_type            VARCHAR(100) COMMENT '测试计划类型',
    tapd_test_plan_start_date      DATE COMMENT '预计开始',
    tapd_test_plan_end_date        DATE COMMENT '预计结束',
    tapd_test_plan_creator         VARCHAR(100) COMMENT '创建人',
    tapd_test_plan_created         DATETIME(0) COMMENT '创建时间',
    tapd_test_plan_modifier        VARCHAR(100) COMMENT '创建人',
    tapd_test_plan_modified        DATETIME(0) COMMENT '最后修改时间',
    tapd_test_plan_created_from    VARCHAR(100) COMMENT '测试计划创建来源',

    tapd_test_plan_custom_field_1  VARCHAR(100) COMMENT '自定义字段001',
    tapd_test_plan_custom_field_2  VARCHAR(100) COMMENT '自定义字段002',
    tapd_test_plan_custom_field_3  VARCHAR(100) COMMENT '自定义字段003',
    tapd_test_plan_custom_field_4  VARCHAR(100) COMMENT '自定义字段004',
    tapd_test_plan_custom_field_5  VARCHAR(100) COMMENT '自定义字段005',
    tapd_test_plan_custom_field_6  VARCHAR(100) COMMENT '自定义字段006',
    tapd_test_plan_custom_field_7  VARCHAR(100) COMMENT '自定义字段007',
    tapd_test_plan_custom_field_8  VARCHAR(100) COMMENT '自定义字段008',
    tapd_test_plan_custom_field_9  VARCHAR(100) COMMENT '自定义字段009',
    tapd_test_plan_custom_field_10 VARCHAR(100) COMMENT '自定义字段010',
    tapd_test_plan_custom_field_11 VARCHAR(100) COMMENT '自定义字段011',
    tapd_test_plan_custom_field_12 VARCHAR(100) COMMENT '自定义字段012',
    tapd_test_plan_custom_field_13 VARCHAR(100) COMMENT '自定义字段013',
    tapd_test_plan_custom_field_14 VARCHAR(100) COMMENT '自定义字段014',
    tapd_test_plan_custom_field_15 VARCHAR(100) COMMENT '自定义字段015',
    tapd_test_plan_custom_field_16 VARCHAR(100) COMMENT '自定义字段016',
    tapd_test_plan_custom_field_17 VARCHAR(100) COMMENT '自定义字段017',
    tapd_test_plan_custom_field_18 VARCHAR(100) COMMENT '自定义字段018',
    tapd_test_plan_custom_field_19 VARCHAR(100) COMMENT '自定义字段019',
    tapd_test_plan_custom_field_20 VARCHAR(100) COMMENT '自定义字段020',
    tapd_test_plan_custom_field_21 VARCHAR(100) COMMENT '自定义字段021',
    tapd_test_plan_custom_field_22 VARCHAR(100) COMMENT '自定义字段022',
    tapd_test_plan_custom_field_23 VARCHAR(100) COMMENT '自定义字段023',
    tapd_test_plan_custom_field_24 VARCHAR(100) COMMENT '自定义字段024',
    tapd_test_plan_custom_field_25 VARCHAR(100) COMMENT '自定义字段025',
    tapd_test_plan_custom_field_26 VARCHAR(100) COMMENT '自定义字段026',
    tapd_test_plan_custom_field_27 VARCHAR(100) COMMENT '自定义字段027',
    tapd_test_plan_custom_field_28 VARCHAR(100) COMMENT '自定义字段028',
    tapd_test_plan_custom_field_29 VARCHAR(100) COMMENT '自定义字段029',
    tapd_test_plan_custom_field_30 VARCHAR(100) COMMENT '自定义字段030',
    tapd_test_plan_custom_field_31 VARCHAR(100) COMMENT '自定义字段031',
    tapd_test_plan_custom_field_32 VARCHAR(100) COMMENT '自定义字段032',
    tapd_test_plan_custom_field_33 VARCHAR(100) COMMENT '自定义字段033',
    tapd_test_plan_custom_field_34 VARCHAR(100) COMMENT '自定义字段034',
    tapd_test_plan_custom_field_35 VARCHAR(100) COMMENT '自定义字段035',
    tapd_test_plan_custom_field_36 VARCHAR(100) COMMENT '自定义字段036',
    tapd_test_plan_custom_field_37 VARCHAR(100) COMMENT '自定义字段037',
    tapd_test_plan_custom_field_38 VARCHAR(100) COMMENT '自定义字段038',
    tapd_test_plan_custom_field_39 VARCHAR(100) COMMENT '自定义字段039',
    tapd_test_plan_custom_field_40 VARCHAR(100) COMMENT '自定义字段040',
    tapd_test_plan_custom_field_41 VARCHAR(100) COMMENT '自定义字段041',
    tapd_test_plan_custom_field_42 VARCHAR(100) COMMENT '自定义字段042',
    tapd_test_plan_custom_field_43 VARCHAR(100) COMMENT '自定义字段043',
    tapd_test_plan_custom_field_44 VARCHAR(100) COMMENT '自定义字段044',
    tapd_test_plan_custom_field_45 VARCHAR(100) COMMENT '自定义字段045',
    tapd_test_plan_custom_field_46 VARCHAR(100) COMMENT '自定义字段046',
    tapd_test_plan_custom_field_47 VARCHAR(100) COMMENT '自定义字段047',
    tapd_test_plan_custom_field_48 VARCHAR(100) COMMENT '自定义字段048',
    tapd_test_plan_custom_field_49 VARCHAR(100) COMMENT '自定义字段049',
    tapd_test_plan_custom_field_50 VARCHAR(100) COMMENT '自定义字段050',

    PRIMARY KEY (id),
    UNIQUE KEY udx_entry_id (tapd_entry_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'tapd测试计划实体表';
