-- ----------------------------
-- 2-1、tapd任务实体表：tapd_entry_task
-- 说明：
-- ----------------------------
select *
from tapd_entry_task;

DROP TABLE IF EXISTS tapd_entry_task;
CREATE TABLE tapd_entry_task
(
    create_user                    VARCHAR(20) COMMENT '创建人',
    create_time                    DATETIME(0) COMMENT '创建时间',
    update_user                    VARCHAR(20) COMMENT '修改人',
    update_time                    DATETIME(0) COMMENT '修改时间',
    stamp                          BIGINT(20) DEFAULT 0 COMMENT '版本',
    id                             BIGINT(20) AUTO_INCREMENT,
    ins_batch_number               BIGINT(20) COMMENT '插入时批次号',
    upd_batch_number               BIGINT(20) COMMENT '最后一次更新时批次号',
    del_batch_number               BIGINT(20) COMMENT '最后一次删除时批次号',
    del_user                       VARCHAR(20) COMMENT '最后一次删除人',
    del_time                       DATETIME(0) COMMENT '最后一次删除时间',
    rec_batch_number               BIGINT(20) COMMENT '最后一次恢复时批次号',
    rec_user                       VARCHAR(20) COMMENT '最后一次恢复人',
    rec_time                       DATETIME(0) COMMENT '最后一次恢复时间',

    entry_status                   INT(4) COMMENT '数据实体状态：1-新记录、2-更新过、3-已删除',
    entry_desc                     VARCHAR(255) COMMENT '数据实体说明',

    res_json                       JSON COMMENT '请求返回Json',
    tapd_workspace_id              BIGINT(20) COMMENT '项目ID',
    tapd_entry_id                  BIGINT(20) NOT NULL COMMENT '数据实体ID（字符非数字）',

    tapd_task_name                 VARCHAR(200) COMMENT '任务标题',
    tapd_task_description          VARCHAR(255) COMMENT '描述（备用）',
    tapd_task_creator              VARCHAR(100) COMMENT '创建人',
    tapd_task_created              DATETIME(0) COMMENT '创建时间',
    tapd_task_modified             DATETIME(0) COMMENT '最后修改时间',
    tapd_task_status               VARCHAR(20) COMMENT '任务状态',
    tapd_task_owner                VARCHAR(100) COMMENT '处理人',
    tapd_task_cc                   VARCHAR(100) COMMENT '抄送人',
    tapd_task_begin                DATE COMMENT '预计开始',
    tapd_task_due                  DATE COMMENT '预计结束',
    tapd_task_story_id             BIGINT(20) COMMENT '故事ID',
    tapd_task_iteration_id         BIGINT(20) COMMENT '迭代ID',
    tapd_task_priority             VARCHAR(50) COMMENT '优先级',
    tapd_task_progress             VARCHAR(20) COMMENT '进步',
    tapd_task_completed            DATETIME(0) COMMENT '完成时间',
    tapd_task_effort_completed     VARCHAR(20) COMMENT '完成工时',

    tapd_task_exceed               FLOAT(6, 2) COMMENT '超出工时',
    tapd_task_remain               FLOAT(6, 2) COMMENT '剩余工时',

    tapd_task_effort               VARCHAR(20) COMMENT '预估工时',
    tapd_task_has_attachment       VARCHAR(20) COMMENT '有附件',
    tapd_task_release_id           BIGINT(20) COMMENT '发布计划ID',
    tapd_task_label                VARCHAR(50) COMMENT '标签查询',
    tapd_task_priority_label       VARCHAR(50) COMMENT '优先级标签',

    tapd_task_custom_field_one     VARCHAR(100) COMMENT '自定义字段001',
    tapd_task_custom_field_two     VARCHAR(100) COMMENT '自定义字段002',
    tapd_task_custom_field_three   VARCHAR(100) COMMENT '自定义字段003',
    tapd_task_custom_field_four    VARCHAR(100) COMMENT '自定义字段004',
    tapd_task_custom_field_five    VARCHAR(100) COMMENT '自定义字段005',
    tapd_task_custom_field_six     VARCHAR(100) COMMENT '自定义字段006',
    tapd_task_custom_field_seven   VARCHAR(100) COMMENT '自定义字段007',
    tapd_task_custom_field_eight   VARCHAR(100) COMMENT '自定义字段008',
    tapd_task_custom_field_9       VARCHAR(100) COMMENT '自定义字段009',
    tapd_task_custom_field_10      VARCHAR(100) COMMENT '自定义字段010',
    tapd_task_custom_field_11      VARCHAR(100) COMMENT '自定义字段011',
    tapd_task_custom_field_12      VARCHAR(100) COMMENT '自定义字段012',
    tapd_task_custom_field_13      VARCHAR(100) COMMENT '自定义字段013',
    tapd_task_custom_field_14      VARCHAR(100) COMMENT '自定义字段014',
    tapd_task_custom_field_15      VARCHAR(100) COMMENT '自定义字段015',
    tapd_task_custom_field_16      VARCHAR(100) COMMENT '自定义字段016',
    tapd_task_custom_field_17      VARCHAR(100) COMMENT '自定义字段017',
    tapd_task_custom_field_18      VARCHAR(100) COMMENT '自定义字段018',
    tapd_task_custom_field_19      VARCHAR(100) COMMENT '自定义字段019',
    tapd_task_custom_field_20      VARCHAR(100) COMMENT '自定义字段020',
    tapd_task_custom_field_21      VARCHAR(100) COMMENT '自定义字段021',
    tapd_task_custom_field_22      VARCHAR(100) COMMENT '自定义字段022',
    tapd_task_custom_field_23      VARCHAR(100) COMMENT '自定义字段023',
    tapd_task_custom_field_24      VARCHAR(100) COMMENT '自定义字段024',
    tapd_task_custom_field_25      VARCHAR(100) COMMENT '自定义字段025',
    tapd_task_custom_field_26      VARCHAR(100) COMMENT '自定义字段026',
    tapd_task_custom_field_27      VARCHAR(100) COMMENT '自定义字段027',
    tapd_task_custom_field_28      VARCHAR(100) COMMENT '自定义字段028',
    tapd_task_custom_field_29      VARCHAR(100) COMMENT '自定义字段029',
    tapd_task_custom_field_30      VARCHAR(100) COMMENT '自定义字段030',
    tapd_task_custom_field_31      VARCHAR(100) COMMENT '自定义字段031',
    tapd_task_custom_field_32      VARCHAR(100) COMMENT '自定义字段032',
    tapd_task_custom_field_33      VARCHAR(100) COMMENT '自定义字段033',
    tapd_task_custom_field_34      VARCHAR(100) COMMENT '自定义字段034',
    tapd_task_custom_field_35      VARCHAR(100) COMMENT '自定义字段035',
    tapd_task_custom_field_36      VARCHAR(100) COMMENT '自定义字段036',
    tapd_task_custom_field_37      VARCHAR(100) COMMENT '自定义字段037',
    tapd_task_custom_field_38      VARCHAR(100) COMMENT '自定义字段038',
    tapd_task_custom_field_39      VARCHAR(100) COMMENT '自定义字段039',
    tapd_task_custom_field_40      VARCHAR(100) COMMENT '自定义字段040',
    tapd_task_custom_field_41      VARCHAR(100) COMMENT '自定义字段041',
    tapd_task_custom_field_42      VARCHAR(100) COMMENT '自定义字段042',
    tapd_task_custom_field_43      VARCHAR(100) COMMENT '自定义字段043',
    tapd_task_custom_field_44      VARCHAR(100) COMMENT '自定义字段044',
    tapd_task_custom_field_45      VARCHAR(100) COMMENT '自定义字段045',
    tapd_task_custom_field_46      VARCHAR(100) COMMENT '自定义字段046',
    tapd_task_custom_field_47      VARCHAR(100) COMMENT '自定义字段047',
    tapd_task_custom_field_48      VARCHAR(100) COMMENT '自定义字段048',
    tapd_task_custom_field_49      VARCHAR(100) COMMENT '自定义字段049',
    tapd_task_custom_field_50      VARCHAR(100) COMMENT '自定义字段050',

    tapd_task_custom_plan_field_1  VARCHAR(100) COMMENT '自定义计划字段01',
    tapd_task_custom_plan_field_2  VARCHAR(100) COMMENT '自定义计划字段02',
    tapd_task_custom_plan_field_3  VARCHAR(100) COMMENT '自定义计划字段03',
    tapd_task_custom_plan_field_4  VARCHAR(100) COMMENT '自定义计划字段04',
    tapd_task_custom_plan_field_5  VARCHAR(100) COMMENT '自定义计划字段05',
    tapd_task_custom_plan_field_6  VARCHAR(100) COMMENT '自定义计划字段06',
    tapd_task_custom_plan_field_7  VARCHAR(100) COMMENT '自定义计划字段07',
    tapd_task_custom_plan_field_8  VARCHAR(100) COMMENT '自定义计划字段08',
    tapd_task_custom_plan_field_9  VARCHAR(100) COMMENT '自定义计划字段09',
    tapd_task_custom_plan_field_10 VARCHAR(100) COMMENT '自定义计划字段10',

    PRIMARY KEY (id),
    UNIQUE KEY udx_entry_id (tapd_entry_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'tapd任务实体表';
