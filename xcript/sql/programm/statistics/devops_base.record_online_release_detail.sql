create table devops_base.record_online_release_detail
(

select
distinct
ori.pipeline_id,
u.cn_name,
ori.module_name,
ori.apply_at,
ori.br_style,
case when json_extract(replace(r.action_value, '\'', '"'), '$.suite_code') like '%zb"' then '灾备' else '生产' end as type,
case ori.team_id
when 3 then '交易中台'
when 2 then '交易后台'
when 1 then 'CRM'
when 7 then '非交易'
end as team,
json_extract(replace(r.action_value, '\'', '"'), '$.node_list[0]') as node,
r.operate_time

from
(
select a.*,
case when @ref_name = a.module_name then @ref_seq := @ref_seq +1 else @ref_seq :=1 end as seq,
@ref_name:=a.module_name as name
 from
(
SELECT
	distinct pa.pipeline_id, pa.applicant, ab.module_name, pa.apply_at, pa.env, ii.br_style, at.team_id
FROM
	spider.iter_mgt_publish_application pa
inner join
spider.app_mgt_app_build ab
on concat(';', pa.appName, ';') like concat('%;', ab.module_name, ';%')
and ab.package_type in ('jar', 'war')
inner join
	spider.app_mgt_app_module am
on ab.module_name = am.module_name
inner join
	spider.app_mgt_app_team at
on am.app_id = at.app_id and at.team_id in (1, 2, 3, 7)
inner join
	spider.iter_mgt_iter_info ii
on ii.pipeline_id = pa.pipeline_id
where
apply_at > '2023-01-01'
and pa.env = 'prod'
order by ab.module_name, pa.apply_at
) a, (select @ref_name:='') b, (select @ref_seq:=0) c
) ori


left outer join
(
select a.*,
case when @next_name = a.module_name then @next_seq := @next_seq +1 else @next_seq :=0 end as seq,
@next_name:=a.module_name as name
 from
(
SELECT
	distinct pa.pipeline_id, pa.applicant, ab.module_name, pa.apply_at, pa.env, ii.br_style, at.team_id
FROM
	spider.iter_mgt_publish_application pa
inner join
spider.app_mgt_app_build ab
on concat(';', pa.appName, ';') like concat('%;', ab.module_name, ';%')
and ab.package_type in ('jar', 'war')
inner join
	spider.app_mgt_app_module am
on ab.module_name = am.module_name
inner join
	spider.app_mgt_app_team at
on am.app_id = at.app_id and at.team_id in (1, 2, 3, 7)
inner join
	spider.iter_mgt_iter_info ii
on ii.pipeline_id = pa.pipeline_id
where
apply_at > '2023-01-01'
and pa.env = 'prod'
order by ab.module_name, pa.apply_at
) a, (select @next_name:='') b, (select @next_seq:=0) c
) next
on ori.seq = next.seq and ori.module_name = next.module_name

-- ===============合并发布记录==============================
inner join
	spider.user_action_record r
on r.operate_time > '2023-01-01' and r.action_item in ('deploy', 'update_and_deploy', 'code_update', 'update_and_deploy_and_verify')
and r.action_value like concat("%'app_name': '", ori.module_name, "'%")
and r.action_value like concat("%'iteration_id': '", ori.pipeline_id, "'%")
and r.operate_time > ori.apply_at
and r.operate_time < (case when next.apply_at is not null then next.apply_at else '2024-01-01' end)
-- ==============================合并发布记录===============

left outer join
	devops_base.v_users u
on u.username = ori.applicant


)