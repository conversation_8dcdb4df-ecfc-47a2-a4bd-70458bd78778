create table record_online_operating
(
select sor.start_at, substring(sor.request_result, locate('-10-', sor.request_result)) as request_result,
	case when sor.exec_cmd like '%rollback%' then 'rollback' else 'release' end as exec_cmd
	from spider.task_mgt_salt_operation_results sor where sor.start_at > '2023-01-01' and sor.start_at < '2023-07-01'
	and sor.cmd_type = 'cmd.run'
	and sor.request_url like 'https://10.%'
	and sor.request_status_code = '200'
	and sor.request_result != '{"return": [{}]}'
)