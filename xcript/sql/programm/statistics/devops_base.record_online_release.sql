
create table devops_base.record_online_release
(
select distinct
ori.pipeline_id as 分支号,
ori.module_name as 应用名,
ori.apply_at as 发布日期,
ori.br_style as 分支类型,
sor.exec_cmd as 操作类型,
case ori.team_id
when 3 then '交易中台'
when 2 then '交易后台'
when 1 then 'CRM'
when 7 then '非交易'
end as 团队
-- , next.apply_at as next_at
-- , locate('-10-', esc.minion_id)
-- , substring(esc.minion_id, locate('-10-', esc.minion_id)) as minion_id
-- , sor.exec_cmd
from
(
select a.*,
case when @ref_name = a.module_name then @ref_seq := @ref_seq +1 else @ref_seq :=1 end as seq,
@ref_name:=a.module_name as name
 from
(
SELECT
	pa.pipeline_id, ab.module_name, pa.apply_at, pa.env, ii.br_style, at.team_id
FROM
	spider.iter_mgt_publish_application pa
inner join
spider.app_mgt_app_build ab
on concat(';', pa.appName, ';') like concat('%;', ab.module_name, ';%')
and ab.package_type in ('jar', 'war')
inner join
	spider.app_mgt_app_module am
on ab.module_name = am.module_name
inner join
	spider.app_mgt_app_team at
on am.app_id = at.app_id and at.team_id in (1, 2, 3, 7)
inner join
	spider.iter_mgt_iter_info ii
on ii.pipeline_id = pa.pipeline_id
where
apply_at > '2023-01-01' and apply_at < '2023-07-01'
and pa.env = 'prod'
order by ab.module_name, pa.apply_at
) a, (select @ref_name:='') b, (select @ref_seq:=0) c
) ori


left outer join
(
select a.*,
case when @next_name = a.module_name then @next_seq := @next_seq +1 else @next_seq :=0 end as seq,
@next_name:=a.module_name as name
 from
(
SELECT
	pa.pipeline_id, ab.module_name, pa.apply_at, pa.env, ii.br_style, at.team_id
FROM
	spider.iter_mgt_publish_application pa
inner join
spider.app_mgt_app_build ab
on concat(';', pa.appName, ';') like concat('%;', ab.module_name, ';%')
and ab.package_type in ('jar', 'war')
inner join
	spider.app_mgt_app_module am
on ab.module_name = am.module_name
inner join
	spider.app_mgt_app_team at
on am.app_id = at.app_id and at.team_id in (1, 2, 3, 7)
inner join
	spider.iter_mgt_iter_info ii
on ii.pipeline_id = pa.pipeline_id
where
apply_at > '2023-01-01' and apply_at < '2023-07-01'
and pa.env = 'prod'
order by ab.module_name, pa.apply_at
) a, (select @next_name:='') b, (select @next_seq:=0) c
) next
on ori.seq = next.seq and ori.module_name = next.module_name

inner join
spider.publish_exec_salt_cmd esc
on esc.app_name = ori.module_name


inner join
	devops_base.record_online_operating sor
on sor.start_at > ori.apply_at and sor.start_at < (case when next.apply_at is not null then next.apply_at else '2024-01-01' end)
and sor.request_result like concat(substring(esc.minion_id, locate('-10-', esc.minion_id)), '%')

)