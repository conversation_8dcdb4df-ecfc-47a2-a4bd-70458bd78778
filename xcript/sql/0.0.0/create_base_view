//迭代应用视图
CREATE VIEW spider_iter_app_info
AS SELECT m.module_name,f.pipeline_id
FROM spider.app_mgt_app_info i RIGHT JOIN
spider.app_mgt_app_module m ON m.app_id = i.id
right join spider.iter_mgt_iter_app_info f on f.appName = m.module_name
WHERE CONCAT(i.git_url, i.git_path) IS NOT NULL AND m.need_online = 1 AND
 i.third_party_middleware = 0


 //迭代应用环境视图
 CREATE VIEW spider_app_env
 AS select DISTINCT  b.module_name,s.suite_code from spider.env_mgt_node_bind b
 left join spider.env_mgt_suite s on b.suite_id = s.id
 LEFT JOIN  spider.app_mgt_app_module m on m.module_name=b.module_name
  LEFT JOIN spider.app_mgt_app_info i on m.app_id = i.id
where m.need_online = 1  AND i.third_party_middleware = 0
