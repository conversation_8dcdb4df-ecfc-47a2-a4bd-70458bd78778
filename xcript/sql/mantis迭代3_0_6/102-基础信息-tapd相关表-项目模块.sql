-- ----------------------------
-- 1-2、tapd项目模块表：tapd_workspace_module
-- 说明：每个项目都启用了哪些模块
-- 模块：iterations、stories、tasks、bugs、launch_forms、test_plans、timesheets
-- ----------------------------
select * from tapd_workspace_module;
DROP TABLE IF EXISTS tapd_workspace_module;
CREATE TABLE tapd_workspace_module(
    create_user VARCHAR(20) COMMENT '创建人',
    create_time DATETIME(0) COMMENT '创建时间',
    update_user VARCHAR(20) COMMENT '修改人',
    update_time DATETIME(0) COMMENT '修改时间',
    stamp BIGINT(20) DEFAULT 0 COMMENT '版本',
    id BIGINT(20) AUTO_INCREMENT,
    workspace_id BIGINT(20) COMMENT '项目ID',
    module_name varchar(50) COMMENT '项目模块名',
    module_url varchar(100) COMMENT '项目url路径',
    module_is_active TINYINT(1) COMMENT '项目模块是否可用',
    module_desc VARCHAR(255) COMMENT '项目模块说明',
    PRIMARY KEY (id),
    UNIQUE KEY udx_workspace_module(workspace_id, module_name)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = 'tapd项目模块表';
-- 100
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 10001, 100, 'workspace', 'workspaces', TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 10002, 100, 'iteration', 'iterations', FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 10003, 100, 'story', 'stories', FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 10004, 100, 'task', 'tasks', FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 10005, 100, 'bug', 'bugs', TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 10006, 100, 'launch_form', 'launch_forms', FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 10007, 100, 'test_plan', 'test_plans', FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 10008, 100, 'timesheet', 'timesheets', FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
-- 200
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 20001, 200, 'workspace', 'workspaces', TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 20002, 200, 'iteration', 'iterations', TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 20003, 200, 'story', 'stories', TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 20004, 200, 'task', 'tasks', TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 20005, 200, 'bug', 'bugs', TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 20006, 200, 'launch_form', 'launch_forms', FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 20007, 200, 'test_plan', 'test_plans', TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 20008, 200, 'timesheet', 'timesheets', TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
-- 201
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 20101, 201, 'workspace', 'workspaces',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 20102, 201, 'iteration', 'iterations',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 20103, 201, 'story', 'stories',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 20104, 201, 'task', 'tasks',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 20105, 201, 'bug', 'bugs',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 20106, 201, 'launch_form', 'launch_forms',  FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 20107, 201, 'test_plan', 'test_plans',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 20108, 201, 'timesheet', 'timesheets',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
-- 300
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 30001, 300, 'workspace', 'workspaces',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 30002, 300, 'iteration', 'iterations',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 30003, 300, 'story', 'stories',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 30004, 300, 'task', 'tasks',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 30005, 300, 'bug', 'bugs',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 30006, 300, 'launch_form', 'launch_forms',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 30007, 300, 'test_plan', 'test_plans',  FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 30008, 300, 'timesheet', 'timesheets',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
-- 400
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 40001, 400, 'workspace', 'workspaces',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 40002, 400, 'iteration', 'iterations',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 40003, 400, 'story', 'stories',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 40004, 400, 'task', 'tasks',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 40005, 400, 'bug', 'bugs',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 40006, 400, 'launch_form', 'launch_forms',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 40007, 400, 'test_plan', 'test_plans',  FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 40008, 400, 'timesheet', 'timesheets',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
-- 410
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41001, 410, 'workspace', 'workspaces',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41002, 410, 'iteration', 'iterations',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41003, 410, 'story', 'stories',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41004, 410, 'task', 'tasks',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41005, 410, 'bug', 'bugs',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41006, 410, 'launch_form', 'launch_forms',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41007, 410, 'test_plan', 'test_plans',  FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41008, 410, 'timesheet', 'timesheets',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
-- 411
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41101, 411, 'workspace', 'workspaces',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41102, 411, 'iteration', 'iterations',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41103, 411, 'story', 'stories',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41104, 411, 'task', 'tasks',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41105, 411, 'bug', 'bugs',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41106, 411, 'launch_form', 'launch_forms',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41107, 411, 'test_plan', 'test_plans',  FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41108, 411, 'timesheet', 'timesheets',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
-- 412
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41201, 412, 'workspace', 'workspaces',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41202, 412, 'iteration', 'iterations',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41203, 412, 'story', 'stories',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41204, 412, 'task', 'tasks',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41205, 412, 'bug', 'bugs',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41206, 412, 'launch_form', 'launch_forms',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41207, 412, 'test_plan', 'test_plans',  FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41208, 412, 'timesheet', 'timesheets',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
-- 413
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41301, 413, 'workspace', 'workspaces',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41302, 413, 'iteration', 'iterations',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41303, 413, 'story', 'stories',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41304, 413, 'task', 'tasks',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41305, 413, 'bug', 'bugs',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41306, 413, 'launch_form', 'launch_forms',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41307, 413, 'test_plan', 'test_plans',  FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41308, 413, 'timesheet', 'timesheets',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
-- 414
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41401, 414, 'workspace', 'workspaces',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41402, 414, 'iteration', 'iterations',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41403, 414, 'story', 'stories',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41404, 414, 'task', 'tasks',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41405, 414, 'bug', 'bugs',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41406, 414, 'launch_form', 'launch_forms',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41407, 414, 'test_plan', 'test_plans',  FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 41408, 414, 'timesheet', 'timesheets',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
-- 420
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42001, 420, 'workspace', 'workspaces',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42002, 420, 'iteration', 'iterations',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42003, 420, 'story', 'stories',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42004, 420, 'task', 'tasks',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42005, 420, 'bug', 'bugs',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42006, 420, 'launch_form', 'launch_forms',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42007, 420, 'test_plan', 'test_plans',  FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42008, 420, 'timesheet', 'timesheets',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
-- 421
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42101, 421, 'workspace', 'workspaces',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42102, 421, 'iteration', 'iterations',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42103, 421, 'story', 'stories',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42104, 421, 'task', 'tasks',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42105, 421, 'bug', 'bugs',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42106, 421, 'launch_form', 'launch_forms',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42107, 421, 'test_plan', 'test_plans',  FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42108, 421, 'timesheet', 'timesheets',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
-- 422
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42201, 422, 'workspace', 'workspaces',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42202, 422, 'iteration', 'iterations',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42203, 422, 'story', 'stories',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42204, 422, 'task', 'tasks',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42205, 422, 'bug', 'bugs',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42206, 422, 'launch_form', 'launch_forms',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42207, 422, 'test_plan', 'test_plans',  FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 42208, 422, 'timesheet', 'timesheets',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
-- 430
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 43001, 430, 'workspace', 'workspaces',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 43002, 430, 'iteration', 'iterations',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 43003, 430, 'story', 'stories',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 43004, 430, 'task', 'tasks',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 43005, 430, 'bug', 'bugs',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 43006, 430, 'launch_form', 'launch_forms',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 43007, 430, 'test_plan', 'test_plans',  FALSE, '初始化tapd项目模块数据。zt@2024-02-20');
INSERT INTO tapd_workspace_module (create_user, create_time, update_user, update_time, stamp, id, workspace_id, module_name, module_url, module_is_active, module_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 43008, 430, 'timesheet', 'timesheets',  TRUE, '初始化tapd项目模块数据。zt@2024-02-20');

select * from tapd_code;
select * from tapd_workspace;
select tw.id as twid, tw.tapd_workspace_id, tw.tapd_workspace_name, tw.workspace_short_name,
       twm.id as twmid, twm.module_name, twm.module_url
from tapd_workspace tw
inner join tapd_workspace_module twm on twm.workspace_id = tw.id
where tw.workspace_is_active = 1
and twm.module_is_active = 1
and twm.module_name = 'bug'
and tw.tapd_workspace_id = 32443486
order by twm.id
;