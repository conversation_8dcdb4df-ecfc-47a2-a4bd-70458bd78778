-- ----------------------------
-- 2-1、tapd类型映射：tapd_workspace_type_conf
-- 说明：区分根据具体模块映射和同一种类型的统一映射
-- ----------------------------
select * from tapd_workspace_type_conf;
DROP TABLE IF EXISTS tapd_workspace_type_conf;
CREATE TABLE tapd_workspace_type_conf
(
    create_user       VARCHAR(20) COMMENT '创建人',
    create_time       DATETIME(0) COMMENT '创建时间',
    update_user       VARCHAR(20) COMMENT '修改人',
    update_time       DATETIME(0) COMMENT '修改时间',
    stamp             BIGINT(20) DEFAULT 0 COMMENT '版本',
    id                BIGINT(20) AUTO_INCREMENT,
    type_conf_name    varchar(100) COMMENT '类型映射名',
    workspace_type    TINYINT(1) COMMENT '项目类型:1-产线bug、2-研发、3-测试',
    tapd_entry_type   varchar(50) COMMENT '所属实体对象',
    tapd_custom_field varchar(50) COMMENT '自定义字段',
    tapd_type         varchar(50) COMMENT '自定义字段类型',
    tapd_name         varchar(50) COMMENT '自定义字段名称',
    tapd_options      varchar(999) COMMENT '自定义字段选项',
    tapd_sort         INT(4) COMMENT '自定义字段排序',
    conf_is_sync      TINYINT(1) COMMENT '映射是否同步',
    conf_table        varchar(50) COMMENT '映射表',
    conf_column       varchar(50) COMMENT '映射字段',
    conf_name         varchar(50) COMMENT '映射名称',
    conf_type         varchar(100) COMMENT '映射类型',
    conf_is_active    TINYINT(1) COMMENT '映射是否可用',
    conf_desc         VARCHAR(255) COMMENT '映射说明',
    PRIMARY KEY (id),
    UNIQUE KEY udx_type_conf_name (type_conf_name)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'tapd类型映射';
