-- ----------------------------
-- 1-1、tapd项目表：tapd_workspace
-- 说明：
-- ----------------------------
select *
from tapd_workspace;
DROP TABLE IF EXISTS tapd_workspace;
CREATE TABLE tapd_workspace
(
    create_user                VARCHAR(20) COMMENT '创建人',
    create_time                DATETIME(0) COMMENT '创建时间',
    update_user                VARCHAR(20) COMMENT '修改人',
    update_time                DATETIME(0) COMMENT '修改时间',
    stamp                      BIGINT(20) DEFAULT 0 COMMENT '版本',
    id                         BIGINT(20) AUTO_INCREMENT,
    tapd_workspace_id          BIGINT(20) COMMENT '项目ID',
    tapd_workspace_parent_id   BIGINT(20) COMMENT '项目父ID',
    tapd_workspace_company_id  BIGINT(20) COMMENT '项目所属公司ID',
    tapd_workspace_name        varchar(100) COMMENT '项目名',
    tapd_workspace_status      varchar(50) COMMENT '项目状态',
    tapd_workspace_creator_id  BIGINT(20) COMMENT '项目创建者ID',
    tapd_workspace_creator     varchar(100) COMMENT '项目创建者',
    tapd_workspace_created     DATETIME(0) COMMENT '项目创建时间',
    tapd_workspace_description varchar(999) COMMENT 'tapd项目说明',
    workspace_short_name       varchar(100) COMMENT '项目简称',
    workspace_type             TINYINT(1) COMMENT '项目类型:1-产线bug、2-平台、3-测试、4-研发',
    workspace_team_id          BIGINT(20) COMMENT '项目对应的小团队ID',
    workspace_is_active        TINYINT(1) COMMENT '项目是否可用',
    workspace_desc             VARCHAR(255) COMMENT '项目说明',
    PRIMARY KEY (id),
    UNIQUE KEY udx_workspace_name (tapd_workspace_name)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'tapd项目表';

INSERT INTO tapd_workspace (create_user, create_time, update_user, update_time, stamp, id, tapd_workspace_id, tapd_workspace_parent_id, tapd_workspace_company_id, tapd_workspace_name, tapd_workspace_status, tapd_workspace_creator_id, tapd_workspace_creator, tapd_workspace_created, tapd_workspace_description, workspace_short_name, workspace_type, workspace_team_id, workspace_is_active, workspace_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 100, 32443486, 23030071, 23030071, '研发产线bug记录', 'normal', 1012273351,'李远相 (<EMAIL>)', '2021-02-07 16:17:12', NULL, 'tapd-prod_bug', 1, 8, TRUE, '初始化tapd项目数据。zt@2024-02-20');
INSERT INTO tapd_workspace (create_user, create_time, update_user, update_time, stamp, id, tapd_workspace_id, tapd_workspace_parent_id, tapd_workspace_company_id, tapd_workspace_name, tapd_workspace_status, tapd_workspace_creator_id, tapd_workspace_creator, tapd_workspace_created, tapd_workspace_description, workspace_short_name, workspace_type, workspace_team_id, workspace_is_active, workspace_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 200, 57711941, 23030071, 23030071, '平台产品1线', 'normal', 2063554698,'张弋翔 (<EMAIL>)', '2019-10-16 14:27:00', '魔戒\r\n配置\r\nCI/CD', 'tapd-pa_one', 2, 1023, TRUE, '初始化tapd项目数据。zt@2024-02-20');
INSERT INTO tapd_workspace (create_user, create_time, update_user, update_time, stamp, id, tapd_workspace_id, tapd_workspace_parent_id, tapd_workspace_company_id, tapd_workspace_name, tapd_workspace_status, tapd_workspace_creator_id, tapd_workspace_creator, tapd_workspace_created, tapd_workspace_description, workspace_short_name, workspace_type, workspace_team_id, workspace_is_active, workspace_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 201, 50651707, 23030071, 23030071, '平台产品2线', 'normal', 2063554698,'张弋翔 (<EMAIL>)', '2019-10-11 10:03:04', NULL, 'tapd-pa_two', 2, 1024, TRUE, '初始化tapd项目数据。zt@2024-02-20');
INSERT INTO tapd_workspace (create_user, create_time, update_user, update_time, stamp, id, tapd_workspace_id, tapd_workspace_parent_id, tapd_workspace_company_id, tapd_workspace_name, tapd_workspace_status, tapd_workspace_creator_id, tapd_workspace_creator, tapd_workspace_created, tapd_workspace_description, workspace_short_name, workspace_type, workspace_team_id, workspace_is_active, workspace_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 300, 55014084, 23030071, 23030071, '研发部测试管理', 'normal', 1012273351,'李远相 (<EMAIL>)', '2021-06-11 11:18:25', NULL, 'tapd-test_mgt', 3, 8, TRUE, '初始化tapd项目数据。zt@2024-02-20');
-- 400
INSERT INTO tapd_workspace (create_user, create_time, update_user, update_time, stamp, id, tapd_workspace_id, tapd_workspace_parent_id, tapd_workspace_company_id, tapd_workspace_name, tapd_workspace_status, tapd_workspace_creator_id, tapd_workspace_creator, tapd_workspace_created, tapd_workspace_description, workspace_short_name, workspace_type, workspace_team_id, workspace_is_active, workspace_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 400, 59626479, 23030071, 23030071, '交易后台', 'normal', 71034848,'马骥 (<EMAIL>)', '2019-11-28 10:12:32', '清算、资金、机构、账户/支付', 'tapd-tp', 4, 2, TRUE, '初始化tapd项目数据。zt@2024-02-20');
-- 410
INSERT INTO tapd_workspace (create_user, create_time, update_user, update_time, stamp, id, tapd_workspace_id, tapd_workspace_parent_id, tapd_workspace_company_id, tapd_workspace_name, tapd_workspace_status, tapd_workspace_creator_id, tapd_workspace_creator, tapd_workspace_created, tapd_workspace_description, workspace_short_name, workspace_type, workspace_team_id, workspace_is_active, workspace_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 410, 41179151, 23030071, 23030071, '交易中台', 'normal', 1760524700,'吕猛 (<EMAIL>)', '2024-01-26 08:16:38', NULL, 'tapd-tms', 4, 3, TRUE, '初始化tapd项目数据。zt@2024-02-20');
INSERT INTO tapd_workspace (create_user, create_time, update_user, update_time, stamp, id, tapd_workspace_id, tapd_workspace_parent_id, tapd_workspace_company_id, tapd_workspace_name, tapd_workspace_status, tapd_workspace_creator_id, tapd_workspace_creator, tapd_workspace_created, tapd_workspace_description, workspace_short_name, workspace_type, workspace_team_id, workspace_is_active, workspace_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 411, 66914855, 23030071, 23030071, '基础组', 'normal', 1760524700,'吕猛 (<EMAIL>)', '2021-03-08 14:50:03', NULL, 'tapd-tms_base', 4, 1006, TRUE, '初始化tapd项目数据。zt@2024-02-20');
INSERT INTO tapd_workspace (create_user, create_time, update_user, update_time, stamp, id, tapd_workspace_id, tapd_workspace_parent_id, tapd_workspace_company_id, tapd_workspace_name, tapd_workspace_status, tapd_workspace_creator_id, tapd_workspace_creator, tapd_workspace_created, tapd_workspace_description, workspace_short_name, workspace_type, workspace_team_id, workspace_is_active, workspace_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 412, 52223238, 23030071, 23030071, '公募交易', 'normal', 1760524700,'吕猛 (<EMAIL>)', '2020-09-03 16:46:02', NULL, 'tapd-tms_gm', 4, 1007, TRUE, '初始化tapd项目数据。zt@2024-02-20');
INSERT INTO tapd_workspace (create_user, create_time, update_user, update_time, stamp, id, tapd_workspace_id, tapd_workspace_parent_id, tapd_workspace_company_id, tapd_workspace_name, tapd_workspace_status, tapd_workspace_creator_id, tapd_workspace_creator, tapd_workspace_created, tapd_workspace_description, workspace_short_name, workspace_type, workspace_team_id, workspace_is_active, workspace_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 413, 36243514, 23030071, 23030071, 'H5敏捷项目', 'normal', 1760524700,'吕猛 (<EMAIL>)', '2020-12-23 09:44:04', NULL, 'tapd-h5', 4, 1020, TRUE, '初始化tapd项目数据。zt@2024-02-20');
INSERT INTO tapd_workspace (create_user, create_time, update_user, update_time, stamp, id, tapd_workspace_id, tapd_workspace_parent_id, tapd_workspace_company_id, tapd_workspace_name, tapd_workspace_status, tapd_workspace_creator_id, tapd_workspace_creator, tapd_workspace_created, tapd_workspace_description, workspace_short_name, workspace_type, workspace_team_id, workspace_is_active, workspace_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 414, 64984051, 23030071, 23030071, '合作产品', 'normal', 1760524700,'吕猛 (<EMAIL>)', '2021-12-22 17:54:56', '达摩院交易合作部分', 'tapd-coop', 4, 1014, TRUE, '初始化tapd项目数据。zt@2024-02-20');
-- 420
INSERT INTO tapd_workspace (create_user, create_time, update_user, update_time, stamp, id, tapd_workspace_id, tapd_workspace_parent_id, tapd_workspace_company_id, tapd_workspace_name, tapd_workspace_status, tapd_workspace_creator_id, tapd_workspace_creator, tapd_workspace_created, tapd_workspace_description, workspace_short_name, workspace_type, workspace_team_id, workspace_is_active, workspace_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 420, 50240374, 23030071, 23030071, '数据团队', 'normal', 1161982812,'杨泽来 (<EMAIL>)', '2021-07-01 19:44:44', 'test', 'tapd-fpc', 4, 7, TRUE, '初始化tapd项目数据。zt@2024-02-20');
INSERT INTO tapd_workspace (create_user, create_time, update_user, update_time, stamp, id, tapd_workspace_id, tapd_workspace_parent_id, tapd_workspace_company_id, tapd_workspace_name, tapd_workspace_status, tapd_workspace_creator_id, tapd_workspace_creator, tapd_workspace_created, tapd_workspace_description, workspace_short_name, workspace_type, workspace_team_id, workspace_is_active, workspace_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 421, 40756777, 23030071, 23030071, '爱码仕', 'normal', 2047818969,'邹国强 (<EMAIL>)', '2022-04-28 17:06:40', '包含 零售 + 高端 app 需求', 'tapd-fpc_compute', 4, 1005, TRUE, '初始化tapd项目数据。zt@2024-02-20');
INSERT INTO tapd_workspace (create_user, create_time, update_user, update_time, stamp, id, tapd_workspace_id, tapd_workspace_parent_id, tapd_workspace_company_id, tapd_workspace_name, tapd_workspace_status, tapd_workspace_creator_id, tapd_workspace_creator, tapd_workspace_created, tapd_workspace_description, workspace_short_name, workspace_type, workspace_team_id, workspace_is_active, workspace_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 422, 63231183, 23030071, 23030071, '爱码仕敏捷_FPS', 'normal', 2047818969,'邹国强 (<EMAIL>)', '2021-07-01 19:44:44', '1. AMS 项目；2. 高端需求；3.零售需求', 'tapd-fps', 4, 1003, TRUE, '初始化tapd项目数据。zt@2024-02-20');
-- 430
INSERT INTO tapd_workspace (create_user, create_time, update_user, update_time, stamp, id, tapd_workspace_id, tapd_workspace_parent_id, tapd_workspace_company_id, tapd_workspace_name, tapd_workspace_status, tapd_workspace_creator_id, tapd_workspace_creator, tapd_workspace_created, tapd_workspace_description, workspace_short_name, workspace_type, workspace_team_id, workspace_is_active, workspace_desc) VALUES ('huaitian.zhang','2024-02-20 10:11:12','huaitian.zhang','2024-02-20 10:11:12', 0, 430, 36225275, 23030071, 23030071, 'CRM', 'normal', 35438887,'谢宏栋 (<EMAIL>)', '020-09-02 13:21:18', NULL, 'tapd-crm', 4, 1, TRUE, '初始化tapd项目数据。zt@2024-02-20');
