-- ----------------------------
-- 2-1、tapd同步批次表：tapd_sync_batch
-- 说明：批次号=时间批（10分钟）+序号批（任意多次）
-- ----------------------------
select * from tapd_sync_batch;
DROP TABLE IF EXISTS tapd_sync_batch;
CREATE TABLE tapd_sync_batch
(
    create_user         VARCHAR(20) COMMENT '创建人',
    create_time         DATETIME(0) COMMENT '创建时间',
    update_user         VARCHAR(20) COMMENT '修改人',
    update_time         DATETIME(0) COMMENT '修改时间',
    stamp               BIGINT(20) DEFAULT 0 COMMENT '版本',
    id                  BIGINT(20) AUTO_INCREMENT,
    time_batch          BIGINT(20) COMMENT '时间批（10分钟）',
    idx_batch           int(4) COMMENT '序号批',
    sync_mode           int(4) COMMENT '同步模式：1-全量、2-项目类型、3-模块类型、4-具体ID',
    sync_workspace_type int(4) COMMENT '项目类型：1234',
    sync_module_type    varchar(20) COMMENT '模块类型（全量）',
    sync_workspace_id   BIGINT(20) COMMENT '同步项目ID：0-所有',
    sync_module_name    varchar(20) COMMENT '项目下的模块名称',
    param_start_time    DATETIME(0) COMMENT '参数起始时间',
    param_end_time      DATETIME(0) COMMENT '参数截至时间',
    sync_status         varchar(20) COMMENT '同步状态（码表）',
    sync_result         TINYINT(1) COMMENT '同步结果',
    req_start_time      DATETIME(0) COMMENT '请求起始时间',
    req_end_time        DATETIME(0) COMMENT '请求截至时间',
    req_cost_time       FLOAT(10, 3) COMMENT '请求耗时（秒）',
    sync_desc           varchar(255) COMMENT '同步说明',
    PRIMARY KEY (id),
    UNIQUE KEY udx_batch (time_batch, idx_batch)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'tapd同步批次表';

INSERT INTO tapd_sync_batch (create_user, create_time, update_user, update_time, stamp, id, time_batch, idx_batch, sync_mode, sync_workspace_type, sync_module_type,sync_workspace_id, sync_module_name, param_start_time, param_end_time, sync_status, sync_result, req_start_time, req_end_time, req_cost_time, sync_desc)
VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 1, 20240205101000, 1, 4, NULL, NULL, 57711941, 'bug', '2024-02-05 10:00:00', '2024-02-05 10:09:59', 'Running', NULL, '2024-02-05 10:11:12', '2024-02-05 10:11:22', 10.2, '模拟全量同步批次：20240205101000+1。zt@2024-02-25');


-- ----------------------------
-- 2-2、tapd同步批次明细表：tapd_sync_batch_detail
-- 说明：
-- ----------------------------
select * from tapd_sync_batch_detail;
DROP TABLE IF EXISTS tapd_sync_batch_detail;
CREATE TABLE tapd_sync_batch_detail
(
    create_user        VARCHAR(20) COMMENT '创建人',
    create_time        DATETIME(0) COMMENT '创建时间',
    update_user        VARCHAR(20) COMMENT '修改人',
    update_time        DATETIME(0) COMMENT '修改时间',
    stamp              BIGINT(20) DEFAULT 0 COMMENT '版本',
    id                 BIGINT(20) AUTO_INCREMENT,
    batch_number       BIGINT(20) COMMENT '批次号',
    sync_workspace_id  BIGINT(20) COMMENT '同步具体的项目ID',
    sync_module_name   varchar(20) COMMENT '同步具体的模块名',
    sync_detail_status varchar(20) COMMENT '明细同步状态（码表）',
    sync_detail_result TINYINT(1) COMMENT '明细同步结果',
    req_start_time     DATETIME(0) COMMENT '请求起始时间',
    req_end_time       DATETIME(0) COMMENT '请求截至时间',
    req_cost_time      FLOAT(10, 3) COMMENT '请求耗时（秒）',
    sync_detail_desc   varchar(255) COMMENT '明细同步说明',
    PRIMARY KEY (id),
    UNIQUE KEY udx_batch_detail (batch_number, sync_workspace_id, sync_module_name)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'tapd同步批次明细表';

INSERT INTO tapd_sync_batch_detail (create_user, create_time, update_user, update_time, stamp, id, batch_number, sync_workspace_id, sync_module_name, sync_detail_status, sync_detail_result, req_start_time, req_end_time, req_cost_time, sync_detail_desc)
VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 1, 20240205101000001, 32443486, 'workspace', 'Completed', 1, '2024-02-05 10:11:12', '2024-02-05 10:11:13', 1.2, '模拟批次明细：20240205101000+1的「研发产线bug记录」的「项目」。zt@2024-02-25');
INSERT INTO tapd_sync_batch_detail (create_user, create_time, update_user, update_time, stamp, id, batch_number, sync_workspace_id, sync_module_name, sync_detail_status, sync_detail_result, req_start_time, req_end_time, req_cost_time, sync_detail_desc)
VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 2, 20240205101000001, 32443486, 'bug', 'Completed', 1, '2024-02-05 10:11:13', '2024-02-05 10:11:15', 3.2, '模拟批次明细：20240205101000+1的「研发产线bug记录」的「缺陷」。zt@2024-02-25');

INSERT INTO tapd_sync_batch_detail (create_user, create_time, update_user, update_time, stamp, id, batch_number, sync_workspace_id, sync_module_name, sync_detail_status, sync_detail_result, req_start_time, req_end_time, req_cost_time, sync_detail_desc)
VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 3, 20240205101000001, 57711941, 'workspace', 'Completed', 1, '2024-02-05 10:11:15', '2024-02-05 10:11:16', 1.2, '模拟批次明细：20240205101000+1的「平台产品1线」的「项目」。zt@2024-02-25');
INSERT INTO tapd_sync_batch_detail (create_user, create_time, update_user, update_time, stamp, id, batch_number, sync_workspace_id, sync_module_name, sync_detail_status, sync_detail_result, req_start_time, req_end_time, req_cost_time, sync_detail_desc)
VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 4, 20240205101000001, 57711941, 'iteration', 'Completed', 1, '2024-02-05 10:11:16', '2024-02-05 10:11:17', 1.3, '模拟批次明细：20240205101000+1的「平台产品1线」的「迭代」。zt@2024-02-25');
INSERT INTO tapd_sync_batch_detail (create_user, create_time, update_user, update_time, stamp, id, batch_number, sync_workspace_id, sync_module_name, sync_detail_status, sync_detail_result, req_start_time, req_end_time, req_cost_time, sync_detail_desc)
VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 5, 20240205101000001, 57711941, 'story', 'Completed', 1, '2024-02-05 10:11:17', '2024-02-05 10:11:18', 1.4, '模拟批次明细：20240205101000+1的「平台产品1线」的「需求」。zt@2024-02-25');
INSERT INTO tapd_sync_batch_detail (create_user, create_time, update_user, update_time, stamp, id, batch_number, sync_workspace_id, sync_module_name, sync_detail_status, sync_detail_result, req_start_time, req_end_time, req_cost_time, sync_detail_desc)
VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 6, 20240205101000001, 57711941, 'task', 'Completed', 1, '2024-02-05 10:11:18', '2024-02-05 10:11:19', 1.5, '模拟批次明细：20240205101000+1的「平台产品1线」的「任务」。zt@2024-02-25');
INSERT INTO tapd_sync_batch_detail (create_user, create_time, update_user, update_time, stamp, id, batch_number, sync_workspace_id, sync_module_name, sync_detail_status, sync_detail_result, req_start_time, req_end_time, req_cost_time, sync_detail_desc)
VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 7, 20240205101000001, 57711941, 'test_plan', 'Completed', 1, '2024-02-05 10:11:19', '2024-02-05 10:11:20', 1.6789, '模拟批次明细：20240205101000+1的「平台产品1线」的「测试计划」。zt@2024-02-25');
INSERT INTO tapd_sync_batch_detail (create_user, create_time, update_user, update_time, stamp, id, batch_number, sync_workspace_id, sync_module_name, sync_detail_status, sync_detail_result, req_start_time, req_end_time, req_cost_time, sync_detail_desc)
VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 8, 20240205101000001, 57711941, 'timesheet', 'Completed', 1, '2024-02-05 10:11:20', '2024-02-05 10:11:21', 1.789, '模拟批次明细：20240205101000+1的「平台产品1线」的「工时」。zt@2024-02-25');
