-- ----------------------------
-- 4-1、缺陷创建表：biz_bug_create
-- 说明：
-- ----------------------------
select *
from biz_bug_create;
DROP TABLE IF EXISTS biz_bug_create;
CREATE TABLE biz_bug_create
(
    create_user           VARCHAR(20) COMMENT '创建人',
    create_time           DATETIME(0) COMMENT '创建时间',
    update_user           VARCHAR(20) COMMENT '修改人',
    update_time           DATETIME(0) COMMENT '修改时间',
    stamp                 BIGINT(20) DEFAULT 0 COMMENT '版本',
    id                    BIGINT(20) AUTO_INCREMENT,
    tapd_workspace_id     BIGINT(20) COMMENT '项目ID',
    tapd_bug_id           BIGINT(20) COMMENT '缺陷ID',
    bug_create_uuid       VARCHAR(200) COMMENT '业务创建uuid',
    bug_create_source     VARCHAR(100) COMMENT '缺陷创建来源',
    bug_create_title      VARCHAR(200) COMMENT '缺陷创建标题',
    bug_create_status     int(4) COMMENT '缺陷创建状态：1-已缓存、2-创建中、3-已完成',
    bug_create_count      int(4) COMMENT '缺陷创建重试次数：3次',
    bug_create_result     TINYINT(1) COMMENT '缺陷创建结果：True/False',
    bug_create_param_json JSON COMMENT '参数Json',
    bug_create_req_json   JSON COMMENT '请求Json',
    bug_create_res_json   JSON COMMENT '返回Json',
    bug_create_start_time DATETIME(0) COMMENT '请求起始时间',
    bug_create_end_time   DATETIME(0) COMMENT '请求截至时间',
    bug_create_cost_time  FLOAT(6, 2) COMMENT '请求耗时（秒）',
    bug_create_desc       varchar(255) COMMENT '缺陷创建说明',
    PRIMARY KEY (id),
    UNIQUE KEY udx_biz_uuid (bug_create_uuid)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '缺陷创建表';


-- ----------------------------
-- 4-2、缺陷创建日志表：biz_bug_create_log
-- 说明：
-- ----------------------------
select *
from biz_bug_create_log;
DROP TABLE IF EXISTS biz_bug_create_log;
CREATE TABLE biz_bug_create_log
(
    create_user           VARCHAR(20) COMMENT '创建人',
    create_time           DATETIME(0) COMMENT '创建时间',
    update_user           VARCHAR(20) COMMENT '修改人',
    update_time           DATETIME(0) COMMENT '修改时间',
    stamp                 BIGINT(20) DEFAULT 0 COMMENT '版本',
    id                    BIGINT(20) AUTO_INCREMENT,
    bug_create_id         BIGINT(20) COMMENT '日志创建ID',
    bug_create_count      int(4) COMMENT '缺陷创建重试次数',
    bug_create_result     TINYINT(1) COMMENT '缺陷创建结果：True/False',
    bug_create_res_json   JSON COMMENT '返回Json',
    bug_create_start_time DATETIME(0) COMMENT '请求起始时间',
    bug_create_end_time   DATETIME(0) COMMENT '请求截至时间',
    bug_create_cost_time  FLOAT(6, 2) COMMENT '请求耗时（秒）',
    bug_create_desc       varchar(255) COMMENT '缺陷创建说明',
    PRIMARY KEY (id),
    UNIQUE KEY udx_id_count (bug_create_id, bug_create_count)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '缺陷创建日志表';
