-- ----------------------------
-- 1-0、tapd码表：tapd_code
-- 说明：
-- ----------------------------
select * from tapd_code;
DROP TABLE IF EXISTS tapd_code;
CREATE TABLE tapd_code
(
    create_user    VARCHAR(20) COMMENT '创建人',
    create_time    DATETIME(0) COMMENT '创建时间',
    update_user    VARCHAR(20) COMMENT '修改人',
    update_time    DATETIME(0) COMMENT '修改时间',
    stamp          BIGINT(20) DEFAULT 0 COMMENT '版本',
    id             BIGINT(20) AUTO_INCREMENT,
    code_type      tinyint(3) COMMENT '数据类型',
    code_idx       int(4) COMMENT '码表数据索引',
    code_value     varchar(100) COMMENT '数据值',
    code_name      varchar(100) COMMENT '数据显示名',
    code_is_active TINYINT(1) COMMENT '码表数据是否可用',
    code_desc      varchar(255) COMMENT '码表数据说明',
    PRIMARY KEY (id),
    UNIQUE KEY udx_type_value (code_type, code_value)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'tapd码表';
-- 项目状态：1
INSERT INTO tapd_code (create_user, create_time, update_user, update_time, stamp, id, code_type, code_idx, code_value, code_name, code_is_active, code_desc) VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 10101, 1, 1, 'normal', '正常', TRUE, '项目状态：正常。zt@2024-02-25');
-- Tapd模块：2
INSERT INTO tapd_code (create_user, create_time, update_user, update_time, stamp, id, code_type, code_idx, code_value, code_name, code_is_active, code_desc) VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 10201, 2, 1, 'workspace', '项目模块', TRUE, 'tapd模块：项目模块。zt@2024-02-25');
INSERT INTO tapd_code (create_user, create_time, update_user, update_time, stamp, id, code_type, code_idx, code_value, code_name, code_is_active, code_desc) VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 10202, 2, 2, 'iteration', '迭代模块', TRUE, 'tapd模块：迭代模块。zt@2024-02-25');
INSERT INTO tapd_code (create_user, create_time, update_user, update_time, stamp, id, code_type, code_idx, code_value, code_name, code_is_active, code_desc) VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 10203, 2, 3, 'story', '需求模块', TRUE, 'tapd模块：需求模块。zt@2024-02-25');
INSERT INTO tapd_code (create_user, create_time, update_user, update_time, stamp, id, code_type, code_idx, code_value, code_name, code_is_active, code_desc) VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 10204, 2, 4, 'task', '任务模块', TRUE, 'tapd模块：任务模块。zt@2024-02-25');
INSERT INTO tapd_code (create_user, create_time, update_user, update_time, stamp, id, code_type, code_idx, code_value, code_name, code_is_active, code_desc) VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 10205, 2, 5, 'bug', '缺陷模块', TRUE, 'tapd模块：缺陷模块。zt@2024-02-25');
INSERT INTO tapd_code (create_user, create_time, update_user, update_time, stamp, id, code_type, code_idx, code_value, code_name, code_is_active, code_desc) VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 10206, 2, 6, 'launch_form', '发布评审模块', TRUE, 'tapd模块：发布评审模块。zt@2024-02-25');
INSERT INTO tapd_code (create_user, create_time, update_user, update_time, stamp, id, code_type, code_idx, code_value, code_name, code_is_active, code_desc) VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 10207, 2, 7, 'test_plan', '测试计划模块', TRUE, 'tapd模块：测试计划模块。zt@2024-02-25');
INSERT INTO tapd_code (create_user, create_time, update_user, update_time, stamp, id, code_type, code_idx, code_value, code_name, code_is_active, code_desc) VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 10208, 2, 8, 'timesheet', '工时模块', TRUE, 'tapd模块：工时模块。zt@2024-02-25');
-- 同步状态：3
INSERT INTO tapd_code (create_user, create_time, update_user, update_time, stamp, id, code_type, code_idx, code_value, code_name, code_is_active, code_desc) VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 10301, 3, 1, 'Ready', '准备好', TRUE, '同步状态：准备好。zt@2024-02-25');
INSERT INTO tapd_code (create_user, create_time, update_user, update_time, stamp, id, code_type, code_idx, code_value, code_name, code_is_active, code_desc) VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 10302, 3, 2, 'Running', '运行中', TRUE, '同步状态：运行中。zt@2024-02-25');
INSERT INTO tapd_code (create_user, create_time, update_user, update_time, stamp, id, code_type, code_idx, code_value, code_name, code_is_active, code_desc) VALUES ('huaitian.zhang','2024-02-05 10:11:12','huaitian.zhang','2024-02-05 10:11:12', 0, 10303, 3, 3, 'Completed', '已完成', TRUE, '同步状态：已完成。zt@2024-02-25');
-- 各模块数据状态：4
INSERT INTO tapd_code (create_user, create_time, update_user, update_time, stamp, id, code_type, code_idx, code_value, code_name, code_is_active, code_desc) VALUES ('huaitian.zhang','2024-02-06 10:11:12','huaitian.zhang','2024-02-06 10:11:12', 0, 10401, 4, 1, 'insert', '新记录', TRUE, '数据状态：新记录。zt@2024-02-26');
INSERT INTO tapd_code (create_user, create_time, update_user, update_time, stamp, id, code_type, code_idx, code_value, code_name, code_is_active, code_desc) VALUES ('huaitian.zhang','2024-02-06 10:11:12','huaitian.zhang','2024-02-06 10:11:12', 0, 10402, 4, 2, 'update', '有更新', TRUE, '数据状态：有更新。zt@2024-02-26');
INSERT INTO tapd_code (create_user, create_time, update_user, update_time, stamp, id, code_type, code_idx, code_value, code_name, code_is_active, code_desc) VALUES ('huaitian.zhang','2024-02-06 10:11:12','huaitian.zhang','2024-02-06 10:11:12', 0, 10403, 4, 3, 'delete', '已删除', TRUE, '数据状态：已删除。zt@2024-02-26');
