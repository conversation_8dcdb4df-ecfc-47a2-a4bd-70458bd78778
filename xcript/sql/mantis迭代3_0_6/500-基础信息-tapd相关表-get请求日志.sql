-- ----------------------------
-- 5-1、get请求日志表：tapd_get_log
-- 说明：
-- ----------------------------
select *
from tapd_get_log
order by id desc;
DROP TABLE IF EXISTS tapd_get_log;
CREATE TABLE tapd_get_log
(
    create_user       VARCHAR(20) COMMENT '创建人',
    create_time       DATETIME(0) COMMENT '创建时间',
    update_user       VARCHAR(20) COMMENT '修改人',
    update_time       DATETIME(0) COMMENT '修改时间',
    stamp             BIGINT(20) DEFAULT 0 COMMENT '版本',
    id                BIGINT(20) AUTO_INCREMENT,
    batch_number      BIGINT(20) COMMENT '请求批次',
    tapd_workspace_id BIGINT(20) COMMENT '项目ID',
    tapd_req_limit    BIGINT(20) COMMENT '请求数量',
    tapd_req_page     BIGINT(20) COMMENT '请求页码',
    req_url           VARCHAR(255) COMMENT '请求地址',
    req_type          VARCHAR(10) COMMENT '请求类型',
    req_json          JSON COMMENT '请求Json',
    req_start_time    DATETIME(0) COMMENT '请求起始时间',
    req_end_time      DATETIME(0) COMMENT '请求截至时间',
    req_cost_time     FLOAT(6, 2) COMMENT '请求耗时（秒）',
    res_status        int(4) COMMENT '返回状态',
    res_json          JSON COMMENT '返回Json',
    tapd_status       VARCHAR(100) COMMENT '返回Json中的状态',
    tapd_info         VARCHAR(255) COMMENT '返回Json中的信息',
    tapd_data         JSON COMMENT '返回Json中的数据',
    data_count        int(4) COMMENT '数据条数',
    res_err_msg       VARCHAR(1000) COMMENT '返回错误信息',
    tapd_get_desc     varchar(255) COMMENT 'get请求说明',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'get请求日志表';

