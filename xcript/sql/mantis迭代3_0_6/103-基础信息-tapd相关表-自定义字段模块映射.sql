-- ----------------------------
-- 1-3、tapd项目模块映射表：tapd_workspace_module_conf
-- 说明：自定义字段映射
-- ----------------------------
select * from tapd_workspace_module_conf;
DROP TABLE IF EXISTS tapd_workspace_module_conf;
CREATE TABLE tapd_workspace_module_conf
(
    create_user       VARCHAR(20) COMMENT '创建人',
    create_time       DATETIME(0) COMMENT '创建时间',
    update_user       VARCHAR(20) COMMENT '修改人',
    update_time       DATETIME(0) COMMENT '修改时间',
    stamp             BIGINT(20) DEFAULT 0 COMMENT '版本',
    id                BIGINT(20) AUTO_INCREMENT,
    module_id         BIGINT(20) COMMENT '模块ID',
    tapd_id           BIGINT(20) COMMENT '自定义字段ID',
    tapd_entry_type   varchar(50) COMMENT '所属实体对象',
    tapd_custom_field varchar(50) COMMENT '自定义字段',
    tapd_type         varchar(50) COMMENT '自定义字段类型',
    tapd_name         varchar(50) COMMENT '自定义字段名称',
    tapd_options      varchar(999) COMMENT '自定义字段选项',
    tapd_sort         INT(4) COMMENT '自定义字段排序',
    conf_is_sync      TINYINT(1) COMMENT '映射是否同步',
    conf_table        varchar(50) COMMENT '映射表',
    conf_column       varchar(50) COMMENT '映射字段',
    conf_name         varchar(50) COMMENT '映射名称',
    conf_type         varchar(100) COMMENT '映射类型',
    conf_is_active    TINYINT(1) COMMENT '映射是否可用',
    conf_desc         VARCHAR(255) COMMENT '映射说明',
    PRIMARY KEY (id),
    UNIQUE KEY udx_module_field (module_id, tapd_custom_field)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'tapd项目模块映射表';
-- 10005
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 10005001, 10005, 1132443486001000085, 'bug', 'custom_field_one', 'select', '所属团队', '藏经阁|达摩院|爱码仕|六扇门|银河战队', 4, TRUE, 'spider.tool_mgt_team_info', 'team_name', '大团队', 'tapd-prod_bug-bug-L1_team', TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 10005002, 10005, 1132443486001000083, 'bug', 'custom_field_two', 'text', '版本', NULL, 2, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 10005003, 10005, 1132443486001000084, 'bug', 'custom_field_three', 'textarea', '修复问题说明', NULL, 3, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 10005004, 10005, 1132443486001000086, 'bug', 'custom_field_four', 'select', '所属小组', 'AMS投研|数据|APP|高端-非交易|公募-非交易|高端-交易|公募-交易|合作|基础|H5|资金|清算|账户|机构|储蓄罐|理财通|CRM|支付', 5, TRUE, 'spider.tool_mgt_team_info', 'team_name', '小团队', 'tapd-prod_bug-bug-L2_team', TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 10005005, 10005, 1132443486001000195, 'bug', 'custom_field_five', 'select', '是否完成测试分析', '是|否', 12, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 10005006, 10005, 1132443486001000088, 'bug', 'custom_field_6', 'select', '缺陷来源', '用户反馈（产品或用户）|数据稽核|系统告警|自我发现|其他', 7, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 10005007, 10005, 1132443486001000089, 'bug', 'custom_field_7', 'dateinput', '发现日期', NULL, 8, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 10005010, 10005, 1132443486001000114, 'bug', 'custom_field_10', 'select', '是否缺陷', '是|不是', 11, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 10005011, 10005, 1132443486001000235, 'bug', 'custom_field_11', 'cascade_checkbox', '缺陷分类', 'Array|Array|Array|Array|Array|Array|Array', 0, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
-- 20003
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20003001, 20003, 1157711941001000029, 'story', 'custom_field_one', 'text', '细分模块', NULL, NULL, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20003002, 20003, 1157711941001000157, 'story', 'custom_field_two', 'integer', '工时', NULL, NULL, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
-- 20004
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20004001, 20004, 1157711941001000260, 'task', 'custom_field_one', 'multi_select', '多选下拉验证', '{\"1\":\"\测\试1\",\"2\":\"\测\试2\"}', NULL, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20004002, 20004, 1157711941001000261, 'task', 'custom_field_two', 'select', '单选下拉验证', '{\"1\":\"\单\选\测\试1\",\"2\":\"\单\选\测\试2\"}', NULL, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20004007, 20004, 1157711941001000010, 'task', 'custom_field_seven', 'select', '类型', '{\"1\":\"\开\发\",\"2\":\"\测\试\",\"3\":\"\需\求\分\析\",\"4\":\"\排\障\运\维\",\"5\":\"\方\案\设\计\",\"6\":\"\界\面\原\型\",\"7\":\"\分\享\培\训\"}', NULL, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
-- 20005
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20005001, 20005, 1157711941001000076, 'bug', 'custom_field_one', 'integer', '修复用时', NULL, 1, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20005002, 20005, 1157711941001000096, 'bug', 'custom_field_two', 'select', '同一缺陷编号', 'A|B|C|D|E|F|G|H', 2, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20005003, 20005, 1157711941001000226, 'bug', 'custom_field_three', 'text', '文档链接', NULL, 3, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
-- 20103
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20103001, 20103, 1150651707001000030, 'story', 'custom_field_one', 'text', '细分模块', NULL, NULL, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20103002, 20103, 1150651707001000158, 'story', 'custom_field_two', 'integer', '工时', NULL, NULL, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20103003, 20103, 1150651707001000176, 'story', 'custom_field_three', 'multi_select', '测试字段', '{\"1\":\"\开\发\",\"2\":\"\测\试\",\"3\":\"\产\品\",\"4\":\"\运\营\"}', NULL, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20103100, 20103, 1150651707001000001, 'story', 'custom_field_100', 'select', '产品线', '{\"1\":\"\宙\斯\",\"2\":\"\公\共\",\"3\":\"\魔\戒\",\"4\":\"\调\度\",\"5\":\"\缓\存\",\"6\":\"\消\息\队\列\"}', NULL, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
-- 20104
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20104008, 20104, 1150651707001000002, 'task', 'custom_field_eight', 'radio', '类型', '{\"1\":\"\需\求\分\析\",\"2\":\"\开\发\",\"3\":\"\测\试\",\"4\":\"\排\障\运\维\",\"5\":\"\方\案\设\计\",\"6\":\"\界\面\原\型\",\"7\":\"\分\享\培\训\"}', NULL, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
-- 20105
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20105001, 20105, 1157711941001000076, 'bug', 'custom_field_one', 'integer', '修复用时', NULL, 1, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20105002, 20105, 1150651707001000074, 'bug', 'custom_field_two', 'select', '用户场景', 'DevOps过程-开发阶段|DevOps过程-构建/环境交付|DevOps过程-测试阶段|DevOps过程-上线申请/发布|产线-新老技术正在迁移|产线-常态运行|其他', 2, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20105003, 20105, 1150651707001000075, 'bug', 'custom_field_three', 'select', '同一缺陷编号', 'A|B|C|D|E|F|G|H', 3, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
INSERT INTO tapd_workspace_module_conf (create_user, create_time, update_user, update_time, stamp, id, module_id, tapd_id, tapd_entry_type, tapd_custom_field, tapd_type, tapd_name, tapd_options, tapd_sort, conf_is_sync, conf_table, conf_column, conf_name, conf_type, conf_is_active, conf_desc) VALUES ('huaitian.zhang','2024-02-18 10:11:12','huaitian.zhang','2024-02-18 10:11:12', 0, 20105004, 20105, 1150651707001000227, 'bug', 'custom_field_four', 'text', '文档链接', NULL, 4, FALSE, NULL, NULL, NULL, NULL, TRUE, '初始化数据。zt@2024-02-18');
