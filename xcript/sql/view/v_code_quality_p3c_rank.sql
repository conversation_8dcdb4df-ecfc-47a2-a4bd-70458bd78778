select 'P3C' as idx, ref.*,

(
select count(0)
from
(
SELECT
	res.rule_num_sum, last.app_name, last.months
FROM
	mantis.code_quality_p3c_result_info res
inner join
(
SELECT
	max(id) as id, app_name, date_format(create_time, '%Y-%m') as months
FROM
	mantis.code_quality_p3c_record_info rec
group by app_name, months
) last
on res.p_id = last.id and res.priority = '1'
) d
where d.months = ref.months and cast(d.rule_num_sum as signed) >= cast(ref.rule_num_sum as signed)
) as rank,
t.total

from
(
SELECT
	res.rule_num_sum, last.app_name, last.months
FROM
	mantis.code_quality_p3c_result_info res
inner join
(
SELECT
	max(id) as id, app_name, date_format(create_time, '%Y-%m') as months
FROM
	mantis.code_quality_p3c_record_info rec
group by app_name, months
) last
on res.p_id = last.id and res.priority = '1'
) ref
left outer join
(
select count(0) as total, months from
(
SELECT
	max(id) as id, app_name, date_format(create_time, '%Y-%m') as months
FROM
	mantis.code_quality_p3c_record_info rec
group by app_name, months
) last
group by months
) t
on t.months = ref.months