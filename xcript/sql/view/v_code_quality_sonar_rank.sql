select ref.*,

(
select count(0)
from
(
SELECT
	res.measure as idx, sum(res.value) as index_value, last.app_name, last.months
FROM
	mantis.code_quality_sonar_result_info res
inner join
(
SELECT
	max(id) as id, app_name, date_format(create_time, '%Y-%m') as months
FROM
	mantis.code_quality_sonar_record_info rec
group by app_name, months
) last
on res.p_id = last.id and res.measure in ('DUPLICATED', 'VULNERABILITY', 'BUG') and res.segment in ('duplicated_lines_density', 'CRITICAL', 'MAJOR', 'BLOCKER')
group by res.measure, last.app_name, last.months
) d
where d.months = ref.months and d.idx = ref.idx and d.index_value >= ref.index_value
) as rank,
t.total

from
(
SELECT
	res.measure as idx, sum(res.value) as index_value, last.app_name, last.months
FROM
	mantis.code_quality_sonar_result_info res
inner join
(
SELECT
	max(id) as id, app_name, date_format(create_time, '%Y-%m') as months
FROM
	mantis.code_quality_sonar_record_info rec
group by app_name, months
) last
on res.p_id = last.id and res.measure in ('DUPLICATED', 'VULNERABILITY', 'BUG') and res.segment in ('duplicated_lines_density', 'CRITICAL', 'MAJOR', 'BLOCKER')
group by res.measure, last.app_name, last.months
) ref
left outer join
(
select count(0) as total, months from
(
SELECT
	max(id) as id, app_name, date_format(create_time, '%Y-%m') as months
FROM
	mantis.code_quality_sonar_record_info rec
group by app_name, months
) last
group by months
) t
on t.months = ref.months