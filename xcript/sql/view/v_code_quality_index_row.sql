select 'CCN' as name, d.*,
case when @months = d.months then @ccn:=@ccn+1 else @ccn:=1 end as row,
case when @months = d.months then @months:=@months else @months:= d.months end

from
(
SELECT
	res.index_value, last.app_name, last.months
FROM
	mantis.code_quality_ccn_result_info res
inner join
(
SELECT
	max(id) as id, app_name, date_format(create_time, '%Y-%m') as months
FROM
	mantis.code_quality_ccn_record_info rec
group by app_name, months
) last
on res.li_id = last.id
order by last.months, cast(res.index_value as signed) desc
) d, (select @ccn:=0) r


union all


select 'P3C' as name, d.*,
case when @months = d.months then @p3c:=@p3c+1 else @p3c:=1 end as row,
case when @months = d.months then @months:=@months else @months:= d.months end

from
(
SELECT
	res.rule_num_sum, last.app_name, last.months
FROM
	mantis.code_quality_p3c_result_info res
inner join
(
SELECT
	max(id) as id, app_name, date_format(create_time, '%Y-%m') as months
FROM
	mantis.code_quality_p3c_record_info rec
group by app_name, months
) last
on res.p_id = last.id and res.priority = '1'
order by last.months, cast(res.rule_num_sum as signed) desc
) d, (select @p3c:=0) r

union all

select d.*,
case when @months = d.months then @sonar:=@sonar+1 else @sonar:=1 end as row,
case when @months = d.months then @months:=@months else @months:= d.months end

from
(
SELECT
	res.measure, sum(res.value) as index_value, last.app_name, last.months
FROM
	mantis.code_quality_sonar_result_info res
inner join
(
SELECT
	max(id) as id, app_name, date_format(create_time, '%Y-%m') as months
FROM
	mantis.code_quality_sonar_record_info rec
group by app_name, months
) last
on res.p_id = last.id and res.measure in ('DUPLICATED', 'VULNERABILITY', 'BUG') and res.segment in ('duplicated_lines_density', 'CRITICAL', 'MAJOR', 'BLOCKER')
group by res.measure, last.app_name, last.months
order by res.measure, last.months, cast(res.value as signed) desc
) d, (select @sonar:=0) r