CREATE VIEW mantis.v_git_commits_diff_effect as
SELECT *
FROM
	mantis.code_stat_git_commits_diff cd
where 1=1
and is_merge = 'n'
and need_stat = 1
and first_commit_id is null
and
(
cd.file_type in ('.java')
or cd.file_type in ('.xml', '.bundle/_CodeSignature/CodeResources')
or cd.file_type in ('.jsp', '.html', '.htm', '.vue', '.js', '.jsx', '.less', '.ftl', '.ts')
or cd.file_type in ('.json', '.json5')
or cd.file_type in ('.m', '.h')
or cd.file_type in ('.xib', '.storyboard', '.nib')
or cd.file_type in ('.py', '.rs', '.toml')
or cd.file_type in ('.sql', '.groovy', '.sh', '.yaml')
or cd.file_type in ('.css', '.scss', '.styl', '.svg')
or cd.file_type in ('.kt', '.ets')
)
and cd.add_counts_effect > 0
and cd.project_id not in ('173', '248', '253', '317', '357', '382', '514', '515', '517', '605', '713', '747', '794', '837', '1070', '1123', '1183', '1259', '1265', '1270', '1274', '1277', '1308', '1315', '1320', '1327', '1334', '1337', '1372', '1376', '1377', '1432')
and
(
    (cd.new_path not like 'HowbuyFund/Pods%' and cd.project_id = '367')
    or (cd.new_path not like 'HBPublicFundModules/Pods%' and cd.project_id = '369')
    or (cd.new_path not like 'PiggyBank/Pods%' and  cd.project_id = '370')
    or (cd.new_path not like 'HBAccountCenterModules/Pods%' and cd.project_id = '374')
    or (cd.new_path not like 'HBPrivateFundModules/Pods%' and cd.project_id = '478')
    or (cd.new_path not like 'Pods%' and cd.project_id = '1263')
    or (cd.new_path not like 'docs/%' and cd.project_id = '1190')
    or cd.project_id not in ('367', '369', '370', '374', '478', '1263', '1190')
)
and new_path not like '.idea/%'
and new_path not in ('pnpm-lock.yaml')

/*
and not exists (
    select 1 from mantis.code_stat_git_commits_diff_exonce cdo
    where cd.datetime = cdo.datetime
    and cd.username = cdo.username
)
and not exists (
    select 1 from mantis.code_stat_git_commits_diff_exonecom cde
    where cd.commit = cde.commit
    and cd.project_id = cde.project_id
    and cd.username = cde.username
)
*/
