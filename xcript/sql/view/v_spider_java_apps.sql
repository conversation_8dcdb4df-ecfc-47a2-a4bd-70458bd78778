SELECT
	`x`.`module_name` AS `module_name`,
	`x`.`app_type` AS `app_type`,
	`x`.`package_type` AS `package_type`,
	`x`.`team_alias` AS `team_alias`
FROM
	(
SELECT
	distinct `am`.`module_name` AS `module_name`,
	(case when (`ab`.`package_type` IN ('jar',
	'war')) then 'java服务' else '其他类型' end) AS `app_type`,
	`ab`.`package_type` AS `package_type`,
	(case when isnull(`a`.`team_alias`) then '未知' else `a`.`team_alias` end) AS
	`team_alias`,
	concat('http://mring:<EMAIL>/',
	`ai`.`git_url`,
	`ai`.`git_path`) AS `git_path`,
	`am`.`app_id` AS `app_id`,
	`a`.`id` AS `app_team_id`,
	`ai`.`third_party_middleware` AS `tpm`
FROM
	(((`spider`.`app_mgt_app_module` `am` JOIN `spider`.`app_mgt_app_info` `ai`
		ON
		(((`am`.`app_id` = `ai`.`id`) AND
	(`ai`.`app_status` <> '0'))))
		LEFT JOIN
		`spider`.`app_mgt_app_build` `ab`
		ON
		((`ab`.`module_name` = `am`.`module_name`)))
		LEFT JOIN
		(
SELECT
	distinct `ti`.`team_alias` AS `team_alias`,
	`at`.`app_id` AS `app_id`,
	`at`.`id` AS `id`
FROM
	(`spider`.`tool_mgt_team_info` `ti` JOIN `spider`.`app_mgt_app_team` `at`)
WHERE
	(`ti`.`id` = `at`.`team_id`)) `a`
		ON
		((`am`.`app_id` = `a`.`app_id`)))
WHERE
	((`am`.`module_status` = '1') AND
	(`am`.`need_online` = '1') AND
	(`am`.`need_check` = '1') AND
	(`am`.`need_ops` <> '0') AND
	(`am`.`is_agent` <> '1'))) `x`
WHERE
	((`x`.`app_type` = 'java服务') AND
	(`x`.`tpm` = '0'))