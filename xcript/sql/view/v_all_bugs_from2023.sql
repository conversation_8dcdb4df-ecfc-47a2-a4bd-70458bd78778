SELECT
TIMESTAMPDIFF(minute , ad_create_time, ad_resolve_time) -
(
TIMESTAMPDIFF(day, date_format(ad_create_time, '%y-%m-%d'), date_format(ad_resolve_time, '%y-%m-%d')) -
(
select count(0) from mantis.v_date_holiday ho
where ad_resolve_time > ho.day and ad_create_time < ho.day
)
) * 900 -
(
select count(0) from mantis.v_date_holiday ho
where ad_resolve_time > ho.day and ad_create_time < ho.day
) * 24 * 60 as res_diff,

TIMESTAMPDIFF(minute , ad_create_time, ad_close_time) -
(
TIMESTAMPDIFF(day, date_format(ad_create_time, '%y-%m-%d'), date_format(ad_close_time, '%y-%m-%d')) -
(
select count(0) from mantis.v_date_holiday ho
where ad_close_time > ho.day and ad_create_time < ho.day
)
) * 900 -
(
select count(0) from mantis.v_date_holiday ho
where ad_close_time > ho.day and ad_create_time < ho.day
) * 24 * 60 as clo_diff,

d.*

from
(
select
case when date_format(tb.create_time, '%H%i%s') > '180000' then concat(date_format(date_add(tb.create_time, interval 1 day), '%Y-%m-%d'), ' 09:00:00') else tb.create_time end as ad_create_time,
case when date_format(tb.resolve_time, '%H%i%s') > '180000' then
concat(
(select min(date_format(date_add(tb.resolve_time, interval s.number day), '%Y-%m-%d')) as day from devops_base.sequence s
where date_format(date_add(tb.resolve_time, interval s.number day), '%Y-%m-%d') not in (select day from mantis.v_date_holiday)),
' 09:00:00'
) else tb.resolve_time end as ad_resolve_time,
case when date_format(tb.close_time, '%H%i%s') > '180000' then
concat(
(select min(date_format(date_add(tb.close_time, interval s.number day), '%Y-%m-%d')) as day from devops_base.sequence s
where date_format(date_add(tb.close_time, interval s.number day), '%Y-%m-%d') not in (select day from mantis.v_date_holiday)),
' 09:00:00') else tb.close_time end as ad_close_time,
tb.*
FROM
	mantis.busi_testing_bugs tb
where tb.create_time > '2023-01-01' or tb.resolve_time > '2023-01-01'
) d