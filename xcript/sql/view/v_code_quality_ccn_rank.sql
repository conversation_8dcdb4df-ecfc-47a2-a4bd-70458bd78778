select 'CCN' as idx, ref.*,
(
select count(0)
from
(
SELECT
	res.index_value, last.app_name, last.months
FROM
	mantis.code_quality_ccn_result_info res
inner join
(
SELECT
	max(id) as id, app_name, date_format(create_time, '%Y-%m') as months
FROM
	mantis.code_quality_ccn_record_info rec
group by app_name, months
) last
on res.li_id = last.id
) d
where d.months = ref.months and cast(d.index_value as signed) >= cast(ref.index_value as signed)
) as rank,
t.total

from
(
SELECT
	res.index_value, last.app_name, last.months
FROM
	mantis.code_quality_ccn_result_info res
inner join
(
SELECT
	max(id) as id, app_name, date_format(create_time, '%Y-%m') as months
FROM
	mantis.code_quality_ccn_record_info rec
group by app_name, months
) last
on res.li_id = last.id
) ref
left outer join
(
select count(0) as total, months from
(
SELECT
	max(id) as id, app_name, date_format(create_time, '%Y-%m') as months
FROM
	mantis.code_quality_ccn_record_info rec
group by app_name, months
) last
group by months
) t
on t.months = ref.months