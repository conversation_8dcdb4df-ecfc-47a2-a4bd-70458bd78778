CREATE TABLE `code_quality_sonar_record_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `create_user` varchar(20) NOT NULL,
  `create_time` datetime(6) NOT NULL,
  `update_user` varchar(20) NOT NULL,
  `update_time` datetime(6) NOT NULL,
  `app_name` varchar(64) NOT NULL,
  `br_name` varchar(64) NOT NULL,
  `iteration_id` varchar(64) NOT NULL,
  `report_url` varchar(256) NOT NULL,
  `task_id` varchar(64) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `u_iter_app` (`app_name`,`iteration_id`)
)