CREATE TABLE `code_quality_sonar_result_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `create_user` varchar(20) NOT NULL,
  `create_time` datetime(6) NOT NULL,
  `update_user` varchar(20) NOT NULL,
  `update_time` datetime(6) NOT NULL,
  `p_id` int(11) NOT NULL,
  `measure` varchar(64) NOT NULL,
  `segment` varchar(64) NOT NULL,
  `value` int(64) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `u_measure` (`p_id`,`measure`,`segment`)
)