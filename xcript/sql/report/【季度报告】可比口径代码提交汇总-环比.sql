select lteam,

round(avg(当季总影响行), 2) as 当季人均影响行,
round(avg(上一季度), 2) as 上一季度,
concat(
round( ( avg(当季总影响行) - avg(上一季度) ) / avg(上一季度) *100, 2 ),
'%') as 环比增幅,
group_concat(distinct username order by username) as 可比口径


from
(
select
t.lteam,
t.team,
t.username,
t.first_commit,
t.当季总影响行,
last.total_lines as 去年同期,
nearest.total_lines as 上一季度,
t.当季提交分类

from
(
select
t_u.team, t_u.lteam, t_u.first_commit,
d.username,
d.user_id,
sum(d.total_lines) as 当季总影响行,
group_concat('【', d.file_type, '：', d.total_lines, '】' order by d.file_type) as 当季提交分类

from
(
select cd.username, cd.user_id, cd.file_type, sum(cd.add_counts_effect) as total_lines
from mantis.v_git_commits_diff_effect cd
where datetime > date_add('${统计截止日期yyyy-mm-01：}', interval -3 month) and datetime <
group by username, cd.user_id, file_type
) d

left outer join

(
SELECT
	distinct gm.git_user_id, pc.user_name, pc.s_team_name as team, pc.team_name as lteam,
	date_add(pc.first_time_commit, interval 2 month) as first_commit
	-- pc.first_time_commit as first_commit
FROM
	spider.user_gitlab_members gm
inner join
	devops_base.project_committer pc
on gm.username = pc.user_name
where gm.git_user_id is not null and pc.bind_is_active = 1
) t_u
on d.user_id = t_u.git_user_id

where d.total_lines <> 0
and d.username not in ('宋懿超', '谢宏栋', '张弋翔', '马骥', '吕猛', '杨泽来')
and t_u.lteam not in ('系统运维')

group by team, username
) t


left outer join
(
select cd.username, cd.user_id, sum(cd.add_counts_effect) as total_lines
from mantis.v_git_commits_diff_effect cd
where datetime > date_add('${统计截止日期yyyy-mm-01：}', interval -15 month) and datetime < date_add('${统计截止日期yyyy-mm-01：}', interval -12 month)
group by username, cd.user_id
) last
on last.user_id = t.user_id


left outer join
(
select cd.username, cd.user_id, sum(cd.add_counts_effect) as total_lines
from mantis.v_git_commits_diff_effect cd
where datetime > date_add('${统计截止日期yyyy-mm-01：}', interval -6 month) and datetime < date_add('${统计截止日期yyyy-mm-01：}', interval -3 month)
group by username, cd.user_id
) nearest
on nearest.user_id = t.user_id



) data

where first_commit < date_add('${统计截止日期yyyy-mm-01：}', interval -6 month)

group by lteam


order by team
