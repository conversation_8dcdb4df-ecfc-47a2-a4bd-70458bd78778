select d.username, d.总影响行 as `19点后影响行`, a.总影响行,
concat(round(d.总影响行/a.总影响行*100, 2), '%') as `19点后占比` , d.天数

from
(
select cd.username, -- cd.user_id,
sum(cd.add_counts_effect) as 总影响行,
-- concat('【', group_concat(distinct date_format(datetime, '%m月%d日%H点') order by date_format(datetime, '%Y-%m-%d %H')), '】') as dates
length( group_concat(distinct date_format(datetime, '%m月%d日')) )
- length( replace( group_concat(distinct date_format(datetime, '%m月%d日')), ',', '') )
as 天数
from mantis.v_git_commits_diff_effect cd
where datetime > date_add('${季度截止日期yyyy-mm-dd：}', interval -3 month) and datetime < datetime < '${季度截止日期yyyy-mm-dd：}'
and cd.add_counts > 0
and is_merge = 'n'
and date_format(datetime, '%H:%i') >= '19:00'
and not exists
(
select * from mantis.v_date_holiday h where date_format(cd.datetime, '%Y-%m-%d') = h.day
)
group by username, cd.user_id
) d
left outer join
(
select cd.username, sum(cd.add_counts_effect) as 总影响行
from mantis.v_git_commits_diff_effect cd
where datetime > date_add('${季度截止日期yyyy-mm-dd：}', interval -3 month) and datetime < datetime < '${季度截止日期yyyy-mm-dd：}'
and cd.add_counts > 0
and is_merge = 'n'
group by username, cd.user_id
) a
on d.username = a.username


order by d.总影响行 desc
