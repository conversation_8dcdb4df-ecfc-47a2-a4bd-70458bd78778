select
case when s.story_project is null then '没选项目' else s.story_project end as project ,
round(sum(s.effort), 1) as 总投入小时数

from
(

SELECT SUBSTRING_INDEX(tt.tapd_time_sheet_owner, '_', 1)  as owner,
story_project,
sum(tt.tapd_time_sheet_timespent) as effort
FROM
	mantis.dev_effic_dev_task ddt
inner join
	mantis.tapd_entry_timesheet tt
on ddt.tapd_task_id = tt.tapd_time_sheet_entity_id
left outer join

(

select ss.*,
case when tm3.map_name is not null then tm3.map_name else
(
case when tm2.map_name is not null then tm2.map_name else
(
tm1.map_name
) end
) end
as category

 from

(
SELECT
	dds1.id,
	ts1.tapd_workspace_id,

case when ts1.tapd_story_category_id is not null and ts1.tapd_story_category_id != '-1' then ts1.tapd_story_category_id else
(
case when ts2.tapd_story_category_id is not null and ts2.tapd_story_category_id != '-1' then ts2.tapd_story_category_id else
(
case when ts3.tapd_story_category_id is not null and ts3.tapd_story_category_id != '-1' then ts3.tapd_story_category_id else
(
case when ts4.tapd_story_category_id is not null and ts4.tapd_story_category_id != '-1' then ts4.tapd_story_category_id else
(
case when ts5.tapd_story_category_id is not null and ts5.tapd_story_category_id != '-1' then ts5.tapd_story_category_id else
(
case when ts6.tapd_story_category_id is not null and ts6.tapd_story_category_id != '-1' then ts6.tapd_story_category_id else
(
case when ts7.tapd_story_category_id is not null then ts7.tapd_story_category_id else '-1' end
) end
) end
) end
) end
) end
) end
as story_category_id,

case when dds1.type is not null and dds1.type != '' then dds1.type else
(
case when dds2.type is not null and dds2.type != '' then dds2.type else
(
case when dds3.type is not null and dds3.type != '' then dds3.type else
(
case when dds4.type is not null and dds4.type != '' then dds4.type else
(
case when dds5.type is not null and dds5.type != '' then dds5.type else
(
case when dds6.type is not null and dds6.type != '' then dds6.type else
(
dds7.type
) end
) end
) end
) end
) end
) end
as story_type,

case when dds1.howbuy_project is not null and dds1.howbuy_project != '' then dds1.howbuy_project else
(
case when dds2.howbuy_project is not null and dds2.howbuy_project != '' then dds2.howbuy_project else
(
case when dds3.howbuy_project is not null and dds3.howbuy_project != '' then dds3.howbuy_project else
(
case when dds4.howbuy_project is not null and dds4.howbuy_project != '' then dds4.howbuy_project else
(
case when dds5.howbuy_project is not null and dds5.howbuy_project != '' then dds5.howbuy_project else
(
case when dds6.howbuy_project is not null and dds6.howbuy_project != '' then dds6.howbuy_project else
(
dds7.howbuy_project
) end
) end
) end
) end
) end
) end
as story_project

FROM
	mantis.tapd_entry_story ts1
inner join
	mantis.dev_effic_dev_story dds1
on dds1.tapd_story_id = ts1.tapd_entry_id and ts1.entry_status != 3
left outer join
	mantis.tapd_entry_story ts2
on ts1.tapd_story_parent_id = ts2.tapd_entry_id and ts2.entry_status != 3
left outer join
	mantis.dev_effic_dev_story dds2
on dds2.tapd_story_id = ts2.tapd_entry_id and ts2.entry_status != 3
left outer join
	mantis.tapd_entry_story ts3
on ts2.tapd_story_parent_id = ts3.tapd_entry_id and ts3.entry_status != 3
left outer join
	mantis.dev_effic_dev_story dds3
on dds3.tapd_story_id = ts3.tapd_entry_id and ts3.entry_status != 3
left outer join
	mantis.tapd_entry_story ts4
on ts3.tapd_story_parent_id = ts4.tapd_entry_id and ts4.entry_status != 3
left outer join
	mantis.dev_effic_dev_story dds4
on dds4.tapd_story_id = ts4.tapd_entry_id and ts4.entry_status != 3
left outer join
	mantis.tapd_entry_story ts5
on ts4.tapd_story_parent_id = ts5.tapd_entry_id and ts5.entry_status != 3
left outer join
	mantis.dev_effic_dev_story dds5
on dds5.tapd_story_id = ts5.tapd_entry_id and ts5.entry_status != 3
left outer join
	mantis.tapd_entry_story ts6
on ts5.tapd_story_parent_id = ts6.tapd_entry_id and ts6.entry_status != 3
left outer join
	mantis.dev_effic_dev_story dds6
on dds6.tapd_story_id = ts6.tapd_entry_id and ts6.entry_status != 3
left outer join
	mantis.tapd_entry_story ts7
on ts6.tapd_story_parent_id = ts7.tapd_entry_id and ts7.entry_status != 3
left outer join
	mantis.dev_effic_dev_story dds7
on dds7.tapd_story_id = ts7.tapd_entry_id and ts7.entry_status != 3

) ss

left outer join
	mantis.tapd_workspace_map tm1
on ss.story_category_id = tm1.map_id

left outer join
	mantis.tapd_workspace_map tm2
on tm1.map_parent_id = tm2.map_id

left outer join
	mantis.tapd_workspace_map tm3
on tm2.map_parent_id = tm3.map_id

left outer join
	mantis.tapd_workspace_map tm4
on tm3.map_parent_id = tm4.map_id

) sss

on ddt.dev_story_id = sss.id

where tt.tapd_time_sheet_spentdate >= date_format( date_add('${截止日期yyyy-mm-dd：}', interval - '${最近几天：}' day), '%Y-%m-%d')
and tt.tapd_time_sheet_spentdate < date_format('${截止日期yyyy-mm-dd：}', '%Y-%m-%d')
and tt.tapd_time_sheet_owner is not null
group by tapd_time_sheet_owner, sss.story_project
) s

group by project

order by 总投入小时数 desc
