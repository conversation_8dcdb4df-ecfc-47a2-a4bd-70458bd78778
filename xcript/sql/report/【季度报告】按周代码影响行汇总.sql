select concat( years, '年', months, '周') as `统计周期`, years, months,
p_count as `贡献者(个)`,
round(line_total, 2) as `总影响行`,
round(line_total/p_count, 2) as `人均有效`

from
(
select years, months, count(0) as p_count, sum(line_total) as line_total from
(
select wd.year_of_date as years, wd.week_of_date as months, sum(add_counts_effect) as line_total, username
FROM
mantis.v_git_commits_diff_effect de,
devops_base.s_real_week_of_date wd
where date_format(de.datetime, '%Y-%m-%d') = wd.real_date and datetime > '2021-10-01'
and '${按回车刷新：}' = '${按回车刷新：}'
group by years, months, username
) date_p
where line_total > 50
group by years, months
) p



order by years, months
