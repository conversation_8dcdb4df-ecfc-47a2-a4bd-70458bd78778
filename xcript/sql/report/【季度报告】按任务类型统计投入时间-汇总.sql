select team_name, s_team_name, eff.task_type, sum(eff.total) as total

from
(
select owner,
p_task_type as task_type,
sum(effort) as total
from
(
SELECT SUBSTRING_INDEX(tt.tapd_time_sheet_owner, '_', 1)  as owner,
case when ddt.task_type is not null and trim(ddt.task_type) != ''
then trim( replace( SUBSTRING_INDEX(ddt.task_type, '（', 1), '	', '' ) )
else '【未填】' end as p_task_type,
sum(tt.tapd_time_sheet_timespent) as effort
FROM
	mantis.dev_effic_dev_task ddt,
	mantis.tapd_entry_timesheet tt
where ddt.tapd_task_id = tt.tapd_time_sheet_entity_id
and tt.tapd_time_sheet_spentdate >= date_format( date_add('${截止日期yyyy-mm-dd：}', interval - '${最近几天：}' day), '%Y-%m-%d')
and tt.tapd_time_sheet_spentdate < date_format('${截止日期yyyy-mm-dd：}', '%Y-%m-%d')
and tt.tapd_time_sheet_owner is not null
group by tapd_time_sheet_owner, p_task_type
) s
group by owner, task_type
) eff

inner join
	devops_base.project_committer pc
on pc.cn_name = eff.owner

group by team_name, s_team_name, task_type
