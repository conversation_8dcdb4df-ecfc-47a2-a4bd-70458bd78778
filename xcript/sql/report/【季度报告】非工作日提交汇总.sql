select d.username, d.总影响行 as 非工作日影响行, a.总影响行,
concat(round(d.总影响行/a.总影响行*100, 2), '%') as 非工作日占比 , d.涉及日期

from
(
select cd.username, -- cd.user_id,
sum(cd.add_counts_effect) as 总影响行,
-- concat('【', group_concat(distinct date_format(datetime, '%m月%d日%H点') order by date_format(datetime, '%Y-%m-%d %H')), '】') as dates
concat('【', group_concat(distinct date_format(datetime, '%m月%d日') order by date_format(datetime, '%Y-%m-%d')), '】') as 涉及日期
from mantis.v_git_commits_diff_effect cd
, mantis.v_date_holiday h
where datetime > date_add('${季度截止日期yyyy-mm-dd：}', interval -3 month) and datetime < '${季度截止日期yyyy-mm-dd：}'
and cd.add_counts > 0
and is_merge = 'n'
and date_format(datetime, '%Y-%m-%d') = h.day
group by username, cd.user_id
) d
left outer join
(
select cd.username, sum(cd.add_counts_effect) as 总影响行
from mantis.v_git_commits_diff_effect cd
where datetime > date_add('${季度截止日期yyyy-mm-dd：}', interval -3 month) and datetime < '${季度截止日期yyyy-mm-dd：}'
and cd.add_counts > 0
and is_merge = 'n'
group by username, cd.user_id
) a
on d.username = a.username

order by d.总影响行 desc
