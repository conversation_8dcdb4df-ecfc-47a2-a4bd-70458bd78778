SELECT
	sum(cd.add_counts_effect) as total
FROM
	mantis.v_git_commits_diff_effect cd
where cd.datetime > date_add('${统计截止日期yyyy-mm-01：}', interval - 13 month)
and cd.datetime < date_add('${统计截止日期yyyy-mm-01：}', interval - 1 month)
and
(
cd.file_type in ('.jsp', '.html', '.htm', '.vue', '.js', '.jsx', '.less', '.ftl', '.ts')
or
cd.file_type in ('.xml', '.bundle/_CodeSignature/CodeResources', '.xib', '.storyboard', '.nib')
or
cd.file_type in ('.m', '.h')
or
cd.file_type in ('.java', '.kt', '.py', '.rs', '.toml', 'ets')
)
