select team, br_style as 分支类型, count(0) as 总分支数,
sum(applies) as 总变更数, sum(prod_applies) as 产线变更数, round(sum(prod_applies)/count(0), 2) as 每分支产线变更次,
sum(rb_times > 0) as 产线发生回滚的分支数, round(sum(rb_times > 0)/count(0) * 100, 2) as 产线发生回滚的比例,
sum(1st) as 产线一次发布成功的, round(sum(1st)/count(0) * 100, 2) as 产线一次发布成功的比例
from
(

select ss.*,
case when prod_applies = 1 then 1 else 0 end as 1st,
case when prod_applies = 2 then 1 else 0 end as 2nd,
sum(rb.module_name is not null) as rb_times
from
(select pipeline_id, module_name, br_style, team, count(0) as applies, sum(types like '%生产%') as prod_applies, min(apply_at) as first, max(next) as next
from
(
select rd.pipeline_id, rd.module_name, rd.br_style, team.team, rd.apply_at, rd.next, group_concat(distinct rd.`type` order by rd.`type`) as types from
devops_base.record_online_release_detail rd
inner join
(
SELECT
	distinct am.module_name, ti.team_name as team
FROM
	spider.app_mgt_app_module am
inner join
	spider.team_mgt_app_bind ab
on ab.app_id = am.app_id
inner join
	spider.tool_mgt_team_info ti
on ab.team_id = ti.id
) team
on team.module_name = rd.module_name
where rd.operate_time > '2024-04-01' and rd.operate_time < '2024-07-01'
group by rd.pipeline_id, rd.module_name, rd.br_style, team.team, rd.apply_at, rd.next
) brs
group by pipeline_id, module_name, br_style, team
) ss

left outer join

(
select
distinct
-- replace(json_extract(replace(r.action_value, '\'', '"'), '$.iteration_id'), '"', '') as iteration_id,
replace(json_extract(replace(r.action_value, '\'', '"'), '$.app_name'), '"', '') as module_name,
-- concat(json_extract(replace(r.action_value, '\'', '"'), '$.suite_code')) as suite_code,
-- r.username,
-- r.action_value,
r.operate_time
from spider.user_action_record r
where r.action_item = 'rollback'
and json_extract(replace(r.action_value, '\'', '"'), '$.suite_code') like '%prod"'
) rb
on ss.module_name = rb.module_name
and ss.first < rb.operate_time and ss.next > rb.operate_time

group by pipeline_id, module_name, br_style, team, applies, first, next

) data


where '${回车刷新：}' = '${回车刷新：}'


group by team, 分支类型
