select
t.team,
t.username,
t.当季总影响行,
last.total_lines as 上年同季,
nearest.total_lines as 上一季,
m1.total_lines as 1月影响行,
m2.total_lines as 2月影响行,
m3.total_lines as 3月影响行,
m4.total_lines as 4月影响行,
m5.total_lines as 5月影响行,
m6.total_lines as 6月影响行,
bugs1.total as Q1总缺陷数,
bugs1.time as `Q1平均修复(时)`,
bugs2.total as Q2总缺陷数,
bugs2.time as `Q2平均修复(时)`,
round(bug.total*1000/code.total, 3) as `截至Q2千行bug`,
t.当季提交分类明细

from
(
select
t_u.team,
d.username,
d.user_id,
sum(d.total_lines) as 当季总影响行,
group_concat('【', d.file_type, '：', d.total_lines, '】' order by d.file_type) as 当季提交分类明细
from
(
select cd.username, cd.user_id, cd.file_type, sum(cd.add_counts_effect) as total_lines
from mantis.v_git_commits_diff_effect cd
where datetime > '2024-04-01' and datetime < '2024-07-01'
group by username, cd.user_id, file_type
) d

left outer join

(
SELECT
	distinct gm.git_user_id, ub.user_name, ti.team_name as team
FROM
	spider.user_gitlab_members gm
inner join
(
select distinct * from
(
select ub.team_id, ub.user_name
from
	spider.team_mgt_user_bind ub
union all
SELECT
	min(ti.id) as id, ti.team_owner
FROM
	spider.tool_mgt_team_info ti
where team_level = 2
group by team_owner
) f
where user_name is not null
) ub
on gm.username = ub.user_name
inner join
	spider.tool_mgt_team_info ti
on ub.team_id = ti.id
where gm.git_user_id is not null
) t_u
on d.user_id = t_u.git_user_id

where d.total_lines <> 0
group by team, username
) t


-- *****拼上1月提交***************
left outer join
(
select cd.username, cd.user_id, sum(cd.add_counts_effect) as total_lines
from mantis.v_git_commits_diff_effect cd
where datetime > '2024-01-01' and datetime < '2024-02-01'
group by username, cd.user_id
) m1
on m1.user_id = t.user_id
-- ***************拼上1月提交*****

-- *****拼上2月提交***************
left outer join
(
select cd.username, cd.user_id, sum(cd.add_counts_effect) as total_lines
from mantis.v_git_commits_diff_effect cd
where datetime > '2024-02-01' and datetime < '2024-03-01'
group by username, cd.user_id
) m2
on m2.user_id = t.user_id
-- ***************拼上2月提交*****

-- *****拼上3月提交***************
left outer join
(
select cd.username, cd.user_id, sum(cd.add_counts_effect) as total_lines
from mantis.v_git_commits_diff_effect cd
where datetime > '2024-03-01' and datetime < '2024-04-01'
group by username, cd.user_id
) m3
on m3.user_id = t.user_id
-- ***************拼上3月提交*****

-- *****拼上4月提交***************
left outer join
(
select cd.username, cd.user_id, sum(cd.add_counts_effect) as total_lines
from mantis.v_git_commits_diff_effect cd
where datetime > '2024-04-01' and datetime < '2024-05-01'
group by username, cd.user_id
) m4
on m4.user_id = t.user_id
-- ***************拼上4月提交*****

-- *****拼上5月提交***************
left outer join
(
select cd.username, cd.user_id, sum(cd.add_counts_effect) as total_lines
from mantis.v_git_commits_diff_effect cd
where datetime > '2024-05-01' and datetime < '2024-06-01'
group by username, cd.user_id
) m5
on m5.user_id = t.user_id
-- ***************拼上5月提交*****

-- *****拼上6月提交***************
left outer join
(
select cd.username, cd.user_id, sum(cd.add_counts_effect) as total_lines
from mantis.v_git_commits_diff_effect cd
where datetime > '2024-06-01' and datetime < '2024-07-01'
group by username, cd.user_id
) m6
on m6.user_id = t.user_id
-- ***************拼上6月提交*****


-- *****拼上Q1缺陷***************
left outer join
(
SELECT
	biz_fixer,
	count( case when biz_status in ('closed', 'resolved') then 1 end ) as total,
	round(avg( case biz_status when  'closed' then res_diff when 'rejected' then clo_diff when 'resolved' then res_diff else null end )/60, 2) as time
FROM
	devops_base.all_bugs_from2023
where DATE_FORMAT(ad_create_time, '%Y-%m') in ('2024-03', '2024-02', '2024-01')
and biz_status in ('closed', 'resolved')
and attribution_analysis != '历史遗留问题'
group by biz_fixer
) bugs1
on bugs1.biz_fixer = t.username
-- ***************拼上Q1缺陷*****

-- *****拼上Q2缺陷***************
left outer join
(
SELECT
	biz_fixer,
	count( case when biz_status in ('closed', 'resolved') then 1 end ) as total,
	round(avg( case biz_status when  'closed' then res_diff when 'rejected' then clo_diff when 'resolved' then res_diff else null end )/60, 2) as time
FROM
	devops_base.all_bugs_from2023
where DATE_FORMAT(ad_create_time, '%Y-%m') in ('2024-06', '2024-05', '2024-04')
and biz_status in ('closed', 'resolved')
and attribution_analysis != '历史遗留问题'
group by biz_fixer
) bugs2
on bugs2.biz_fixer = t.username
-- ***************拼上Q2缺陷*****


-- *****拼上同比提交***************
left outer join
(
select cd.username, cd.user_id, sum(cd.add_counts_effect) as total_lines
from mantis.v_git_commits_diff_effect cd
where datetime > '2023-04-01' and datetime < '2023-07-01'
group by username, cd.user_id
) last
on last.user_id = t.user_id
-- ***************拼上同比提交*****

-- *****拼上环比提交***************
left outer join
(
select cd.username, cd.user_id, sum(cd.add_counts_effect) as total_lines
from mantis.v_git_commits_diff_effect cd
where datetime > '2024-01-01' and datetime < '2024-04-01'
group by username, cd.user_id
) nearest
on nearest.user_id = t.user_id
-- ***************拼上环比提交*****


left outer join
(
select b2.biz_fixer as username, count(0) as total from
	devops_base.all_bugs_from2023 b2
where b2.biz_status in ('closed', 'resolved') and b2.biz_created < '2024-07-01' and b2.biz_created >= '2023-07-01'
and b2.attribution_analysis != '历史遗留问题'
and b2.biz_fixer in
(select distinct d.fixer from devops_base.t_git_dever d where d.fixer is not null)
group by username
) bug

on t.username = bug.username

left outer join
(
select cd.username, sum(cd.add_counts_effect) as total from
mantis.v_git_commits_diff_effect cd
where
(
cd.file_type in ('.jsp', '.html', '.htm', '.vue', '.js', '.jsx', '.less', '.ftl', '.ts')
or
cd.file_type in ('.xml', '.bundle/_CodeSignature/CodeResources', '.xib', '.storyboard', '.nib')
or
cd.file_type in ('.m', '.h')
or
cd.file_type in ('.java', '.kt', '.py', '.rs', '.toml', 'ets')
)
and cd.datetime >= '2023-07-01' and cd.datetime < '2024-07-01'
and cd.username in
(select distinct d.fixer from devops_base.t_git_dever d where d.fixer is not null)
group by username
) code
on t.username = code.username
