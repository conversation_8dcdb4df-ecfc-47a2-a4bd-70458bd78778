select team as 团队, name as 发布者, ss.module_name as 应用名, pipeline_id as 分支号, br_style as 分支类型, first as 首次变更, applies as 变更次数,
-- case when applies = 1 then 1 else 0 end as 1st,
-- case when applies = 2 then 1 else 0 end as 2nd,
case when sum(rb.module_name is not null) > 0 then '有' else '无' end as 产线回滚
from
(
select pipeline_id, module_name, br_style, team, name, count(0) as applies, min(apply_at) as first, max(next) as next
from
(
select rd.pipeline_id, rd.module_name, rd.br_style, team.team, rd.apply_at, rd.next, group_concat(distinct rd.cn_name order by rd.cn_name) as name
from
devops_base.record_online_release_detail rd
inner join
(
SELECT
	distinct am.module_name, ti.team_name as team
FROM
	spider.app_mgt_app_module am
inner join
	spider.team_mgt_app_bind ab
on ab.app_id = am.app_id
inner join
	spider.tool_mgt_team_info ti
on ab.team_id = ti.id
) team
on team.module_name = rd.module_name
where rd.operate_time > '2024-04-01' and rd.operate_time < '2024-07-01'
group by pipeline_id, module_name, br_style, team, apply_at, next
) brs
group by pipeline_id, module_name, br_style, team
) ss

left outer join

(
select
distinct
-- replace(json_extract(replace(r.action_value, '\'', '"'), '$.iteration_id'), '"', '') as iteration_id,
replace(json_extract(replace(r.action_value, '\'', '"'), '$.app_name'), '"', '') as module_name,
-- concat(json_extract(replace(r.action_value, '\'', '"'), '$.suite_code')) as suite_code,
-- r.username,
-- r.action_value,
r.operate_time
from spider.user_action_record r
where r.action_item = 'rollback'
and json_extract(replace(r.action_value, '\'', '"'), '$.suite_code') like '%prod"'
) rb
on ss.module_name = rb.module_name
and ss.first < rb.operate_time and ss.next > rb.operate_time

group by pipeline_id, ss.module_name, br_style, team, name, applies, first, next

order by team desc, ss.module_name, first desc
