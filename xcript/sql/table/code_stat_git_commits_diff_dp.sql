CREATE TABLE mantis.`code_stat_git_commits_diff_dp` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `project_id` int(10) NOT NULL,
  `commit` varchar(80) DEFAULT NULL,
  `old_path` varchar(500) DEFAULT NULL,
  `new_path` varchar(500) DEFAULT NULL,
  `diff` mediumblob,
  `datetime` datetime DEFAULT NULL,
  `user_id` varchar(25) DEFAULT NULL,
  `username` varchar(50) DEFAULT NULL,
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `action` varchar(200) DEFAULT NULL,
  `ref` varchar(80) DEFAULT NULL,
  `new_file` varchar(100) DEFAULT NULL,
  `message` varchar(2000) DEFAULT NULL,
  `file_type` varchar(100) DEFAULT NULL,
  `add_counts` int(11) DEFAULT NULL,
  `del_counts` int(11) DEFAULT NULL,
  `add_counts_effect` int(11) DEFAULT NULL,
  `title` varchar(1000) DEFAULT NULL,
  `is_merge` varchar(5) DEFAULT NULL,
  `diff_md5` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`id`, `project_id`),
  UNIQUE KEY `uni_1` (`project_id`,`commit`,`new_path`,`datetime`,`ref`),
  KEY `idx_0` (`username`),
  KEY `idx_1` (`user_id`),
  KEY `idx_2` (`datetime`),
  KEY `idx_3` (`file_type`),
  KEY `idx_4` (`add_counts`),
  KEY `idx_5` (`del_counts`),
  KEY `idx_7` (`add_counts_effect`),
  KEY `idx_8` (`is_merge`),
  KEY `idx_9` (`diff_md5`),
  KEY `idx_10` (`commit`),
  KEY `idx_6` (`new_path`),
  KEY `idx_11` (`create_date`)
)
PARTITION BY Hash(project_id)
PARTITIONS 8
