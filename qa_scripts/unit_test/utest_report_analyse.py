import csv
import datetime
import os
from os.path import getsize

from qa_scripts.common.routers.router import Result
from qa_scripts.common.shell.shell_cmd import exec_local_cmd
from mantis.settings import logger, UTEST
from qa_scripts.unit_test.model import UtestReportInfo, SpiderProductMgtTestReportInfo, UtestReportBehaviors, UtestReportSuites


class UtestAnalyseForm:

    @classmethod
    def as_view(cls, request):
        utest_analyse_form = UtestAnalyseForm()
        return utest_analyse_form.utest_analyse_form(request)

    def utest_analyse_form(self, request):
        logger.info("发布分析请求参数 {}".format(request))
        iteration_id = request.get('iteration_id')
        app_name = request.get('app_name')
        lib_repo_url = request.get('lib_repo_url')
        batch_num = request.get('batch_num')

        utest_report_dir = UTEST['utest_report_dir']

        if not os.path.exists(utest_report_dir):
            cmd = "mkdir -p {}".format(utest_report_dir)
            exec_local_cmd(cmd)

        app_utest_report_dir = os.path.join(utest_report_dir, app_name, iteration_id)

        utest_report_data_dir = self.__download_utest_report_zip(app_utest_report_dir, lib_repo_url)
        logger.info("应用单元测试报告数据路径为：{}".format(utest_report_data_dir))
        try:
            utest_report_info_id = self.__handle_utest_report_info(iteration_id, app_name, lib_repo_url, batch_num)

            utest_report_behaviors_id = self.__handle_utest_report_behaviors(utest_report_info_id, utest_report_data_dir)

            self.__handle_utest_reprot_suites(utest_report_behaviors_id, utest_report_data_dir)
        except Exception as e:
            logger.error("单元测试报告分析失败！")
            logger.error(e)
            return Result.failed_dict("单元测试报告分析失败！")

        return Result.success_dict("单元测试报告分析成功！")

    def __handle_utest_reprot_suites(self, urb_id, utest_report_data_dir):
        path = os.path.join(utest_report_data_dir, 'data')
        file_name = 'suites.csv'
        for root, dirs, files in os.walk(path):
            if file_name in files:
                suites_file_path = os.path.join(root, file_name)
                if getsize(suites_file_path):
                    with open(suites_file_path, 'r') as fo:
                        total_count = len(fo.readlines())
                        logger.info("suites.csv中的总行数为（不含表头）：{}".format(total_count-1))
                        # 重新设置文件读取指针到开头
                        fo.seek(0, 0)

                        f = csv.reader(fo)
                        count = 0
                        for row in f:
                            if count > 0:
                                result = UtestReportSuites.select().where(UtestReportSuites.behaviors_id == urb_id,
                                                                          UtestReportSuites.suite == row[5],
                                                                          UtestReportSuites.test_class == row[7],
                                                                          UtestReportSuites.name == row[9])
                                now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                                if result:
                                    urs_id = result.get()
                                    q = UtestReportSuites.update({UtestReportSuites.status: row[0],
                                                                  UtestReportSuites.duration: row[3],
                                                                  UtestReportSuites.description: row[10],
                                                                  UtestReportSuites.update_time: now_time}).where(
                                        UtestReportSuites.id == urs_id)
                                    q.execute()
                                else:
                                    utest_report_suites_id = UtestReportSuites(behaviors_id=urb_id, status=row[0],
                                                                               start_time=row[1] if row[1] else None,
                                                                               stop_time=row[2] if row[2] else None,
                                                                               duration=row[3],
                                                                               parent_suite=row[4],
                                                                               suite=row[5], sub_suite=row[6],
                                                                               test_class=row[7],
                                                                               test_method=row[8], name=row[9],
                                                                               description=row[10],
                                                                               create_time=now_time)
                                    utest_report_suites_id.save()
                            count += 1
                        logger.info("插入数据库（表）utest_report_suites中的总行数为：{}".format(count-1))
                        if total_count == count:
                            logger.info("本次导入全部成功")
                        else:
                            logger.info("本次导入有部分数据丢失")

    def __handle_utest_report_behaviors(self, uri_id, utest_report_data_dir):
        data_src_path = os.path.join(utest_report_data_dir, 'data')
        file_name = 'behaviors.csv'
        for root, dirs, files in os.walk(data_src_path):
            if file_name in files:
                behaviors_file_path = os.path.join(root, file_name)
                if getsize(behaviors_file_path):
                    with open(behaviors_file_path, 'r') as fo:
                        total_count = len(fo.readlines())
                        logger.info("behaviors.csv中的总行数为（不含表头）：{}".format(total_count - 1))

                        # 重新设置文件读取指针到开头
                        fo.seek(0, 0)

                        f = csv.reader(fo)
                        count = 0
                        for row in f:
                            if count > 0:
                                result = UtestReportBehaviors.select().\
                                    where(UtestReportBehaviors.unit_report_id == uri_id)
                                now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                                if result:
                                    utest_report_behaviors_id = result.get()
                                    q = UtestReportBehaviors.update({UtestReportBehaviors.epic: row[0],
                                                                     UtestReportBehaviors.feature: row[1],
                                                                     UtestReportBehaviors.story: row[2],
                                                                     UtestReportBehaviors.failed: row[3],
                                                                     UtestReportBehaviors.broken: row[4],
                                                                     UtestReportBehaviors.passed: row[5],
                                                                     UtestReportBehaviors.skipped: row[6],
                                                                     UtestReportBehaviors.unknown: row[7],
                                                                     UtestReportBehaviors.update_time: now_time
                                                                     }).\
                                        where(UtestReportBehaviors.id == utest_report_behaviors_id)
                                    q.execute()
                                else:
                                    utest_report_behaviors_id = UtestReportBehaviors(unit_report_id=uri_id,
                                                                                     epic=row[0] if row[0] else 0,
                                                                                     feature=row[1] if row[1] else 0,
                                                                                     story=row[2] if row[2] else 0,
                                                                                     failed=row[3], broken=row[4],
                                                                                     passed=row[5], skipped=row[6],
                                                                                     unknown=row[7],
                                                                                     create_time=now_time)
                                    utest_report_behaviors_id.save()
                            count += 1
                        logger.info("插入数据库（表）utest_report_behaviors中的总行数为：{}".format(count - 1))
                        if total_count == count:
                            logger.info("本次导入全部成功")
                        else:
                            logger.info("本次导入有部分数据丢失")

        return utest_report_behaviors_id

    def __handle_utest_report_info(self, iteration_id, app_name, lib_repo_url, batch_num):

        result = UtestReportInfo.select().where(UtestReportInfo.module_name == app_name,
                                                UtestReportInfo.iteration_id == iteration_id,
                                                UtestReportInfo.lib_repo_url == lib_repo_url)

        report = SpiderProductMgtTestReportInfo.select().\
            where(SpiderProductMgtTestReportInfo.module_name == app_name,
                  SpiderProductMgtTestReportInfo.iteration_id == iteration_id,
                  SpiderProductMgtTestReportInfo.lib_repo_url == lib_repo_url).get()

        report_url = report.report_url
        jenkins_job_id = report.jenkins_job_id
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logger.info("batch_num={}".format(batch_num))
        if result:
            uri_id = result.get()

            update_data = {"report_url": report_url, 'jenkins_job_id': jenkins_job_id, 'update_time': now_time}

            if batch_num:
                update_data.update({'batch_num': batch_num})

            q = UtestReportInfo.update(update_data).where(UtestReportInfo.id == uri_id)

            q.execute()
        else:
            uri_id = UtestReportInfo(module_name=app_name,
                                     jenkins_job_id=jenkins_job_id,
                                     report_url=report_url,
                                     lib_repo_url=lib_repo_url,
                                     create_time=now_time,
                                     iteration_id=iteration_id,
                                     batch_num=batch_num)
            uri_id.save()

        return uri_id

    def __download_utest_report_zip(self, app_utest_report_dir, lib_repo_url):
        if not os.path.exists(app_utest_report_dir):
            cmd = "mkdir -p {}".format(app_utest_report_dir)
            exec_local_cmd(cmd)

        zip_name = lib_repo_url.split('/')[-1]
        zip_dir = zip_name.split('.')[0]

        os.chdir(app_utest_report_dir)
        cmd = "wget {} -O {}".format(lib_repo_url, zip_name)
        exec_local_cmd(cmd)

        cmd = "mkdir -p {}".format(zip_dir)
        exec_local_cmd(cmd)

        cmd = "unzip -o {} -d {}/".format(zip_name, zip_dir)
        exec_local_cmd(cmd)

        utest_report_data_dir = os.path.join(app_utest_report_dir, zip_dir)
        return utest_report_data_dir


class BatchUtestAnalyseFrom:

    @classmethod
    def as_view(cls, request):
        batch_utest_analyse_form = BatchUtestAnalyseFrom()
        return batch_utest_analyse_form.batch_utest_analyse_form(request)

    def batch_utest_analyse_form(self, request):
        logger.info("发布分析请求参数 {}".format(request))
        batch_app_report_info_list = request.get('batch_app_report_info_list')
        utest_analyse_form = UtestAnalyseForm()
        for row in batch_app_report_info_list:
            utest_analyse_form.utest_analyse_form(row)

        return Result.success_dict("单元测试报告批量分析成功！")
