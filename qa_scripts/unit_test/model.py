from peewee import *
from qa_scripts.common.db.base_model import BaseModel


class UtestReportInfo(BaseModel):
    module_name = CharField(verbose_name='应用名', max_length=50)
    jenkins_job_id = IntegerField(null=True, verbose_name='jenkins执行记录id')
    lib_repo_url = CharField(null=True, verbose_name='单元测试制品地址', max_length=255)
    report_url = CharField(null=True, verbose_name='单元测试报告地址', max_length=255)
    create_time = DateTimeField(null=True, verbose_name='创建时间')
    iteration_id = CharField(verbose_name='迭代号', max_length=100)
    batch_num = IntegerField(verbose_name='批次号')
    update_time = DateTimeField(null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'utest_report_info'
        verbose_name = '单元测试报告信息表'


class UtestReportBehaviors(BaseModel):
    unit_report_id = IntegerField(verbose_name='单元测试报告id')
    epic = IntegerField(verbose_name='epic数')
    feature = IntegerField(verbose_name='特性数')
    story = IntegerField(verbose_name='故事数')
    failed = IntegerField(verbose_name='失败数')
    broken = IntegerField(verbose_name='中断数')
    passed = IntegerField(verbose_name='通过数')
    skipped = IntegerField(verbose_name='跳过数')
    unknown = IntegerField(verbose_name='未知数')
    create_time = DateTimeField(null=True, verbose_name='创建时间')
    update_time = DateTimeField(null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'utest_report_behaviors'
        verbose_name = '单元测试行为统计表'


class UtestReportSuites(BaseModel):
    behaviors_id = IntegerField(verbose_name='单元测试报告行为统计表id')
    status = CharField(verbose_name='执行状态', max_length=255)
    start_time = DateTimeField(null=True, verbose_name='开始时间')
    stop_time = DateTimeField(null=True, verbose_name='结束时间')
    duration = IntegerField(verbose_name='持续时长（ms）')
    parent_suite = CharField(verbose_name='父测试套', max_length=255)
    suite = CharField(verbose_name='测试套', max_length=255)
    sub_suite = CharField(verbose_name='子测试套', max_length=255)
    test_class = CharField(verbose_name='测试类', max_length=255)
    test_method = CharField(verbose_name='测试方法', max_length=255)
    name = CharField(verbose_name='案例名', max_length=255)
    description = CharField(verbose_name='描述', max_length=255)
    create_time = DateTimeField(null=True, verbose_name='创建时间')
    update_time = DateTimeField(null=True, verbose_name='更新时间')

    class Meta:
        db_table = 'utest_report_suites'
        verbose_name = '单元测试案例执行表'


class SpiderProductMgtTestReportInfo(BaseModel):
    module_name = CharField(verbose_name='应用名', max_length=255)
    jenkins_job_id = CharField(verbose_name='jenkins任务id', max_length=255)
    report_url = CharField(verbose_name='报告地址', max_length=255)
    lib_repo_url = CharField(verbose_name='制品地址', max_length=255)
    lib_repo_branch = CharField(verbose_name='制品分支名', max_length=255)
    lib_repo_size = CharField(verbose_name='制品大小', max_length=255)
    create_time = DateTimeField(null=True, verbose_name='创建时间')
    iteration_id = CharField(verbose_name='迭代号', max_length=100)

    class Meta:
        db_table = 'spider_product_mgt_test_report_info'
        verbose_name = 'spider测试报告制品表视图'