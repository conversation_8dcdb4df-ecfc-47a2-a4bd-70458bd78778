import os
import sys
import subprocess
import csv
import datetime

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from os.path import isdir, abspath, getsize, join
from qa_scripts.unit_test.model import UtestReportInfo, UtestReportBehaviors, UtestReportSuites


def shell_cmd(cmd):
    print(cmd)
    p = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    stdout, stderr = p.communicate()
    rt_code = p.returncode
    stdout_info = stdout.decode()
    stderr_info = stderr.decode()
    print(stdout_info)
    return rt_code, stdout_info, stderr_info


def handle_suites(path, urb_id):
    file_name = 'suites.csv'
    for root, dirs, files in os.walk(path):
        if file_name in files:
            suites_file_path = os.path.join(root, file_name)
            if getsize(suites_file_path):
                f = csv.reader(open(suites_file_path, 'r'))
                count = 0
                for row in f:
                    if count > 0:
                        result = UtestReportSuites.select().where(UtestReportSuites.behaviors_id == urb_id,
                                                                  UtestReportSuites.suite == row[5],
                                                                  UtestReportSuites.test_class == row[7],
                                                                  UtestReportSuites.name == row[9])
                        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        if result:
                            urs_id = result.get()
                            q = UtestReportSuites.update({UtestReportSuites.status: row[0],
                                                          UtestReportSuites.duration: row[3],
                                                          UtestReportSuites.description: row[10],
                                                          UtestReportSuites.update_time: now_time}).where(UtestReportSuites.id == urs_id)
                            q.execute()
                        else:
                            utest_report_suites_id = UtestReportSuites(behaviors_id=urb_id, status=row[0], start_time=row[1],
                                                                       stop_time=row[2], duration=row[3], parent_suite=row[4],
                                                                       suite=row[5], sub_suite=row[6], test_class=row[7],
                                                                       test_method=row[8], name=row[9], description=row[10],
                                                                       create_time=now_time)
                            utest_report_suites_id.save()
                    count += 1


def handle_behaviors(path, uri_id):
    data_src_path = os.path.join(path, 'data')
    file_name = 'behaviors.csv'
    for root, dirs, files in os.walk(data_src_path):
        if file_name in files:
            behaviors_file_path = os.path.join(root, file_name)
            if getsize(behaviors_file_path):
                f = csv.reader(open(behaviors_file_path, 'r'))
                count = 0
                for row in f:
                    if count > 0:
                        result = UtestReportBehaviors.select().where(UtestReportBehaviors.unit_report_id == uri_id)
                        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        if result:
                            utest_report_behaviors_id = result.get()
                            q = UtestReportBehaviors.update({UtestReportBehaviors.epic: row[0],
                                                             UtestReportBehaviors.feature: row[1],
                                                             UtestReportBehaviors.story: row[2],
                                                             UtestReportBehaviors.failed: row[3],
                                                             UtestReportBehaviors.broken: row[4],
                                                             UtestReportBehaviors.passed: row[5],
                                                             UtestReportBehaviors.skipped: row[6],
                                                             UtestReportBehaviors.unknown: row[7],
                                                             UtestReportBehaviors.update_time: now_time
                                                             }).where(UtestReportBehaviors.id == utest_report_behaviors_id)
                            q.execute()
                        else:
                            utest_report_behaviors_id = UtestReportBehaviors(unit_report_id=uri_id,
                                                                             epic=row[0] if row[0] else 0,
                                                                             feature=row[1] if row[1] else 0,
                                                                             story=row[2] if row[2] else 0,
                                                                             failed=row[3], broken=row[4],
                                                                             passed=row[5], skipped=row[6],
                                                                             unknown=row[7], create_time=now_time)
                            utest_report_behaviors_id.save()
                        handle_suites(data_src_path, utest_report_behaviors_id)
                    count += 1


def find_unit_report(dir_path, batch_num):

    print("batch_num: {}".format(batch_num))
    return 0
    for row in os.listdir(dir_path):
        if 'unit0.0.0' in row:
            src_path = os.path.join(dir_path, row, 'target', 'allure-report-unit')
            module_name = row.split('_')[-1]
            iteration_id = '_'.join(row.split('_')[:-1])
            if os.path.exists(src_path):
                result = UtestReportInfo.select().where(UtestReportInfo.module_name == module_name,
                                                        UtestReportInfo.iteration_id == iteration_id,
                                                        UtestReportInfo.batch_num == batch_num)
                now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                if result:
                    # 主键ID
                    uri_id = result.get()
                    q = UtestReportInfo.update({UtestReportInfo.update_time: now_time}).where(UtestReportInfo.id == uri_id)
                    q.execute()
                else:
                    # 主键ID
                    uri_id = UtestReportInfo(module_name=module_name, iteration_id=iteration_id,
                                             batch_num=batch_num, create_time=now_time)
                    uri_id.save()

                handle_behaviors(src_path, uri_id)


if __name__ == '__main__':
    dir_path = '/data/jenkinsworkspace/workspace'
    batch_num = sys.argv[1]
    # dir_path = 'D:\\data\\utest'
    # batch_num = 1
    find_unit_report(dir_path, batch_num)

