import datetime
import sys
import os
import chinese_calendar


PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from qa_scripts.common.holidays.model import DateHoliday


class CalendarTools:

    def collect_holidays(self):
        holidays = chinese_calendar.holidays

        for k, v in holidays.items():
            if k > datetime.date(2024, 12, 31):
                d = DateHoliday(day_date=k.strftime("%Y-%m-%d"), is_holiday=1)
                d.save()

        workdays = chinese_calendar.workdays

        for k, v in workdays.items():
            if k > datetime.date(2024, 12, 31):
                d = DateHoliday(day_date=k.strftime("%Y-%m-%d"), is_holiday=0)
                d.save()


if __name__ == "__main__":
    c = CalendarTools()
    c.collect_holidays()