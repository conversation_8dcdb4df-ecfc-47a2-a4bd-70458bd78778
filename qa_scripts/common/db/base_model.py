from peewee import MySQLDatabase, Model
from mantis.settings import DATABASES


mysql_db = MySQLDatabase(DATABASES['default']['NAME'], user=DATABASES['default']['USER'],
                         password=DATABASES['default']['PASSWORD'], host=DATABASES['default']['HOST'],
                         port=DATABASES['default']['PORT'], charset=DATABASES['default']['CHARSET'])


class BaseModel(Model):
    class Meta:
        database = mysql_db
