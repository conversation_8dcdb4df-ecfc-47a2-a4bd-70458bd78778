import re
import subprocess

from mantis.settings import logger as log
from mantis.settings import COMMON


class CmdException(object):
    pass


def exec_local_cmd(cmd, timeout=None):
    """本地执行命令 zt@2020-09-08"""
    log.info("本地执行命令：{}".format(re.sub(r'^sshpass -p \S*', "sshpass -p ******", cmd)))
    try:
        if not timeout:
            timeout = int(COMMON['exec_cmd_timeout'])
        completed_process_obj = subprocess.run(cmd, shell=True,
                                               timeout=timeout,
                                               capture_output=True, check=True)
        log.info("本地执行结果：{}".format(bytes.decode(completed_process_obj.stdout)))
    except subprocess.CalledProcessError as err:
        msg = "本地执行命令出错：{}".format(bytes.decode(err.stderr))
        log.error(msg)
        raise CmdException(msg)
    return completed_process_obj