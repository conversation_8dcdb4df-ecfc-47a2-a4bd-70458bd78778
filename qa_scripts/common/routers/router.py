from qa_scripts.dao.get.mysql.service_results import get_exec_params
from qa_scripts.dao.update.mysql.service_results import update_exec_results
from mantis.settings import logger, URL_DEF
import json


class Result:
    """调用结果类"""
    success = "success"
    failed = "failure"
    status = ""
    msg = ""

    @classmethod
    def success_dict(cls, msg):
        result = Result()
        result.status = cls.success
        result.msg = msg
        return result

    @classmethod
    def failed_dict(cls, msg):
        result = Result()
        result.status = cls.failed
        result.msg = msg
        return result


class Router:
    """路由分发功能"""
    def __init__(self, urls={}, url_def=URL_DEF):
        self.urls = urls
        self.url_def = url_def

    def register(self, url_name, func):
        self.urls["url_name"] = func

    def register_dict(self, urls_dict):
        self.urls = self.urls + urls_dict

    @staticmethod
    def _get_params(sid):
        """获取调用参数"""
        return json.loads(get_exec_params(sid))

    def _get_url_name(self, exec_parameter):
        """获取调用url"""
        return exec_parameter[self.url_def]

    @staticmethod
    def _record_result(sid, result):
        """记录调用结果"""
        # 如果结果是一个列表的话，其中有一个是失败的，则证明整个过程是失败的
        if isinstance(result, list):
            status = Result.success
            for row in result:
                if row["status"] == Result.failed:
                    status = Result.failed
            logger.info(result)
            update_exec_results(sid, status, json.dumps(result, ensure_ascii=False))
        else:
            logger.info("调用状态{}".format(result.status))
            logger.info("调用结果{}".format(result.msg))
            update_exec_results(sid, result.status, result.msg)

    def dispatch(self, sid):
        """路由分发"""
        exec_parameter = self._get_params(sid)
        logger.info(exec_parameter)
        url_name = self._get_url_name(exec_parameter)
        # 执行调用函数
        result = self.urls[url_name](exec_parameter)
        self._record_result(sid, result)




