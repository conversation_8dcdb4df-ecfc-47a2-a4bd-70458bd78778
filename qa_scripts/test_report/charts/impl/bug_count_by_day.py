from pyecharts.charts import  Line
from mantis.pool import InstanceDbSession
from mantis.settings import TEST_REPORT
from qa_scripts.test_report.charts.abstract_charts import TestReportCharts
from pyecharts.globals import ThemeType
from pyecharts import options as opts


class BugCountByDayCharts(TestReportCharts):
    def __init__(self):
        self.create_date = []
        self.bug_counts_by_create_date = []
        self.workspace_id = TEST_REPORT.get("workspace_id")

    def _get_data(self, project_id):
        query_sql = '''
                    SELECT COUNT(id) AS COUNT, DATE_FORMAT(t.created, '%Y-%m-%d') AS created_date 
                    FROM dev_effic_test_bug t
                    WHERE t.project_id = {}
                    GROUP BY created_date;
                '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        created_date_list = []
        bug_counts_by_created_date = []
        for obj in obj_list:
            created_date_list.append(obj.created_date)
            bug_counts_by_created_date.append(obj.COUNT)
        if not created_date_list:
            created_date_list = ['None']
        if not bug_counts_by_created_date:
            bug_counts_by_created_date = [0]
        return created_date_list, bug_counts_by_created_date

    def _generate_data(self, project_id):
        self.create_date, self.bug_counts_by_create_date = self._get_data(project_id)

    def _set_style(self) -> Line:
        c = (
            Line({"theme": ThemeType.MACARONS})
            .add_xaxis(self.create_date)
            .add_yaxis(
                "按创建日期统计缺陷",
                self.bug_counts_by_create_date,
                markpoint_opts=opts.MarkPointOpts(data=[opts.MarkPointItem(type_="min")]),
            )
            .set_global_opts(title_opts=opts.TitleOpts(title="缺陷趋势图"))
        )
        return c