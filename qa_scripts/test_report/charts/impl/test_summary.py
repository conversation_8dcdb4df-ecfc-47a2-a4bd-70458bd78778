from mantis.pool import instance_db_session, InstanceDbSession
from qa_scripts.test_report.charts.abstract_charts import TestReportCharts
from pyecharts.components import Table
from pyecharts.globals import ThemeType
from pyecharts import options as opts


class TestSummaryCharts(TestReportCharts):
    def __init__(self):
        self.test_summary = []

    def _get_data(self, project_id):
        query_sql = '''
                        SELECT t.test_summary
                        FROM dev_effic_test_project t
                        WHERE t.id = {};
                    '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        return obj_list

    def _generate_data(self, project_id):
        test_summary_list = self._get_data(project_id)
        for obj in test_summary_list:
            self.test_summary.append(obj.test_summary)

    def _set_style(self) -> Table:
        table = Table({"theme": ThemeType.MACARONS})

        headers = ["测试总结"]
        rows = [
            self.test_summary
        ]
        table.add(headers, rows, attributes={
            "border": "false",  # 修复布尔值导致的AttributeError
            "padding": "10px",
            "style": "width:1000px; font-size:16px; color:darkmagenta; font-family: cursive;"
        }).set_global_opts(
            title_opts=opts.ComponentTitleOpts(title="测试总结详情")
        )
        return table