import datetime
from mantis.settings import logger as log

from pyecharts import options as opts
from pyecharts.components import Table
from pyecharts.globals import ThemeType

from mantis.pool import InstanceDbSession
from qa_scripts.test_report.charts.abstract_charts import TestReportCharts


class TestReportDetailCharts(TestReportCharts):
    def __init__(self):
        self.iteration_summary = []
        self.test_report_name = ""

    def _get_data(self, project_id):
        query_sql = '''
                    SELECT 
                        t.name, 
                        t.owner, 
                        CONCAT(t.`expected_start`, '~', t.`expected_end`) AS plan_time,
                        CONCAT(t.`actual_start`, '~', t.`actual_end`) AS real_time
                    FROM dev_effic_test_project t
                    WHERE t.id = {};
                  '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        return obj_list

    def _get_work_hours(self, project_id):
        query_sql = '''
                    SELECT
                        IFNULL(SUM(CAST(m.effort_completed AS DECIMAL(10,2))), 0) AS work_hours
                    FROM dev_effic_test_task m
                    WHERE m.project_id = {}
                    AND m.status = 'done'
                    AND m.task_type IN ('测试设计', '测试数据开发', '手工执行', '自动化案例开发', '自动化执行', '自动化测试设计', '灰度环境测试');
                  '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        return obj_list

    def _set_project_work_hours(self, project_id, work_hours):
        if work_hours and work_hours > 0:
            query_sql = '''
                        UPDATE dev_effic_test_project t
                        SET t.work_hours = {work_hours}
                        WHERE t.id = {project_id};
                      '''.format(work_hours=work_hours, project_id=project_id)
            with InstanceDbSession() as db:
                db.execute(query_sql)
                db.commit()

    def _generate_data(self, project_id):
        schedule_list = self._get_data(project_id)
        work_hours = self._get_work_hours(project_id)
        work_hours = work_hours[0][0]
        log.info('project_id--------->:{},work_hours--------->:{}'.format(project_id, work_hours))
        self._set_project_work_hours(project_id, work_hours)
        for obj in schedule_list:
            self.test_report_name = obj.name
            self.iteration_summary.append(obj.owner)
            cur_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self.iteration_summary.append(cur_time)
            self.iteration_summary.append(obj.plan_time)
            self.iteration_summary.append(obj.real_time)
            self.iteration_summary.append(work_hours)
            self.iteration_summary.append("查看报告评审状态")

    def _set_style(self) -> Table:
        try:
            table = Table({"theme": ThemeType.MACARONS})

            headers = ["测试负责人", "报告日期", "计划测试时间", "实际测试时间", "测试总工时（H）", "操作"]
            rows = [
                self.iteration_summary
            ]
            table.add(headers, rows, attributes={
                "border": "false",  # 修复布尔值导致的AttributeError
                "padding": "10px",
                "style": "width:1000px; font-size:16px; color:darkmagenta; font-family: cursive;"
            }).set_global_opts(
                title_opts=opts.ComponentTitleOpts(title="{}测试报告详情".format(self.test_report_name))
            )
            return table
        except Exception as e:
            log.error("生成测试报告详情表格时发生错误: {}".format(str(e)))
            # 返回一个简单的空表格作为备选方案
            fallback_table = Table({"theme": ThemeType.MACARONS})
            fallback_table.add(["错误"], [["生成报告详情时发生错误"]], attributes={
                "style": "color:red;"
            })
            return fallback_table

#
# if __name__ == '__main__':
#     project_id = 784
#     work_hours = 320
#     detail = TestReportDetailCharts()
#     detail._set_project_work_hours(project_id, work_hours)
