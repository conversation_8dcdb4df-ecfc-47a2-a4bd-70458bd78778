
from pyecharts.charts import Bar

from mantis.pool import InstanceDbSession
from qa_scripts.test_report.charts.abstract_charts import TestReportCharts
from pyecharts.globals import ThemeType
from pyecharts import options as opts


class BugCountByFixerCharts(TestReportCharts):
    def __init__(self):
        self.fixer_list = []
        self.fatal_count = []
        self.serious_count = []
        self.normal_count = []
        self.prompt_count = []
        self.advice_count = []

    def _get_data(self, project_id):
        query_sql = '''
                SELECT COUNT(1) AS COUNT, t.fixer, t.severity
                FROM dev_effic_test_bug t
                WHERE t.project_id = '{}' AND t.status != 'postponed' AND t.status != 'rejected'
                GROUP BY t.fixer, t.severity;
            '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        fixer_list = []
        fatal_count = []
        serious_count = []
        normal_count = []
        prompt_count = []
        advice_count = []
        if obj_list:
            for obj in obj_list:
                if obj.fixer not in fixer_list:
                    fixer_list.append(obj.fixer)
            for fixer in fixer_list:
                idx = fixer_list.index(fixer)
                fatal_count.append('')
                serious_count.append('')
                normal_count.append('')
                prompt_count.append('')
                advice_count.append('')
                for obj in obj_list:
                    if obj.fixer == fixer:
                        if obj.severity == 'fatal':
                            fatal_count[idx] = obj.COUNT
                        elif obj.severity == 'serious':
                            serious_count[idx] = obj.COUNT
                        elif obj.severity == 'normal':
                            normal_count[idx] = obj.COUNT
                        elif obj.severity == 'prompt':
                            prompt_count[idx] = obj.COUNT
                        elif obj.severity == 'advice':
                            advice_count[idx] = obj.COUNT
        else:
            fixer_list = ['None']
            fatal_count = [0]
            serious_count = [0]
            normal_count = [0]
            prompt_count = [0]
            advice_count = [0]

        return fixer_list, fatal_count, serious_count, normal_count, prompt_count, advice_count

    def _generate_data(self, project_id):
        self.fixer_list, self.fatal_count, self.serious_count, self.normal_count, self.prompt_count, self.advice_count = self._get_data(project_id)

    def _set_style(self) -> Bar:
        bzd_obj = opts.DataZoomOpts()
        # bzd_obj.update(is_disabled=True)
        c = (
            Bar({"theme": ThemeType.LIGHT})
            .add_xaxis(self.fixer_list)
            .add_yaxis("致命", self.fatal_count, stack="stack1", itemstyle_opts=opts.ItemStyleOpts(color="purple"))
            .add_yaxis("严重", self.serious_count, stack="stack1", itemstyle_opts=opts.ItemStyleOpts(color="red"))
            .add_yaxis("一般", self.normal_count, stack="stack1", itemstyle_opts=opts.ItemStyleOpts(color="orange"))
            .add_yaxis("提示", self.prompt_count, stack="stack1", itemstyle_opts=opts.ItemStyleOpts(color="yellow"))
            .add_yaxis("建议", self.advice_count, stack="stack1", itemstyle_opts=opts.ItemStyleOpts(color="blue"))
            .set_global_opts(
                title_opts=opts.TitleOpts(title="缺陷按修复人、级别统计"),
                datazoom_opts=[bzd_obj],
            )
        )
        return c


if __name__ == "__main__":
    obj = BugCountByFixerCharts()
    obj._get_data(8)