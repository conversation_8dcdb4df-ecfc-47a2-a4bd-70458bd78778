from pyecharts.charts import Pie
from mantis.pool import  InstanceDbSession
from qa_scripts.test_report.charts.abstract_charts import TestReportCharts
from pyecharts.globals import ThemeType
from pyecharts import options as opts


class BugAttributionAnalysisCharts(TestReportCharts):
    def __init__(self):
        self.bug_attribution_analysis = []
        self.bug_attribution_analysis_count = []

    def _get_data(self, project_id):
        query_sql = '''
                SELECT COUNT(1) AS COUNT, t.attribution_analysis as attribution
                FROM dev_effic_test_bug t
                WHERE t.project_id = {}
                GROUP BY t.attribution_analysis;
            '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        attribution_list = []
        bug_counts_by_attribution_analysis = []
        for obj in obj_list:
            attribution_list.append(obj.attribution)
            bug_counts_by_attribution_analysis.append(obj.COUNT)
        if not attribution_list:
            attribution_list = ['无']
        if not bug_counts_by_attribution_analysis:
            bug_counts_by_attribution_analysis = [0]
        return attribution_list, bug_counts_by_attribution_analysis

    def _generate_data(self, project_id):
        self.bug_attribution_analysis, self.bug_attribution_analysis_count = self._get_data(project_id)

    def _set_style(self) -> Pie:
        v = self.bug_attribution_analysis
        c = (
            Pie({"theme": ThemeType.WONDERLAND})
            .add(
                "",
                [list(z) for z in zip(v, self.bug_attribution_analysis_count)],
                radius=["25%", "75%"],
                center=["50%", "50%"],
                rosetype="area",
                )
            .set_global_opts(title_opts=opts.TitleOpts(title="缺陷归因分析"))
        )
        return c


if __name__ == "__main__":
    # obj = BugAttributionAnalysisCharts()
    # obj._get_data(2)
    dict1 = {'a': 1, 'b': 2}
    dict2 = {'b': 3, 'c': 4}

    # 使用字典解包合并
    # merged_dict = {**dict1, **dict2}
    # print(merged_dict)
    # max_value = max(max([[1, 2, 3], [5, 1], [4]], key=lambda v: max(v)))
    # print(max_value)
    # order_value = list(range(10, -1, -1))
    # result = abs(-6)
    # result = all([1, 0, 3, 6])
    # result = any([0, 0, 1])
    # 复数
    # result = complex(1, 2)
    # 分别取商和余数
    # result, a = divmod(10, 3)
    # print(result)
    # print(a)
    # 计算表达式
    # s = "1 + 3 +5"
    # result = eval(s)
    # print(result)
    import sys
    # a = {'a': 1, 'b': 2.0}
    # result = sys.getsizeof(a)
    # print(result)
    # global 声明全局变量
    # i = 0
    # def g():
    #     global i
    #     i += 1
    # def h():
    #     global i
    #     i += 1
    # g()
    # h()
    # print(i)

    def score_mean(lst):
        lst.sort()
        # 去掉首尾两个数
        lst2 = lst[1:(len(lst) - 1)]
        return round((sum(lst2) / len(lst2)), 1)
    lst = [9.1, 9.0, 8.1, 9.7, 19, 8.2, 8.6, 9.8]
    result = score_mean(lst)
    print(result)
    # [10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0]
