import datetime
import chinese_calendar
import numpy as np

from pyecharts.charts import Bar
from mantis.pool import InstanceDbSession
from qa_scripts.test_report.charts.abstract_charts import TestReportCharts
from pyecharts.globals import ThemeType
from pyecharts import options as opts


class BugFixTimeByFixerCharts(TestReportCharts):
    def __init__(self):
        self.fixer_list = []
        self.fix_avg_time = []
        self.fix_mid_time = []
        self.fix_eighty_percent_time = []

    def _get_data(self, project_id):
        bug_time_info = self.__get_bug_time_info(project_id)

        fix_dict = self.__get_fix_time_dict_by_fixer(bug_time_info)

        fixer_list = []
        fix_avg_list = []
        fix_mid_list = []
        fix_eighty_percent_list = []
        for k, v in fix_dict.items():
            fix_time_list_float = v
            fixer_list.append(k)
            fix_avg_list.append(round(np.average(fix_time_list_float), 1))
            fix_mid_list.append(round(np.median(fix_time_list_float), 1))
            fix_eighty_percent_list.append(round(np.percentile(fix_time_list_float, 80), 1))
        if not fixer_list:
            fixer_list = ['None']
        if not fix_avg_list:
            fix_avg_list = [0]
        if not fix_mid_list:
            fix_mid_list = [0]
        if not fix_eighty_percent_list:
            fix_eighty_percent_list = [0]
        return fixer_list, fix_avg_list, fix_mid_list, fix_eighty_percent_list

    def _generate_data(self, project_id):
        self.fixer_list, self.fix_avg_time, self.fix_mid_time, self.fix_eighty_percent_time = self._get_data(project_id)

    def _set_style(self) -> Bar:
        c = (
            Bar({"theme": ThemeType.VINTAGE})
            .add_xaxis(self.fixer_list)
            .add_yaxis("修复用时均值", self.fix_avg_time, stack="stack1", itemstyle_opts=opts.ItemStyleOpts(color="blue"))
            .add_yaxis("修复用时中位线", self.fix_mid_time, stack="stack1", itemstyle_opts=opts.ItemStyleOpts(color="yellow"))
            .add_yaxis("修复用时80分位线", self.fix_eighty_percent_time, stack="stack1", itemstyle_opts=opts.ItemStyleOpts(color="orange"))
            .set_global_opts(
                title_opts=opts.TitleOpts(title="缺陷修复用时（H）"),
                datazoom_opts=[opts.DataZoomOpts()],
                )
        )
        return c

    def __get_fix_time_dict_by_fixer(self, bug_time_info):
        fix_time_dict = []
        for bug_time in bug_time_info:
            fix_time_dict.append(self.__get_bug_fix_time(bug_time.create_time, bug_time.resolve_time, bug_time.fixer))

        fix_time_dict_by_fixer = {}
        for row in fix_time_dict:
            if list(row.keys())[0] in fix_time_dict_by_fixer:
                fix_time_dict_by_fixer[list(row.keys())[0]].append(list(row.values())[0])
            else:
                fix_time_dict_by_fixer[list(row.keys())[0]] = [list(row.values())[0]]
        return fix_time_dict_by_fixer

    def __get_bug_time_info(self, project_id):
        query_sql = '''
                SELECT t.created as create_time, t.resolved as resolve_time, t.fixer
                FROM dev_effic_test_bug t 
                WHERE t.project_id = {} AND t.fixer IS NOT NULL 
                AND t.resolved IS NOT NULL;
            '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        return obj_list

    def __get_bug_fix_time(self, create_time, resolved_time, fixer):
        date_workday = chinese_calendar.get_workdays(create_time, resolved_time)
        fix_time = 0
        if len(date_workday) == 0:
            pass
        elif len(date_workday) == 1:
            fix_time = round((resolved_time - create_time).total_seconds()/3600, 1)
        elif len(date_workday) > 1:
            # 设置一个创建日当天的 18:00:00 的时间
            first_day_end_time = datetime.datetime.strptime(create_time.strftime("%Y-%m-%d") + " 18:00:00", '%Y-%m-%d %H:%M:%S')
            first_day_time_diff = (first_day_end_time - create_time).total_seconds()
            fix_time = round(first_day_time_diff/3600, 1) if first_day_time_diff > 0 else 0

            # 设置一个创建日当天的 08:45:00 的时间
            last_day_start_time = datetime.datetime.strptime(resolved_time.strftime("%Y-%m-%d") + " 08:45:00", '%Y-%m-%d %H:%M:%S')
            last_day_time_diff = (resolved_time - last_day_start_time).total_seconds()
            if last_day_time_diff > 0:
                fix_time += round(last_day_time_diff/3600, 1) + 8 * (len(date_workday) - 2)
            else:
                fix_time += 8 * (len(date_workday) - 2)
        return {fixer: fix_time}


if __name__ == "__main__":
    # start_time = datetime.datetime.strptime(datetime.datetime.now().strftime("%Y-%m-%d") + " 18:00:00", '%Y-%m-%d %H:%M:%S')
    # date = '2022-12-01 19:00:00'
    # end_time = datetime.datetime.strptime(date, '%Y-%m-%d %H:%M:%S')
    #
    # #date_workday = chinese_calendar.get_workdays(start_time, end_time)
    # print(type(start_time))
    # print(type(end_time))
    # print(start_time, end_time)
    # print(end_time - start_time)
    # print((end_time - start_time).seconds)
    # print((end_time - start_time).total_seconds())
    # print(type(start_time - end_time))
    # print((start_time - end_time).total_seconds())
    #
    # #print(date_workday)
    obj = BugFixTimeByFixerCharts()
    obj._get_data(8)