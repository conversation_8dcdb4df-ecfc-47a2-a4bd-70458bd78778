from mantis.pool import InstanceDbSession
from qa_scripts.test_report.charts.abstract_charts import TestReportCharts
from pyecharts.components import Table
from pyecharts.globals import ThemeType
from pyecharts import options as opts


class CompatibilityTestingDescriptionCharts(TestReportCharts):
    def __init__(self):
        self.compatibility = []

    def _get_data(self, project_id):
        query_sql = '''
                        SELECT t.compatibility_description
                        FROM dev_effic_test_project t
                        WHERE t.id = {};
                    '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        return obj_list

    def _generate_data(self, project_id):
        compatibility_list = self._get_data(project_id)
        for obj in compatibility_list:
            self.compatibility.append(obj.compatibility_description)

    def _set_style(self) -> Table:
        table = Table({"theme": ThemeType.MACARONS})

        headers = ["兼容性测试/升级测试"]
        rows = [
            self.compatibility
        ]
        table.add(headers, rows, attributes={
            "border": "false",  # 修复布尔值导致的AttributeError
            "padding": "10px",
            "style": "width:1000px; font-size:16px; color:darkmagenta; font-family: cursive;"
        }).set_global_opts(
            title_opts=opts.ComponentTitleOpts(title="兼容性测试详情")
        )
        return table