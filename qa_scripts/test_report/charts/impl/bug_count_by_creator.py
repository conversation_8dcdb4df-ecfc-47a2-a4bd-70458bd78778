from pyecharts.charts import Bar
from mantis.pool import InstanceDbSession
from qa_scripts.test_report.charts.abstract_charts import TestReportCharts
from pyecharts.globals import ThemeType
from pyecharts import options as opts


class BugCountByCreatorCharts(TestReportCharts):
    def __init__(self):
        self.creator_attrs = []
        self.bug_counts = []

    def _get_data(self, project_id):
        query_sql = '''
                SELECT COUNT(1) AS COUNT, t.reporter
                FROM dev_effic_test_bug t
                WHERE t.project_id = '{}'
                GROUP BY t.reporter;
            '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        creator_list = []
        bug_counts = []

        for obj in obj_list:
            creator_list.append(obj.reporter)
            bug_counts.append(obj.COUNT)
        if not creator_list:
            creator_list = ['None']
        if not bug_counts:
            bug_counts = [0]
        return creator_list, bug_counts

    def _generate_data(self, project_id):
        self.creator_attrs, self.bug_counts = self._get_data(project_id)

    def _set_style(self) -> Bar:
        c = (
            Bar({"theme": ThemeType.VINTAGE})
            .add_xaxis(self.creator_attrs)
            .add_yaxis("缺陷数", self.bug_counts)
            .set_global_opts(
                title_opts=opts.TitleOpts(title="缺陷按创建人统计"),
                datazoom_opts=[opts.DataZoomOpts()],
            )
        )
        return c