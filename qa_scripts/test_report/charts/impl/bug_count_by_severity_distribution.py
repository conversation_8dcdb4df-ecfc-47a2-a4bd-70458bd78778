from pyecharts.charts import Pie
from mantis.pool import InstanceDbSession
from mantis.settings import TEST_REPORT
from qa_scripts.test_report.charts.abstract_charts import TestReportCharts
from pyecharts.globals import ThemeType
from pyecharts import options as opts


class BugCountBySeverityDistributionCharts(TestReportCharts):
    def __init__(self):
        self.bug_severity = []
        self.bug_severity_count = []
        self.workspace_id = TEST_REPORT.get("workspace_id")
        self.severity_map = {'serious': '严重', 'fatal': '致命', 'normal': '一般', 'prompt': '提示', 'advice': '建议'}

    def _get_data(self, project_id):
        query_sql = '''
                        SELECT COUNT(id) AS COUNT, t.severity
                        FROM dev_effic_test_bug t
                        WHERE t.project_id = {}
                        GROUP BY t.severity;
                    '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        severity_list = []
        bug_counts_by_severity = []

        for obj in obj_list:
            severity_list.append(self.severity_map.get(obj.severity))
            bug_counts_by_severity.append(obj.COUNT)
        if not severity_list:
            severity_list = ['None']
        if not bug_counts_by_severity:
            bug_counts_by_severity = [0]
        return severity_list, bug_counts_by_severity

    def _generate_data(self, project_id):
        self.bug_severity, self.bug_severity_count = self._get_data(project_id)

    def _set_style(self) -> Pie:
        v = self.bug_severity
        c = (
            Pie({"theme": ThemeType.INFOGRAPHIC})
            .add(
                "",
                [list(z) for z in zip(v, self.bug_severity_count)],
                radius=["25%", "75%"],
                center=["50%", "50%"],
                rosetype="area",
            )
            .set_global_opts(title_opts=opts.TitleOpts(title="缺陷级别分布"))
        )
        return c