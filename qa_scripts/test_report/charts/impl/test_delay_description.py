import datetime

from mantis.pool import instance_db_session, InstanceDbSession
from mantis.settings import logger, TEST_REPORT
from qa_scripts.dao.get.mysql.mysql import DBConnectionManager
from qa_scripts.test_report.charts.abstract_charts import TestReportCharts
from pyecharts.components import Table
from pyecharts.globals import ThemeType
from pyecharts import options as opts


class TestDelayDescriptionCharts(TestReportCharts):
    def __init__(self):
        self.delay_info = []

    def _get_data(self, project_id):
        query_sql = '''
                        SELECT t.delay_days, t.delay_description
                        FROM dev_effic_test_project t
                        WHERE t.id = {};
                    '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        return obj_list

    def _generate_data(self, project_id):
        delay_info_list = self._get_data(project_id)
        for obj in delay_info_list:
            self.delay_info.append(obj.delay_days)
            self.delay_info.append(obj.delay_description)

    def _set_style(self) -> Table:
        table = Table({"theme": ThemeType.MACARONS})

        headers = ["延期天数", "延期描述"]
        rows = [
            self.delay_info
        ]
        table.add(headers, rows, attributes={
            "border": "false",  # 修复布尔值导致的AttributeError
            "padding": "10px",
            "style": "width:1000px; font-size:16px; color:darkmagenta; font-family: cursive;"
        }).set_global_opts(
            title_opts=opts.ComponentTitleOpts(title="测试延期详情")
        )
        return table