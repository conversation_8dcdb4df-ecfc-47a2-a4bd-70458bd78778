import datetime

import chinese_calendar
import numpy as np

from mantis.pool import InstanceDbSession
from qa_scripts.test_report.charts.abstract_charts import TestReportCharts
from pyecharts.components import Table
from pyecharts.globals import ThemeType
from pyecharts import options as opts


class BugSummaryCharts(TestReportCharts):
    def __init__(self):
        self.bug_summary = []

    def _get_data(self, project_id):
        pass

    def _generate_data(self, project_id):
        bug_count = self._get_bug_count_total(project_id)
        if bug_count:
            self.bug_summary = [bug_count, self._get_bug_count_closed(project_id),
                                self._get_bug_count_rejected(project_id), self._get_bug_count_new(project_id),
                                self._get_bug_count_postponed(project_id), self._get_bug_count_reopend(project_id),
                                self._get_bug_middle_value_fix_time(project_id),
                                self._get_bug_eighty_percent_value_fix_time(project_id),
                                self._get_bug_sum_fix_time(project_id)]
        else:
            self.bug_summary = [0, 0, 0, 0, 0, 0, 0, 0, 0]

    def _set_style(self) -> Table:
        table = Table({"theme": ThemeType.MACARONS})

        headers = ["总缺陷数", "已关闭缺陷数", "无效缺陷数", "未解决缺陷数", "延期解决缺陷数", "Reopen缺陷数", "修复用时中位数（H）", "修复用时80分位数（H）", "总修复用时"]
        rows = [
            self.bug_summary
        ]
        table.add(headers, rows, attributes={
            "border": "false",
            "padding": "10px",
            "style": "width:1000px; font-size:16px; color:darkmagenta; font-family: cursive;"
        }).set_global_opts(
            title_opts=opts.ComponentTitleOpts(title="缺陷概要")
        )
        return table

    def _get_bug_count_total(self, project_id):
        query_sql = '''
                        SELECT COUNT(id) AS COUNT
                        FROM dev_effic_test_bug t 
                        WHERE t.project_id = {};
                    '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        return obj_list[0].COUNT

    def _get_bug_count_closed(self, project_id):
        query_sql = '''
                        SELECT COUNT(id) AS COUNT
                        FROM dev_effic_test_bug t 
                        WHERE t.project_id = {} AND t.status = 'closed';
                    '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        return obj_list[0].COUNT

    def _get_bug_count_rejected(self, project_id):
        query_sql = '''
                        SELECT COUNT(id) AS COUNT
                        FROM dev_effic_test_bug t 
                        WHERE t.project_id = {} AND t.status = 'rejected';
                    '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        return obj_list[0].COUNT

    def _get_bug_count_new(self, project_id):
        query_sql = '''
                        SELECT COUNT(id) AS COUNT
                        FROM dev_effic_test_bug t 
                        WHERE t.project_id = {} AND t.status = 'new';
                    '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        return obj_list[0].COUNT

    def _get_bug_count_postponed(self, project_id):
        query_sql = '''
                        SELECT COUNT(id) AS COUNT
                        FROM dev_effic_test_bug t 
                        WHERE t.project_id = {} AND t.status = 'postponed';
                    '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        return obj_list[0].COUNT

    def _get_bug_count_reopend(self, project_id):
        query_sql = '''
                        SELECT COUNT(id) AS COUNT
                        FROM dev_effic_test_bug t 
                        WHERE t.project_id = {} AND t.flows LIKE '%reopened%';
                    '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        return obj_list[0].COUNT

    def _get_bug_middle_value_fix_time(self, project_id):
        fix_time_list = self._get_fix_time_all(project_id)
        if fix_time_list:
            return round(np.median(fix_time_list), 1)
        else:
            return 0

    def _get_bug_sum_fix_time(self, project_id):
        fix_time_list = self._get_fix_time_all(project_id)
        if fix_time_list:
            return round(np.sum(fix_time_list), 1)
        else:
            return 0

    def _get_bug_eighty_percent_value_fix_time(self, project_id):
        fix_time_list = self._get_fix_time_all(project_id)
        if fix_time_list:
            return round(np.percentile(fix_time_list, 80), 1)
        else:
            return 0

    def _get_fix_time_all(self, project_id):
        bug_time_info = self.__get_bug_time_info(project_id)
        fix_time_list = []
        for bug_time in bug_time_info:
            fix_time_list.append(self.__get_bug_fix_time(bug_time.create_time, bug_time.resolve_time))
        return fix_time_list

    def __get_bug_time_info(self, project_id):
        query_sql = '''
                        SELECT t.created as create_time, t.resolved as resolve_time, t.fixer
                        FROM dev_effic_test_bug t 
                        WHERE t.project_id = {} AND t.fixer IS NOT NULL 
                        AND t.resolved IS NOT NULL AND t.created IS NOT NULL;
                    '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        return obj_list

    def __get_bug_fix_time(self, create_time, resolved_time):
        date_workday = chinese_calendar.get_workdays(create_time, resolved_time)
        fix_time = 0
        if len(date_workday) == 0:
            pass
        elif len(date_workday) == 1:
            fix_time = round((resolved_time - create_time).total_seconds()/3600, 1)
        elif len(date_workday) > 1:
            # 设置一个创建日当天的 18:00:00 的时间
            first_day_end_time = datetime.datetime.strptime(create_time.strftime("%Y-%m-%d") + " 18:00:00", '%Y-%m-%d %H:%M:%S')
            first_day_time_diff = (first_day_end_time - create_time).total_seconds()
            fix_time = round(first_day_time_diff/3600, 1) if first_day_time_diff > 0 else 0

            # 设置一个创建日当天的 08:45:00 的时间
            last_day_start_time = datetime.datetime.strptime(resolved_time.strftime("%Y-%m-%d") + " 08:45:00", '%Y-%m-%d %H:%M:%S')
            last_day_time_diff = (resolved_time - last_day_start_time).total_seconds()
            if last_day_time_diff > 0:
                fix_time += round(last_day_time_diff/3600, 1) + 8 * (len(date_workday) - 2)
            else:
                fix_time += 8 * (len(date_workday) - 2)
        return fix_time


if __name__ == "__main__":
    obj = BugSummaryCharts()
    obj._generate_data(970)