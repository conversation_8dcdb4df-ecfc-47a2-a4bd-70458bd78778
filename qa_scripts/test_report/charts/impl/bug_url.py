from mantis.pool import InstanceDbSession
from mantis.settings import TEST_REPORT, TAPD
from qa_scripts.test_report.charts.abstract_charts import TestReportCharts
from pyecharts.components import Table
from pyecharts.globals import ThemeType
from pyecharts import options as opts


class BugUrlCharts(TestReportCharts):
    def __init__(self):
        self.bug_url = []
        self.workspace_id = TEST_REPORT.get("workspace_id")

    def _get_data(self, project_id):
        query_sql = '''
                        select t.tapd_iteration_id as extend_id from dev_effic_test_project t
                        where t.id = {};
                    '''.format(project_id)

        with InstanceDbSession() as db:
            obj_list = db.execute(query_sql).fetchall()

        return obj_list[0].extend_id

    def _generate_data(self, project_id):
        iteration_id = self._get_data(project_id)
        tapd_url = 'https://www.tapd.cn/{workspace_id}/prong/iterations/view/{iteration_id}#tab=Bugs'.format(
            workspace_id=self.workspace_id, iteration_id=iteration_id)
        self.bug_url.append(tapd_url)

    def _set_style(self) -> Table:
        table = Table({"theme": ThemeType.MACARONS})

        headers = ["缺陷详情链接"]
        rows = [
            self.bug_url
        ]
        table.add(headers, rows, attributes={
            "border": "false",  # 修复布尔值导致的AttributeError
            "padding": "10px",
            "style": "width:1000px; font-size:16px; color:darkmagenta; font-family: cursive;"
        }).set_global_opts(
            title_opts=opts.ComponentTitleOpts(title="缺陷详情")
        )
        # table.add_js_funcs('''
        #
        #                        function objclick(event) {{
        #                                   if(event && event.target && ["{url}"].indexOf(event.target.innerText) >= 0)
        #                                         {{ window.open(event.target.innerText)
        #                                        }}
        #
        #
        #                             }};
        #                        document.evaluate("/html/body/div[1]/div[12]/table/tbody/tr/td", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.addEventListener('click',objclick,true);
        #                         '''.format(url=self.bug_url[0]))
        return table
