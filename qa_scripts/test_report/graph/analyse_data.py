from pyecharts.charts import Bar, Bar3D
from pyecharts import options as opts

# V1 版本开始支持链式调用
bar = (
    Bar()
    .add_xaxis(["衬衫", "毛衣", "领带", "裤子", "风衣", "高跟鞋", "袜子"])
    .add_yaxis("商家A", [114, 55, 27, 101, 125, 27, 105])
    .add_yaxis("商家B", [57, 134, 137, 129, 145, 60, 49])
    .set_global_opts(title_opts=opts.TitleOpts(title="某商场销售情况"))
)
bar.render("./demo1.html")

# 不习惯链式调用的开发者依旧可以单独调用方法
bar = Bar()
bar.add_xaxis(["衬衫", "毛衣", "领带", "裤子", "风衣", "高跟鞋", "袜子"])
bar.add_yaxis("商家A", [114, 55, 27, 101, 125, 27, 105])
bar.add_yaxis("商家B", [57, 134, 137, 129, 145, 60, 49])
bar.set_global_opts(title_opts=opts.TitleOpts(title="某商场销售情况"))
bar.render("./demo2.html")

import random
hours = ["12a","1a","2a","3a","4a","5a","6a","7a","8a","9a","10a","11a","12p","1p","2p","3p","4p","5p","6p","7p","8p","9p","10p","11p",]
days = ["Saturday", "Friday", "Thursday", "Wednesday", "Tuesday", "Monday", "Sunday"]

data = [(i, j, random.randint(0, 12)) for i in range(6) for j in range(24)]
data = [[d[1], d[0], d[2]] for d in data]


c=(
    Bar3D(init_opts=opts.InitOpts(width="900px", height="600px"))
    .add(
        series_name="",
        data=data,
        xaxis3d_opts=opts.Axis3DOpts(type_="category", data=hours),
        yaxis3d_opts=opts.Axis3DOpts(type_="category", data=days),
        zaxis3d_opts=opts.Axis3DOpts(type_="value"),
    )
    .set_global_opts(
        title_opts=opts.TitleOpts("标准3D柱状图"),
        visualmap_opts=opts.VisualMapOpts(
            max_=20,
            range_color=[
                "#313695",
                "#4575b4",
                "#74add1",
                "#abd9e9",
                "#e0f3f8",
                "#ffffbf",
                "#fee090",
                "#fdae61",
                "#f46d43",
                "#d73027",
                "#a50026",
            ],
        )
    )
     .render("标准3D柱状图.html")
)
#c.render_notebook()

from pyecharts import options as opts

# from pyecharts.charts import Bar, Page
#
from pyecharts.globals import ThemeType
# def bar_base_dict_config() -> Bar:
#     c = (
#         Bar({"theme": ThemeType.MACARONS})
#         .add_xaxis(["吃喝", "杂物", "交通", "书费", "游玩", "花呗", "网购"])
#         .add_yaxis("旁友A", [600, 20,30, 50, 100, 125, 200])
#         .add_yaxis("旁友B", [650,25,25, 80, 150, 145, 100])
#         .set_global_opts(
#             title_opts={"text": "同学们的生活开支", "subtext": "通过 dict 进行配置"}
#         )
#     )
#     return c
# bar_base_dict_config().render()