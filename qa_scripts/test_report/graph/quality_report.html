<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/echarts.min.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/themes/westeros.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/themes/essos.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/themes/macarons.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/themes/infographic.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/themes/wonderland.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/themes/vintage.js"></script>

    
</head>
<body>
    <style>.box { justify-content:center; display:flex; flex-wrap:wrap;  }; </style>
    <div class="box">
                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="7e04a269af7e4be19f7363093959d4b6" class="chart-container" style="">
            <p class="title" style="font-size: 18px; font-weight:bold;" > 测试报告详情</p>
            <p class="subtitle" style="font-size: 12px;" > </p>
            <table align="left" border="False" padding="10px" style="width:1000px; font-size:16px; color:darkmagenta; font-family: cursive;">
    <thead>
        <tr>
            <th>迭代名称</th>
            <th>测试负责人</th>
            <th>报告日期</th>
            <th>测试开始时间</th>
            <th>测试结束时间</th>
            <th>报告总结描述</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>冯伟敏测试迭代</td>
            <td>冯伟敏</td>
            <td>2022-09-05 13:19:50</td>
            <td>2022-09-06</td>
            <td>2022-09-09</td>
            <td>测试延期1天，测试结论为：通过</td>
        </tr>
    </tbody>
</table>
        </div>

<br/>                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="c70bf26685624eb78a5d0fe25d523809" class="chart-container" style="">
            <p class="title" style="font-size: 18px; font-weight:bold;" > 缺陷概要</p>
            <p class="subtitle" style="font-size: 12px;" > </p>
            <table align="left" border="False" padding="10px" style="width:1000px; font-size:16px; color:darkmagenta; font-family: cursive;">
    <thead>
        <tr>
            <th>总缺陷数</th>
            <th>已关闭缺陷数</th>
            <th>无效缺陷数(含重复缺陷、无法重现缺陷)</th>
            <th>未解决缺陷数</th>
            <th>Reopen缺陷数</th>
            <th>修复用时中位数</th>
            <th>修复用时80分位数</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>27</td>
            <td>6</td>
            <td>7</td>
            <td>5</td>
            <td>2</td>
            <td>102.3</td>
            <td>183.2</td>
        </tr>
    </tbody>
</table>
        </div>

<br/>                <div id="f784f91369e248fbb9569e7ca090fb22" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_f784f91369e248fbb9569e7ca090fb22 = echarts.init(
            document.getElementById('f784f91369e248fbb9569e7ca090fb22'), 'westeros', {renderer: 'canvas'});
        var option_f784f91369e248fbb9569e7ca090fb22 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "series": [
        {
            "type": "bar",
            "name": "\u6708\u5ea6\u53d1\u73b0\u7f3a\u9677\u6570",
            "legendHoverLink": true,
            "data": [
                1,
                26
            ],
            "showBackground": false,
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u6708\u5ea6\u53d1\u73b0\u7f3a\u9677\u6570"
            ],
            "selected": {
                "\u6708\u5ea6\u53d1\u73b0\u7f3a\u9677\u6570": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "2022-09",
                "2022-11"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "\u6708\u4efd\u7ef4\u5ea6\u5206\u6790",
            "padding": 5,
            "itemGap": 10
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_f784f91369e248fbb9569e7ca090fb22.setOption(option_f784f91369e248fbb9569e7ca090fb22);
    </script>
<br/>                <div id="804a95d180324644973e11bb8ed6ee6a" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_804a95d180324644973e11bb8ed6ee6a = echarts.init(
            document.getElementById('804a95d180324644973e11bb8ed6ee6a'), 'essos', {renderer: 'canvas'});
        var option_804a95d180324644973e11bb8ed6ee6a = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "series": [
        {
            "type": "bar",
            "name": "\u7f3a\u9677\u6570",
            "legendHoverLink": true,
            "data": [
                2,
                3,
                1,
                3,
                4,
                1,
                3,
                4,
                6
            ],
            "showBackground": false,
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u7f3a\u9677\u6570"
            ],
            "selected": {
                "\u7f3a\u9677\u6570": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u51af\u4f1f\u654f",
                "\u5218\u589e\u6c9b",
                "\u5218\u5f6c\u5f6c",
                "\u5218\u68a6\u4f1a",
                "\u738b\u6885",
                "\u848b\u8389\u742a",
                "\u8d75\u4e39\u4e1c",
                "\u9648\u5229\u78ca",
                "\u9ad8\u6653\u6770"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "\u7f3a\u9677\u6309\u521b\u5efa\u4eba\u7edf\u8ba1",
            "padding": 5,
            "itemGap": 10
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_804a95d180324644973e11bb8ed6ee6a.setOption(option_804a95d180324644973e11bb8ed6ee6a);
    </script>
<br/>                <div id="679d8b8016734af09f87653596f2398b" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_679d8b8016734af09f87653596f2398b = echarts.init(
            document.getElementById('679d8b8016734af09f87653596f2398b'), 'macarons', {renderer: 'canvas'});
        var option_679d8b8016734af09f87653596f2398b = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "series": [
        {
            "type": "line",
            "name": "\u6309\u5929\u7edf\u8ba1\u7f3a\u9677",
            "connectNulls": false,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "data": [
                [
                    "2022-09-06",
                    1
                ],
                [
                    "2022-11-07",
                    1
                ],
                [
                    "2022-11-08",
                    1
                ],
                [
                    "2022-11-09",
                    4
                ],
                [
                    "2022-11-10",
                    3
                ],
                [
                    "2022-11-11",
                    3
                ],
                [
                    "2022-11-14",
                    10
                ],
                [
                    "2022-11-15",
                    1
                ],
                [
                    "2022-11-16",
                    2
                ],
                [
                    "2022-11-18",
                    1
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "markPoint": {
                "label": {
                    "show": true,
                    "position": "inside",
                    "color": "#fff",
                    "margin": 8
                },
                "data": [
                    {
                        "type": "min"
                    }
                ]
            },
            "zlevel": 0,
            "z": 0
        }
    ],
    "legend": [
        {
            "data": [
                "\u6309\u5929\u7edf\u8ba1\u7f3a\u9677"
            ],
            "selected": {
                "\u6309\u5929\u7edf\u8ba1\u7f3a\u9677": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "2022-09-06",
                "2022-11-07",
                "2022-11-08",
                "2022-11-09",
                "2022-11-10",
                "2022-11-11",
                "2022-11-14",
                "2022-11-15",
                "2022-11-16",
                "2022-11-18"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "\u7f3a\u9677\u8d8b\u52bf\u56fe",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_679d8b8016734af09f87653596f2398b.setOption(option_679d8b8016734af09f87653596f2398b);
    </script>
<br/>                <div id="fed371372db64b4ab9e6ab0fed948110" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_fed371372db64b4ab9e6ab0fed948110 = echarts.init(
            document.getElementById('fed371372db64b4ab9e6ab0fed948110'), 'infographic', {renderer: 'canvas'});
        var option_fed371372db64b4ab9e6ab0fed948110 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "series": [
        {
            "type": "pie",
            "clockwise": true,
            "data": [
                {
                    "name": "\u5efa\u8bae",
                    "value": 4
                },
                {
                    "name": "\u81f4\u547d",
                    "value": 4
                },
                {
                    "name": "\u4e00\u822c",
                    "value": 5
                },
                {
                    "name": "\u63d0\u793a",
                    "value": 3
                },
                {
                    "name": "\u4e25\u91cd",
                    "value": 11
                }
            ],
            "radius": [
                "25%",
                "75%"
            ],
            "center": [
                "50%",
                "50%"
            ],
            "roseType": "area",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5efa\u8bae",
                "\u81f4\u547d",
                "\u4e00\u822c",
                "\u63d0\u793a",
                "\u4e25\u91cd"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "title": [
        {
            "text": "\u7f3a\u9677\u7ea7\u522b\u5206\u5e03",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_fed371372db64b4ab9e6ab0fed948110.setOption(option_fed371372db64b4ab9e6ab0fed948110);
    </script>
<br/>                <div id="c4b5b8059bc4413b904e3fefeb7b21ce" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_c4b5b8059bc4413b904e3fefeb7b21ce = echarts.init(
            document.getElementById('c4b5b8059bc4413b904e3fefeb7b21ce'), 'wonderland', {renderer: 'canvas'});
        var option_c4b5b8059bc4413b904e3fefeb7b21ce = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "series": [
        {
            "type": "pie",
            "clockwise": true,
            "data": [
                {
                    "value": 6
                },
                {
                    "name": "Android\u7f16\u7801\u56e0\u7d20",
                    "value": 4
                },
                {
                    "name": "IOS\u7f16\u7801\u56e0\u7d20",
                    "value": 2
                },
                {
                    "name": "\u6570\u636e\u95ee\u9898",
                    "value": 5
                },
                {
                    "name": "\u6784\u5efa\u4e0e\u90e8\u7f72",
                    "value": 4
                },
                {
                    "name": "\u7528\u6237\u4f53\u9a8c",
                    "value": 5
                },
                {
                    "name": "\u9700\u6c42\u56e0\u7d20",
                    "value": 1
                }
            ],
            "radius": [
                "25%",
                "75%"
            ],
            "center": [
                "50%",
                "50%"
            ],
            "roseType": "area",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                null,
                "Android\u7f16\u7801\u56e0\u7d20",
                "IOS\u7f16\u7801\u56e0\u7d20",
                "\u6570\u636e\u95ee\u9898",
                "\u6784\u5efa\u4e0e\u90e8\u7f72",
                "\u7528\u6237\u4f53\u9a8c",
                "\u9700\u6c42\u56e0\u7d20"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "title": [
        {
            "text": "\u7f3a\u9677\u5f52\u56e0\u5206\u6790",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_c4b5b8059bc4413b904e3fefeb7b21ce.setOption(option_c4b5b8059bc4413b904e3fefeb7b21ce);
    </script>
<br/>                <div id="53c0d2daa4544f66b7d34d46a54cacfe" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_53c0d2daa4544f66b7d34d46a54cacfe = echarts.init(
            document.getElementById('53c0d2daa4544f66b7d34d46a54cacfe'), 'vintage', {renderer: 'canvas'});
        var option_53c0d2daa4544f66b7d34d46a54cacfe = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "series": [
        {
            "type": "bar",
            "name": "\u4fee\u590d\u7528\u65f6\u5747\u503c",
            "legendHoverLink": true,
            "data": [
                101.3,
                165.4,
                123.6
            ],
            "showBackground": false,
            "stack": "stack1",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            },
            "itemStyle": {
                "color": "blue"
            }
        },
        {
            "type": "bar",
            "name": "\u4fee\u590d\u7528\u65f6\u4e2d\u4f4d\u7ebf",
            "legendHoverLink": true,
            "data": [
                101.3,
                102.4,
                174.3
            ],
            "showBackground": false,
            "stack": "stack1",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            },
            "itemStyle": {
                "color": "yellow"
            }
        },
        {
            "type": "bar",
            "name": "\u4fee\u590d\u7528\u65f680\u5206\u4f4d\u7ebf",
            "legendHoverLink": true,
            "data": [
                101.8,
                204.0,
                187.6
            ],
            "showBackground": false,
            "stack": "stack1",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            },
            "itemStyle": {
                "color": "orange"
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u4fee\u590d\u7528\u65f6\u5747\u503c",
                "\u4fee\u590d\u7528\u65f6\u4e2d\u4f4d\u7ebf",
                "\u4fee\u590d\u7528\u65f680\u5206\u4f4d\u7ebf"
            ],
            "selected": {
                "\u4fee\u590d\u7528\u65f6\u5747\u503c": true,
                "\u4fee\u590d\u7528\u65f6\u4e2d\u4f4d\u7ebf": true,
                "\u4fee\u590d\u7528\u65f680\u5206\u4f4d\u7ebf": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u51af\u4f1f\u654f",
                "\u5218\u5fae",
                "\u5b59\u53cc\u5e86"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "\u7f3a\u9677\u4fee\u590d\u7528\u65f6",
            "padding": 5,
            "itemGap": 10
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_53c0d2daa4544f66b7d34d46a54cacfe.setOption(option_53c0d2daa4544f66b7d34d46a54cacfe);
    </script>
<br/>    </div>
    <script>
    </script>
</body>
</html>
