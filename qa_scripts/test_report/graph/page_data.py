import datetime

from pyecharts import options as opts
from pyecharts.charts import Bar, Line, Page, Pie
from pyecharts.components import Table
from pyecharts.globals import ThemeType

from qa_scripts.test_report.bug import Bug


def bar_count_by_month() -> Bar:
    c = (
        Bar({"theme": ThemeType.WESTEROS})
        .add_xaxis(bug.months)
        .add_yaxis("月度发现缺陷数", bug.bug_count_by_months)
        .set_global_opts(
            title_opts=opts.TitleOpts(title="月份维度分析"),
            datazoom_opts=[opts.DataZoomOpts()],
        )
    )
    return c


def bug_count_by_creator() -> Bar:
    c = (
        Bar({"theme": ThemeType.ESSOS})
        .add_xaxis(bug.creator_attrs)
        .add_yaxis("缺陷数", bug.bug_counts)
        .set_global_opts(
            title_opts=opts.TitleOpts(title="缺陷按创建人统计"),
            datazoom_opts=[opts.DataZoomOpts()],
        )
    )
    return c


def line_bug_by_day() -> Line:
    c = (
        Line({"theme": ThemeType.MACARONS})
        .add_xaxis(bug.create_date)
        .add_yaxis(
            "按天统计缺陷",
            bug.bug_counts_by_create_date,
            markpoint_opts=opts.MarkPointOpts(data=[opts.MarkPointItem(type_="min")]),
        )
        .set_global_opts(title_opts=opts.TitleOpts(title="缺陷趋势图"))
    )
    return c


def bug_severity_distribution() -> Pie:
    v = bug.bug_severity
    c = (
        Pie({"theme": ThemeType.INFOGRAPHIC})
        # .add(
        #     "",
        #     [list(z) for z in zip(v, Faker.values())],
        #     radius=["30%", "75%"],
        #     center=["25%", "50%"],
        #     rosetype="radius",
        #     label_opts=opts.LabelOpts(is_show=False),
        # )
        .add(
            "",
            [list(z) for z in zip(v, bug.bug_severity_count)],
            radius=["25%", "75%"],
            center=["50%", "50%"],
            rosetype="area",
        )
        .set_global_opts(title_opts=opts.TitleOpts(title="缺陷级别分布"))
    )
    return c


def bug_attribution_analysis() -> Pie:
    v = bug.bug_attribution_analysis
    c = (
        Pie({"theme": ThemeType.WONDERLAND})
        .add(
            "",
            [list(z) for z in zip(v, bug.bug_attribution_analysis_count)],
            radius=["25%", "75%"],
            center=["50%", "50%"],
            rosetype="area",
        )
        .set_global_opts(title_opts=opts.TitleOpts(title="缺陷归因分析"))
    )
    return c


def report_summary() -> Table:
    table = Table({"theme": ThemeType.MACARONS})

    headers = ["迭代名称", "测试负责人", "报告日期", "测试开始时间", "测试结束时间", "报告总结描述"]
    rows = [
        bug.iteration_summary
    ]
    table.add(headers, rows, attributes={
                "align": "left",
                "border": False,
                "padding": "10px",
                "style": "width:1000px; font-size:16px; color:darkmagenta; font-family: cursive;"
            }).set_global_opts(
        title_opts=opts.ComponentTitleOpts(title="测试报告详情")
    )
    return table


def bug_summary() -> Table:
    table = Table({"theme": ThemeType.MACARONS})

    headers = ["总缺陷数", "已关闭缺陷数", "无效缺陷数(含重复缺陷、无法重现缺陷)", "未解决缺陷数", "Reopen缺陷数", "修复用时中位数", "修复用时80分位数"]
    rows = [
        bug.bug_summary
    ]
    table.add(headers, rows, attributes={
                "align": "left",
                "border": False,
                "padding": "10px",
                "style": "width:1000px; font-size:16px; color:darkmagenta; font-family: cursive;"
            }).set_global_opts(
                title_opts=opts.ComponentTitleOpts(title="缺陷概要")
            )
    return table


def bug_fix_time_by_fixer() -> Bar:
    c = (
        Bar({"theme": ThemeType.VINTAGE})
        .add_xaxis(bug.fixer_list)
        .add_yaxis("修复用时均值", bug.fix_avg_time, stack="stack1", itemstyle_opts=opts.ItemStyleOpts(color="blue"))
        .add_yaxis("修复用时中位线", bug.fix_mid_time, stack="stack1", itemstyle_opts=opts.ItemStyleOpts(color="yellow"))
        .add_yaxis("修复用时80分位线", bug.fix_eighty_percent_time, stack="stack1", itemstyle_opts=opts.ItemStyleOpts(color="orange"))
        .set_global_opts(
            title_opts=opts.TitleOpts(title="缺陷修复用时"),
            datazoom_opts=[opts.DataZoomOpts()],
        )
    )
    return c


def page_simple_layout():
    page = Page(layout=Page.SimplePageLayout)
    # page.add(
    #     report_summary(),
    #     bug_summary(),
    #     bar_count_by_month(),
    #     bug_count_by_creator(),
    #     line_bug_by_day(),
    #     bug_severity_distribution(),
    #     bug_attribution_analysis(),
    #     bug_fix_time_by_fixer(),
    # )
    page.add(report_summary())
    page.add(bug_summary())
    page.add(bar_count_by_month())
    page.add(bug_count_by_creator())
    page.add(line_bug_by_day())
    page.add(bug_severity_distribution())
    page.add(bug_attribution_analysis())
    page.add(bug_fix_time_by_fixer())
    cur_time = datetime.datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
    # page.render("./质量报告-{}.html".format(cur_time))
    page.render("./quality_report.html")


if __name__ == "__main__":
    bug = Bug('55014084', '1155014084001002093')
    page_simple_layout()

