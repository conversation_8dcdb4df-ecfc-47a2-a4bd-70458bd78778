import datetime
import os
import re

from pyecharts.charts import Page
from spider_common_utils.external_command.spider_ssh.spider_sftp import SCP
from spider_common_utils.external_command.spider_ssh.ssh_connect import SSHConnectionManager

from mantis.settings import NGINX_LIB_REPO, MANTIS
from qa_scripts.common.shell.shell_cmd import exec_local_cmd
from qa_scripts.test_report.model import DevEfficTestReportLibInfo


class GenerateHtml:
    def __init__(self, report_local_cache, report_filename):
        self.chart_list = []
        self.page = Page(layout=Page.SimplePageLayout)
        self.report_local_cache = self.__get_report_local_cache(report_local_cache)
        self.report_filename = report_filename

    def _page_put(self, chart_list):
        for chart in chart_list:
            self.page.add(chart)

    def __get_report_local_cache(self, report_local_cache):
        if not os.path.exists(report_local_cache):
            cmd = "mkdir -p {}".format(report_local_cache)
            exec_local_cmd(cmd)
        return report_local_cache

    def _render(self):
        absolute_path = os.path.join(self.report_local_cache, self.report_filename)
        self.page.render(absolute_path)

    def _replace_html_js(self, iteration_id):
        local_file = os.path.join(self.report_local_cache, self.report_filename)
        with open(local_file, "r+", encoding="utf8") as f:
            html = f.read()
            html = re.sub("https://assets.pyecharts.org/assets",
                          "http://nginx-lib.howbuy.pa/report-lib/test_report/software_quality/pyechartsjs", html)

            custom_script = '''</body>
                                <script type="text/javascript">
                                    function objclick(event) {{  window.open("{url}")
                                          if(event && event.target && ["{url}"].indexOf(event.target.innerText) >= 0)
                                               {{ window.open(event.target.innerText) }}
                                            }};
                                           document.evaluate("/html/body/div[1]/div[12]/table/tbody/tr/td", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.addEventListener('click',objclick,true);
                                </script>
                                <script type="text/javascript">
                                    function objclick1(event) {{  window.open("{url1}")
                                          if(event && event.target && ["{url1}"].indexOf(event.target.innerText) >= 0)
                                               {{ window.open(event.target.innerText) }}
                                            }};
                                           document.evaluate("/html/body/div/div[1]/table/tbody/tr/td[6]", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.addEventListener('click',objclick1,true);
                                </script>
                        '''.format(url="https://www.tapd.cn//55014084/prong/iterations/view/{iteration_id}#tab=Bugs".format(iteration_id=iteration_id),
                                   url1="{mantis_url}/mantis/test_report/tapd/get_launch_forms_status/?iteration_id={iteration_id}".format(iteration_id=iteration_id, mantis_url=MANTIS["url"]))

            html = html.replace("</body>", custom_script)

            f.seek(0, 0)
            f.truncate()
            f.write(html)

    def _push_to_nginx(self, workspace_id, iteration_id, report_id):
        report_cache_src = os.path.join(self.report_local_cache, self.report_filename)
        report_cache_tgt = os.path.join(NGINX_LIB_REPO["root_path"], NGINX_LIB_REPO["test_report_dir"],
                                        NGINX_LIB_REPO["software_quality_dir"], workspace_id, iteration_id)
        cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        with SSHConnectionManager(NGINX_LIB_REPO["ip"], NGINX_LIB_REPO["username"], NGINX_LIB_REPO["password"]) as ssh:
            scp = SCP(ssh.SFTP)
            if not scp.is_file_found(report_cache_tgt):
                scp.mkdir_p(report_cache_tgt)
            scp.push_file(report_cache_src, os.path.join(report_cache_tgt, self.report_filename))

        # 记录制表信息表
        p = DevEfficTestReportLibInfo(report_id=report_id,
                                      report_url=os.path.join(NGINX_LIB_REPO["base_url"],
                                                              NGINX_LIB_REPO["test_report_dir"],
                                                              NGINX_LIB_REPO["software_quality_dir"],
                                                              workspace_id,
                                                              iteration_id,
                                                              self.report_filename),
                                      create_time=cur_time, create_user='howbuyscm')
        p.save()

