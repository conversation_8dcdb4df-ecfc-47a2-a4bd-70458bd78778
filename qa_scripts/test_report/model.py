from peewee import *
from qa_scripts.common.db.base_model import BaseModel


class ProductMgtTestReportInfo(BaseModel):
    workspace_id = IntegerField(null=True, verbose_name='迭代ID')
    iteration_id = IntegerField(null=True, verbose_name='迭代ID')
    report_url = CharField(null=True, verbose_name='测试报告地址', max_length=256)
    create_time = DateTimeField(null=True, verbose_name='创建时间')
    status = IntegerField(null=True, verbose_name='报告状态')

    class Meta:
        db_table = 'software_quality_report_info'
        verbose_name = '软件质量报告信息表'


class DevEfficTestReportLibInfo(BaseModel):
    report_id = IntegerField(null=True, verbose_name='报告ID')
    report_url = CharField(null=True, verbose_name='测试报告地址', max_length=500)
    create_time = DateTimeField(null=True, verbose_name='创建时间')
    create_user = CharField(null=True, verbose_name='创建人', max_length=50)
    update_time = DateTimeField(null=True, verbose_name='更新时间')
    update_user = CharField(null=True, verbose_name='更新人', max_length=50)

    class Meta:
        db_table = 'dev_effic_test_report_lib_info'
        verbose_name = '测试报告库信息表'
