import datetime
import json
import os
import traceback

from mantis.settings import logger, TEST_REPORT
from qa_scripts.common.routers.router import Result
from qa_scripts.common.shell.shell_cmd import exec_local_cmd
from qa_scripts.test_report.config.analysis_ini import LoadConfig
from qa_scripts.test_report.generate_html import GenerateHtml
from qa_scripts.test_report.report_forms.impl.test_report_factory import TestReportFactory
from test_report.dao.test_report_dao import DevEfficTestProjectDao, DevEfficTestReportDao


class TestReportForm:

    @classmethod
    def as_view(cls, request):
        test_report = TestReportForm()
        return test_report.test_report_form(request)

    def test_report_form(self, request):
        logger.info("发布分析请求参数 {}".format(request))
        iteration_id = request.get('iteration_id')
        workspace_id = TEST_REPORT.get("workspace_id")
        test_report_dir = TEST_REPORT.get("test_report_dir")

        project_id = self.__get_project(workspace_id, iteration_id)

        if project_id:
            if not os.path.exists(test_report_dir):
                cmd = "mkdir -p {}".format(test_report_dir)
                exec_local_cmd(cmd)
            report_local_path = os.path.join(test_report_dir, workspace_id, iteration_id)
            cur_time = datetime.datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
            report_filename = "test-report-{}.html".format(cur_time)

            chart_list = []
            gh = GenerateHtml(report_local_path, report_filename)

            lc = LoadConfig()
            res = lc.loading("actived_report")["Test_report_config"]
            try:
                for k, v in json.loads(res.get("chart_class_list")).items():
                    logger.info("开始处理报告数据，内容：{}".format(k))
                    logger.info("脚本路径是：{}".format(v))
                    handler = TestReportFactory.get_instance(k, v)
                    handler._generate_data(project_id)
                    chart_list.append(handler._set_style())

                gh._page_put(chart_list)
                gh._render()
                gh._replace_html_js(iteration_id)
                dao = DevEfficTestReportDao(project_id)
                report_id = dao.get_report()
                gh._push_to_nginx(workspace_id, iteration_id, report_id)
            except Exception as e:
                logger.error("测试报告生成过程中发生错误: {}".format(str(e)))
                logger.error("错误详情: {}".format(traceback.format_exc()))
                return Result.failed_dict("测试报告生成失败: {}".format(str(e)))

            return Result.success_dict("业务测试报告分析成功！")
        else:
            return Result.success_dict("无可用业务测试报告可分析！")

    def __get_project(self, workspace_id, iteration_id):
        dao = DevEfficTestProjectDao(workspace_id, iteration_id)
        return dao.get_project()