import sys
import os


PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from mantis.settings import URL_DEF, logger
from qa_scripts.common.routers.router import Router
from qa_scripts.unit_test.utest_report_analyse import UtestAnalyseForm, BatchUtestAnalyseFrom
from qa_scripts.test_report.report_forms.impl.test_report_analyse import TestReportForm

urls = {
        "analyze_unit_test_data": UtestAnalyseForm.as_view,
        "batch_analyze_unit_test_data": BatchUtestAnalyseFrom.as_view,
        "create_test_report": TestReportForm.as_view,
}


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    rt = Router(urls, url_def=URL_DEF)
    rt.dispatch(sys.argv[1])
    #rt.dispatch(18118)

