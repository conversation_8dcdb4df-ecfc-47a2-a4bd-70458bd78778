import datetime

from qa_scripts.dao.get.mysql.model import TaskMgtServiceResults


def update_exec_results(tid, status, msg):
    """
    更新脚本执行结果
    :param tid:
    :param status:
    :param msg:
    :return:
    """
    q = TaskMgtServiceResults.update({TaskMgtServiceResults.status: status,
                                      TaskMgtServiceResults.detail: msg.replace('"', "'"),
                                      TaskMgtServiceResults.end_at: datetime.datetime.now()}).where(TaskMgtServiceResults.id == tid)
    q.execute()
