import pymysql
from mantis.settings import DATABASES


class DBConnectionManager:
    """
    通过上下文管理器的方式管理数据库连接
    """

    def __init__(self, host=DATABASES['default']['HOST'], port=DATABASES['default']['PORT'],
                 user=DATABASES['default']['USER'], password=DATABASES['default']['PASSWORD'],
                 db=DATABASES['default']['NAME'], charset=DATABASES['default']["CHARSET"]):
        """
        :param host: 数据库地址
        :param port: 端口
        :param user: 用户名
        :param password: 密码
        :param db: 数据库名称
        :param charset: 字符集
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.db = db
        self.charset = charset

    def __enter__(self):
        self.connection = pymysql.connect(host=self.host, port=self.port, user=self.user,
                                          passwd=self.password, db=self.db,
                                          charset=self.charset)
        self.cur = self.connection.cursor(cursor=pymysql.cursors.DictCursor)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.connection.close()