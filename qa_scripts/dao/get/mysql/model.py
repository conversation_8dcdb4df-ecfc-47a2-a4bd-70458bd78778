from peewee import *
from qa_scripts.common.db.base_model import BaseModel


class TaskMgtServiceResults(BaseModel):
    log_path = CharField(verbose_name='执行日志路径', max_length=128)
    status = CharField(verbose_name='执行状态（running/failure/success）', max_length=10)
    detail = TextField(verbose_name='详情')
    start_at = DateTimeField(null=True, verbose_name='开始时间')
    end_at = DateTimeField(null=True, verbose_name='结束时间')
    exec_cmd = CharField(verbose_name='执行命令', max_length=256)
    business_name = CharField(verbose_name='业务名称', max_length=32)
    operator = CharField(verbose_name='操作人', max_length=24)
    script_params = TextField(verbose_name='执行参数')

    class Meta:
        db_table = 'task_mgt_service_results'
        verbose_name = '服务调用结果表'
