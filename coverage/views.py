# Create your views here.
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet
from django.contrib.auth import authenticate, login, logout
from rest_framework import status
from public import exceptions as my_exceptions
from mantis.settings import SESSION_EXPIRE_AGE
from mantis import settings


class CoverageReport(ViewSet):
    """覆盖率报告接口"""


    # post方法
    def create(self, request, *args, **kwargs):
        pass

    # get方法
    def list(self, request, *args, **kwargs):
        pass

