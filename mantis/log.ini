[loggers]
keys=root,sampleLogger

[handlers]
keys=consoleHandler

[formatters]
keys=sampleFormatter

[logger_root]
level=DEBUG
handlers=consoleHandler

[logger_sampleLogger]
level=DEBUG
handlers=consoleHandler
qualname=sampleLogger
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=sampleFormatter
args=(sys.stdout,)

[formatter_sampleFormatter]
#format=%(asctime)s - %(name)s - %(levelname)s - %(message)s
#format=%(asctime)s - %(levelname)s - %(message)s
format=%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s
datefmt=%Y-%m-%d %H:%M:%S
