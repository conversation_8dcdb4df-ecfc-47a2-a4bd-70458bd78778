import os
import datetime
import logging.config
from pathlib import Path

# 获取项目根目录的上一级目录
LOG_DIR = '/data/logs/mantis/'

# 确保日志目录存在
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)


class SpiderFilter(logging.Filter):
    def filter(self, record):
        #logging.info(record.pathname)
        if "publish_mgt" in record.pathname:
            return True
        return False


DictConfig = {
    'version': 1,
    'disable_existing_loggers': False,
    'incremental': False,
    'formatters': {
        'master_format': {
            'class': 'logging.Formatter',
            #'format': '%(asctime)s [%(threadName)s] [%(name)s] [%(module)s] [%(levelname)s] %(filename)s[line:%(lineno)d]: %(message)s',
            'format': '%(asctime)s - [%(pathname)s]--[%(module)s]-%(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        }
    },
    'filters': {
        'filter_by_name': {
            '()': SpiderFilter,

        },
        # 仅 INFO 能输出
        'filter_single_level_pass': {
            'class': 'logging.StreamHandler',
            'pass_level': logging.INFO
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': logging.INFO,
            'formatter': 'master_format',
           # 'filters': ['filter_by_name', ],
        },
        # 'fileHandler': {
        #     'class': 'logging.FileHandler',
        #     'filename': 'logfile.log',
        #     'level': logging.INFO,
        #     'formatter': 'master_format',
        #     #'filters': ['filter_by_name', ],
        # },
        'timedRotatingFileHandler': {
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': os.path.join(LOG_DIR, 'mantis_info.log'),
            'level': logging.INFO,
            'formatter': 'master_format',
            #'filters': ['filter_by_name', ],
            'when': 'd',
            'interval': 1,
            'backupCount': 2
        },
        'timedErrorRotatingFileHandler': {
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': os.path.join(LOG_DIR, 'mantis_error.log'),
            'level': logging.WARNING,
            'formatter': 'master_format',
            #'filters': ['filter_by_name', ],
            'when': 'd',
            'interval': 1,
            'backupCount': 2
        },
    },
    'loggers': {
        # 'root': {
        #     'handlers': ['console', ],
        #     'level': 'INFO',
        #     'propagate': False
        # },
        # 'fileLogger': {
        #     'handlers': ['console', 'fileHandler'],
        #     'filters': ['filter_by_name', ],
        #     'level': 'DEBUG'
        # },
        'rotatingFileLogger': {
            'handlers': ['console', 'timedRotatingFileHandler', 'timedErrorRotatingFileHandler'],
            'level': 'INFO'
        }
    }
}
#logging.config.dictConfig(dictConfig)
# log = logging.getLogger(name='fileLogger')
# log.debug('log debug')
# log.info('log info')
# log.warning('log warning')
# log.error('log error')
# log.critical('log critical')
#
# log2 = logging.getLogger(name='rotatingFileLogger')
# log2.debug('log debug')
# log2.info('log info')
# log2.warning('log warning')
# log2.error('log error')
# log2.critical('log critical')