"""mantis URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from mcp_server.views import MCPServerStreamableHttpView
from rest_framework import routers

from rest_framework.documentation import include_docs_urls
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView

API_TITLE = 'API Documents'
API_DESCRIPTION = 'API Information'

route = routers.DefaultRouter(trailing_slash=False)

urlpatterns = [
    path('', include(route.urls)),
    path('admin/', admin.site.urls),
    path('mantis/user/', include('user.urls')),
    path('mantis/coverage/', include('coverage.urls')),
    path('mantis/utest/', include('utest.urls')),
    path('mantis/test_report/tapd/', include('test_report.tapd.urls')),
    path('mantis/test_report/', include('test_report.urls')),
    path('mantis/code_quality/', include('code_quality.urls')),
    path('mantis/assistant/', include('assistant.urls')),
    path('mantis/code_fetcher/', include('code_fetcher.urls')),
    path('mantis/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('mantis/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('mantis/tapd_gateway/', include('tapd_gateway.urls')),
    path('mantis/dev_effective/', include('dev_effective.urls')),
    path('mantis/tapd_gateway/from_tapd/', include('tapd_gateway.from_tapd.urls')),
    path('mantis/quality/', include('quality.bug.urls')),
    path('mantis/measurement/', include('measurement.urls')),

    path(r'mantis/docs/', include_docs_urls(title=API_TITLE, description=API_DESCRIPTION, authentication_classes=[],
                                            permission_classes=[])),

    path('mantis/mcp/', include('pa_mcp.urls')),
]
