[MYSQL]
charset = utf8
port = 3306
;password = ************
;ip = mantisdb.inner.howbuy.com
;user = mantis
password = 123456
ip = mantisdb-test.inner.howbuy.com
user = scm
db = mantis


[QA_SCRIPTS_INFO]
ssh_user = tomcat
ip = **************
ssh_password = howbuy2015

[JENKINS_INFO]
ssh_user = tomcat
password = Howbuy!@#
ip = **************
ssh_password = howbuy2015
user = howbuyscm
url = http://jenkinstest-master.howbuy.pa/jenkins
default_root = /home/<USER>/.jenkins/workspace/

[UTEST]
utest_report_dir = /data/utest_report

[COMMON]
exec_cmd_timeout = 300

[DISK_CACHE]
cache_dir = /data/cache/mantis
code_repos = D:/data/code_repos

[TAPD]
api_user = m?WYBPmq
api_password = AA73FEF3-4A72-17C4-0B46-1400C4D4DF2A
host_url = https://api.tapd.cn
create_tapd_bug_url = /bugs
sync_user = howbuyscm
req_limit = 200
req_page = 1
retry_max = 3
request_limit_number = 30
request_limit_seconds = 60
request_limit_retry = 3
prod_bug_workspace_id = 30324855
pa_one_workspace_id = 57711941
test_mgt_workspace_id = 55014084
workitem_type_id = 1155014084001000146
test_submit_story_workitem_type_id = 1155014084001000252
launch_template_id = 1155014084001000048
sync_fields = name,description,priority,attachment,business_value,status
work_item_config = {"36243514":"FEATURE","52223238":"FEATURE","66914855":"FEATURE"}
tapd_check_content = [{"checkType":"notNull","workspace_type":3,"modules":"story","status":"open"},{"checkType":"notNull","workspace_type":3,"modules":"task","status":"open"},{"checkType":"notNull","workspace_type":4,"modules":"task","status":"open"},{"checkType":"repeatCheck","workspace_type":3,"modules":"story","status":"open"},{"checkType":"repeatCheck","workspace_type":4,"modules":"test_plan","status":"open"},{"checkType":"storyLevel","workspace_type":3,"modules":"story","status":"open"},{"checkType":"appBind","workspace_type":3,"modules":"bug","status":"open"}]

[EMAIL]
password = cksEsDF2vjeQwTPg
sender = <EMAIL>
mail_host = smtp.corpease.net

node_apply_send_to = <EMAIL>
node_apply_err_to = <EMAIL>
node_apply_send_cc = <EMAIL>
node_apply_err_cc = <EMAIL>

node_apply_infra = <EMAIL>
node_apply_ops = <EMAIL>
story_duplication_name_to = <EMAIL>
story_duplication_name_cc = <EMAIL>

[INTERFACE_URL]
mantis = http://mantis-test.howbuy.pa
mantis_port = 80
mantis_context = mantis

[TEST_REPORT]
test_report_dir = /data/test_report
workspace_id = 55014084
story_workitem_type_id = 1155014084001000252

[NGINX_LIB_REPO]
ip = **************
password = howbuy2015
root_path = /data/report_lib
base_url = http://nginx-lib.howbuy.pa/report-lib
username = tomcat
test_report_dir = test_report
software_quality_dir = software_quality
test_flow_dir = test_flow

[CODE_SCAN]
custom_index = 95
local_ccn_path = /data/ccn_report.csv
local_p3c_path = /data/p3c_report.csv
ccn_lowest_threshold_value = 10
p3c_lowest_threshold_value = 0
ccn_first_scan = 20
p3c_first_scan = 0
dul_first_scan = 10
fbs_first_scan = 0
seh_first_scan = 0
duplicated_standard = 4
duplicated_highest = 10

[SONAR_QUBE]
;url = http://sonar-dev.howbuy.pa/
;token = ****************************************
url = http://sonar.howbuy.pa/sonarqube/
token = ****************************************

[SPIDER]
url = http://127.0.0.1:8000/spider/

[AUTH]
auth_key=S_g2SKPv7HANuZthUlgJCTXJKWQy3SVyFFUq-_cF_zE=
[MANTIS]
url = http://127.0.0.1:8088/
[QAP]
url = http://qamanage.it29.k8s.howbuy.com/
;url = http://qa.howbuy.pa/

[GITCODESTORE]
gitlab_http = http://gitlab-code.howbuy.pa
gitlab_token = ********************