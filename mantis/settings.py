"""
Django settings for mantis project.

Generated by 'django-admin startproject' using Django 3.2.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""
import datetime
import os
from pathlib import Path
import configparser
import logging.config
from enum import unique, Enum

from logging.handlers import TimedRotatingFileHandler

# 本地如果没有此目录需替换 /data/logs/mantis/
log_directory = os.path.join("/data/logs", "mantis")
if not os.path.exists(log_directory):
    os.makedirs(log_directory, mode=0o755, exist_ok=True)

# 创建日志处理器
audit_log_handler = TimedRotatingFileHandler(
    filename=os.path.join(log_directory, "audit_info.log"),
    backupCount=100
)
audit_log_handler.setLevel(logging.ERROR)
formatter = logging.Formatter('%(message)s')
audit_log_handler.setFormatter(formatter)
# 创建单独的日志记录器，并添加处理器
audit_logger = logging.getLogger('audit_logger')
audit_logger.addHandler(audit_log_handler)

# 解决项目默认启动不需要指定的问题。zt@2025-07-25
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
default_settings_path = os.path.join(BASE_DIR, 'mantis')

# 解析命令行参数
CONFIG_PATH = os.getenv('CONFIG_PATH', default_settings_path)
logging.info('------------{}------'.format(CONFIG_PATH))
local_settings_path = os.path.join(CONFIG_PATH, 'settings.ini')
logging.info('-----------local_settings_path:{}-------'.format(local_settings_path))
local_settings = configparser.ConfigParser()
# 确保配置文件存在
if not os.path.exists(local_settings_path):
    raise FileNotFoundError(f"配置文件不存在: {local_settings_path}")
# 读取配置文件
# local_settings_path = os.path.join(BASE_DIR, 'mantis', 'settings.ini')
local_settings.read(local_settings_path)
# config_dict = {s: dict(local_settings[s]) for s in local_settings.sections()}

# 获取UTEST配置
utest_report = local_settings.get('UTEST', 'utest_report_dir')
utest_report_directory = os.path.join(utest_report)
if not os.path.exists(utest_report_directory):
    os.makedirs(utest_report_directory, mode=0o755, exist_ok=True)
# 获取DISK_CACHE配置
cache_dir = local_settings.get('DISK_CACHE', 'cache_dir')
cache_directory = os.path.join(cache_dir)
if not os.path.exists(cache_directory):
    os.makedirs(cache_directory, mode=0o755, exist_ok=True)

code_repos = local_settings.get('DISK_CACHE', 'code_repos')
code_repos_directory = os.path.join(code_repos)
if not os.path.exists(code_repos_directory):
    os.makedirs(code_repos_directory, mode=0o755, exist_ok=True)
# 获取TEST_REPORT配置
test_report_dir = local_settings.get('TEST_REPORT', 'test_report_dir')
test_report_directory = os.path.join(test_report_dir)
if not os.path.exists(test_report_directory):
    os.makedirs(test_report_directory, mode=0o755, exist_ok=True)
# 获取NGINX_LIB_REPO配置
# root_path = local_settings.get('NGINX_LIB_REPO', 'root_path')
# root_path_directory = os.path.join(root_path)
# if not os.path.exists(root_path_directory):
#     os.makedirs(root_path_directory)

# Build paths inside the project like this: BASE_DIR / 'subdir'.
# BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-8ie=+7sbfmcl9u1=wlc_napn6_@n6_k!50glol(6_m@!ns1c7a'
TOKEN_SECRET_KEY = 'insecure-8ie=+7sbfmcl9u1=wlc_napn6_@n6_k!50glol(6_m@!ns1c7a'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']

# SESSION_ENGINE = 'django.contrib.sessions.backends.cache'

SESSION_ENGINE = 'django.contrib.sessions.backends.db'  # 引擎（默认）
SESSION_COOKIE_NAME = "mantis_session_id"  # Session的cookie保存在浏览器上时的key，即：mantis_session_id＝随机字符串（默认）
SESSION_COOKIE_PATH = "/"  # Session的cookie保存的路径（默认）
SESSION_SERIALIZER = "django.contrib.sessions.serializers.PickleSerializer"
SESSION_COOKIE_DOMAIN = None  # Session的cookie保存的域名（默认）
SESSION_COOKIE_SECURE = False  # 是否Https传输cookie（默认）
SESSION_COOKIE_HTTPONLY = True  # 是否Session的cookie只支持http传输（默认）
SESSION_COOKIE_AGE = 1209600  # Session的cookie失效日期（2周）（默认）
SESSION_EXPIRE_AT_BROWSER_CLOSE = False  # 是否关闭浏览器使得Session过期（默认）
SESSION_SAVE_EVERY_REQUEST = False  # 是否每次请求都保存Session，默认修改之后才保存（默认）
SESSION_EXPIRE_AGE = 21600

REST_FRAMEWORK = {
    # 全局使用的认证类
    'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.coreapi.AutoSchema',
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    "UNAUTHENTICATED_USER": None,
    "UNAUTHENTICATED_TOKEN": None,
    "DEFAULT_RENDERER_CLASSES": [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ]
}

INSTALLED_APPS = [
    'corsheaders',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    'mcp_server',
    'pa_mcp',

    'rest_framework',
    'rest_framework_simplejwt',

    'public',
    'user',
    'task_mgt',
    'utest',
    'qa_scripts',
    'test_report.tapd',
    'test_report',
    'code_quality',
    'assistant',
    'code_fetcher',
    'tapd_gateway',
    'quality',
    'measurement',
    'dev_effective'

]

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": datetime.timedelta(hours=12),
    "REFRESH_TOKEN_LIFETIME": datetime.timedelta(days=3),
    "ALGORITHM": "HS256",
    "SIGNING_KEY": TOKEN_SECRET_KEY,
    "AUTH_HEADER_TYPES": ("Bearer",),
}

# Application definition
CORS_ORIGIN_ALLOW_ALL = True
# 是否允许携带cookie
CORS_ALLOW_CREDENTIALS = True

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    # 'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'common_middle.OpLogs.AuditLogMiddleware'
]

ROOT_URLCONF = 'mantis.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

# email配置
EMAIL_BASES = {
    "MAIL_HOST": local_settings.get('EMAIL', 'MAIL_HOST'),
    "PASSWORD": local_settings.get('EMAIL', 'PASSWORD'),
    "SENDER": local_settings.get('EMAIL', 'SENDER'),
    "node_apply_send_to": local_settings.get('EMAIL', 'node_apply_send_to'),
    "node_apply_send_cc": local_settings.get('EMAIL', 'node_apply_send_cc'),
    "node_apply_err_to": local_settings.get('EMAIL', 'node_apply_err_to'),
    "node_apply_err_cc": local_settings.get('EMAIL', 'node_apply_err_cc'),
    "node_apply_ops": local_settings.get('EMAIL', 'node_apply_ops'),
    "node_apply_infra": local_settings.get('EMAIL', 'node_apply_infra'),
    "story_duplication_name_cc": local_settings.get('EMAIL', 'story_duplication_name_cc'),
}

# 外部接口地址
INTERFACE_URL = {
    "mantis": local_settings.get('INTERFACE_URL', 'mantis'),
    "mantis_port": local_settings.get('INTERFACE_URL', 'mantis_port'),
    "mantis_context": local_settings.get('INTERFACE_URL', 'mantis_context'),
}

WSGI_APPLICATION = 'mantis.wsgi.application'
# 日志模块
# logging.config.fileConfig(fname=os.path.join(BASE_DIR, 'log.ini'), disable_existing_loggers=False)
# logger = logging.getLogger(name="sampleLogger")
# logger = logging.getLogger(name="rotatingFileLogger")
from .log_config import DictConfig

logging.config.dictConfig(DictConfig)
logger = logging.getLogger(name="rotatingFileLogger")

# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

# 定义url 取值参数
URL_DEF = "business_name"

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': local_settings.get('MYSQL', 'DB'),
        'USER': local_settings.get('MYSQL', 'USER'),
        'PASSWORD': local_settings.get('MYSQL', 'PASSWORD'),
        'HOST': local_settings.get('MYSQL', 'IP'),
        'PORT': int(local_settings.get('MYSQL', 'PORT')),
        "CHARSET": local_settings.get('MYSQL', 'CHARSET'),
        'TEST': {
            'NAME': "test_" + local_settings.get('MYSQL', 'DB'),
            'CHARSET': 'utf8mb4',
        },
        'OPTIONS': {'init_command': "SET sql_mode='STRICT_TRANS_TABLES'", 'charset': 'utf8mb4'},
    }

}

QA_SCRIPTS = {
    "IP": local_settings.get('QA_SCRIPTS_INFO', 'IP'),
    "SSH_USER": local_settings.get('QA_SCRIPTS_INFO', 'SSH_USER'),
    "SSH_PASSWORD": local_settings.get('QA_SCRIPTS_INFO', 'SSH_PASSWORD'),
}

GITLAP_INFO = {
    "HTTP_URL": local_settings.get('GITCODESTORE', 'GITLAB_HTTP'),
    "TOKEN": local_settings.get('GITCODESTORE', 'GITLAB_TOKEN'),
}

JENKINS_INFO = {
    "USER": local_settings.get('JENKINS_INFO', 'USER'),
    "PASSWORD": local_settings.get('JENKINS_INFO', 'PASSWORD'),
    "URL": local_settings.get('JENKINS_INFO', 'URL'),
    "IP": local_settings.get('JENKINS_INFO', 'IP'),
    "SSH_USER": local_settings.get('JENKINS_INFO', 'SSH_USER'),
    "SSH_PASSWORD": local_settings.get('JENKINS_INFO', 'SSH_PASSWORD'),
    "DEFAULT_ROOT": local_settings.get('JENKINS_INFO', 'DEFAULT_ROOT'),
    "TIME_OUT": 3600 if not local_settings.has_option('JENKINS_INFO', 'TIME_OUT') else local_settings.get(
        'JENKINS_INFO', 'TIME_OUT')
}

UTEST = {
    "utest_report_dir": local_settings.get('UTEST', 'utest_report_dir')
}

TEST_REPORT = {
    "test_report_dir": local_settings.get('TEST_REPORT', 'test_report_dir'),
    "workspace_id": local_settings.get('TEST_REPORT', 'workspace_id'),
    "story_workitem_type_id": local_settings.get('TEST_REPORT', 'story_workitem_type_id'),
}

NGINX_LIB_REPO = {
    "ip": local_settings.get('NGINX_LIB_REPO', 'ip'),
    "password": local_settings.get('NGINX_LIB_REPO', 'password'),
    "root_path": local_settings.get('NGINX_LIB_REPO', 'root_path'),
    "base_url": local_settings.get('NGINX_LIB_REPO', 'base_url'),
    "username": local_settings.get('NGINX_LIB_REPO', 'username'),
    "test_report_dir": local_settings.get('NGINX_LIB_REPO', 'test_report_dir'),
    "software_quality_dir": local_settings.get('NGINX_LIB_REPO', 'software_quality_dir'),
    "test_flow_dir": local_settings.get('NGINX_LIB_REPO', 'test_flow_dir')
}

COMMON = {
    "exec_cmd_timeout": local_settings.get('COMMON', 'exec_cmd_timeout')
}

DISK_CACHE = {
    "cache_dir": local_settings.get('DISK_CACHE', 'cache_dir'),
    "code_repos": local_settings.get('DISK_CACHE', 'code_repos')
}

TAPD = {
    "api_user": local_settings.get('TAPD', 'api_user'),
    "api_password": local_settings.get('TAPD', 'api_password'),
    "host_url": local_settings.get('TAPD', 'host_url'),
    "create_tapd_bug_url": local_settings.get('TAPD', 'create_tapd_bug_url'),
    "sync_user": local_settings.get('TAPD', 'sync_user'),
    "req_limit": local_settings.get('TAPD', 'req_limit'),
    "req_page": local_settings.get('TAPD', 'req_page'),
    "retry_max": local_settings.get('TAPD', 'retry_max'),
    "request_limit_number": local_settings.get('TAPD', 'request_limit_number'),
    "request_limit_seconds": local_settings.get('TAPD', 'request_limit_seconds'),
    "request_limit_retry": local_settings.get('TAPD', 'request_limit_retry'),
    "prod_bug_workspace_id": local_settings.get('TAPD', 'prod_bug_workspace_id'),
    "pa_one_workspace_id": local_settings.get('TAPD', 'pa_one_workspace_id'),
    "test_mgt_workspace_id": local_settings.get('TAPD', 'test_mgt_workspace_id'),
    "tapd_check_content": local_settings.get('TAPD', 'tapd_check_content'),
    "workitem_type_id": local_settings.get('TAPD', 'workitem_type_id'),
    "launch_template_id": local_settings.get('TAPD', 'launch_template_id'),
    "test_submit_story_workitem_type_id": local_settings.get('TAPD', 'test_submit_story_workitem_type_id'),
    "sync_fields": local_settings.get('TAPD', 'sync_fields'),
    "work_item_config": local_settings.get('TAPD', 'work_item_config')
}

CODE_SCAN = {
    "custom_index": local_settings.get('CODE_SCAN', 'custom_index'),
    "local_ccn_path": local_settings.get('CODE_SCAN', 'local_ccn_path'),
    "local_p3c_path": local_settings.get('CODE_SCAN', 'local_p3c_path'),
    "ccn_lowest_threshold_value": local_settings.get('CODE_SCAN', 'ccn_lowest_threshold_value'),
    "p3c_lowest_threshold_value": local_settings.get('CODE_SCAN', 'p3c_lowest_threshold_value'),
    "ccn_first_scan": local_settings.get('CODE_SCAN', 'ccn_first_scan'),
    "p3c_first_scan": local_settings.get('CODE_SCAN', 'p3c_first_scan'),
    "dul_first_scan": local_settings.get('CODE_SCAN', 'dul_first_scan'),
    "BUG": local_settings.get('CODE_SCAN', 'fbs_first_scan'),
    "VULNERABILITY": local_settings.get('CODE_SCAN', 'seh_first_scan'),
    "duplicated_standard": local_settings.get('CODE_SCAN', 'duplicated_standard'),
    "DUPLICATED": local_settings.get('CODE_SCAN', 'duplicated_highest'),
}

SONAR_QUBE = {
    "url": local_settings.get('SONAR_QUBE', 'url'),
    "token": local_settings.get('SONAR_QUBE', 'token'),
}

SPIDER = {
    "url": local_settings.get('SPIDER', 'url')
}

MANTIS = {
    "url": local_settings.get('MANTIS', 'url')
}

QAP = {
    "url": local_settings.get('QAP', 'url')
}

AUTH = {
    "auth_key": local_settings.get('AUTH', 'auth_key')
}


class ConsJenkinsKeys:
    ip = 'IP'
    ssh_user = 'SSH_USER'
    ssh_password = 'SSH_PASSWORD'


# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

AUTHENTICATION_BACKENDS = (
    'django.contrib.auth.backends.ModelBackend',
    'django_python3_ldap.auth.LDAPBackend',
)

# The URL of the LDAP server.
LDAP_AUTH_URL = "ldap://**************:389"

# Initiate TLS on connection.
LDAP_AUTH_USE_TLS = False

# The LDAP search base for looking up users.
# LDAP_AUTH_SEARCH_BASE = "OU=魔数工厂,OU=staff-hk,OU=howbuy-hk,OU=howbuy,DC=howbuy,DC=domain"
LDAP_AUTH_SEARCH_BASE = "OU=howbuy,DC=howbuy,DC=domain"

# # The LDAP class that represents a user.
LDAP_AUTH_OBJECT_CLASS = "person"

# User model fields mapped to the LDAP
# attributes that represent them.
LDAP_AUTH_USER_FIELDS = {
    "username": "sAMAccountName",
    "first_name": "givenName",
    "last_name": "sn",
    "email": "mail",
}

# A tuple of django model fields used to uniquely identify a user.
LDAP_AUTH_USER_LOOKUP_FIELDS = ("username",)

# Path to a callable that takes a dict of {model_field_name: value},
# returning a dict of clean model data.
# Use this to customize how data loaded from LDAP is saved to the User model.
LDAP_AUTH_CLEAN_USER_DATA = "django_python3_ldap.utils.clean_user_data"

# Path to a callable that takes a user model and a dict of {ldap_field_name: [value]},
# and saves any additional user relationships based on the LDAP data.
# Use this to customize how data loaded from LDAP is saved to User model relations.
# For customizing non-related User model fields, use LDAP_AUTH_CLEAN_USER_DATA.
LDAP_AUTH_SYNC_USER_RELATIONS = "django_python3_ldap.utils.sync_user_relations"

# Path to a callable that takes a dict of {ldap_field_name: value},
# returning a list of [ldap_search_filter]. The search filters will then be AND'd
# together when creating the final search filter.
LDAP_AUTH_FORMAT_SEARCH_FILTERS = "django_python3_ldap.utils.format_search_filters"

# Path to a callable that takes a dict of {model_field_name: value}, and returns
# a string of the username to bind to the LDAP server.
# Use this to support different types of LDAP server.
# LDAP_AUTH_FORMAT_USERNAME = "django_python3_ldap.utils.format_username_openldap"

LDAP_AUTH_FORMAT_USERNAME = "django_python3_ldap.utils.format_username_active_directory_principal"
# Sets the login domain for Active Directory users.
LDAP_AUTH_ACTIVE_DIRECTORY_DOMAIN = 'howbuy.domain'
# The LDAP username and password of a user for querying the LDAP database for user
# details. If None, then the authenticated user will be used for querying, and
# the `ldap_sync_users` command will perform an anonymous query.
LDAP_AUTH_CONNECTION_USERNAME = 'ldap_mofanglog'
LDAP_AUTH_CONNECTION_PASSWORD = 'Howbuy2007'

# Set connection/receive timeouts (in seconds) on the underlying `ldap3` library.
LDAP_AUTH_CONNECT_TIMEOUT = None
LDAP_AUTH_RECEIVE_TIMEOUT = None

# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = False

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = '/static/'

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

CORS_ORIGIN_ALLOW_ALL = True

class ConsSessionKeys:
    username = 'username'
    is_login = 'is_login'


@unique
class ResultStatus(Enum):
    success = 1
    failed = 2
    dicey = 3


class ApiResult:
    def __init__(self, status: ResultStatus = ResultStatus.failed, data='', msg: str = ''):
        self.status = status
        self.data = data
        self.msg = msg

    def get_dict(self):
        return {'status': self.status.name, 'data': self.data, 'msg': self.msg}

    @staticmethod
    def success_dict(msg: str = '', data=''):
        return ApiResult(ResultStatus.success, data, msg).get_dict()

    @staticmethod
    def dicey_dict(msg: str = '', data=''):
        return ApiResult(ResultStatus.dicey, data, msg).get_dict()

    @staticmethod
    def failed_dict(msg: str = '', data: str = ''):
        return ApiResult(ResultStatus.failed, data, msg).get_dict()

DJANGO_MCP_GLOBAL_SERVER_CONFIG = {
    "name":"mymcp"
}

DJANGO_MCP_AUTHENTICATION_CLASSES = ["rest_framework.authentication.TokenAuthentication"]

DJANGO_MCP_GET_SERVER_INSTRUCTIONS_TOOL = True