import csv
import codecs
from mantis.settings import logger
from qa_scripts.common.shell.shell_cmd import exec_local_cmd
from pathlib import Path


def get_report(report_path, local_path):
    """
    获取ngnix上的报告
    """
    exec_local_cmd("wget {} -O {}".format(report_path, local_path))

def count_ccn_entrance_guard_index(last_p3c_index, now_p3c_index, lowest_threshold_value):
    """
    计算门禁阈值
    """
    if float(now_p3c_index) <= float(lowest_threshold_value):
        return True
    if float(last_p3c_index) >= float(now_p3c_index):
        return True
    else:
        return False

def count_p3c_entrance_guard_index(last_p3c_index, now_p3c_index):
    """
    计算门禁阈值
    """
    if float(last_p3c_index) >= float(now_p3c_index):
        return True
    else:
        return False

def count_first_scan_entrance_guard_index(current_index, threshold_value):
    """
    首次扫描门禁计算
    """
    if float(threshold_value) >= float(current_index):
        return True
    else:
        return False

def analysis_report(local_path):
    """
    解析csv文件，返回list
    """
    detail_list = []
    with codecs.open(local_path, 'r') as f:
        for row in csv.reader(f, skipinitialspace=True):
            detail_list.append(row)
    return detail_list

def analysis_report_dict(local_path):
    """
    解析csv文件，返回json
    """
    detail_list = []
    with codecs.open(local_path, 'r') as f:
        for row in csv.DictReader(f, skipinitialspace=True):
            detail_list.append(row)
    return detail_list

def del_file(path):
    """删除文件"""
    if len(path.split('/')) > 2 and Path(path).exists():
        exec_local_cmd("rm -rf {}".format(path))
    else:
        logger.info("目录层级低于2级")

def record_code_scan_info():
    pass
