from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from mantis.settings import DATABASES

Base = declarative_base()


def instance_db_session(pool_size=1):
    url = 'mysql+mysqldb://{0}:{1}@{2}:3306/{3}?charset=utf8mb4'.format(DATABASES.get("default").get("USER"),
                                                                        DATABASES.get("default").get("PASSWORD"),
                                                                        DATABASES.get("default").get("HOST"),
                                                                        DATABASES.get("default").get("NAME"))
    engine = create_engine(url, pool_size=pool_size)
    # 在同一个线程中，有 scoped_session 的时候，返回的是同一个 Session 对象。
    # 在多线程下，即使通过  scoped_session 创建Session，每个线程下的 Session 都是不一样的，每个线程都有一个属于自己的 Session 对象，这个对象只在本线程下共享。
    db_session = scoped_session(sessionmaker(autocommit=False, autoflush=False, bind=engine))
    Base.query = db_session.query_property()
    return db_session


class SustainablePool:

    def __init__(self, pool_size=1):
        url = 'mysql+mysqldb://{0}:{1}@{2}:3306/{3}?charset=utf8mb4'.format(DATABASES.get("default").get("USER"),
                                                                            DATABASES.get("default").get("PASSWORD"),
                                                                            DATABASES.get("default").get("HOST"),
                                                                            DATABASES.get("default").get("NAME"))
        self.engine = create_engine(url, pool_size=pool_size, max_overflow=pool_size/2, pool_recycle=600)

    def get_db_session(self):
        db_session = scoped_session(sessionmaker(autocommit=False, autoflush=False, bind=self.engine))
        Base.query = db_session.query_property()
        return db_session


class InstanceDbSession:
    def __init__(self, user=DATABASES["default"]["USER"], password=DATABASES["default"]["PASSWORD"],
                 host=DATABASES["default"]["HOST"], name=DATABASES["default"]["NAME"]):
        self.user = user
        self.password = password
        self.host = host
        self.name = name
        self.db_session = None

    def __enter__(self):
        """
        创建链接
        """
        url = 'mysql+mysqldb://{0}:{1}@{2}:3306/{3}?charset=utf8mb4'.format(self.user,
                                                                            self.password,
                                                                            self.host,
                                                                            self.name)
        engine = create_engine(url, pool_size=1)
        self.db_session = scoped_session(sessionmaker(autocommit=False, autoflush=False, bind=engine))
        Base.query = self.db_session.query_property()
        return self.db_session

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.db_session.commit()
        self.db_session.close()


if __name__ == '__main__':
    with InstanceDbSession() as db:
        pass
        # query_sql = '''select * from code_quality_p3c_detail_info'''
        # query_sql_info = db.execute(query_sql).fetchall()
