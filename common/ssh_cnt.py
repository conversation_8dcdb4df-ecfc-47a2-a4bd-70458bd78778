import paramiko
from mantis.settings import logger, Cons<PERSON><PERSON>kinsKeys
from mantis.settings import JENKINS_INFO
from public.ip import get_host_ip


class SSHConnectionManager:
    def __init__(self, ip=JENKINS_INFO["IP"], username=JENKINS_INFO["SSH_USER"], password=JENKINS_INFO["SSH_PASSWORD"]):
        """
        :param ip:
        :param username:
        :param password:
        """
        self.ip = ip
        self.username = username
        self.password = password
        self.SSH = paramiko.SSHClient()
        self.SSH.load_system_host_keys()
        self.SSH.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    def __enter__(self):
        logger.info("建立到{}的SSH连接".format(self.ip))
        try:
            self.SSH.connect(self.ip, port=22, username=self.username,
                             password=self.password, compress=True)
        except Exception as e:
            logger.error("{}连接失败,报错日志{}".format(self.ip, str(e)))
        logger.info("{}连接成功".format(self.ip))
        self.SFTP = self.SSH.open_sftp()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        logger.info("断开到{}的SSH连接".format(self.ip))
        self.SSH.close()
        logger.info("{}断开连接成功".format(self.ip))

    def exec_ssh(self, cmd):
        stdin, stdout, stderr = self.SSH.exec_command(cmd)
        code = stdout.channel.recv_exit_status()
        out = stdout.read().decode('utf-8')
        err = stderr.read().decode('utf-8')
        return code, out, err

    @staticmethod
    def exec_ssh_and_close(cmd):
        SSH = paramiko.SSHClient()
        SSH.load_system_host_keys()
        SSH.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        SSH.connect(JENKINS_INFO["IP"], username=JENKINS_INFO["SSH_USER"], password=JENKINS_INFO["SSH_PASSWORD"],
                    port=22, compress=True)
        stdin, stdout, stderr = SSH.exec_command(cmd)
        code = stdout.channel.recv_exit_status()
        out = stdout.read().decode('utf-8')
        err = stderr.read().decode('utf-8')
        SSH.close()
        return code, out, err

    def exec_ssh_raw(self, cmd):
        return self.SSH.exec_command(cmd)

    def repo_ssh(self, cmd):
        stdin, stdout, stderr = self.SSH.exec_command(cmd)
        code = stdout.channel.recv_exit_status()
        out = stdout.read().decode('utf-8')
        err = stderr.read().decode('utf-8')
        return code, out, err


if __name__ == '__main__':
    # logger.info(type("***************"))
    with SSHConnectionManager() as ssh:
        tdin, stdout, stderr = ssh.SSH.exec_command("df -lh", bufsize=-1)
        print(stdout.read())
