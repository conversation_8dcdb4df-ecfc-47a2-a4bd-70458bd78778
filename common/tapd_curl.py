import datetime
import json
from time import sleep

import requests

from mantis.settings import TAPD, logger
from task_mgt import models


def record_interface_results(url, interface_name, res, data=None):
    interface_results = models.InterfaceResults.objects.create(
        interface_name=interface_name,
        request_params=data if data else "",
        request_url=url,
        start_at=datetime.datetime.now(),
        request_status=models.InterfaceResults.RUNNING,
    )

    if res.status_code == 200:
        interface_results.request_status = models.InterfaceResults.SUCCESS
    else:
        interface_results.request_status = models.InterfaceResults.FAILURE
    interface_results.end_at = datetime.datetime.now()
    interface_results.request_result = res.json()
    interface_results.save()


class CurlRequestsManager:
    def __init__(self, api_user=TAPD["api_user"], api_password=TAPD["api_password"]):
        """
        :param api_user: tapd访问用户名
        :param api_password: tapd访问密码
        """
        self.api_user = api_user
        self.api_password = api_password
        self.headers = {
            "Authorization": "Basic bT9XWUJQbXE6QUE3M0ZFRjMtNEE3Mi0xN0M0LTBCNDYtMTQwMEM0RDRERjJB",
            "Content-Type": "application/json",
        }

    def __enter__(self):
        logger.info("建立requests连接")
        try:
            s = requests.session()
            s.auth = (self.api_user, self.api_password)
            self.s = s
            logger.info("建立requests连接成功")
            return self
        except Exception as e:
            logger.error("requests连接失败,报错日志{}".format(str(e)))
            raise Exception("requests连接失败,报错日志{}".format(str(e)))

    def __exit__(self, exc_type, exc_val, exc_tb):
        logger.info("断开requests连接")
        self.s.close()
        logger.info("断开requests连接成功")

    def curl_get(self, url, interface_name):
        logger.info("请求URL：{}".format(url))
        try:
            res = self.s.get(url)
            logger.info("结果码为{}".format(res.status_code))
            while res.status_code == 429:
                logger.info("请求过于频繁，超过tapd限制，休眠10s")
                sleep(10)
                res = self.s.get(url)
            record_interface_results(url, interface_name, res)
            return res.status_code, res.json()
        except Exception as e:
            logger.error("地址{}请求失败，请稍后重试！异常信息为：{}".format(url, str(e)))
            raise Exception("地址{}请求失败，请稍后重试！异常信息为：{}".format(url, str(e)))

    def curl_post(self, url, data, interface_name):
        try:
            res = self.s.post(url, headers=self.headers, data=json.dumps(data))
            record_interface_results(url, interface_name, res)
            return res.status_code, res.json()
        except Exception as e:
            logger.error("地址{}请求失败，请稍后重试！异常信息为：{}".format(url, str(e)))
            raise Exception("地址{}请求失败，请稍后重试！异常信息为：{}".format(url, str(e)))
