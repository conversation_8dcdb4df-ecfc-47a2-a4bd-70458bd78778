from django.db import models


class TestFlowScheduleInfo(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    biz_code = models.CharField(max_length=100, verbose_name='业务编码')
    biz_iter_branch = models.CharField(max_length=100, verbose_name='业务分支')
    biz_flow_name = models.CharField(max_length=200, verbose_name='业务编排名称')
    suite_code = models.CharField(max_length=50, verbose_name='执行环境')
    is_active = models.IntegerField(verbose_name='是否有效')
    execute_time = models.TimeField(verbose_name='定时执行时间')
    schedule_creator = models.CharField(max_length=50, verbose_name='编排创建人')
    schedule_updater = models.CharField(max_length=50, verbose_name='编排更新人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    create_user = models.CharField(max_length=50, verbose_name='创建人')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    update_user = models.CharField(max_length=50, verbose_name='更新人')
    stamp = models.BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'test_flow_schedule_info'
        verbose_name = '定时自动化测试任务'


class TestFlowRunRecord(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    biz_code = models.CharField(max_length=100, verbose_name='业务编码')
    biz_iter_branch = models.CharField(max_length=100, verbose_name='业务分支')
    biz_flow_name = models.CharField(max_length=200, verbose_name='业务编排名称')
    suite_code = models.CharField(max_length=50, verbose_name='执行环境')
    run_time = models.DateTimeField(verbose_name='执行时间')
    run_batch_number = models.CharField(max_length=255, verbose_name='执行批次号')
    exec_action_type = models.CharField(max_length=100, verbose_name='执行类型')
    test_report_lib_id = models.BigIntegerField(verbose_name='测试报告制品ID')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    create_user = models.CharField(max_length=50, verbose_name='创建人')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    update_user = models.CharField(max_length=50, verbose_name='更新人')
    stamp = models.BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'test_flow_run_record'
        verbose_name = '编排线执行记录表'


class TestFlowRunRecordTestset(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    run_batch_number = models.CharField(max_length=255, verbose_name='执行批次号')
    testset_id = models.IntegerField(verbose_name='测试集ID')
    testset_detail = models.JSONField(verbose_name='测试集应用脚本分支详情')
    testset_version_type = models.CharField(max_length=10, verbose_name='测试集版本类型')
    testset_app_run_detail = models.JSONField(verbose_name='测试集执行应用详情')
    exec_order = models.IntegerField(verbose_name='执行顺序')
    execute_id = models.CharField(max_length=255, verbose_name='测试集执行ID')
    testset_status = models.CharField(max_length=20, verbose_name='执行状态：running，success，failure')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    create_user = models.CharField(max_length=50, verbose_name='创建人')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    update_user = models.CharField(max_length=50, verbose_name='更新人')
    stamp = models.BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'test_flow_run_record_testset'
        verbose_name = '编排线执行测试集记录表'


class TestFlowAppDeployInfo(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    run_batch_number = models.CharField(max_length=255, verbose_name='执行批次号')
    app_name = models.CharField(max_length=100, verbose_name='应用名')
    app_deploy_branch = models.CharField(max_length=100, verbose_name='应用部署版本')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    create_user = models.CharField(max_length=50, verbose_name='创建人')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    update_user = models.CharField(max_length=50, verbose_name='更新人')
    stamp = models.BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'test_flow_app_deploy_info'
        verbose_name = '编排线执行测试集记录表'


class MeasurementSyncLog(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    sync_content = models.CharField(max_length=50, verbose_name='同步内容')
    success_time = models.DateTimeField(auto_now_add=True, verbose_name='最近同步成功时间')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    create_user = models.CharField(max_length=50, verbose_name='创建人')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    update_user = models.CharField(max_length=50, verbose_name='更新人')

    class Meta:
        db_table = 'measurement_sync_log'
        verbose_name = '度量同步日志表'


class DevelopsStakeholderInfo(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    app_name = models.CharField(max_length=100, verbose_name='应用名')
    branch_name = models.CharField(max_length=200, verbose_name='迭代ID')
    biz_flow_name = models.CharField(max_length=200, verbose_name='编排线名称')
    opt_type = models.CharField(max_length=50, verbose_name='类型')
    opt_user = models.CharField(max_length=50, verbose_name='操作人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    create_user = models.CharField(max_length=50, verbose_name='创建人')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    update_user = models.CharField(max_length=50, verbose_name='更新人')
    stamp = models.BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'devops_stakeholder_info'
        verbose_name = '编排线应用干系人信息'


class DevopsAppTeamInfo(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    app_name = models.CharField(max_length=100, verbose_name='应用名')
    team_owner = models.CharField(max_length=20, verbose_name='团队负责人')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    create_user = models.CharField(max_length=20, verbose_name='创建人')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    update_user = models.CharField(max_length=20, verbose_name='更新人')

    class Meta:
        db_table = 'devops_app_team_owner_info'
        verbose_name = '应用负责人'


class TestFlowRunRecordTestSetResult(models.Model):
    id = models.AutoField(verbose_name='主键ID', primary_key=True)
    t_id = models.BigIntegerField(verbose_name='测试集执行记录id')
    app_name = models.CharField(max_length=50, verbose_name='应用名')
    script_branch = models.CharField(max_length=50, verbose_name='测试脚本版本')
    version_mode = models.CharField(max_length=20, verbose_name='案例版本类型，原version_type')
    interface_pass_rate = models.CharField(max_length=20, verbose_name='接口通过率')
    case_pass_rate = models.CharField(max_length=20, verbose_name='案例通过率')
    start_time = models.DateTimeField(verbose_name='执行开始时间')
    end_time = models.DateTimeField(verbose_name='执行结束时间')
    duration = models.CharField(max_length=64, verbose_name='执行时长')
    merge_latest_status = models.BooleanField(verbose_name='是否合并最新的master')
    result_url = models.CharField(max_length=999, verbose_name='详情地址url')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    create_user = models.CharField(max_length=50, verbose_name='创建人')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    update_user = models.CharField(max_length=50, verbose_name='更新人')
    stamp = models.BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'test_flow_run_record_testset_result'
        unique_together = (('t_id', 'app_name', 'script_branch'),)
        verbose_name = '编排线执行测试集结果'


class TeamMgtUserInfo(models.Model):
    id = models.AutoField(verbose_name='主键', primary_key=True)
    user_name = models.CharField(max_length=50, verbose_name='用户')
    cn_name = models.CharField(max_length=50, verbose_name='用户中文名')
    p_team_id = models.BigIntegerField(verbose_name='小团队ID')
    team_id = models.BigIntegerField(verbose_name='大团队ID')
    p_team_name = models.CharField(max_length=100, verbose_name='小团队名称')
    team_name = models.CharField(max_length=100, verbose_name='大团队名称')
    main_skill = models.CharField(max_length=100, verbose_name='主技能')
    bind_is_active = models.BooleanField(verbose_name='绑定是否可用')
    create_time = models.DateTimeField(auto_now_add=True, null=True, verbose_name='创建时间')
    create_user = models.CharField(max_length=50, null=True, verbose_name='创建人')
    update_time = models.DateTimeField(auto_now=True, null=True, verbose_name='更新时间')
    update_user = models.CharField(max_length=20, null=True, verbose_name='更新人')

    class Meta:
        db_table = 'team_mgt_user_info'
        verbose_name = '人员团队信息表'


class DevopsCheckRecordInfo(models.Model):
    id = models.AutoField(verbose_name='主键', primary_key=True)
    iteration_id = models.CharField(max_length=500, verbose_name='迭代id')
    app_name = models.CharField(max_length=100, verbose_name='应用名称')
    run_batch_number = models.CharField(max_length=255, verbose_name='执行批次号')
    business_name = models.CharField(max_length=50, verbose_name='业务名称')
    report_lib_id = models.BigIntegerField(verbose_name='质量报告制品id')
    check_content = models.CharField(max_length=50, verbose_name='检查内容')
    create_time = models.DateTimeField(verbose_name='创建时间')
    create_user = models.CharField(max_length=50, null=True, verbose_name='创建人')
    update_time = models.DateTimeField(verbose_name='更新时间')
    update_user = models.CharField(max_length=50, null=True, verbose_name='更新人')
    stamp = models.BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'devops_check_record_info'
        verbose_name = '检查记录表'


class AutoTestStatisticsRecordInfo(models.Model):
    id = models.AutoField(verbose_name='主键', primary_key=True)
    user_name = models.CharField(max_length=50, verbose_name='用户')
    opt_time = models.CharField(max_length=50, verbose_name='日期')
    script_number = models.IntegerField(verbose_name='脚本数量')
    case_number = models.IntegerField(verbose_name='案例数量')
    validation_set_number = models.IntegerField(verbose_name='验证集数量')
    validation_set_case_number = models.IntegerField(verbose_name='验证集案例数量')
    create_time = models.DateTimeField(verbose_name='创建时间')
    create_user = models.CharField(max_length=50, null=True, verbose_name='创建人')
    update_time = models.DateTimeField(verbose_name='更新时间')
    update_user = models.CharField(max_length=50, null=True, verbose_name='更新人')
    stamp = models.BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'auto_test_statistics_record'
        verbose_name = '自动化变更统计'


class AutoTestCaseRecordInfo(models.Model):
    create_time = models.DateTimeField(verbose_name='创建时间')
    create_user = models.CharField(max_length=50, null=True, verbose_name='创建人')
    update_time = models.DateTimeField(verbose_name='更新时间')
    update_user = models.CharField(max_length=50, null=True, verbose_name='更新人')
    stamp = models.BigIntegerField(verbose_name='版本')
    id = models.AutoField(verbose_name='主键', primary_key=True)
    time_batch = models.BigIntegerField(verbose_name='时间批')
    batch_number = models.IntegerField( verbose_name='批次号')
    time_cycle_enum = models.CharField(max_length=100, verbose_name='时间区间枚举字符串')
    req_start_time = models.DateTimeField(verbose_name='请求开始时间')
    req_end_time = models.DateTimeField(verbose_name='请求结束时间')
    user_name = models.CharField(max_length=50, verbose_name='用户')
    cn_name = models.CharField(max_length=50, verbose_name='用户中文名')
    main_skill = models.CharField(max_length=100, verbose_name='主技能')
    team_id = models.IntegerField(verbose_name='团队id')
    team_name = models.CharField(max_length=100, verbose_name='团队名称')
    p_team_id = models.IntegerField(verbose_name='大团队id')
    p_team_name = models.CharField(max_length=100, verbose_name='大团队名称')
    ret_creator = models.CharField(max_length=100, verbose_name='返回的创建人')
    ret_case_count = models.IntegerField(verbose_name='脚本数量')
    ret_assertion_count = models.IntegerField(verbose_name='案例数量')
    record_desc = models.CharField(max_length=255, verbose_name='记录说明')
    class Meta:
        db_table = 'auto_test_case_record'
        verbose_name = '新增测试集和验证集数量记录表'