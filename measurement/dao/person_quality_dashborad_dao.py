from django.db import connection

from mantis.settings import logger


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


def get_biz_flow_run_result(opt_user=None, opt_date=None, biz_flow_name=None, page_num=None, page_size=None,
                            total=None, is_person=None):
    sql = '''
            SELECT DISTINCT r.biz_flow_name, r.run_batch_number, r.suite_code, r.run_time {test_set_result_column}
            FROM test_flow_run_record r 
            INNER JOIN test_flow_app_deploy_info di ON di.run_batch_number = r.run_batch_number
            INNER JOIN test_flow_run_record_testset t ON t.run_batch_number = r.run_batch_number
            INNER JOIN test_flow_run_record_testset_result tr ON tr.t_id = t.id AND tr.app_name = di.app_name
            INNER JOIN devops_stakeholder_info dsi ON dsi.biz_flow_name = r.biz_flow_name
            INNER JOIN devops_app_team_owner_info toi ON tr.app_name = toi.app_name
            WHERE 1=1 {where_sql}
            
            UNION
            
            SELECT DISTINCT r.biz_flow_name, r.run_batch_number, r.suite_code, r.run_time {test_set_result_column}
            FROM test_flow_run_record r 
            INNER JOIN test_flow_app_deploy_info di ON di.run_batch_number = r.run_batch_number
            INNER JOIN test_flow_run_record_testset t ON t.run_batch_number = r.run_batch_number
            INNER JOIN test_flow_run_record_testset_result tr ON tr.t_id = t.id AND tr.app_name = di.app_name
            INNER JOIN devops_stakeholder_info dsi ON di.app_name = dsi.app_name AND di.app_deploy_branch = dsi.branch_name
            INNER JOIN devops_app_team_owner_info toi ON tr.app_name = toi.app_name
            WHERE 1=1 {where_sql} {order_sql}
          '''
    if opt_user and opt_date and biz_flow_name:
        sql = sql.format(
            where_sql=" AND r.run_time > '{opt_date}' AND r.biz_flow_name = '{biz_flow_name}' AND (dsi.opt_user = '{opt_user}' OR toi.team_owner = '{opt_user}') ".format(
                opt_date=opt_date,
                opt_user=opt_user,
                biz_flow_name=biz_flow_name), test_set_result_column='{test_set_result_column}',
            order_sql='{order_sql}')
    elif opt_date and opt_user:
        sql = sql.format(
            where_sql=" AND r.run_time > '{opt_date}' AND (dsi.opt_user = '{opt_user}' OR toi.team_owner = '{opt_user}') ".format(
                opt_date=opt_date, opt_user=opt_user), test_set_result_column='{test_set_result_column}',
            order_sql='{order_sql}')
    elif opt_date and biz_flow_name:
        sql = sql.format(where_sql=" AND r.run_time > '{opt_date}' AND r.biz_flow_name = '{biz_flow_name}' ".format(
            opt_date=opt_date, biz_flow_name=biz_flow_name), test_set_result_column='{test_set_result_column}',
            order_sql='{order_sql}')
    elif opt_user and biz_flow_name:
        sql = sql.format(
            where_sql=" AND r.biz_flow_name = '{biz_flow_name}' AND (dsi.opt_user = '{opt_user}' OR toi.team_owner = '{opt_user}') ".format(
                opt_user=opt_user, biz_flow_name=biz_flow_name), test_set_result_column='{test_set_result_column}',
            order_sql='{order_sql}')
    elif opt_user:
        sql = sql.format(
            where_sql=" AND (dsi.opt_user = '{opt_user}' OR toi.team_owner = '{opt_user}') ".format(opt_user=opt_user),
            test_set_result_column='{test_set_result_column}', order_sql='{order_sql}')
    elif opt_date:
        sql = sql.format(where_sql=" AND r.run_time > '{opt_date}' ".format(opt_date=opt_date),
                         test_set_result_column='{test_set_result_column}', order_sql='{order_sql}')
    elif biz_flow_name:
        sql = sql.format(where_sql=" AND r.biz_flow_name = '{biz_flow_name}' ".format(biz_flow_name=biz_flow_name),
                         test_set_result_column='{test_set_result_column}', order_sql='{order_sql}')

    if not is_person:
        sql = sql.format(
            test_set_result_column=''', CONCAT(t.exec_order, "-", t.testset_id) AS testset_id, tr.app_name, di.app_deploy_branch, CONCAT(tr.script_branch, " ( ", IFNULL(tr.version_mode, ""), " )") AS script_branch, tr.interface_pass_rate, tr.case_pass_rate, tr.start_time, tr.end_time, tr.duration, tr.result_url, t.exec_order''',
            order_sql=''' ORDER BY biz_flow_name, run_batch_number DESC, exec_order''')

    else:
        sql = sql.format(test_set_result_column='', order_sql=''' ORDER BY biz_flow_name, run_batch_number DESC''')

    cursor = connection.cursor()
    if total:
        cursor.execute("SELECT COUNT(*) FROM ({}) t".format(sql))
        total = cursor.fetchone()[0]
        logger.info("total={}".format(total))

    if page_num and page_size:
        sql = sql + " LIMIT {}, {}".format((page_num - 1) * page_size, page_size)

    cursor.execute(sql)
    logger.info("sql={}".format(sql))

    return dict_fetchall(cursor), total


def get_every_auto_test_result(opt_date):
    sql = '''
            SELECT DISTINCT si.biz_flow_name, si.suite_code, r.run_batch_number, r.run_time , 
                CONCAT(t.exec_order, '-', t.testset_id) AS testset_id, tr.app_name, di.app_deploy_branch, 
                CONCAT(tr.script_branch, " ( ", IFNULL(tr.version_mode, ""), " )") AS script_branch, 
                tr.interface_pass_rate, tr.case_pass_rate, tr.start_time, tr.end_time, tr.duration, tr.result_url, t.exec_order
            FROM test_flow_schedule_info si 
            LEFT JOIN test_flow_run_record r ON si.biz_flow_name = r.biz_flow_name AND si.suite_code = r.suite_code AND CONCAT(si.biz_code,'_',si.biz_iter_branch) = r.biz_iter_branch
            LEFT JOIN test_flow_run_record_testset t ON t.run_batch_number = r.run_batch_number
            LEFT JOIN test_flow_app_deploy_info di ON di.run_batch_number = r.run_batch_number
            INNER JOIN test_flow_run_record_testset_result tr ON tr.t_id = t.id AND tr.app_name = di.app_name
            WHERE si.is_active = 1 AND r.exec_action_type = 'flow_schedule' AND r.run_time > '{opt_date}'
            ORDER BY biz_flow_name, run_batch_number DESC, exec_order
          '''.format(opt_date=opt_date)
    logger.info("sql={}".format(sql))
    cursor = connection.cursor()
    cursor.execute(sql)
    data_list = dict_fetchall(cursor)
    return data_list


def get_biz_flow_run_result_by_batch_number(batch_number_list):
    sql = '''
            SELECT DISTINCT r.biz_flow_name, r.run_batch_number, r.suite_code, r.run_time, 
            CONCAT(t.exec_order, "-", t.testset_id) AS testset_id, tr.app_name, di.app_deploy_branch, 
            CONCAT(tr.script_branch, " ( ", IFNULL(tr.version_mode, ""), " )") AS script_branch, 
            tr.interface_pass_rate, tr.case_pass_rate, DATE_FORMAT(tr.start_time, '%Y-%m-%d %H:%i:%s') AS start_time, 
            DATE_FORMAT(tr.end_time, '%Y-%m-%d %H:%i:%s') AS end_time, tr.duration, tr.result_url, t.exec_order
            FROM test_flow_run_record r 
            INNER JOIN test_flow_app_deploy_info di ON di.run_batch_number = r.run_batch_number
            INNER JOIN test_flow_run_record_testset t ON t.run_batch_number = r.run_batch_number
            INNER JOIN test_flow_run_record_testset_result tr ON tr.t_id = t.id AND tr.app_name = di.app_name
            WHERE r.run_batch_number IN ('{}')
            ORDER BY run_batch_number, exec_order
          '''.format("','".join(batch_number_list))
    cursor = connection.cursor()
    cursor.execute(sql)
    data_list = dict_fetchall(cursor)
    return data_list


def get_run_batch_app_deploy_list(run_batch_number):
    sql = '''
            SELECT DISTINCT di.app_name, di.app_deploy_branch
            FROM test_flow_app_deploy_info di
            WHERE di.run_batch_number = '{run_batch_number}'
          '''.format(run_batch_number=run_batch_number)
    cursor = connection.cursor()
    cursor.execute(sql)
    data_list = dict_fetchall(cursor)
    return data_list

def get_iteration_linechart_data(opt_user=None, start_time=None, app_name=None, branch_name=None):
    sql = '''
            SELECT DISTINCT v.app_name, v.branch_name, SUBSTRING(rt.run_batch_number, 1, 14) AS run_batch_number, rt.testset_id, 
            CONCAT(rtr.script_branch, " ( ", IFNULL(rtr.version_mode, ""), " )") AS script_branch, 
            rtr.interface_pass_rate, IFNULL(rtr.case_pass_rate, 0) as case_pass_rate, rt.create_time 
            FROM test_flow_run_record_testset rt
            INNER JOIN test_flow_run_record_testset_result rtr ON rt.id = rtr.t_id
            INNER JOIN (
            SELECT adi.run_batch_number, dsi.app_name, dsi.branch_name, dsi.create_time FROM devops_stakeholder_info dsi
            INNER JOIN test_flow_app_deploy_info adi ON dsi.app_name = adi.app_name AND dsi.branch_name = adi.app_deploy_branch
            WHERE dsi.opt_user = '{opt_user}' AND dsi.app_name = '{app_name}' AND dsi.branch_name = '{branch_name}'
            ) v ON rt.run_batch_number = v.run_batch_number AND rtr.app_name = v.app_name
            WHERE rt.create_time > '{start_time}'
            ORDER BY rt.testset_id, rt.run_batch_number;
          '''.format(opt_user=opt_user, start_time=start_time, app_name=app_name, branch_name=branch_name)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info("sql={}".format(sql))
    return dict_fetchall(cursor)


def get_iteration_run_result(opt_user=None, start_time=None, page_num=None, page_size=None):
    sql = '''
            SELECT v.app_name, v.branch_name, rt.run_batch_number, rt.testset_id, 
            CONCAT(rtr.script_branch, " ( ", IFNULL(rtr.version_mode, ""), " )") AS script_branch, 
            rtr.interface_pass_rate, rtr.case_pass_rate 
            FROM test_flow_run_record_testset rt
            INNER JOIN test_flow_run_record_testset_result rtr ON rt.id = rtr.t_id
            INNER JOIN (
            SELECT MAX(adi.run_batch_number) AS max_batch_number, dsi.app_name, dsi.branch_name FROM devops_stakeholder_info dsi
            INNER JOIN test_flow_app_deploy_info adi ON dsi.app_name = adi.app_name AND dsi.branch_name = adi.app_deploy_branch
            WHERE dsi.opt_user = '{opt_user}'
            GROUP BY adi.app_name, adi.app_deploy_branch) v ON rt.run_batch_number = v.max_batch_number AND rtr.app_name = v.app_name
            where rt.create_time > '{start_time}'
            ORDER BY v.app_name, rt.run_batch_number 
          '''.format(opt_user=opt_user, start_time=start_time)
    cursor = connection.cursor()
    cursor.execute("SELECT COUNT(*) FROM ({}) t".format(sql))
    total = cursor.fetchone()[0] if cursor else 0
    logger.info("total={}".format(total))

    if page_num and page_size:
        sql = sql + " LIMIT {}, {}".format((page_num - 1) * page_size, page_size)

    cursor.execute(sql)
    logger.info("sql={}".format(sql))

    return dict_fetchall(cursor), total


def get_dev_effective_data(operator, start_time):
    sql = '''
            select round(sum(tapd_task_effort_completed)/total, 2) as exhausted
            from (
            SELECT distinct
                tds.project_id, 
                ds.tapd_story_id, sta.total, tet.tapd_entry_id as task_id, tet.tapd_task_effort_completed, 
                SUBSTRING_INDEX(tt.tapd_time_sheet_owner, '_', 1)  as owner
            FROM
                dev_effic_test_dev_submit tds
            inner join
                dev_effic_test_dev_submit_story tdss
            on tds.id = tdss.dev_submit_id
            inner join
                dev_effic_dev_story ds
            on ds.id = tdss.dev_story_id
            inner join
                tapd_entry_task tet
            on tet.tapd_task_story_id = ds.tapd_story_id
            and
            (
            trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_one, '（', 1)) in ('设计', '研发', '联调', '自测', 'code-review', '测试支持')
            or
            trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_two, '（', 1)) in ('设计', '研发', '联调', '自测', 'code-review', '测试支持')
            or
            trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_four, '（', 1)) in ('设计', '研发', '联调', '自测', 'code-review', '测试支持')
            or
            trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_eight, '（', 1)) in ('设计', '研发', '联调', '自测', 'code-review', '测试支持')
            )
            inner join
                tapd_entry_timesheet tt
            on tet.tapd_entry_id = tt.tapd_time_sheet_entity_id
            and tt.tapd_time_sheet_spentdate >= '{start_time}'
            and tt.tapd_time_sheet_spentdate < now()
            and tt.tapd_time_sheet_owner is not null
            inner join
            (
            select tdss.dev_story_id, count(0) as total from
                dev_effic_test_dev_submit_story tdss,
                dev_effic_test_dev_submit tds
            where tdss.dev_submit_id = tds.id and tds.tapd_test_plan_status != 0
            group by dev_story_id
            ) sta
            on sta.dev_story_id = tdss.dev_story_id
            where project_id is not null and tds.tapd_test_plan_status != 0
            AND SUBSTRING_INDEX(tt.tapd_time_sheet_owner, '_', 1) = '{operator}'
            ) t
          '''.format(start_time=start_time, operator=operator)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor.fetchone()[0]


def get_team_avg(operator, start_time):
    """根据人和数据，获取团队故事数的「均值」。zt@2024-10-21"""
    sql = '''
        SELECT AVG(team_v.exhausted) AS exhausted_avg
        FROM (
            select round(sum(tapd_task_effort_completed)/total, 2) as exhausted
            from (
                SELECT distinct
                    tds.project_id,
                    ds.tapd_story_id,
                    sta.total,
                    tet.tapd_entry_id as task_id,
                    tet.tapd_task_effort_completed,
                    SUBSTRING_INDEX(tt.tapd_time_sheet_owner, '_', 1)  as owner
                FROM dev_effic_test_dev_submit tds   -- 提测单（研发测试计划 --> 测试的devtest）
                inner join dev_effic_test_dev_submit_story tdss on tds.id = tdss.dev_submit_id   -- 研发测试计划中的故事
                inner join dev_effic_dev_story ds on ds.id = tdss.dev_story_id   -- 对应研发团队中的「故事」
                inner join tapd_entry_task tet on tet.tapd_task_story_id = ds.tapd_story_id  -- 故事中的任务
                    and (
                        trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_one, '（', 1)) in ('设计', '研发', '联调', '自测', 'code-review', '测试支持')
                        or
                        trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_two, '（', 1)) in ('设计', '研发', '联调', '自测', 'code-review', '测试支持')
                        or
                        trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_four, '（', 1)) in ('设计', '研发', '联调', '自测', 'code-review', '测试支持')
                        or
                        trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_eight, '（', 1)) in ('设计', '研发', '联调', '自测', 'code-review', '测试支持')
                    )
                inner join tapd_entry_timesheet tt on tet.tapd_entry_id = tt.tapd_time_sheet_entity_id   -- 任务耗时
                                                                 and tt.tapd_time_sheet_spentdate >= '{start_time}'
                                                                 and tt.tapd_time_sheet_spentdate < now()
                                                                 and tt.tapd_time_sheet_owner is not null
                inner join
                    (
                        select tdss.dev_story_id, count(0) as total
                        from dev_effic_test_dev_submit_story tdss,
                             dev_effic_test_dev_submit tds
                        where tdss.dev_submit_id = tds.id
                          and tds.tapd_test_plan_status != 0
                        group by dev_story_id
                    ) sta on sta.dev_story_id = tdss.dev_story_id   -- 一个故事会在多个提测单
                inner join team_mgt_user_info src_user on src_user.cn_name = SUBSTRING_INDEX(tt.tapd_time_sheet_owner, '_', 1)
                inner join team_mgt_user_info tgt_user on tgt_user.team_id = src_user.team_id
                where project_id is not null
                  and tds.tapd_test_plan_status != 0
                  AND tgt_user.cn_name = '{operator}'
            ) t GROUP BY t.owner
        )team_v
        '''.format(start_time=start_time, operator=operator)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor.fetchone()[0]


def get_team_median(operator, start_time):
    """根据人和数据，获取团队故事数的「中位数」。zt@2024-10-21"""
    sql = '''
        SELECT AVG(v.exhausted) AS exhausted_median
        FROM(
            select @ROW_NUMBER := @ROW_NUMBER + 1 as row_num,
                   team_v.exhausted,
                   T2.row_total
            from(
                select round(sum(tapd_task_effort_completed)/total, 2) as exhausted,
                       t.owner
                from (
                    SELECT distinct
                        tds.project_id,
                        ds.tapd_story_id,
                        sta.total,
                        tet.tapd_entry_id as task_id,
                        tet.tapd_task_effort_completed,
                        SUBSTRING_INDEX(tt.tapd_time_sheet_owner, '_', 1)  as owner
                    FROM dev_effic_test_dev_submit tds   -- 提测单（研发测试计划 --> 测试的devtest）
                    inner join dev_effic_test_dev_submit_story tdss on tds.id = tdss.dev_submit_id   -- 研发测试计划中的故事
                    inner join dev_effic_dev_story ds on ds.id = tdss.dev_story_id   -- 对应研发团队中的「故事」
                    inner join tapd_entry_task tet on tet.tapd_task_story_id = ds.tapd_story_id
                                                                 and(trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_one, '（', 1)) in ('设计', '研发', '联调', '自测', 'code-review', '测试支持')
                                                                         or trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_two, '（', 1)) in ('设计', '研发', '联调', '自测', 'code-review', '测试支持')
                                                                         or trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_four, '（', 1)) in ('设计', '研发', '联调', '自测', 'code-review', '测试支持')
                                                                         or trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_eight, '（', 1)) in ('设计', '研发', '联调', '自测', 'code-review', '测试支持'))
                    inner join tapd_entry_timesheet tt on tet.tapd_entry_id = tt.tapd_time_sheet_entity_id   -- 任务耗时
                                                                     and tt.tapd_time_sheet_spentdate >= '{start_time}'
                                                                     and tt.tapd_time_sheet_spentdate < now()
                                                                     and tt.tapd_time_sheet_owner is not null
                    inner join
                        (
                            select tdss.dev_story_id, count(0) as total
                            from dev_effic_test_dev_submit_story tdss,
                                 dev_effic_test_dev_submit tds
                            where tdss.dev_submit_id = tds.id
                              and tds.tapd_test_plan_status != 0
                            group by dev_story_id
                        ) sta on sta.dev_story_id = tdss.dev_story_id   -- 一个故事会在多个提测单
                    inner join team_mgt_user_info src_user on src_user.cn_name = SUBSTRING_INDEX(tt.tapd_time_sheet_owner, '_', 1)
                    inner join team_mgt_user_info tgt_user on tgt_user.team_id = src_user.team_id
                    where project_id is not null
                      and tds.tapd_test_plan_status != 0
                      AND tgt_user.cn_name = '{operator}'
                ) t GROUP BY t.owner
            )team_v,
                (select @ROW_NUMBER:=0)T1,
                (select @ROW_TOTAL:= COUNT(DISTINCT tgt_user.cn_name) AS row_total
                    from team_mgt_user_info src_user
                    inner join team_mgt_user_info tgt_user on tgt_user.team_id = src_user.team_id
                    inner join tapd_entry_timesheet timesheet on SUBSTRING_INDEX(timesheet.tapd_time_sheet_owner, '_', 1) = tgt_user.cn_name
                                                                     and timesheet.tapd_time_sheet_spentdate >= '{start_time}'
                                                                     and timesheet.tapd_time_sheet_spentdate < now()
                    inner join tapd_entry_task task on task.tapd_entry_id = timesheet.tapd_time_sheet_entity_id
                                                           and (trim(SUBSTRING_INDEX(task.tapd_task_custom_field_one, '（', 1)) in ('设计', '研发', '联调', '自测', 'code-review', '测试支持')
                                                                    or trim(SUBSTRING_INDEX(task.tapd_task_custom_field_two, '（', 1)) in ('设计', '研发', '联调', '自测', 'code-review', '测试支持')
                                                                    or trim(SUBSTRING_INDEX(task.tapd_task_custom_field_four, '（', 1)) in ('设计', '研发', '联调', '自测', 'code-review', '测试支持')
                                                                    or trim(SUBSTRING_INDEX(task.tapd_task_custom_field_eight, '（', 1)) in ('设计', '研发', '联调', '自测', 'code-review', '测试支持'))
                    inner join dev_effic_dev_story story on story.tapd_story_id = task.tapd_task_story_id
                    inner join dev_effic_test_dev_submit_story submit_story on submit_story.dev_story_id = story.id
                    inner join dev_effic_test_dev_submit submit on submit.id = submit_story.dev_submit_id
                                                                       and submit.project_id is not null
                                                                       and submit.tapd_test_plan_status != 0
                    where src_user.cn_name = '{operator}'
                )T2
            ORDER BY team_v.exhausted
        )v WHERE v.row_num IN (FLOOR((row_total+1)/2), FLOOR((row_total+2)/2))
        '''.format(start_time=start_time, operator=operator)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor.fetchone()[0]

def get_user_cn_name(operator):
    sql = '''
    SELECT cn_name FROM team_mgt_user_info WHERE user_name = '{operator}'
    '''.format(operator=operator)
    cursor = connection.cursor()
    cursor.execute(sql)
    result = cursor.fetchall()
    name = None
    for item in result:
        name = item[0]
        break
    return name