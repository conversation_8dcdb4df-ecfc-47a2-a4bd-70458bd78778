from django.db import connection

from mantis.settings import logger


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


def get_team_data_list_for_other(user_name):
    """根据人获取团队所有人，值为1的测试数据。zt@2024-10-28"""
    sql = '''
        select tgt_user.user_name,
           tgt_user.cn_name,
           tgt_user.main_skill,
           tgt_user.team_id,
           tgt_user.team_name,
           tgt_user.p_team_id,
           tgt_user.p_team_name,
           1 as user_value
        from team_mgt_user_info tgt_user
        inner join team_mgt_user_info src_user on src_user.p_team_id = tgt_user.p_team_id
        where tgt_user.bind_is_active = 1
          and src_user.user_name = '{user_name}'
        order by tgt_user.p_team_id, tgt_user.team_id, tgt_user.user_name, user_value;
        '''.format(user_name=user_name)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_person_data_list_for_other(user_name):
    """获取个人的考核季指标。zt@2024-12-30"""
    sql = '''
        select user.user_name,
               user.cn_name,
               user.main_skill,
               user.team_id,
               user.team_name,
               user.p_team_id,
               user.p_team_name,
               1 as user_val
        from team_mgt_user_info user
        where user.bind_is_active = 1
          and user.user_name = '{user_name}'
        order by user.p_team_id, user.team_id, user.user_name, user_val;
        '''.format(user_name=user_name)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_team_data_list_for_cost_time(cn_name, start_time):
    """根据人获取团队所有人，有效工时的合。zt@2024-10-28"""
    sql = '''
        select tgt_user.user_name,
               tgt_user.cn_name,
               tgt_user.main_skill,
               tgt_user.team_id,
               tgt_user.team_name,
               tgt_user.p_team_id,
               tgt_user.p_team_name,
               sum(timesheet.tapd_time_sheet_timespent) as user_value
        from tapd_entry_task task
        inner join dev_effic_code c100 on c100.code_type = 100 and c100.code_is_active = 1
                                             and (c100.code_val = trim(SUBSTRING_INDEX(task.tapd_task_custom_field_one, '（', 1))
                                                      or c100.code_val = trim(SUBSTRING_INDEX(task.tapd_task_custom_field_two, '（', 1))
                                                      or c100.code_val = trim(SUBSTRING_INDEX(task.tapd_task_custom_field_four, '（', 1))
                                                      or c100.code_val = trim(SUBSTRING_INDEX(task.tapd_task_custom_field_eight, '（', 1)))
        inner join tapd_entry_timesheet timesheet on timesheet.tapd_time_sheet_entity_id = task.tapd_entry_id
        inner join team_mgt_user_info tgt_user on tgt_user.cn_name = SUBSTRING_INDEX(timesheet.tapd_time_sheet_owner, '_', 1)
        inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
        where src_usr.cn_name = '{cn_name}'
        and timesheet.tapd_time_sheet_spentdate >= '{start_time}'
        and timesheet.tapd_time_sheet_spentdate < now()
        group by timesheet.tapd_time_sheet_owner, tgt_user.team_id, tgt_user.p_team_id, tgt_user.user_name
        order by tgt_user.p_team_id, tgt_user.team_id, tgt_user.user_name, user_value
        '''.format(cn_name=cn_name, start_time=start_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)

    return dict_fetchall(cursor)


def get_team_data_list_for_dev_delivery_time_with_biz(cn_name, start_time, end_time):
    """根据人获取团队所有人这段时间的交付任务工时合。zt@2024-12-11"""
    sql = '''
        select tgt_user.user_name,
               tgt_user.cn_name,
               tgt_user.main_skill,
               tgt_user.team_id,
               tgt_user.team_name,
               tgt_user.p_team_id,
               tgt_user.p_team_name,
               sum(dev_t.dev_task_timesheet_timespent) as user_value
        from dev_effic_dev_task dev_task
                 inner join dev_effic_code c100 on c100.code_type = 100
            and c100.code_is_active = 1
            and c100.code_val = trim(SUBSTRING_INDEX(dev_task.task_type, '（', 1))
                 inner join dev_effic_dev_task_timesheet dev_t on dev_t.dev_task_id = dev_task.tapd_task_id
                 inner join team_mgt_user_info tgt_user
                            on tgt_user.cn_name = SUBSTRING_INDEX(dev_t.dev_task_timesheet_owner, '_', 1)
                 inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
        where src_usr.cn_name = '{cn_name}'
          and dev_t.dev_task_timesheet_spentdate >= '{start_time}'
          and dev_t.dev_task_timesheet_spentdate < '{end_time}'
        group by dev_t.dev_task_timesheet_owner, tgt_user.team_id, tgt_user.p_team_id, tgt_user.user_name
        ;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)

    return dict_fetchall(cursor)


def get_person_data_list_for_dev_delivery_time_with_biz(cn_name, start_time, end_time):
    """获取个人的考核季指标--研发交付工时。zt@2024-12-30"""
    sql = '''
        select dev_user.user_name,
               dev_user.cn_name,
               dev_user.main_skill,
               dev_user.team_id,
               dev_user.team_name,
               dev_user.p_team_id,
               dev_user.p_team_name,
               sum(dev_t.dev_task_timesheet_timespent) as user_value
        from dev_effic_dev_task dev_task
                 inner join dev_effic_code c100 on c100.code_type = 100
            and c100.code_is_active = 1
            and c100.code_val = trim(SUBSTRING_INDEX(dev_task.task_type, '（', 1))
                 inner join dev_effic_dev_task_timesheet dev_t on dev_t.dev_task_id = dev_task.tapd_task_id
                 inner join team_mgt_user_info dev_user
                            on dev_user.cn_name = SUBSTRING_INDEX(dev_t.dev_task_timesheet_owner, '_', 1)
        where dev_user.cn_name = '{cn_name}'
          and dev_t.dev_task_timesheet_spentdate >= '{start_time}'
          and dev_t.dev_task_timesheet_spentdate < '{end_time}'
        group by dev_t.dev_task_timesheet_owner, dev_user.team_id, dev_user.p_team_id, dev_user.user_name
        ;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)

    return dict_fetchall(cursor)


def get_person_data_list_for_dev_test_delivery_time_percent(cn_name, start_time, end_time):
    """获取个人的考核季指标--研发测试交付时长比"""
    sql = """
        select test_user.user_name,
               test_user.cn_name,
               test_user.main_skill,
               test_user.team_id,
               test_user.team_name,
               test_user.p_team_id,
               test_user.p_team_name,
               round(sum(dev_time) / sum(effort_completed), 2) as user_value
        from (
            select
                r.ratio,
                round(
                        case when c.divided = 1
                            then JSON_EXTRACT(m_r.division_ratio, concat('$."', p_th.proc, '"') )*c.coe
                            else r.ratio
                            end, 2) as manual_ratio, -- 优先以填的比例为准
                c.coe,
                c.division_ratio,
                c.divided,
                tp.id,
                tp.actual_start,
                tp.actual_end,
                tp.name,
                tp.tapd_iteration_id,
                p_th.proc,
                ifnull(p_th.effort_completed, 0) as effort_completed,
                ifnull(
                        round(p_dt.t_exhausted*
                              case when c.divided = 1
                                  then JSON_EXTRACT(m_r.division_ratio, concat('$."', p_th.proc, '"') )*c.coe
                                  else r.ratio
                                  end , 0),
                        0) as dev_time, -- 已经分好了的研发时间
                tp.team,
                tp.business_system
            from dev_effic_test_project tp
                inner join dev_effic_test_report tr on tr.project_id = tp.id and tr.status = 'success'
                left outer join (
                    -- *****项目中测试人员投入工时（计算研测比的）***************
                    SELECT
                        project_id,
                        case when owner != '' and owner is not null
                            then owner
                            else creator
                            end as proc,
                        sum(
                                case when tt.task_type in ('自动化测试设计', '自动化案例开发')
                                    then round(ifnull(effort_completed, 0)/2, 2)
                                    else effort_completed
                                    end
                        ) as effort_completed
                    FROM
                        dev_effic_test_task tt
                    where tt.project_id is not null
                      and tt.effort_completed is not null
                      and tt.effort_completed > 0
                      and tt.task_type in ('知识图谱', '手工测试设计', '手工执行', '自动化测试设计', '自动化案例开发', '自动化执行')
                      and (owner  = '{cn_name}' or creator = '{cn_name}') -- 这里是变量
                    group by project_id, proc
                    -- ***************项目中测试人员投入工时（计算研测比的）*****
                    ) p_th on p_th.project_id = tp.id
                left outer join (
                    -- *****项目中研发投入时间***************
                    select
                        project_id,
                        sum(exhausted) as t_exhausted
                    from (
                        select
                            project_id,
                            tapd_story_id,
                            round(sum(tapd_task_effort_completed)/total, 2) as exhausted
                        from (
                            SELECT distinct
                                tds.project_id,
                                ds.tapd_story_id,
                                sta.total,
                                tet.tapd_entry_id as task_id,
                                tet.tapd_task_effort_completed
                            FROM dev_effic_test_dev_submit tds
                                inner join dev_effic_test_dev_submit_story tdss on tds.id = tdss.dev_submit_id
                                inner join dev_effic_dev_story ds on ds.id = tdss.dev_story_id
                                inner join tapd_entry_task tet on tet.tapd_task_story_id = ds.tapd_story_id
                                                                             and (
                                                                                 trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_one, '（', 1)) in ('设计', '研发', '测试支持', '自测', 'code-review')
                                                                                     or
                                                                                 trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_two, '（', 1)) in ('设计', '研发', '测试支持', '自测', 'code-review')
                                                                                     or
                                                                                 trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_four, '（', 1)) in ('设计', '研发', '测试支持', '自测', 'code-review')
                                                                                     or
                                                                                 trim(SUBSTRING_INDEX(tet.tapd_task_custom_field_eight, '（', 1)) in ('设计', '研发', '测试支持', '自测', 'code-review')
                                                                                 )
                                inner join (
                                    select
                                        tdss.dev_story_id,
                                        count(0) as total
                                    from dev_effic_test_dev_submit_story tdss, dev_effic_test_dev_submit tds
                                    where tdss.dev_submit_id = tds.id and tds.tapd_test_plan_status != 0
                                    group by dev_story_id
                                    ) sta on sta.dev_story_id = tdss.dev_story_id
                                inner join dev_effic_test_project p on p.id = tds.project_id
                                                                                  and p.actual_end > '{start_time}'
                                                                                  and p.actual_end < '{end_time}' -- 这里是变量
                            where project_id is not null and tds.tapd_test_plan_status != 0
                            ) t
                        group by project_id, tapd_story_id
                        ) t_t
                    group by project_id
                    -- ***************项目中研发投入时间*****
                    ) p_dt on p_dt.project_id = tp.id
                inner join  (
                    -- *****按参与人的分工比例***************
                    select
                        project_id,
                        round(1/count(0), 2) as ratio
                    from (
                        SELECT distinct
                            project_id,
                            case when tt.owner != '' and tt.owner is not null
                                then tt.owner
                                else tt.creator
                                end as proc
                        FROM dev_effic_test_task tt
                            inner join dev_effic_test_project p on p.id = tt.project_id
                                                                              and p.actual_end > '{start_time}'
                                                                              and p.actual_end < '{end_time}' -- 这里是变量
                        where tt.project_id is not null
                          and tt.effort_completed is not null
                          and tt.effort_completed > 0
                          and tt.task_type in ('自动化测试设计', '自动化案例开发', '自动化执行', '知识图谱', '手工测试设计', '手工执行', '灰度环境测试')
                        ) d
                    group by project_id
                    -- ***************按参与人的分工比例*****
                    ) r on tp.id = r.project_id
                left outer join (
                    -- *****是否有手填的分工比例（优先以手填为准）***************
                    SELECT
                        case when task_division_ratio like '{{%' and JSON_VALID( replace( replace( task_division_ratio, '：', ':'), '，', ',') ) = 1
                            then 1
                            else 0
                            end as divided,
                        replace( replace( task_division_ratio, '：', ':'), '，', ',') as division_ratio,
                        p.id
                    FROM dev_effic_test_project p
                    where p.actual_end > '{start_time}'
                      and p.actual_end < '{end_time}' -- 这里是变量
                    -- ***************是否有手填的分工比例（优先以手填为准）*****
                    ) m_r on m_r.id = tp.id and division_ratio like concat('%', p_th.proc, '%')
                left outer join (
                    -- *****计算出有比例的真实比例（因为有些填的不足100%）***************
                    select
                        id as project_id,
                        divided,
                        division_ratio,
                        -- ==把json中每一个人的比例取出加总后计算整体比例======
                        1/sum(JSON_EXTRACT(division_ratio,
                                           concat('$.',
                                                  JSON_EXTRACT(
                                                          JSON_KEYS(division_ratio),
                                                          concat('$[', se.`number` - 1, ']')
                                                  )
                                           )
                              )
                          ) as coe
                        -- ======把json中每一个人的比例取出加总后计算整体比例==
                    from (
                        SELECT
                            case when task_division_ratio like '{{%' and JSON_VALID( replace( replace( task_division_ratio, '：', ':'), '，', ',') ) = 1
                                then 1
                                else 0
                                end as divided,
                            replace( replace( task_division_ratio, '：', ':'), '，', ',') as division_ratio,
                            p.id
                        FROM dev_effic_test_project p
                        where p.actual_end > '{start_time}'
                          and p.actual_end < '{end_time}' -- 这里是变量
                        ) d
                        inner join sequence se on se.`number` <= JSON_LENGTH(division_ratio) and divided = 1
                    group by id
                    -- ***************计算出有比例的真实比例（因为有些填的不足100%）*****
                    ) c on c.project_id = tp.id
            where tp.actual_end > '{start_time}'
              and tp.actual_end < '{end_time}'
              and proc = '{cn_name}' -- 这里是变量
              and tp.id not in (
                select
                    code201.code_val
                from dev_effic_code code201
                where code201.code_type = 201)
            ) data
            inner join team_mgt_user_info test_user on test_user.cn_name = data.proc
        ;
    """.format(cn_name=cn_name, start_time=start_time, end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)

    return dict_fetchall(cursor)


def get_team_data_list_for_total_time(cn_name, start_time):
    """根据人获取团队所有人，总工时的合。zt@2024-10-28"""
    sql = '''
        select tgt_user.user_name,
               tgt_user.cn_name,
               tgt_user.main_skill,
               tgt_user.team_id,
               tgt_user.team_name,
               tgt_user.p_team_id,
               tgt_user.p_team_name,
               sum(timesheet.tapd_time_sheet_timespent) as user_value
        from tapd_entry_task task
        inner join tapd_entry_timesheet timesheet on timesheet.tapd_time_sheet_entity_id = task.tapd_entry_id
        inner join team_mgt_user_info tgt_user on tgt_user.cn_name = SUBSTRING_INDEX(timesheet.tapd_time_sheet_owner, '_', 1)
        inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
        where src_usr.cn_name = '{cn_name}'
        and timesheet.tapd_time_sheet_spentdate >= '{start_time}'
        and timesheet.tapd_time_sheet_spentdate < now()
        group by timesheet.tapd_time_sheet_owner, tgt_user.team_id, tgt_user.p_team_id, tgt_user.user_name
        order by tgt_user.p_team_id, tgt_user.team_id, tgt_user.user_name, user_value
        '''.format(cn_name=cn_name, start_time=start_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    return dict_fetchall(cursor)


def get_team_data_list_for_total_time_with_biz(cn_name, start_time, end_time):
    """根据人获取团队所有人，总工时的合。zt@2024-10-28"""
    sql = '''
        select
            dev_tu.user_name,
            dev_tu.cn_name,
            dev_tu.main_skill,
            dev_tu.team_id,
            dev_tu.team_name,
            dev_tu.p_team_id,
            dev_tu.p_team_name,
            sum(dev_t.tapd_time_sheet_timespent) as user_value
        from dev_effic_dev_task dev_task
        inner join tapd_entry_timesheet dev_t on dev_t.tapd_time_sheet_entity_id = dev_task.tapd_task_id
                                                     and UPPER(dev_t.tapd_time_sheet_entity_type) = 'TASK'
        inner join team_mgt_user_info dev_tu on dev_tu.cn_name = SUBSTRING_INDEX(dev_t.tapd_time_sheet_owner, '_', 1)
        inner join team_mgt_user_info dev_su on dev_su.p_team_id = dev_tu.p_team_id
        where dev_su.cn_name = '{cn_name}'
        and dev_t.tapd_time_sheet_spentdate >= '{start_time}'
        and dev_t.tapd_time_sheet_spentdate < '{end_time}'
        group by
            dev_t.tapd_time_sheet_owner, dev_tu.team_id, dev_tu.p_team_id, dev_tu.user_name
        union all
        select
            test_tu.user_name,
            test_tu.cn_name,
            test_tu.main_skill,
            test_tu.team_id,
            test_tu.team_name,
            test_tu.p_team_id,
            test_tu.p_team_name,
            sum(test_t.tapd_time_sheet_timespent) as user_value
        from dev_effic_test_task test_task
        inner join dev_effic_code c200 on c200.code_type = 200
                                              and c200.code_is_active = 1
                                              and c200.code_val = trim(SUBSTRING_INDEX(test_task.task_type, '（', 1))
        inner join tapd_entry_timesheet test_t on test_t.tapd_time_sheet_entity_id = test_task.tapd_task_id
                                                      and UPPER(test_t.tapd_time_sheet_entity_type) = 'TASK'
        inner join team_mgt_user_info test_tu on test_tu.cn_name = SUBSTRING_INDEX(test_t.tapd_time_sheet_owner, '_', 1)
        inner join team_mgt_user_info test_su on test_su.p_team_id = test_tu.p_team_id
        inner join dev_effic_test_project test_p on test_p.id = test_task.project_id
        where test_su.cn_name = '{cn_name}'
        and test_t.tapd_time_sheet_spentdate >= '{start_time}'
        and test_t.tapd_time_sheet_spentdate < '{end_time}'
        group by
            test_t.tapd_time_sheet_owner, test_tu.team_id, test_tu.p_team_id, test_tu.user_name
        ;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    return dict_fetchall(cursor)


def get_team_data_list_for_dev_total_time_with_biz(cn_name, start_time, end_time):
    """根据开发人员获取开发团队所有人，总工时的合。zt@2024-12-27"""
    sql = '''
        select tgt_user.user_name,
               tgt_user.cn_name,
               tgt_user.main_skill,
               tgt_user.team_id,
               tgt_user.team_name,
               tgt_user.p_team_id,
               tgt_user.p_team_name,
               sum(dev_t.dev_task_timesheet_timespent) as user_value
        from dev_effic_dev_task dev_task
                 inner join dev_effic_dev_task_timesheet dev_t on dev_t.dev_task_id = dev_task.tapd_task_id
                 inner join team_mgt_user_info tgt_user
                            on tgt_user.cn_name = SUBSTRING_INDEX(dev_t.dev_task_timesheet_owner, '_', 1)
                 inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
        where src_usr.cn_name = '{cn_name}'
          and dev_t.dev_task_timesheet_spentdate >= '{start_time}'
          and dev_t.dev_task_timesheet_spentdate < '{end_time}'
        group by dev_t.dev_task_timesheet_owner, tgt_user.team_id, tgt_user.p_team_id, tgt_user.user_name
        ;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    return dict_fetchall(cursor)


def get_person_data_list_for_dev_total_time_with_biz(cn_name, start_time, end_time):
    """获取个人的考核季指标--研发填写总工时。zt@2024-12-30"""
    sql = '''
        select dev_user.user_name,
               dev_user.cn_name,
               dev_user.main_skill,
               dev_user.team_id,
               dev_user.team_name,
               dev_user.p_team_id,
               dev_user.p_team_name,
               sum(dev_t.dev_task_timesheet_timespent) as user_value
        from dev_effic_dev_task dev_task
                 inner join dev_effic_dev_task_timesheet dev_t on dev_t.dev_task_id = dev_task.tapd_task_id
                 inner join team_mgt_user_info dev_user
                            on dev_user.cn_name = SUBSTRING_INDEX(dev_t.dev_task_timesheet_owner, '_', 1)
        where dev_user.cn_name = '{cn_name}'
          and dev_t.dev_task_timesheet_spentdate >= '{start_time}'
          and dev_t.dev_task_timesheet_spentdate < '{end_time}'
        group by dev_t.dev_task_timesheet_owner, dev_user.team_id, dev_user.p_team_id, dev_user.user_name
        ;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    return dict_fetchall(cursor)


def get_team_data_list_for_test_total_time_with_biz(cn_name, start_time, end_time):
    """根据测试人员获取测试团队所有人，总工时的合。zt@2024-12-27"""
    sql = '''
        select tgt_user.user_name,
               tgt_user.cn_name,
               tgt_user.main_skill,
               tgt_user.team_id,
               tgt_user.team_name,
               tgt_user.p_team_id,
               tgt_user.p_team_name,
               sum(test_t.test_task_timesheet_timespent) as user_value
        from dev_effic_test_task test_task
                 inner join dev_effic_test_task_timesheet test_t on test_t.test_task_id = test_task.tapd_task_id
                 inner join team_mgt_user_info tgt_user
                            on tgt_user.cn_name = SUBSTRING_INDEX(test_t.test_task_timesheet_owner, '_', 1)
                 inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
                 inner join dev_effic_test_project test_p on test_p.id = test_task.project_id
        where src_usr.cn_name = '{cn_name}'
          and test_t.test_task_timesheet_spentdate >= '{start_time}'
          and test_t.test_task_timesheet_spentdate < '{end_time}'
        group by test_t.test_task_timesheet_owner, tgt_user.team_id, tgt_user.p_team_id, tgt_user.user_name
        ;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    return dict_fetchall(cursor)


def get_person_data_list_for_test_total_time_with_biz(cn_name, start_time, end_time):
    """获取个人的考核季指标--测试填写总工时。zt@2024-12-30"""
    sql = '''
        select test_user.user_name,
               test_user.cn_name,
               test_user.main_skill,
               test_user.team_id,
               test_user.team_name,
               test_user.p_team_id,
               test_user.p_team_name,
               sum(test_t.test_task_timesheet_timespent) as user_value
        from dev_effic_test_task test_task
                 inner join dev_effic_test_task_timesheet test_t on test_t.test_task_id = test_task.tapd_task_id
                 inner join team_mgt_user_info test_user
                            on test_user.cn_name = SUBSTRING_INDEX(test_t.test_task_timesheet_owner, '_', 1)
                 inner join dev_effic_test_project test_p on test_p.id = test_task.project_id
        where test_user.cn_name = '{cn_name}'
          and test_t.test_task_timesheet_spentdate >= '{start_time}'
          and test_t.test_task_timesheet_spentdate < '{end_time}'
        group by test_t.test_task_timesheet_owner, test_user.team_id, test_user.p_team_id, test_user.user_name
        ;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    return dict_fetchall(cursor)


def get_team_data_list_for_test_time(cn_name, start_time):
    """根据人获取团队所有人，测试工时的合。zt@2024-10-29"""
    sql = '''
        select tgt_user.user_name,
               tgt_user.cn_name,
               tgt_user.main_skill,
               tgt_user.team_id,
               tgt_user.team_name,
               tgt_user.p_team_id,
               tgt_user.p_team_name,
               sum(timesheet.tapd_time_sheet_timespent) as user_value
        from tapd_entry_task task
        inner join dev_effic_code c200 on c200.code_type = 200 and c200.code_is_active = 1
                                             and c200.code_val = trim(SUBSTRING_INDEX(task.tapd_task_custom_field_two, '（', 1))
        inner join tapd_entry_timesheet timesheet on timesheet.tapd_time_sheet_entity_id = task.tapd_entry_id
        inner join team_mgt_user_info tgt_user on tgt_user.cn_name = SUBSTRING_INDEX(timesheet.tapd_time_sheet_owner, '_', 1)
        inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
        left join tapd_entry_story story on story.tapd_entry_id = task.tapd_task_story_id
        left join tapd_entry_iteration iter on iter.tapd_entry_id = story.tapd_story_iteration_id or iter.tapd_entry_id = task.tapd_task_iteration_id
        where src_usr.cn_name = '{cn_name}'
        and timesheet.tapd_time_sheet_spentdate >= '{start_time}'
        and timesheet.tapd_time_sheet_spentdate < now()
        and (story.id is not null or iter.id is not null)
        group by timesheet.tapd_time_sheet_owner, tgt_user.team_id, tgt_user.p_team_id, tgt_user.user_name
        order by tgt_user.p_team_id, tgt_user.team_id, tgt_user.user_name, user_value;
        '''.format(cn_name=cn_name, start_time=start_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    return dict_fetchall(cursor)


def get_team_data_list_for_test_delivery_time_with_biz(cn_name, start_time, end_time):
    """根据人获取团队所有人这段时间的测试交付任务工时合。zt@2024-12-11"""
    sql = '''
        select tgt_user.user_name,
               tgt_user.cn_name,
               tgt_user.main_skill,
               tgt_user.team_id,
               tgt_user.team_name,
               tgt_user.p_team_id,
               tgt_user.p_team_name,
               sum(test_t.test_task_timesheet_timespent) as user_value
        from dev_effic_test_task test_task
                 inner join dev_effic_code c200 on c200.code_type = 200
            and c200.code_is_active = 1
            and c200.code_val = trim(SUBSTRING_INDEX(test_task.task_type, '（', 1))
                 inner join dev_effic_test_task_timesheet test_t on test_t.test_task_id = test_task.tapd_task_id
                 inner join team_mgt_user_info tgt_user
                            on tgt_user.cn_name = SUBSTRING_INDEX(test_t.test_task_timesheet_owner, '_', 1)
                 inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
                 inner join dev_effic_test_project test_p on test_p.id = test_task.project_id
        where src_usr.cn_name = '{cn_name}'
          and test_t.test_task_timesheet_spentdate >= '{start_time}'
          and test_t.test_task_timesheet_spentdate < '{end_time}'
        group by test_t.test_task_timesheet_owner, tgt_user.team_id, tgt_user.p_team_id, tgt_user.user_name
        ;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    return dict_fetchall(cursor)


def get_person_data_list_for_test_delivery_time_with_biz(cn_name, start_time, end_time):
    """获取个人的考核季指标--测试交付工时。zt@2024-12-30"""
    sql = '''
        select test_user.user_name,
               test_user.cn_name,
               test_user.main_skill,
               test_user.team_id,
               test_user.team_name,
               test_user.p_team_id,
               test_user.p_team_name,
               sum(test_t.test_task_timesheet_timespent) as user_value
        from dev_effic_test_task test_task
                 inner join dev_effic_code c200 on c200.code_type = 200
            and c200.code_is_active = 1
            and c200.code_val = trim(SUBSTRING_INDEX(test_task.task_type, '（', 1))
                 inner join dev_effic_test_task_timesheet test_t on test_t.test_task_id = test_task.tapd_task_id
                 inner join team_mgt_user_info test_user
                            on test_user.cn_name = SUBSTRING_INDEX(test_t.test_task_timesheet_owner, '_', 1)
                 inner join dev_effic_test_project test_p on test_p.id = test_task.project_id
        where test_user.cn_name = '{cn_name}'
          and test_t.test_task_timesheet_spentdate >= '{start_time}'
          and test_t.test_task_timesheet_spentdate < '{end_time}'
        group by test_t.test_task_timesheet_owner, test_user.team_id, test_user.p_team_id, test_user.user_name
        ;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    logger.info(sql)
    return dict_fetchall(cursor)


# @SimpleCache(expire_seconds=60 * 60 * 24 * 7)
def get_team_data_list_for_code_line(cn_name, start_time, end_time):
    """根据人获取团队所有人，代码行数的合。zt@2024-10-30"""
    sql = '''
        select tgt_user.user_name,
               tgt_user.cn_name,
               tgt_user.main_skill,
               tgt_user.team_id,
               tgt_user.team_name,
               tgt_user.p_team_id,
               tgt_user.p_team_name,
               sum(code_line.add_counts_effect) as user_value
        from code_stat_git_commits_diff_stat code_line
        inner join team_mgt_user_info tgt_user on tgt_user.cn_name = code_line.username
        inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
        where src_usr.cn_name = '{cn_name}'
        and code_line.p_datetime >= '{start_time}'
        and code_line.p_datetime < '{end_time}'
        group by tgt_user.p_team_id, tgt_user.team_id, tgt_user.user_name
        order by tgt_user.p_team_id, tgt_user.team_id, user_value;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_person_data_list_for_code_line(cn_name, start_time, end_time):
    """获取个人的考核季指标--研发代码行数。zt@2024-12-30"""
    sql = '''
        select dev_user.user_name,
               dev_user.cn_name,
               dev_user.main_skill,
               dev_user.team_id,
               dev_user.team_name,
               dev_user.p_team_id,
               dev_user.p_team_name,
               sum(code_line.add_counts_effect) as user_value
        from code_stat_git_commits_diff_stat code_line
        inner join team_mgt_user_info dev_user on dev_user.cn_name = code_line.username
        where dev_user.cn_name = '{cn_name}'
        and code_line.p_datetime >= '{start_time}'
        and code_line.p_datetime < '{end_time}'
        group by dev_user.p_team_id, dev_user.team_id, dev_user.user_name
        order by dev_user.p_team_id, dev_user.team_id, user_value;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_team_data_list_for_auto_test(cn_name, start_time, end_time):
    sql = '''
        select tgt_user.user_name,
               tgt_user.cn_name,
               tgt_user.main_skill,
               tgt_user.team_id,
               tgt_user.team_name,
               tgt_user.p_team_id,
               tgt_user.p_team_name,
               SUM(sr.case_number) as user_value
        from auto_test_statistics_record sr
        inner join team_mgt_user_info tgt_user on tgt_user.cn_name = sr.user_name
        inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
        where src_usr.cn_name = '{cn_name}'
        and sr.opt_time between '{start_time}' and '{end_time}'
        group by tgt_user.p_team_id, tgt_user.team_id, tgt_user.user_name
        order by tgt_user.p_team_id, tgt_user.team_id, user_value;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_person_data_list_for_auto_test(cn_name, start_time, end_time):
    """获取个人的考核季指标--自动化测试用例数。zt@2024-12-30"""
    sql = '''
        select test_user.user_name,
               test_user.cn_name,
               test_user.main_skill,
               test_user.team_id,
               test_user.team_name,
               test_user.p_team_id,
               test_user.p_team_name,
               SUM(sr.case_number) as user_value
        from auto_test_statistics_record sr
        inner join team_mgt_user_info test_user on test_user.cn_name = sr.user_name
#         inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
        where test_user.cn_name = '{cn_name}'
        and sr.opt_time between '{start_time}' and '{end_time}'
        group by test_user.p_team_id, test_user.team_id, test_user.user_name
        order by test_user.p_team_id, test_user.team_id, user_value;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_team_data_list_for_auto_test_case(cn_name, time_cycle_code, start_time, end_time):
    """获取测试人员所在团队所有人的自动化测试「案例」数。zt@2025-04-25"""
    sql = '''
        select tgt_user.user_name,
               tgt_user.cn_name,
               tgt_user.main_skill,
               tgt_user.team_id,
               tgt_user.team_name,
               tgt_user.p_team_id,
               tgt_user.p_team_name,
               SUM(r.ret_case_count) as user_value
        from auto_test_case_record r
            inner join(
                select time_batch,
                       max(batch_number) as max_batch_num
                from auto_test_case_record
                where time_batch = (
                    select max(time_batch)
                    from auto_test_case_record)
                ) v_max on (v_max.time_batch, v_max.max_batch_num) = (r.time_batch, r.batch_number)
            inner join team_mgt_user_info tgt_user on tgt_user.cn_name = r.cn_name
            inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
        where src_usr.cn_name = '{cn_name}'
          and r.time_cycle_enum = '{time_cycle_enum}'
        group by tgt_user.p_team_id,
                 tgt_user.team_id,
                 tgt_user.user_name
        order by tgt_user.p_team_id,
                 tgt_user.team_id,
                 user_value;
        '''.format(cn_name=cn_name,
                   time_cycle_enum=time_cycle_code)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_person_data_list_for_auto_test_case(cn_name, time_cycle_code, start_time, end_time):
    """获取测试人员个人的自动化测试「案例」数。zt@2025-04-25"""
    sql = '''
        select test_user.user_name,
               test_user.cn_name,
               test_user.main_skill,
               test_user.team_id,
               test_user.team_name,
               test_user.p_team_id,
               test_user.p_team_name,
               SUM(r.ret_case_count) as user_value
        from auto_test_case_record r
            inner join(
                select time_batch,
                       max(batch_number) as max_batch_num
                from auto_test_case_record
                where time_batch = (
                    select max(time_batch)
                    from auto_test_case_record)
                ) v_max on (v_max.time_batch, v_max.max_batch_num) = (r.time_batch, r.batch_number)
            inner join team_mgt_user_info test_user on test_user.cn_name = r.cn_name
        #     inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
        where test_user.cn_name = '{cn_name}'
          and r.time_cycle_enum = '{time_cycle_enum}'
        group by test_user.p_team_id,
                 test_user.team_id,
                 test_user.user_name
        order by test_user.p_team_id,
                 test_user.team_id,
                 user_value;
        '''.format(cn_name=cn_name,
                   time_cycle_enum=time_cycle_code)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_team_data_list_for_auto_test_assertion(cn_name, time_cycle_code, start_time, end_time):
    """获取测试人员所在团队所有人的自动化测试「验证集」数。zt@2025-04-25"""
    sql = '''
        select tgt_user.user_name,
               tgt_user.cn_name,
               tgt_user.main_skill,
               tgt_user.team_id,
               tgt_user.team_name,
               tgt_user.p_team_id,
               tgt_user.p_team_name,
               SUM(r.ret_assertion_count) as user_value
        from auto_test_case_record r
            inner join(
                select time_batch,
                       max(batch_number) as max_batch_num
                from auto_test_case_record
                where time_batch = (
                    select max(time_batch)
                    from auto_test_case_record)
                ) v_max on (v_max.time_batch, v_max.max_batch_num) = (r.time_batch, r.batch_number)
            inner join team_mgt_user_info tgt_user on tgt_user.cn_name = r.cn_name
            inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
        where src_usr.cn_name = '{cn_name}'
          and r.time_cycle_enum = '{time_cycle_enum}'
        group by tgt_user.p_team_id,
                 tgt_user.team_id,
                 tgt_user.user_name
        order by tgt_user.p_team_id,
                 tgt_user.team_id,
                 user_value;
        '''.format(cn_name=cn_name,
                   time_cycle_enum=time_cycle_code)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_person_data_list_for_auto_test_assertion(cn_name, time_cycle_code, start_time, end_time):
    """获取测试人员个人的自动化测试「验证集」数。zt@2025-04-25"""
    sql = '''
        select test_user.user_name,
               test_user.cn_name,
               test_user.main_skill,
               test_user.team_id,
               test_user.team_name,
               test_user.p_team_id,
               test_user.p_team_name,
               SUM(r.ret_assertion_count) as user_value
        from auto_test_case_record r
            inner join(
                select time_batch,
                       max(batch_number) as max_batch_num
                from auto_test_case_record
                where time_batch = (
                    select max(time_batch)
                    from auto_test_case_record)
                ) v_max on (v_max.time_batch, v_max.max_batch_num) = (r.time_batch, r.batch_number)
            inner join team_mgt_user_info test_user on test_user.cn_name = r.cn_name
        #     inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
        where test_user.cn_name = '{cn_name}'
          and r.time_cycle_enum = '{time_cycle_enum}'
        group by test_user.p_team_id,
                 test_user.team_id,
                 test_user.user_name
        order by test_user.p_team_id,
                 test_user.team_id,
                 user_value;
        '''.format(cn_name=cn_name,
                   time_cycle_enum=time_cycle_code)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_person_data_list_for_project_quality_inferior(cn_name, start_time, end_time):
    """获取个人的考核季指标--参与项目质量差数 (日均bug超标项目)"""
    sql = '''
        SELECT 
                m.user_name,
                m.cn_name,
                m.main_skill,
                m.team_id,
                m.team_name,
                m.p_team_id,
                m.p_team_name,
                m.id,
                m.name,
                CASE
                WHEN(m.work_hours > 40) THEN ROUND((m.bug_num/(m.work_hours - 8) * 8),3)
                ELSE ROUND((m.bug_num / m.work_hours),3)
                END AS user_value
         FROM (
                SELECT  
                        tgt_user.user_name,
                        tgt_user.cn_name,
                        tgt_user.main_skill,
                        tgt_user.team_id,
                        tgt_user.team_name,
                        tgt_user.p_team_id,
                        tgt_user.p_team_name,
                        tp.id,
                        tp.name,
                        tp.work_hours,
                        COUNT( DISTINCT tb.id) as bug_num 
                FROM dev_effic_test_project tp 
                INNER JOIN dev_effic_test_bug tb on tp.id = tb.project_id
                INNER JOIN (
                    SELECT DISTINCT * FROM (
                                SELECT DISTINCT a.id 
                                FROM dev_effic_test_project a 
                                INNER JOIN (
                                    SELECT DISTINCT etp.id 
                                    FROM dev_effic_test_project etp
                                    LEFT JOIN dev_effic_test_dev_submit ds on etp.id = ds.project_id
                                    LEFT JOIN dev_effic_test_dev_submit_story ss ON ds.id = ss.dev_submit_id
                                    LEFT JOIN dev_effic_dev_story eds on eds.id = ss.dev_story_id
                                    WHERE eds.`owner` = '{cn_name}'
                                ) b ON a.id = b.id
                                INNER JOIN dev_effic_test_bug c ON a.id = c.project_id
        
                                UNION
                                SELECT DISTINCT e.id FROM dev_effic_test_project e 
                                LEFT JOIN dev_effic_test_bug f ON e.id = f.project_id
                                WHERE f.fixer = '{cn_name}'
                        )as m
                ) as n on n.id = tp.id
                inner join team_mgt_user_info tgt_user on tgt_user.cn_name = '{cn_name}'
                WHERE tp.actual_end BETWEEN '{start_time}' AND '{end_time}'
                AND tb.`status` != 'rejected'
                group by tgt_user.p_team_id, tgt_user.team_id, tgt_user.user_name,tp.id
                order by tgt_user.p_team_id, tgt_user.team_id, bug_num
        ) as m;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_team_data_list_for_project_quality_inferior(cn_name, start_time, end_time):
    """获取个人的考核季指标--参与项目质量差数 (日均bug超标项目)"""
    sql = '''
        SELECT DISTINCT
                r.user_name,
                r.cn_name,
                r.main_skill,
                r.team_id,
                r.team_name,
                r.p_team_id,
                r.p_team_name,
                r.id,
                r.name,
                CASE
                WHEN(r.work_hours > 40) THEN ROUND((r.bug_num/(r.work_hours - 8) * 8),3)
                ELSE ROUND((r.bug_num / r.work_hours),3)
                END AS user_value
         FROM (
                SELECT  DISTINCT
                        tgt_user.user_name,
                        tgt_user.cn_name,
                        tgt_user.main_skill,
                        tgt_user.team_id,
                        tgt_user.team_name,
                        tgt_user.p_team_id,
                        tgt_user.p_team_name,
                        tp.id,
                        tp.name,
                        tp.work_hours,
                        COUNT( DISTINCT tb.id) as bug_num 
                FROM dev_effic_test_project tp 
                INNER JOIN dev_effic_test_bug tb on tp.id = tb.project_id
                INNER JOIN (
                    SELECT DISTINCT m.project_id FROM (
                        SELECT DISTINCT 
                                 tgt_user.user_name,
                                 tgt_user.cn_name,
                                 tgt_user.main_skill,
                                 tgt_user.team_id,
                                 tgt_user.team_name,
                                 tgt_user.p_team_id,
                                 tgt_user.p_team_name,
                                 bug.project_id 
                        FROM dev_effic_test_bug bug
                        LEFT JOIN dev_effic_test_dev_submit ds on bug.project_id = ds.project_id
                        LEFT JOIN dev_effic_test_dev_submit_story ss ON ds.id = ss.dev_submit_id
                        LEFT JOIN dev_effic_dev_story eds on eds.id = ss.dev_story_id
                        INNER JOIN team_mgt_user_info tgt_user on tgt_user.cn_name = eds.`owner`
                        INNER JOIN team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
                        WHERE src_usr.cn_name = '{cn_name}'
                        and src_usr.bind_is_active = 1
                        group by tgt_user.p_team_id, tgt_user.team_id, tgt_user.user_name
                
                        UNION
                        select distinct 
                                         tgt_user.user_name,
                                         tgt_user.cn_name,
                                         tgt_user.main_skill,
                                         tgt_user.team_id,
                                         tgt_user.team_name,
                                         tgt_user.p_team_id,
                                         tgt_user.p_team_name,
                                         bug.project_id
                        from dev_effic_test_bug bug
                        inner join team_mgt_user_info tgt_user on tgt_user.cn_name = bug.fixer
                        inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
                        where src_usr.cn_name = '{cn_name}'
                        and src_usr.bind_is_active = 1
                        group by tgt_user.p_team_id, tgt_user.team_id, tgt_user.user_name
                
                    )as m
                ) as n on n.project_id = tp.id
                inner join team_mgt_user_info tgt_user on tgt_user.cn_name = '{cn_name}'
                inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
                WHERE tp.status = 'resolved'
                AND tp.actual_end BETWEEN '{start_time}' AND '{end_time}'
                AND tb.`status` != 'rejected'
                AND src_usr.bind_is_active = 1
                group by src_usr.p_team_id, src_usr.team_id, src_usr.user_name, tp.id
                order by src_usr.p_team_id, src_usr.team_id, bug_num
        ) as r;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_person_data_list_for_project_every_day_bug(cn_name, start_time, end_time):
    """获取个人的考核季指标--参与项目日均检出bug数 """
    sql = '''
            SELECT
                tgt_user.user_name,
                tgt_user.cn_name,
                tgt_user.main_skill,
                tgt_user.team_id,
                tgt_user.team_name,
                tgt_user.p_team_id,
                tgt_user.p_team_name,
                IFNULL(COUNT( DISTINCT tb.id), 0) as user_value 
            FROM dev_effic_test_project tp
            INNER JOIN dev_effic_test_bug tb on tp.id = tb.project_id
            INNER JOIN (
                SELECT DISTINCT * FROM (
                    SELECT DISTINCT a.id 
                    FROM dev_effic_test_project a 
                    INNER JOIN (
                        SELECT DISTINCT etp.id 
                        FROM dev_effic_test_project etp
                        LEFT JOIN dev_effic_test_dev_submit ds on etp.id = ds.project_id
                        LEFT JOIN dev_effic_test_dev_submit_story ss ON ds.id = ss.dev_submit_id
                        LEFT JOIN dev_effic_dev_story eds on eds.id = ss.dev_story_id
                        WHERE eds.`owner` = '{cn_name}'
                    ) b ON a.id = b.id
                    INNER JOIN dev_effic_test_bug c ON a.id = c.project_id
                    WHERE c.created between '{start_time}' and '{end_time}'
            
                    UNION
                    SELECT DISTINCT e.id FROM dev_effic_test_project e 
                    LEFT JOIN dev_effic_test_bug f ON e.id = f.project_id
                    WHERE f.fixer = '{cn_name}'
                    AND f.created between '{start_time}' and '{end_time}'
                )as m
            ) as n on n.id = tp.id
            LEFT JOIN team_mgt_user_info tgt_user on tgt_user.cn_name = '{cn_name}'
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_team_data_list_for_project_every_day_bug(cn_name, start_time, end_time):
    """获取个人的考核季指标--参与项目日均检出bug数 """
    sql = '''
        SELECT
            src_usr.user_name,
            src_usr.cn_name,
            src_usr.main_skill,
            src_usr.team_id,
            src_usr.team_name,
            src_usr.p_team_id,
            src_usr.p_team_name,
            IFNULL(COUNT( DISTINCT tb.id), 0) as user_value 
		FROM dev_effic_test_project tp
		INNER JOIN dev_effic_test_bug tb on tp.id = tb.project_id
        INNER JOIN (
            SELECT DISTINCT * FROM (
                    SELECT DISTINCT a.id 
                    FROM dev_effic_test_project a 
                    INNER JOIN (
                        SELECT DISTINCT etp.id 
                        FROM dev_effic_test_project etp
                        LEFT JOIN dev_effic_test_dev_submit ds on etp.id = ds.project_id
                        LEFT JOIN dev_effic_test_dev_submit_story ss ON ds.id = ss.dev_submit_id
                        LEFT JOIN dev_effic_dev_story eds on eds.id = ss.dev_story_id
                        WHERE eds.`owner` = '{cn_name}'
                    ) b ON a.id = b.id
                    INNER JOIN dev_effic_test_bug c ON a.id = c.project_id
                    WHERE c.created between '{start_time}' and '{end_time}'
            
                    UNION
                    SELECT DISTINCT e.id FROM dev_effic_test_project e 
                    LEFT JOIN dev_effic_test_bug f ON e.id = f.project_id
                    WHERE f.fixer = '{cn_name}'
                    AND f.created between '{start_time}' and '{end_time}'
                ) as m
        ) as n on n.id = tp.id
        inner join team_mgt_user_info tgt_user on tgt_user.cn_name = '{cn_name}'
        inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
        WHERE src_usr.bind_is_active = 1
        group by src_usr.p_team_id, src_usr.team_id, src_usr.user_name
        order by src_usr.p_team_id, src_usr.team_id, user_value;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_person_data_list_for_month_bug(cn_name, start_time, end_time):
    """获取个人的考核季指标--月均bug数 """
    sql = '''
        select tgt_user.user_name,
               tgt_user.cn_name,
               tgt_user.main_skill,
               tgt_user.team_id,
               tgt_user.team_name,
               tgt_user.p_team_id,
               tgt_user.p_team_name,
               count(1) as user_value
        from dev_effic_test_bug bug
        inner join team_mgt_user_info tgt_user on tgt_user.cn_name = bug.fixer
        where bug.fixer = '{cn_name}'
        and bug.created between '{start_time}' and '{end_time}';
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_team_data_list_for_month_bug(cn_name, start_time, end_time):
    """获取团队的考核季指标--月均bug数 """
    sql = '''
        select tgt_user.user_name,
               tgt_user.cn_name,
               tgt_user.main_skill,
               tgt_user.team_id,
               tgt_user.team_name,
               tgt_user.p_team_id,
               tgt_user.p_team_name,
               count(1) as user_value
        from dev_effic_test_bug bug
        inner join team_mgt_user_info tgt_user on tgt_user.cn_name = bug.fixer
        inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
        where src_usr.cn_name = '{cn_name}'
        and bug.created between '{start_time}' and '{end_time}'
        group by tgt_user.p_team_id, tgt_user.team_id, tgt_user.user_name
        order by tgt_user.p_team_id, tgt_user.team_id, user_value;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_team_data_list_for_bug_resolve_num(cn_name, start_time, end_time):
    """根据人获取团队所有人，缺陷数。zt@2024-10-31"""
    sql = '''
        select tgt_user.user_name,
               tgt_user.cn_name,
               tgt_user.main_skill,
               tgt_user.team_id,
               tgt_user.team_name,
               tgt_user.p_team_id,
               tgt_user.p_team_name,
               count(1) as user_value
        from dev_effic_test_bug bug
                 inner join team_mgt_user_info tgt_user on tgt_user.cn_name = bug.fixer
                 inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
        where src_usr.cn_name = '{cn_name}'
          and (
            (bug.created >= '{start_time}' and bug.created < '{end_time}')
                or (bug.resolved >= '{start_time}' and bug.resolved < '{end_time}')
            )
          and bug.status != 'rejected'
          and bug.attribution_analysis not in
              (select c400.code_val from dev_effic_code c400 where c400.code_type = 400 and c400.code_is_active = 1)
        group by tgt_user.p_team_id, tgt_user.team_id, tgt_user.user_name
        order by tgt_user.p_team_id, tgt_user.team_id, user_value;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_person_data_list_for_bug_resolve_num(cn_name, start_time, end_time):
    """获取个人的考核季指标--研发缺陷修复数。zt@2024-12-30"""
    sql = '''
        select dev_user.user_name,
               dev_user.cn_name,
               dev_user.main_skill,
               dev_user.team_id,
               dev_user.team_name,
               dev_user.p_team_id,
               dev_user.p_team_name,
               count(1) as user_value
        from dev_effic_test_bug bug
                 inner join team_mgt_user_info dev_user on dev_user.cn_name = bug.fixer
        where dev_user.cn_name = '{cn_name}'
          and (
            (bug.created >= '{start_time}' and bug.created < '{end_time}')
                or (bug.resolved >= '{start_time}' and bug.resolved < '{end_time}')
            )
          and bug.status != 'rejected'
          and bug.attribution_analysis not in
              (select c400.code_val from dev_effic_code c400 where c400.code_type = 400 and c400.code_is_active = 1)
        group by dev_user.p_team_id, dev_user.team_id, dev_user.user_name
        order by dev_user.p_team_id, dev_user.team_id, user_value;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_team_data_list_for_bug_resolve_time(cn_name, start_time, end_time):
    """根据人获取团队所有人，缺陷修复耗时数。zt@2024-11-01"""
    sql = '''
        select tgt_user.user_name,
               tgt_user.cn_name,
               tgt_user.main_skill,
               tgt_user.team_id,
               tgt_user.team_name,
               tgt_user.p_team_id,
               tgt_user.p_team_name,
               AVG(
                   TIMESTAMPDIFF(minute , v_time.created_time, v_time.resolved_time)
                       - TIMESTAMPDIFF(day, v_time.created_time, v_time.resolved_time) * 60 * (6 + 9)
                       - ( select count(1) from v_date_holiday
                                     where day > v_time.created_time and day < v_time.resolved_time ) * 60 * 9)/60 as user_value
        from dev_effic_test_bug bug
        -- ******** 1、汇总创建、解决、关闭三个时间 ******** --
        inner join (
            select v_bug.id as v_bug_id,
                   IF(DATE_FORMAT(v_bug.created, '%Y-%m-%d 09:00:00') = v_create.created_day,
                      IF(v_bug.created < DATE_FORMAT(v_bug.created, '%Y-%m-%d 09:00:00'),
                         DATE_FORMAT(v_bug.created, '%Y-%m-%d 09:00:00'),
                         v_bug.created),
                      v_create.created_day
                   ) as created_time,
                   IF(DATE_FORMAT(v_bug.resolved, '%Y-%m-%d 09:00:00') = v_resolve.resolved_day,
                      IF(v_bug.resolved < DATE_FORMAT(v_bug.resolved, '%Y-%m-%d 09:00:00'),
                         DATE_FORMAT(v_bug.resolved, '%Y-%m-%d 09:00:00'),
                         v_bug.resolved),
                      v_resolve.resolved_day
                   ) as resolved_time,
                   IF(DATE_FORMAT(v_bug.closed, '%Y-%m-%d 09:00:00') = v_close.closed_day,
                      IF(v_bug.closed < DATE_FORMAT(v_bug.closed, '%Y-%m-%d 09:00:00'),
                         DATE_FORMAT(v_bug.closed, '%Y-%m-%d 09:00:00'),
                         v_bug.closed),
                      v_close.closed_day
                   ) as closed_time
            from dev_effic_test_bug v_bug
            left join (
                -- ******** 1）缺陷创建时间 ******** --
                select bug_create.id as bug_id,
                       DATE_FORMAT(MIN(v_created.every_day), '%Y-%m-%d 09:00:00') as created_day
                from dev_effic_test_bug bug_create
                inner join (
                    select 365_day.every_day
                    from (
                        select DATE_FORMAT(DATE_SUB(NOW(), interval seq.number day), '%Y-%m-%d') as every_day
                        from devops_base_sequence seq
                        where seq.number <= 365
                        ) 365_day
                    where 365_day.every_day not in (select holiday.day from v_date_holiday holiday)
                ) v_created on DATE_FORMAT(v_created.every_day, '%Y-%m-%d 18:00:00') >= bug_create.created
                where (bug_create.created >= '{start_time}' or bug_create.resolved >= '{start_time}')
                and (bug_create.created < '{end_time}' or bug_create.resolved < '{end_time}')
                group by bug_create.id
            )v_create on v_create.bug_id = v_bug.id
            left join (
                -- ******** 2）缺陷解决时间 ******** --
                select bug_resolve.id as bug_id,
                       DATE_FORMAT(MIN(v_resolved.every_day), '%Y-%m-%d 09:00:00') as resolved_day
                from dev_effic_test_bug bug_resolve
                inner join (
                    select 365_day.every_day
                    from (
                        select DATE_FORMAT(DATE_SUB(NOW(), interval seq.number day), '%Y-%m-%d') as every_day
                        from devops_base_sequence seq
                        where seq.number <= 365
                        ) 365_day
                    where 365_day.every_day not in (select holiday.day from v_date_holiday holiday)
                ) v_resolved on DATE_FORMAT(v_resolved.every_day, '%Y-%m-%d 18:00:00') >= bug_resolve.resolved
                where (bug_resolve.created >= '{start_time}' or bug_resolve.resolved >= '{start_time}')
                and (bug_resolve.created < '{end_time}' or bug_resolve.resolved < '{end_time}')
                group by bug_resolve.id
            )v_resolve on v_resolve.bug_id = v_bug.id
            left join (
                -- ******** 3）缺陷关闭时间 ******** --
                select bug_close.id as bug_id,
                       DATE_FORMAT(MIN(v_resolved.every_day), '%Y-%m-%d 09:00:00') as closed_day
                from dev_effic_test_bug bug_close
                inner join (
                    select 365_day.every_day
                    from (
                        select DATE_FORMAT(DATE_SUB(NOW(), interval seq.number day), '%Y-%m-%d') as every_day
                        from devops_base_sequence seq
                        where seq.number <= 365
                        ) 365_day
                    where 365_day.every_day not in (select holiday.day from v_date_holiday holiday)
                ) v_resolved on DATE_FORMAT(v_resolved.every_day, '%Y-%m-%d 18:00:00') >= bug_close.closed
                where (bug_close.created >= '{start_time}' or bug_close.resolved >= '{start_time}')
                and (bug_close.created < '{end_time}' or bug_close.resolved < '{end_time}')
                group by bug_close.id
            )v_close on v_close.bug_id = v_bug.id
            where (v_bug.created >= '{start_time}' or v_bug.resolved >= '{start_time}')
            and (v_bug.created < '{end_time}' or v_bug.resolved < '{end_time}')
        ) v_time on v_time.v_bug_id = bug.id
        inner join team_mgt_user_info tgt_user on tgt_user.cn_name = bug.fixer
        inner join team_mgt_user_info src_usr on src_usr.p_team_id = tgt_user.p_team_id
        where src_usr.cn_name = '{cn_name}'
        and bug.status != 'rejected'
        and bug.resolved is not null
        and bug.attribution_analysis not in
              (select c400.code_val from dev_effic_code c400 where c400.code_type = 400 and c400.code_is_active = 1)
        group by tgt_user.p_team_id, tgt_user.team_id, tgt_user.user_name
        order by tgt_user.p_team_id, tgt_user.team_id, user_value;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_person_data_list_for_bug_resolve_time(cn_name, start_time, end_time):
    """获取个人的考核季指标--研发缺陷修复耗时。zt@2024-12-30"""
    sql = '''
        select dev_user.user_name,
               dev_user.cn_name,
               dev_user.main_skill,
               dev_user.team_id,
               dev_user.team_name,
               dev_user.p_team_id,
               dev_user.p_team_name,
               AVG(
                   TIMESTAMPDIFF(minute , v_time.created_time, v_time.resolved_time)
                       - TIMESTAMPDIFF(day, v_time.created_time, v_time.resolved_time) * 60 * (6 + 9)
                       - ( select count(1) from v_date_holiday
                                     where day > v_time.created_time and day < v_time.resolved_time ) * 60 * 9)/60 as user_value
        from dev_effic_test_bug bug
        -- ******** 1、汇总创建、解决、关闭三个时间 ******** --
        inner join (
            select v_bug.id as v_bug_id,
                   IF(DATE_FORMAT(v_bug.created, '%Y-%m-%d 09:00:00') = v_create.created_day,
                      IF(v_bug.created < DATE_FORMAT(v_bug.created, '%Y-%m-%d 09:00:00'),
                         DATE_FORMAT(v_bug.created, '%Y-%m-%d 09:00:00'),
                         v_bug.created),
                      v_create.created_day
                   ) as created_time,
                   IF(DATE_FORMAT(v_bug.resolved, '%Y-%m-%d 09:00:00') = v_resolve.resolved_day,
                      IF(v_bug.resolved < DATE_FORMAT(v_bug.resolved, '%Y-%m-%d 09:00:00'),
                         DATE_FORMAT(v_bug.resolved, '%Y-%m-%d 09:00:00'),
                         v_bug.resolved),
                      v_resolve.resolved_day
                   ) as resolved_time,
                   IF(DATE_FORMAT(v_bug.closed, '%Y-%m-%d 09:00:00') = v_close.closed_day,
                      IF(v_bug.closed < DATE_FORMAT(v_bug.closed, '%Y-%m-%d 09:00:00'),
                         DATE_FORMAT(v_bug.closed, '%Y-%m-%d 09:00:00'),
                         v_bug.closed),
                      v_close.closed_day
                   ) as closed_time
            from dev_effic_test_bug v_bug
            left join (
                -- ******** 1）缺陷创建时间 ******** --
                select bug_create.id as bug_id,
                       DATE_FORMAT(MIN(v_created.every_day), '%Y-%m-%d 09:00:00') as created_day
                from dev_effic_test_bug bug_create
                inner join (
                    select 365_day.every_day
                    from (
                        select DATE_FORMAT(DATE_SUB(NOW(), interval seq.number day), '%Y-%m-%d') as every_day
                        from devops_base_sequence seq
                        where seq.number <= 365
                        ) 365_day
                    where 365_day.every_day not in (select holiday.day from v_date_holiday holiday)
                ) v_created on DATE_FORMAT(v_created.every_day, '%Y-%m-%d 18:00:00') >= bug_create.created
                where (bug_create.created >= '{start_time}' or bug_create.resolved >= '{start_time}')
                and (bug_create.created < '{end_time}' or bug_create.resolved < '{end_time}')
                group by bug_create.id
            )v_create on v_create.bug_id = v_bug.id
            left join (
                -- ******** 2）缺陷解决时间 ******** --
                select bug_resolve.id as bug_id,
                       DATE_FORMAT(MIN(v_resolved.every_day), '%Y-%m-%d 09:00:00') as resolved_day
                from dev_effic_test_bug bug_resolve
                inner join (
                    select 365_day.every_day
                    from (
                        select DATE_FORMAT(DATE_SUB(NOW(), interval seq.number day), '%Y-%m-%d') as every_day
                        from devops_base_sequence seq
                        where seq.number <= 365
                        ) 365_day
                    where 365_day.every_day not in (select holiday.day from v_date_holiday holiday)
                ) v_resolved on DATE_FORMAT(v_resolved.every_day, '%Y-%m-%d 18:00:00') >= bug_resolve.resolved
                where (bug_resolve.created >= '{start_time}' or bug_resolve.resolved >= '{start_time}')
                and (bug_resolve.created < '{end_time}' or bug_resolve.resolved < '{end_time}')
                group by bug_resolve.id
            )v_resolve on v_resolve.bug_id = v_bug.id
            left join (
                -- ******** 3）缺陷关闭时间 ******** --
                select bug_close.id as bug_id,
                       DATE_FORMAT(MIN(v_resolved.every_day), '%Y-%m-%d 09:00:00') as closed_day
                from dev_effic_test_bug bug_close
                inner join (
                    select 365_day.every_day
                    from (
                        select DATE_FORMAT(DATE_SUB(NOW(), interval seq.number day), '%Y-%m-%d') as every_day
                        from devops_base_sequence seq
                        where seq.number <= 365
                        ) 365_day
                    where 365_day.every_day not in (select holiday.day from v_date_holiday holiday)
                ) v_resolved on DATE_FORMAT(v_resolved.every_day, '%Y-%m-%d 18:00:00') >= bug_close.closed
                where (bug_close.created >= '{start_time}' or bug_close.resolved >= '{start_time}')
                and (bug_close.created < '{end_time}' or bug_close.resolved < '{end_time}')
                group by bug_close.id
            )v_close on v_close.bug_id = v_bug.id
            where (v_bug.created >= '{start_time}' or v_bug.resolved >= '{start_time}')
            and (v_bug.created < '{end_time}' or v_bug.resolved < '{end_time}')
        ) v_time on v_time.v_bug_id = bug.id
        inner join team_mgt_user_info dev_user on dev_user.cn_name = bug.fixer
        where dev_user.cn_name = '{cn_name}'
        and bug.status != 'rejected'
        and bug.resolved is not null
        and bug.attribution_analysis not in
              (select c400.code_val from dev_effic_code c400 where c400.code_type = 400 and c400.code_is_active = 1)
        group by dev_user.p_team_id, dev_user.team_id, dev_user.user_name
        order by dev_user.p_team_id, dev_user.team_id, user_value;
        '''.format(cn_name=cn_name,
                   start_time=start_time,
                   end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_team_data_list_for_bug_kloc(end_time):
    """根据人获取团队所有人，缺陷修复耗时数。zt@2024-11-01"""
    sql = '''
        SELECT vv.user_name, vv.cn_name, vv.main_skill, vv.team_id, vv.team_name, vv.p_team_id,
                       vv.p_team_name, vv.bugs AS user_value FROM (
        SELECT m.untilDay, m.startDay, d.user_name, d.cn_name, d.main_skill, d.team_id, d.team_name, d.p_team_id,
                       d.p_team_name,
        IFNULL(ROUND(bug.total*1000/code.total, 2), 0) AS bugs

        FROM
        (
            SELECT
                DATE_FORMAT( DATE_ADD(NOW(), INTERVAL - NUMBER + 1 MONTH), '%Y-%m-01' ) AS untilDay,
                DATE_FORMAT( DATE_ADD(NOW(), INTERVAL - NUMBER + 1-12 MONTH), '%Y-%m-01' ) AS startDay
            FROM
                devops_base_sequence
            WHERE NUMBER <= TIMESTAMPDIFF(MONTH,'2024-03-01', NOW())
        ) m
        LEFT OUTER JOIN team_mgt_user_info d ON main_skill <> '测试' AND bind_is_active = 1
        LEFT OUTER JOIN
        (
            SELECT m.*, b2.fixer AS username, COUNT(0) AS total FROM
            (
                SELECT
                    DATE_FORMAT( DATE_ADD(NOW(), INTERVAL - NUMBER + 1 MONTH), '%Y-%m-01' ) AS untilDay,
                    DATE_FORMAT( DATE_ADD(NOW(), INTERVAL - NUMBER + 1-12 MONTH), '%Y-%m-01' ) AS startDay
                FROM
                    devops_base_sequence
                WHERE NUMBER < 7
            ) m
            INNER JOIN
                dev_effic_test_bug b2
            ON b2.status IN ('closed', 'resolved') AND b2.created < m.untilDay AND b2.created > m.startDay
            AND b2.attribution_analysis != '历史遗留问题'
            AND b2.fixer IN
            (SELECT DISTINCT d.cn_name AS fixer FROM team_mgt_user_info d WHERE d.main_skill <> '测试' AND d.bind_is_active = 1)
            GROUP BY untilDay, username
        ) bug

        ON m.untilDay = bug.untilDay AND d.cn_name = bug.username

        LEFT OUTER JOIN
            (
                SELECT m.*, cd.username, SUM(cd.add_counts_effect) AS total FROM
                (
                    SELECT
                        DATE_FORMAT( DATE_ADD(NOW(), INTERVAL - NUMBER + 1 MONTH), '%Y-%m-01' ) AS untilDay,
                        DATE_FORMAT( DATE_ADD(NOW(), INTERVAL - NUMBER + 1-12 MONTH), '%Y-%m-01' ) AS startDay
                    FROM
                        devops_base_sequence
                    WHERE NUMBER < 7
                ) m
                INNER JOIN code_stat_git_commits_diff_stat cd ON
                    (
                    cd.file_type IN ('.jsp', '.html', '.htm', '.vue', '.js', '.jsx', '.less', '.ftl', '.ts') 
                    OR
                    cd.file_type IN ('.xml', '.bundle/_CodeSignature/CodeResources', '.xib', '.storyboard', '.nib')
                    OR
                    cd.file_type IN ('.m', '.h') 
                    OR
                    cd.file_type IN ('.java', '.kt', '.py', '.rs', '.toml', '.ets')
                    )
                    AND cd.p_datetime >= m.startDay AND cd.p_datetime < m.untilDay
                    AND cd.username IN
                    (SELECT DISTINCT d.cn_name AS fixer FROM team_mgt_user_info d WHERE d.main_skill <> '测试' AND d.bind_is_active = 1)
                    GROUP BY untilDay, username
            ) CODE
        ON m.untilDay = code.untilDay AND d.cn_name = code.username

        WHERE cn_name NOT IN ('宋懿超', '谢宏栋', '张弋翔', '马骥', '吕猛', '杨泽来') 

        ORDER BY untilDay, bugs DESC
        ) vv
        WHERE vv.untilDay = '{}'
        '''.format(end_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_person_data_list_for_bug_kloc(user_name, start_time, end_time):
    execute_sql = '''
            SELECT d.user_name, d.cn_name, d.main_skill, d.team_id, d.team_name, d.p_team_id,
                                         d.p_team_name,
            IFNULL(ROUND(bug.total*1000/code.total, 2), 0) AS user_value
            
            FROM
            team_mgt_user_info d
            LEFT OUTER JOIN
            (
                    SELECT b2.fixer AS username, COUNT(0) AS total FROM
                    dev_effic_test_bug b2
                    WHERE b2.status IN ('closed', 'resolved') 
                    AND b2.created BETWEEN DATE_FORMAT( '{start_time}', '%Y-%m-01' ) AND DATE_FORMAT( '{end_time}', '%Y-%m-01' )
                    AND b2.attribution_analysis != '历史遗留问题'
                    AND b2.fixer = '{user_name}'
                    GROUP BY username
            ) bug
            
            ON d.cn_name = bug.username
            
            LEFT OUTER JOIN
                    (
                            SELECT cd.username, SUM(cd.add_counts_effect) AS total FROM
                            code_stat_git_commits_diff_stat cd WHERE
                                    (
                                    cd.file_type IN ('.jsp', '.html', '.htm', '.vue', '.js', '.jsx', '.less', '.ftl', '.ts') 
                                    OR
                                    cd.file_type IN ('.xml', '.bundle/_CodeSignature/CodeResources', '.xib', '.storyboard', '.nib')
                                    OR
                                    cd.file_type IN ('.m', '.h') 
                                    OR
                                    cd.file_type IN ('.java', '.kt', '.py', '.rs', '.toml', '.ets')
                                    )
                                    AND cd.p_datetime BETWEEN DATE_FORMAT( '{start_time}', '%Y-%m-01' ) AND DATE_FORMAT( '{end_time}', '%Y-%m-01' )
                                    AND cd.username = '{user_name}'
                                    GROUP BY username
                    ) CODE
            ON d.cn_name = code.username
            WHERE d.cn_name = '{user_name}'
        '''.format(user_name=user_name, start_time=start_time, end_time=end_time)
    cursor = connection.cursor()
    cursor.execute(execute_sql)
    return dict_fetchall(cursor)
