from django.db import connection
from mantis.settings import logger


def dict_fetchall(cursor):
    """将数据库游标结果转换为字典列表"""
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class AppAutoTestCheckDao:
    """应用自动化测试检查DAO层"""

    @staticmethod
    def get_test_results_by_testset_id(t_id):
        """
        通过测试集ID获取测试结果
        
        Args:
            t_id: 测试集ID
            
        Returns:
            list: 测试结果列表，每个元素为(interface_pass_rate, merge_latest_status, app_name, script_branch)的元组
        """
        # 构建SQL查询 - 获取测试集ID和测试结果
        sql = '''
            SELECT r.interface_pass_rate, r.merge_latest_status, r.app_name, r.script_branch, t.testset_id
            FROM test_flow_run_record_testset_result r 
            INNER JOIN test_flow_run_record_testset t ON r.t_id = t.id 
            WHERE t.id = %s
        '''
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql, [t_id])
                # 将结果转换为元组列表
                test_results = [(row[0], row[1], row[2], row[3], row[4]) for row in cursor.fetchall()]
                return test_results
        except Exception as e:
            logger.error(f"获取测试结果失败，原因为：{str(e)}")
            raise Exception(f"获取测试结果失败，原因为：{str(e)}")