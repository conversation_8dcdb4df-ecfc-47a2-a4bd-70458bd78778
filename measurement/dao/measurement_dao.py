from django.db import connection
import datetime
import enum
from mantis.settings import logger as log, logger


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


@enum.unique
class TestsetStatusEnum(enum.Enum):
    SUCCESS = ('success', '成功')
    RUNNING = ('running', '运行中')
    FAILURE = ('failure', '失败')

    def __init__(self, status, status_desc):
        self.status = status
        self.status_desc = status_desc


@enum.unique
class TestsetVersionModeEnum(enum.Enum):
    LOCAL = ('LOCAL', '本地')
    REMOTE = ('REMOTE', '远端')

    def __init__(self, status, status_desc):
        self.status = status
        self.status_desc = status_desc


class MeasurementDao:

    @staticmethod
    def get_last_batch_no(app_name, branch_name, create_time, biz_code, biz_flow_name, suite_code=None):
        if not app_name or not branch_name or not create_time:
            raise Exception('参数缺失')

        sql = '''
            SELECT di.run_batch_number FROM test_flow_app_deploy_info di
            INNER JOIN test_flow_run_record rr on rr.run_batch_number = di.run_batch_number 
            AND di.app_name = '{app_name}' AND di.app_deploy_branch = '{branch_name}'
            WHERE di.create_time > '{create_time}'
        '''.format(app_name=app_name, branch_name=branch_name, create_time=create_time)
        if biz_code:
            sql = sql + ' AND rr.biz_code = "{biz_code}"'.format(biz_code=biz_code)
        if biz_flow_name:
            sql = sql + ' AND rr.biz_flow_name = "{biz_flow_name}"'.format(biz_flow_name=biz_flow_name)
        if suite_code:
            sql = sql + ' AND rr.suite_code = "{suite_code}" '.format(suite_code=suite_code)
        sql = sql + ' ORDER BY run_batch_number desc limit 1'
        log.info(sql)

        cursor = connection.cursor()
        cursor.execute(sql)
        dict_list = dict_fetchall(cursor)
        return dict_list

    @staticmethod
    def get_last_run_time(app_name, branch_name, biz_code, biz_flow_name, suite_code=None):
        if not app_name or not branch_name:
            raise Exception('参数缺失')

        sql = '''
            SELECT rr.run_time FROM test_flow_run_record rr
            INNER JOIN test_flow_app_deploy_info di on rr.run_batch_number = di.run_batch_number
            AND di.app_name = '{app_name}' AND di.app_deploy_branch = '{branch_name}'
        '''.format(app_name=app_name, branch_name=branch_name)
        if biz_code:
            sql = sql + ' AND rr.biz_code = "{biz_code}"'.format(biz_code=biz_code)
        if biz_flow_name:
            sql = sql + ' AND rr.biz_flow_name = "{biz_flow_name}"'.format(biz_flow_name=biz_flow_name)
        if suite_code:
            sql = sql + ' AND rr.suite_code = "{suite_code}" '.format(suite_code=suite_code)
        sql = sql + ' ORDER BY run_time desc limit 1'
        log.info(sql)

        cursor = connection.cursor()
        cursor.execute(sql)
        dict_list = dict_fetchall(cursor)
        return dict_list[0]['run_time'] if dict_list else None

    @staticmethod
    def get_batch_no_and_suite_list():
        sql = '''
                SELECT DISTINCT r.run_batch_number, suite_code FROM test_flow_run_record r
                LEFT JOIN test_flow_app_deploy_info t ON r.run_batch_number = t.run_batch_number
                WHERE t.run_batch_number IS NULL AND r.exec_action_type != 'biz_job';
              '''

        cursor = connection.cursor()
        cursor.execute(sql)
        batch_no_and_suite_list = []
        for row in cursor.fetchall():
            batch_no_and_suite_list.append({"run_batch_number": row[0], "suite_code": row[1]})
        return batch_no_and_suite_list

    @staticmethod
    def get_execute_id_list():
        cut_time = datetime.datetime.now()
        any_hours_ago = (cut_time - datetime.timedelta(hours=24)).strftime("%Y-%m-%d %H:%M:%S")
        # 获取最近24小时内的还没有最终测试集结果数据的执行id
        sql = '''
            SELECT DISTINCT t.id, t.execute_id,tr.id AS result_id, t.testset_status, t.testset_detail
            FROM test_flow_run_record_testset t
            LEFT JOIN  test_flow_run_record_testset_result tr ON t.id = tr.t_id
            WHERE (tr.id IS NULL OR tr.interface_pass_rate IS NULL)
            AND t.create_time > '{}' 
              '''.format(any_hours_ago)
        logger.info(sql)
        cursor = connection.cursor()
        cursor.execute(sql)
        execute_id_list = []
        execute_id_t_id_map = {}
        result_id_map = {}
        result_status_map = {}
        execute_id_testset_detail_map = {}
        for row in cursor.fetchall():
            if row[1]:
                execute_id_list.append(row[1])
                execute_id_t_id_map[row[1]] = row[0]
            # 一个testset可能有多个结果，所以用list存储
            if row[2]:
                result_id_map.setdefault(row[0], []).append(row[2])
            if row[3]:
                result_status_map[row[1]] = row[3]
            if row[4]:
                execute_id_testset_detail_map[row[1]] = row[4]
        return execute_id_list, execute_id_t_id_map, result_id_map, result_status_map, execute_id_testset_detail_map

    @staticmethod
    def get_execute_id_app_run_detail_map():
        cut_time = datetime.datetime.now()
        any_hours_ago = (cut_time - datetime.timedelta(hours=24)).strftime("%Y-%m-%d %H:%M:%S")
        # 获取最近24小时内的testset_app_run_detail数据
        sql = '''
            SELECT DISTINCT t.execute_id, t.testset_app_run_detail
            FROM test_flow_run_record_testset t
            WHERE t.testset_app_run_detail IS NOT NULL
            AND t.create_time > '{}' 
              '''.format(any_hours_ago)
        logger.info(sql)
        cursor = connection.cursor()
        cursor.execute(sql)
        execute_id_app_run_detail_map = {}
        for row in cursor.fetchall():
            if row[0] and row[1]:
                execute_id_app_run_detail_map[row[0]] = row[1]
        return execute_id_app_run_detail_map

    @staticmethod
    def get_batch_no_list():
        cut_time = datetime.datetime.now()
        any_hours_ago = cut_time - datetime.timedelta(hours=24)

        sql = '''
                SELECT DISTINCT r.run_batch_number FROM test_flow_run_record r
                LEFT JOIN test_flow_run_record_testset t ON r.run_batch_number = t.run_batch_number
                WHERE t.run_batch_number IS NULL

                UNION 

                SELECT DISTINCT run_batch_number FROM test_flow_run_record_testset 
                WHERE create_time > '{}' AND testset_status = '{}';
              '''.format(any_hours_ago, TestsetStatusEnum.RUNNING.status)

        cursor = connection.cursor()
        cursor.execute(sql)
        batch_no_list = []
        for row in cursor.fetchall():
            batch_no_list.append(row[0])
        return batch_no_list

    @staticmethod
    def get_batch_no_list_by_start_time(start_time=None):
        sql = '''
                SELECT r.id, r.run_batch_number FROM test_flow_run_record r
              '''
        if start_time:
            sql += "WHERE r.create_time >= '{start_time}'".format(start_time=start_time)
        cursor = connection.cursor()
        cursor.execute(sql)
        batch_no_list = []
        batch_no_map = {}
        for row in cursor.fetchall():
            batch_no_list.append(row[1])
            batch_no_map[row[1]] = row[0]
        return batch_no_list, batch_no_map

    @staticmethod
    def get_all_user_team_cn_name_info_from_db():
        """从数据库获取现有的用户团队和中文名。zt@2024-10-22"""
        sql = '''
        select user.id,
               user.user_name,
               user.cn_name,
               user.p_team_id,
               user.team_id,
               user.p_team_name,
               user.team_name,
               user.main_skill,
               user.bind_is_active,
               user.create_time,
               user.create_user,
               user.update_time,
               user.update_user
        from team_mgt_user_info user
        where 1=1
        order by user.cn_name;
        '''.format()
        cursor = connection.cursor()
        cursor.execute(sql)
        dict_list = dict_fetchall(cursor)
        return dict_list


def get_person_data_list_by_team_name_str(team_name_str):
    """根据团队名称字符串（多个），获取启用人员信息列表。zt@2025-04-23"""
    sql = '''
        select user.user_name,
               user.cn_name,
               user.main_skill,
               user.team_id,
               user.team_name,
               user.p_team_id,
               user.p_team_name
        from team_mgt_user_info user
        where user.bind_is_active = 1
          and user.team_name in ({team_name_str})
        order by user.p_team_id, user.team_id, user.cn_name;
        '''.format(team_name_str=team_name_str)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)


def get_batch_by_time(param_time):
    """无参变量，获取当前时间批次。zt@2025-04-23"""
    sql = '''
        select @P_TIME_BATCH                                       as time_batch,
               IF(MAX(r.batch_number), MAX(r.batch_number) + 1, 1) as batch_number
        from auto_test_case_record r,
             (select @C_TIME := '{param_time}') T0,
             (select @P_TIME := DATE_FORMAT(@C_TIME, '%Y%m%d%H')) T1,
             (select @P_MINUTE := DATE_FORMAT(@C_TIME, '%i')) T2,
             (select @P_TIME_BATCH := CONCAT(@P_TIME, LPAD(@P_MINUTE - @P_MINUTE % 10, 2, '0'), '00')) T3
        where r.time_batch = @P_TIME_BATCH
        '''.format(param_time=param_time)
    cursor = connection.cursor()
    cursor.execute(sql)
    return dict_fetchall(cursor)
