# 研效--我的产能：常量模块
# 第1版 zt@2024-10-25
import enum


@enum.unique
class MainSkillEnum(enum.Enum):
    SERVER = ("server", "服务端")
    H5 = ("h5", "H5")
    APP = ("app", "APP")
    AI = ("ai", "AI")
    DEVOPS = ("devops", "平台")
    TEST = ("test", "测试")
    OPS = ("ops", "运维")
    OTHER = ("other", "其它")

    def __init__(self, skill_code, skill_name):
        self.skill_code = skill_code
        self.skill_name = skill_name


@enum.unique
class PersCapaTypeEnum(enum.Enum):
    DEV_TOTAL_TIME = ("dev_total_time", "月均填写总时长（小时）", "所有类型工时", 100)
    TEST_TOTAL_TIME = ("test_total_time", "月均填写总时长（小时）", "所有类型工时", 110)
    DEV_DELIVERY_TIME = ("dev_delivery_time", "月均填写交付工时时长（小时）", "指定类型的工时", 120)
    TEST_DELIVERY_TIME = ("test_delivery_time", "月均填写交付工时时长（小时）", "指定类型的工时", 130)
    CODE_LINE = ("code_line", "月均提交代码有效影响行数（行）", "代码行数", 140)
    BUG_RESOLVE_TIME = ("bug_resolve_time", "缺陷修复平均耗时（小时）", "缺陷修复平均耗时", 150)
    AUTO_TEST_CASE_NUM = ("auto_test_case_num", "月均自动化案例新增数（个）", "自动化测试案例数量", 160)
    AUTO_TEST_ASSERTION_NUM = ("auto_test_assertion_num", "月均自动化验证集新增数（个）", "自动化测试验证集数量", 161)
    DEV_TEST_DELIVERY_TIME_PERCENT = ("dev_test_delivery_time_percent", "研发测试交付时长比(比例)", "研发交付时长比", 170)
    BUG_RESOLVE_NUM = ("bug_resolve_num", "缺陷修复数", "缺陷修复数", 300)
    MONTH_BUG_NUM = ("month_bug_num", "月均bug数（个）", "月均bug数", 310)
    BUG_KLOC = ("bug_kloc", "千行代码bug数（个）", "千行代码缺陷数", 320)
    PROJECT_QUALITY_INFERIOR_NUM = ("project_quality_inferior_num", "参与项目质量较差数（个）", "参与项目质量较差数", 340)
    PROJECT_QUALITY_INFERIOR_PERCENT = ("project_quality_inferior_percent", "参与项目质量较差占比（%）", "参与项目质量较差占比", 350)
    PROJECT_EVERY_DAY_BUG_NUM = ("project_every_day_bug_num", "参与项目日均检出bug数（个）", "参与项目日均检出bug数", 360)
    TOTAL_TIME_TO_IDEAL_TIME_RATE = ("total_time_to_ideal_time_rate", "总填写时长占理想工作时长（%）", "总填写时长占理想工作时长（%）", 400)
    DELIVERY_TIME_TO_TOTAL_TIME_RATE = ("delivery_time_to_total_time_rate", "交付时长占总填写时长（%）", "交付时长占总填写时长（%）", 410)
    OTHER = ("other", "其它", "其它", 999)

    def __init__(self, type_code, type_name, type_desc, type_order):
        self.type_code = type_code
        self.type_name = type_name
        self.type_desc = type_desc
        self.type_order = type_order


@enum.unique
class PersCapaBaseTypeEnum(enum.Enum):
    CHECK_INDEX = ("check_index", "考核季指标")
    OUTPUT_INDEX = ("out_put", "我的产出")
    QUALITY_INDEX = ("quality_index", "我的质量")

    def __init__(self, type_code, type_name):
        self.type_code = type_code
        self.type_name = type_name


@enum.unique
class PersCapaAllTimeCycleEnum(enum.Enum):
    THIS_QUARTER = ("this_quarter", "当季完成月", "第一列数据：当季已发生的完整月数据（可能是0，可能是1个月，也可能是2个月的）")
    THIS_YEAR = ("this_year", "当年完成季", "第二列数据：当年已发生的完整季度数据")
    BEFORE_FIRST_QUARTER = ("before_first_quarter", "前一季度", "第三列数据：前一季度")
    BEFORE_SECOND_QUARTER = ("before_second_quarter", "往前第二季度", "第四列数据：往前第二季度")
    BEFORE_THIRD_QUARTER = ("before_third_quarter", "往前第三季度", "第五列数据：往前第三季度")
    BEFORE_FOURTH_QUARTER = ("before_fourth_quarter", "往前第四季度", "第六列数据：往前第四季度")

    FIRST_QUARTER = ("first_quarter", "第一季度", "当年第一季度已发生的完整月数据")
    SECOND_QUARTER = ("second_quarter", "第二季度", "当年第二季度已发生的完整月数据")
    THIRD_QUARTER = ("third_quarter", "第三季度", "当年第三季度已发生的完整月数据")
    FOURTH_QUARTER = ("fourth_quarter", "第四季度", "当年第四季度已发生的完整月数据")
    FIRST_HALF_OF_THE_YEAR = ("first_half_of_the_year", "上半年", "当年上半年已发生的完整月数据")
    SECOND_HALF_OF_THE_YEAR = ("second_half_of_the_year", "下半年", "当年下半年已发生的完整月数据")

    LAST_WEEK = ("last_week", "近一周", "往前7天（含今天）")
    LAST_MONTH = ("last_month", "近一月", "往前30天（含今天）")
    LAST_QUARTER = ("last_quarter", "近季度", "往前90天（含今天）")
    LAST_YEAR = ("last_year", "近一年", "往前365天（含今天）")

    THIS_MONTH = ("this_month", "当月", "当前月（非完整月）数据")
    BEFORE_FIRST_MONTH = ("before_first_month", "上个月", "上个月数据")
    BEFORE_SECOND_MONTH = ("before_second_month", "上上月", "上上月数据）")
    BEFORE_THIRD_MONTH = ("before_third_month", "往前第三个月", "第4列数据：往前第三个月")
    BEFORE_FOURTH_MONTH = ("before_fourth_month", "往前第四个月", "第5列数据：往前第四个月")
    BEFORE_FIFTH_MONTH = ("before_fifth_month", "往前第五个月", "第6列数据：往前第五个月")
    BEFORE_SIXTH_MONTH = ("before_sixth_month", "往前第六个月", "第7列数据：往前第六个月")
    BEFORE_SEVENTH_MONTH = ("before_seventh_month", "往前第七个月", "第8列数据：往前第七个月")
    BEFORE_EIGHTH_MONTH = ("before_eighth_month", "往前第八个月", "第9列数据：往前第八个月")
    BEFORE_NINTH_MONTH = ("before_ninth_month", "往前第九个月", "第10列数据：往前第九个月")

    def __init__(self, time_cycle_code, time_cycle_name, time_cycle_desc):
        self.time_cycle_code = time_cycle_code
        self.time_cycle_name = time_cycle_name
        self.time_cycle_desc = time_cycle_desc


@enum.unique
class PersCapaCheckTimeCycleEnum(enum.Enum):
    THIS_QUARTER = ("this_quarter", "当季完成月", "第一列数据：当季已发生的完整月数据（可能是0，可能是1个月，也可能是2个月的）")
    THIS_YEAR = ("this_year", "当年完成季", "第二列数据：当年已发生的完整季度数据")
    BEFORE_FIRST_QUARTER = ("before_first_quarter", "前一季度", "第三列数据：前一季度")
    BEFORE_SECOND_QUARTER = ("before_second_quarter", "往前第二季度", "第四列数据：往前第二季度")
    BEFORE_THIRD_QUARTER = ("before_third_quarter", "往前第三季度", "第五列数据：往前第三季度")
    BEFORE_FOURTH_QUARTER = ("before_fourth_quarter", "往前第四季度", "第六列数据：往前第四季度")

    # THIS_QUARTER = ("this_quarter", "当季度", "当季已发生的完整月数据（可能是0，可能是1个月，也可能是2个月的）")
    FIRST_QUARTER = ("first_quarter", "第一季度", "当年第一季度已发生的完整月数据")
    SECOND_QUARTER = ("second_quarter", "第二季度", "当年第二季度已发生的完整月数据")
    THIRD_QUARTER = ("third_quarter", "第三季度", "当年第三季度已发生的完整月数据")
    FOURTH_QUARTER = ("fourth_quarter", "第四季度", "当年第四季度已发生的完整月数据")
    FIRST_HALF_OF_THE_YEAR = ("first_half_of_the_year", "上半年", "当年上半年已发生的完整月数据")
    SECOND_HALF_OF_THE_YEAR = ("second_half_of_the_year", "下半年", "当年下半年已发生的完整月数据")
    # THIS_YEAR = ("this_year", "当年", "当年已发生的完整月数据")
    #
    # LAST_YEAR_FIRST_QUARTER = ("last_year_first_quarter", "去年第一季度", "去年第一季度")
    # LAST_YEAR_SECOND_QUARTER = ("last_year_second_quarter", "去年第二季度", "去年第二季度")
    # LAST_YEAR_THIRD_QUARTER = ("last_year_third_quarter", "去年第三季度", "去年第三季度")
    # LAST_YEAR_FOURTH_QUARTER = ("last_year_fourth_quarter", "去年第四季度", "去年第四季度")
    # LAST_YEAR_FIRST_HALF_OF_THE_YEAR = ("last_year_first_half_of_the_year", "去年上半年", "去年上半年")
    # LAST_YEAR_SECOND_HALF_OF_THE_YEAR = ("last_year_second_half_of_the_year", "去年下半年", "去年下半年")
    # LAST_FULL_YEAR = ("last_full_year", "去年", "去年全年")

    def __init__(self, time_cycle_code, time_cycle_name, time_cycle_desc):
        self.time_cycle_code = time_cycle_code
        self.time_cycle_name = time_cycle_name
        self.time_cycle_desc = time_cycle_desc


@enum.unique
class PersCapaOutputTimeCycleEnum(enum.Enum):
    LAST_WEEK = ("last_week", "近一周", "往前7天（含今天）")
    LAST_MONTH = ("last_month", "近一月", "往前30天（含今天）")
    LAST_QUARTER = ("last_quarter", "近季度", "往前90天（含今天）")
    LAST_YEAR = ("last_year", "近一年", "往前365天（含今天）")

    def __init__(self, time_cycle_code, time_cycle_name, time_cycle_desc):
        self.time_cycle_code = time_cycle_code
        self.time_cycle_name = time_cycle_name
        self.time_cycle_desc = time_cycle_desc


@enum.unique
class PersCapaQualityTimeCycleEnum(enum.Enum):
    LAST_WEEK = ("last_week", "近一周", "往前7天（含今天）")
    LAST_MONTH = ("last_month", "近一月", "往前30天（含今天）")
    LAST_QUARTER = ("last_quarter", "近季度", "往前90天（含今天）")
    LAST_YEAR = ("last_year", "近一年", "往前365天（含今天）")

    def __init__(self, time_cycle_code, time_cycle_name, time_cycle_desc):
        self.time_cycle_code = time_cycle_code
        self.time_cycle_name = time_cycle_name
        self.time_cycle_desc = time_cycle_desc
