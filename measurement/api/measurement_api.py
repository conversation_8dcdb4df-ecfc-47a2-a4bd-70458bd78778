import abc
import datetime
import logging

from rest_framework import status
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet
from django.db import transaction

from mantis.settings import ApiResult
from mantis.settings import logger as log
from mantis.settings import SPIDER
from measurement.model.models import MeasurementSyncLog, TeamMgtUserInfo
from measurement.service.app_auto_test_check_service import AppAutoTestCheckService, AppAutoTestStatusService, \
    AppAutoTestCheckParam
from measurement.service.auto_test_record_service import AutoTestStatisticsRecordSer
from measurement.service.auto_test_record_service import AutoTestCaseRecordSrv
from measurement.service.flow_run_app_deploy_service import TestFlowAppDeploySer
from measurement.service.flow_run_record_service import TestFlowRunRecordSer
from measurement.service.flow_run_record_testSet_result_service import TestFlowRunRecordTestSetResultSer
from measurement.service.flow_run_record_testset_service import TestFlowRunRecordTestsetSer
from measurement.service.flow_schedule_config_service import TestFlowScheduleInfoSer
from measurement.service.flow_stakeholder_service import Test<PERSON>lowStakeholderSer, DevopsAppTeamSer
from measurement.service.person_dev_effective_service import PersonDevEffectiveSer
from measurement.service.person_iteration_dashborad_service import PersonIterationDashboardSer, PersonIterationLinechartSer
from measurement.dao.measurement_dao import MeasurementDao
from measurement.dao.person_quality_dashborad_dao import get_biz_flow_run_result, get_every_auto_test_result, \
    get_iteration_linechart_data, get_iteration_run_result
from measurement.service.measurement_utils import send_request
from measurement.utils.diskcache_utils import DiskCacheUtil


class TestFlowScheduleInfoView(ViewSet):
    authentication_classes = []
    """"
    定时自动化任务同步
    """

    def list(self, request):

        try:
            flow_schedule_ser = TestFlowScheduleInfoSer()
            flow_schedule_ser.create_or_update_flow_schedule_info()
        except Exception as e:
            log.error(e)
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict(
                                "自动化定时任务同步失败"))
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(
                            "自动化定时任务同步成功"))


class TestFlowRunRecordView(ViewSet):
    authentication_classes = []
    """"
    编排线执行记录同步
    """

    def list(self, request):
        try:
            sync_content = "test_flow_run_record"

            obj = MeasurementSyncLog.objects.filter(sync_content=sync_content).first()
            if obj:
                start_time = obj.success_time
            else:
                start_time = None

            tfrrs = TestFlowRunRecordSer()
            success_time = tfrrs.create_or_update_flow_run_record(start_time)
            executor_result = True

        except Exception as e:
            log.error("同步编排线执行记录失败")
            log.error(e)
            executor_result = False
            raise Exception(str(e))
        finally:
            if executor_result:
                MeasurementSyncLog.objects.update_or_create(sync_content=sync_content,
                                                            defaults={"success_time": success_time})
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(
                            "编排线执行记录同步成功"))


class AutoTestStatisticsRecordView(ViewSet):
    authentication_classes = []
    """"
    自动化测试变更记录同步
    """

    def list(self, request):
        try:
            sync_content = "auto_test_record"
            obj = MeasurementSyncLog.objects.filter(sync_content=sync_content).first()
            if obj:
                start_time = obj.success_time
            else:
                start_time = datetime.datetime.now()
            formatted_date = start_time.strftime('%Y-%m-%d')
            tfrrs = AutoTestStatisticsRecordSer()
            success_time = tfrrs.create_or_update_auto_test_record(formatted_date)
            executor_result = True

        except Exception as e:
            log.error("自动化测试变更记录同步失败")
            log.error(e)
            executor_result = False
            raise Exception(str(e))
        finally:
            if executor_result:
                MeasurementSyncLog.objects.update_or_create(sync_content=sync_content,
                                                            defaults={"success_time": success_time})
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(
                            "自动化测试变更记录同步"))


class AutoTestCaseRecordView(ViewSet):
    """从qa平台同步测试集和验证集数量。zt@2025-04-23"""
    authentication_classes = []

    def list(self, request):
        executor_result = None
        sync_content = "sync_auto_test_case_record"
        success_time = None
        try:
            obj = MeasurementSyncLog.objects.filter(sync_content=sync_content).first()
            if obj:
                start_time = obj.success_time
            else:
                start_time = datetime.datetime.now()
            formatted_date = start_time.strftime('%Y-%m-%d')
            auto_test_case_record_srv = AutoTestCaseRecordSrv()
            success_time = auto_test_case_record_srv.create_or_update_auto_test_case_record(formatted_date)
            executor_result = True

        except Exception as e:
            log.error("自动化测试变更记录同步失败")
            log.error(e)
            executor_result = False
            raise Exception(str(e))
        finally:
            if executor_result:
                MeasurementSyncLog.objects.update_or_create(sync_content=sync_content,
                                                            defaults={"success_time": success_time})
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(
                            "自动化测试变更记录同步"))


class TestFlowRunRecordTestsetView(ViewSet):
    authentication_classes = []
    """"
    编排线测试集执行顺序同步
    """

    def list(self, request):
        try:
            tfrrs = TestFlowRunRecordTestsetSer()
            tfrrs.create_flow_run_record_testset()
        except Exception as e:
            log.error("同步编排线执行测试集记录失败")
            log.error(e)
            raise Exception(str(e))

        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(
                            "编排线执行测试集记录同步成功"))


class TestFlowAppDeployView(ViewSet):
    authentication_classes = []
    """"
    编排线对应的应用部署信息同步
    """

    def list(self, request):
        try:
            tfrrs = TestFlowAppDeploySer()
            tfrrs.create_flow_app_deploy_info()
        except Exception as e:
            log.error("同步编排线应用部署信息失败")
            log.error(e)
            raise Exception(str(e))

        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(
                            "同步编排线应用部署信息成功"))


class TestFlowRunRecordTestSetResultView(ViewSet):
    authentication_classes = []
    """"
    测试集执行结果数据
    """

    def list(self, request):
        tfrrs = TestFlowRunRecordTestSetResultSer()
        tfrrs.create_flow_run_record_testSet_result()
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(
                            "测试集执行结果同步成功"))


class DevopsAppTeamView(ViewSet):
    def list(self, request):
        tfrrs = DevopsAppTeamSer()
        tfrrs.sync_app_team_info()
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(
                            "应用负责人同步成功"))


class TestFlowStakeholderView(ViewSet):
    authentication_classes = []
    """"
    同步自动化干系人数据
    """

    def list(self, request):
        sync_content = "test_flow_stakeholder"
        try:
            obj = MeasurementSyncLog.objects.filter(sync_content=sync_content).first()
            if obj:
                start_time = obj.success_time
            else:
                start_time = None

            tfrrs = TestFlowStakeholderSer()
            suceess_time = tfrrs.create_or_update_flow_stakeholder(start_time)
            executor_result = True

        except Exception as e:
            log.error("同步干系人失败")
            log.error(e)
            executor_result = False
            raise Exception(str(e))
        finally:
            if executor_result and suceess_time:
                MeasurementSyncLog.objects.update_or_create(sync_content=sync_content,
                                                            defaults={"success_time": suceess_time})
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(
                            "干系人同步成功"))


class MyInterface(metaclass=abc.ABCMeta):
    @abc.abstractmethod
    def method_one(self):
        pass


class Adaptee():
    def specificRequest(self, url, method, params, headers, data):
        logging.info('aaaaaa')


class Adapter(MyInterface):

    def __init__(self, adaptee):
        self.adaptee = adaptee

    def method_one(self, url, method, params, headers, data):
        print("Method One implementation")
        adaptee.specificRequest(url, method, params, headers, data)


if __name__ == '__main__':
    adaptee = Adaptee()
    target = Adapter(adaptee)
    url = ""
    method = ""
    params = ""
    headers = ""
    data = ""
    target.method_one(url, method, params, headers, data)


class BizFlowRunResultView(ViewSet):
    authentication_classes = []
    """"
    查询编排线质量报告
    """

    def list(self, request):
        opt_date = request.query_params.get("opt_date")
        biz_flow_name = request.query_params.get("biz_flow_name")
        if not opt_date:
            # opt_date 要当前日期的前一天的日期。
            opt_date = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y-%m-%d")

        dashboard_data_list, total = get_biz_flow_run_result(opt_date=opt_date, biz_flow_name=biz_flow_name)
        result = self.trans_auto_test_result(dashboard_data_list)

        return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(msg="执行成功", data=result))

    def trans_auto_test_result(self, dashboard_data_list):
        result = {}
        for item in dashboard_data_list:
            # 获取流程名称
            biz_flow_name = item["biz_flow_name"]

            # 判断字典中是否已存在该流程名称的数据，有则继续添加，无则新建
            if biz_flow_name in result:
                # 获取流程对应的字典
                flow_dict = result[biz_flow_name]

                # 获取批次号
                run_batch_no = item["run_batch_number"]

                # 判断字典中是否已存在该批次号的数据，有则继续添加，无则新建
                if "run_record" in flow_dict:
                    run_record = flow_dict["run_record"]
                    index = -1
                    for i in range(len(run_record)):
                        if run_record[i]["run_batch_no"] == run_batch_no:
                            index = i
                            break
                    if index >= 0:
                        run_result = run_record[index]["run_result"]
                        testset_id = item["testset_id"]
                        testset_app_result = {
                            "app_name": item["app_name"],
                            "deploy_branch": item["app_deploy_branch"],
                            "srcipt_branch": item["script_branch"],
                            "interface_pass_rate": item["interface_pass_rate"],
                            "case_pass_rate": item["case_pass_rate"],
                            "start_time": item["start_time"],
                            "end_time": item["end_time"],
                            "duration": item["duration"],
                            "result_url": item["result_url"]}
                        index_0 = -1
                        for i in range(len(run_result)):
                            if run_result[i]["testset_id"] == testset_id:
                                index_0 = i
                                break

                        if index_0 >= 0:
                            run_result[index_0]["appList"].append(testset_app_result)
                        else:
                            result_dict = {
                                "testset_id": testset_id,
                                "appList": [
                                    testset_app_result
                                ]
                            }
                            run_result.append(result_dict)
                    else:
                        run_record_dict = {
                            "run_batch_no": run_batch_no,
                            "suite_code": item["suite_code"],
                            "run_result": [
                                {
                                    "testset_id": item["testset_id"],
                                    "appList": [
                                        {
                                            "app_name": item["app_name"],
                                            "deploy_branch": item["app_deploy_branch"],
                                            "srcipt_branch": item["script_branch"],
                                            "interface_pass_rate": item["interface_pass_rate"],
                                            "case_pass_rate": item["case_pass_rate"],
                                            "start_time": item["start_time"],
                                            "end_time": item["end_time"],
                                            "duration": item["duration"],
                                            "result_url": item["result_url"]
                                        }
                                    ]
                                }
                            ]
                        }
                        run_record.append(run_record_dict)
                else:
                    run_record_dict = {
                        "run_batch_no": item["run_batch_number"],
                        "suite_code": item["suite_code"],
                        "run_result": [
                            {
                                "testset_id": item["testset_id"],
                                "appList": [
                                    {
                                        "app_name": item["app_name"],
                                        "deploy_branch": item["app_deploy_branch"],
                                        "srcipt_branch": item["script_branch"],
                                        "interface_pass_rate": item["interface_pass_rate"],
                                        "case_pass_rate": item["case_pass_rate"],
                                        "start_time": item["start_time"],
                                        "end_time": item["end_time"],
                                        "duration": item["duration"],
                                        "result_url": item["result_url"]
                                    }
                                ]
                            }
                        ]
                    }
                    flow_dict["run_record"] = [run_record_dict]
            else:
                result[biz_flow_name] = {
                    "run_record": [
                        {
                            "run_batch_no": item["run_batch_number"],
                            "suite_code": item["suite_code"],
                            "run_result": [
                                {
                                    "testset_id": item["testset_id"],
                                    "appList": [
                                        {
                                            "app_name": item["app_name"],
                                            "deploy_branch": item["app_deploy_branch"],
                                            "srcipt_branch": item["script_branch"],
                                            "interface_pass_rate": item["interface_pass_rate"],
                                            "case_pass_rate": item["case_pass_rate"],
                                            "start_time": item["start_time"],
                                            "end_time": item["end_time"],
                                            "duration": item["duration"],
                                            "result_url": item["result_url"]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
        return result


class PersonQualityDashboardView(ViewSet):
    authentication_classes = []
    """"
    查询个人面板质量数据
    """

    def list(self, request):
        opt_user = request.query_params.get("opt_user")
        opt_date = request.query_params.get("opt_date")
        page_num = request.query_params.get("page_num")
        page_size = request.query_params.get("page_size")
        total = True
        if not opt_date:
            # opt_date 要当前日期的前一天的日期。
            opt_date = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y-%m-%d")

        dashboard_data_list, total = get_biz_flow_run_result(opt_user=opt_user, opt_date=opt_date,
                                                             page_num=int(page_num), page_size=int(page_size),
                                                             total=total, is_person=True)

        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(msg="执行成功",
                                                    data={"total": total, "dashboard_data_list": dashboard_data_list}))


class EveryDayAutoTestResultView(ViewSet):
    authentication_classes = []
    """"
    每日自动化测试执行情况
    """

    def list(self, request):
        opt_date = datetime.datetime.now().strftime("%Y-%m-%d")
        auto_test_result = get_every_auto_test_result(opt_date=opt_date)
        run_result = BizFlowRunResultView()
        result = run_result.trans_auto_test_result(auto_test_result)
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(msg="执行成功",
                                                    data=result))


class PersonDevEffectiveView(ViewSet):
    authentication_classes = []
    """"
    查询个人面板研效数据
    """

    def list(self, request):
        opt_user = request.query_params.get("opt_user")
        type_name = request.query_params.get("type_name")
        time_cycle = request.query_params.get("time_cycle")
        data_list = list()
        if 'capacity' == type_name:
            dev_effective_ser = PersonDevEffectiveSer()
            data_list = dev_effective_ser.get_person_dev_effective(opt_user, time_cycle)
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(msg="执行成功", data=data_list))


class CheckAppAutoTestResultView(ViewSet):
    authentication_classes = []
    """
    查询应用版本的自动化测试结果
    """

    def list(self, request):
        business_name = request.query_params.get("business_name")
        iteration_id = request.query_params.get("iteration_id")
        app_name = request.query_params.get("app_name")
        br_name = request.query_params.get("br_name")
        biz_code = request.query_params.get("biz_code")
        biz_flow_name = request.query_params.get("biz_flow_name")
        suite_code = request.query_params.get("suite_code")
        create_time = request.query_params.get("create_time")
        pass_rate_threshold = request.query_params.get("pass_rate_threshold")

        app_auto_test_check_param = AppAutoTestCheckParam(business_name=business_name, biz_flow_name=biz_flow_name,
                                                          suite_code=suite_code,
                                                          pass_rate_threshold=pass_rate_threshold, app_name=app_name,
                                                          br_name=br_name, iteration_id=iteration_id,
                                                          biz_code=biz_code, create_time=create_time)

        app_auto_test_check_ser = AppAutoTestCheckService()
        result, msg = app_auto_test_check_ser.app_auto_test_check(app_auto_test_check_param)

        if result:
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.success_dict(msg="校验成功, 结果：{}".format(msg)))
        else:
            return Response(status=status.HTTP_200_OK,
                            data=ApiResult.failed_dict(msg="校验失败，原因：{}".format(msg)))


class CheckAppAutoTestStatusView(ViewSet):
    authentication_classes = []
    """
    查询应用版本的自动化测试结果
    """

    def list(self, request):
        iteration_id = request.query_params.get("iteration_id")
        app_name = request.query_params.get("app_name")
        branch_name = request.query_params.get("br_name")
        biz_code = request.query_params.get("biz_code")
        biz_flow_name = request.query_params.get("biz_flow_name")
        suite_code = request.query_params.get("suite_code")
        create_time = request.query_params.get("create_time")
        app_auto_test_check_ser = AppAutoTestStatusService()
        result = app_auto_test_check_ser.app_auto_test_status(iteration_id, app_name, branch_name, create_time,
                                                              biz_code, biz_flow_name, suite_code)
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(data=result, msg="校验成功, 结果：{}".format(result)))


class UserTeamInfoView(ViewSet):
    """用户的团队和中文名。zt@2024-10-22"""
    authentication_classes = []

    opt_user = "howbuyscm"

    def create(self, request):
        log.info(request.data)
        curr_time = datetime.datetime.now()

        spider_url = '{}user/get_all_user_team_cn_name_info/'.format(SPIDER["url"])
        spider_params = {}
        spider_headers = {'Content-Type': 'application/json'}
        ret_data = send_request(spider_url, 'get', data=spider_params, headers=spider_headers)
        if ret_data:
            db_dict_list = MeasurementDao.get_all_user_team_cn_name_info_from_db()
            ins_list, upd_list, del_list = UserTeamInfoView.compare_user_list(ret_data, db_dict_list)
            log.info(">>>> ins_list = {}".format(ins_list))
            log.info(">>>> upd_list = {}".format(upd_list))
            log.info(">>>> del_list = {}".format(del_list))
            ins_count = self.ins_user_team_list(ins_list)
            log.info(">>>> ins_count = {}".format(ins_count))
            upd_count = self.upd_user_team_list(upd_list)
            log.info(">>>> upd_count = {}".format(upd_count))
            del_count = self.del_user_team_list(del_list)
            log.info(">>>> del_count = {}".format(del_count))

        return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(data=ret_data, msg="bug创建成功"))

    @staticmethod
    def dict_fetchall(cursor):
        return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]

    @staticmethod
    def compare_user_list(spider_user_list, db_user_list):
        ins_list = []
        upd_list = []
        del_list = []
        if spider_user_list:
            spider_user_dict = {}
            for spider_user in spider_user_list:
                spider_user_cn_name = spider_user.get("cn_name")
                if spider_user_cn_name:
                    spider_user_dict[spider_user_cn_name] = spider_user

            if spider_user_dict:
                if db_user_list:
                    for db_user in db_user_list:
                        db_id = db_user.get("id")
                        db_user_name = db_user.get("user_name")
                        db_cn_name = db_user.get("cn_name")
                        db_p_team_id = db_user.get("p_team_id")
                        db_team_id = db_user.get("team_id")
                        db_p_team_name = db_user.get("p_team_name")
                        db_team_name = db_user.get("team_name")
                        db_main_skill = db_user.get("main_skill")
                        db_bind_is_active = db_user.get("bind_is_active")
                        # key会有编码异常：
                        # log.info(">>>> db_cn_name = {}".format(db_cn_name))
                        spider_user = spider_user_dict.pop(db_cn_name, None)
                        if spider_user:
                            is_upd = False
                            if db_user_name != spider_user.get("user_name"):
                                is_upd = True
                            if not is_upd and db_cn_name != spider_user.get("cn_name"):
                                is_upd = True
                            if not is_upd and db_team_id != spider_user.get("s_team_id"):
                                is_upd = True
                            if not is_upd and db_p_team_id != spider_user.get("team_id"):
                                is_upd = True
                            if not is_upd and db_team_name != spider_user.get("s_team_name"):
                                is_upd = True
                            if not is_upd and db_p_team_name != spider_user.get("team_name"):
                                is_upd = True
                            if not is_upd and db_main_skill != spider_user.get("main_skill"):
                                is_upd = True
                            if not is_upd and db_bind_is_active != spider_user.get("bind_is_active"):
                                is_upd = True

                            if is_upd:
                                # upd_cn_name = spider_user.get("cn_name")
                                # upd_user_name = spider_user.get("user_name")
                                # upd_team_id = spider_user.get("s_team_id")
                                # upd_team_name = spider_user.get("s_team_name")
                                # upd_p_team_id = spider_user.get("team_id")
                                # upd_p_team_name = spider_user.get("team_name")
                                # upd_main_skill = spider_user.get("main_skill")
                                # upd_bind_is_active = spider_user.get("bind_is_active")
                                #
                                # upd_id = db_id
                                #
                                # upd_user_tuple = (
                                #     upd_cn_name,
                                #     upd_user_name,
                                #     upd_team_id,
                                #     upd_team_name,
                                #     upd_p_team_id,
                                #     upd_p_team_name,
                                #     upd_main_skill,
                                #     upd_bind_is_active,
                                #     upd_id,
                                # )
                                spider_user["id"] = db_id
                                upd_list.append(spider_user)
                        else:
                            del_list.append(db_user)

                # 循环完dict还有，需要新增：
                if spider_user_dict:
                    for user_val in spider_user_dict.values():
                        # ins_cn_name = user_val.get("cn_name")
                        # ins_user_name = user_val.get("user_name")
                        # ins_team_id = user_val.get("team_id")
                        # ins_team_name = user_val.get("team_name")
                        # ins_p_team_id = user_val.get("s_team_id")
                        # ins_p_team_name = user_val.get("s_team_name")
                        # ins_main_skill = user_val.get("main_skill")
                        # ins_bind_is_active = user_val.get("bind_is_active")
                        #
                        # ins_user_tuple = (
                        #     ins_cn_name,
                        #     ins_user_name,
                        #     ins_team_id,
                        #     ins_team_name,
                        #     ins_p_team_id,
                        #     ins_p_team_name,
                        #     ins_main_skill,
                        #     ins_bind_is_active,
                        # )
                        ins_list.append(user_val)

        return ins_list, upd_list, del_list

    def ins_user_team_list(self, ins_dict_list, curr_time=None, opt_user=None):
        ins_count = 0
        if ins_dict_list:
            if not curr_time:
                curr_time = datetime.datetime.now()
            if not opt_user:
                opt_user = self.opt_user

            ins_obj_list = []
            for ins_dict in ins_dict_list:
                if ins_dict:
                    ins_obj = TeamMgtUserInfo(
                        user_name=ins_dict["user_name"],
                        cn_name=ins_dict["cn_name"],
                        p_team_id=ins_dict["team_id"],
                        team_id=ins_dict["s_team_id"],
                        p_team_name=ins_dict["team_name"],
                        team_name=ins_dict["s_team_name"],
                        main_skill=ins_dict["main_skill"],
                        bind_is_active=ins_dict["bind_is_active"],
                        create_time=curr_time,
                        create_user=opt_user,
                        update_time=curr_time,
                        update_user=opt_user,
                    )
                    ins_obj_list.append(ins_obj)
            if ins_obj_list:
                try:
                    with transaction.atomic():
                        TeamMgtUserInfo.objects.bulk_create(ins_obj_list)
                        ins_count = len(ins_obj_list)
                except Exception as e:
                    log.error(e)
        return ins_count

    def upd_user_team_list(self, upd_dict_list, curr_time=None, opt_user=None):
        upd_count = 0
        if upd_dict_list:
            if not curr_time:
                curr_time = datetime.datetime.now()
            if not opt_user:
                opt_user = self.opt_user

            upd_obj_list = []
            for upd_dict in upd_dict_list:
                if upd_dict:
                    upd_obj = TeamMgtUserInfo(
                        user_name=upd_dict["user_name"],
                        cn_name=upd_dict["cn_name"],
                        p_team_id=upd_dict["team_id"],
                        team_id=upd_dict["s_team_id"],
                        p_team_name=upd_dict["team_name"],
                        team_name=upd_dict["s_team_name"],
                        main_skill=upd_dict["main_skill"],
                        bind_is_active=upd_dict["bind_is_active"],
                        update_time=curr_time,
                        update_user=opt_user,

                        id=upd_dict["id"],
                    )
                    upd_obj_list.append(upd_obj)
            if upd_obj_list:
                upd_fields = [
                    'user_name',
                    'cn_name',
                    'p_team_id',
                    'team_id',
                    'p_team_name',
                    'team_name',
                    'main_skill',
                    'bind_is_active',
                    'update_time',
                    'update_user',
                ]
                try:
                    with transaction.atomic():
                        TeamMgtUserInfo.objects.bulk_update(upd_obj_list, upd_fields)
                        upd_count = len(upd_obj_list)
                except Exception as e:
                    log.error(e)
        return upd_count

    def del_user_team_list(self, del_dict_list, curr_time=None, opt_user=None):
        del_count = 0
        if del_dict_list:
            if not curr_time:
                curr_time = datetime.datetime.now()
            if not opt_user:
                opt_user = self.opt_user

            del_id_list = []
            for del_dict in del_dict_list:
                if del_dict:
                    del_id = del_dict['id']
                    if del_id:
                        del_id_list.append(del_id)

            if del_id_list:
                TeamMgtUserInfo.objects.filter(id__in=del_id_list).delete()
                del_count = len(del_id_list)
        return del_count


class PersonIterationDashboardView(ViewSet):
    authentication_classes = []
    """"
    查询我的迭代报告数据
    """

    def list(self, request):
        opt_user = request.query_params.get("opt_user")
        time_cycle = request.query_params.get("time_cycle")
        page_num = request.query_params.get("page_num")
        page_size = request.query_params.get("page_size")

        pde = PersonIterationDashboardSer()
        start_time = pde.get_start_time(time_cycle)

        # cache_key = pde.get_cache_key(time_cycle, opt_user)
        # cache_dict = DiskCacheUtil.get_value(cache_key)
        # if not cache_dict:
        dashboard_data_list, total = get_iteration_run_result(opt_user=opt_user, start_time=start_time,
                                                              page_num=int(page_num), page_size=int(page_size))
        cache_dict = {"total": total, "dashboard_data_list": dashboard_data_list}
        # DiskCacheUtil.set_value(cache_key, cache_dict, 60 * 60 * 4)

        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(msg="执行成功",
                                                    data=cache_dict))


class PersonIterationLineChartView(ViewSet):
    authentication_classes = []
    """
        迭代应用折线图
    """

    def list(self, request):
        opt_user = request.query_params.get("opt_user")

        app_name = request.query_params.get("app_name")
        branch_name = request.query_params.get("branch_name")
        time_cycle = request.query_params.get("time_cycle")

        pde = PersonIterationLinechartSer()
        start_time = pde.get_start_time(time_cycle)

        cache_key = pde.get_linechart_cache_key(time_cycle, app_name, branch_name)
        linechart_data = DiskCacheUtil.get_value(cache_key)
        if not linechart_data:

            data_list = get_iteration_linechart_data(opt_user=opt_user, app_name=app_name, branch_name=branch_name,
                                                     start_time=start_time)

            linechart_data = {}
            for item in data_list:
                testset_id = item["testset_id"]
                run_batch_number = item["run_batch_number"]
                case_pass_rate = self.process_case_pass_rate(item["case_pass_rate"])

                if testset_id not in linechart_data:
                    linechart_data[testset_id] = {run_batch_number: [case_pass_rate]}
                else:
                    batch_number_pass_rate_dict = linechart_data[testset_id]

                    if run_batch_number not in batch_number_pass_rate_dict.keys():
                        batch_number_pass_rate_dict.update({run_batch_number: [case_pass_rate]})
                    else:
                        batch_number_pass_rate_dict[run_batch_number].append(case_pass_rate)
            # 一个测试集id下有多个case版本号的执行记录，这里合并取平均值
            for testset_id, batch_number_pass_rate_dict in linechart_data.items():
                for batch_number, pass_rate_list in batch_number_pass_rate_dict.items():
                    linechart_data[testset_id][batch_number] = sum(pass_rate_list) / len(pass_rate_list)

            # 处理x轴数据，拉平所有的测试集id下的执行批次号
            x_axis = []
            for testset_id, batch_number_pass_rate_dict in linechart_data.items():
                x_axis.extend(batch_number_pass_rate_dict.keys())

            # x_axis 去重并
            x_axis = list(set(x_axis))

            for testset_id, batch_number_pass_rate_dict in linechart_data.items():
                for batch_number in x_axis:
                    # 原数据中没有某x轴数据，用null填充
                    if batch_number not in batch_number_pass_rate_dict.keys():
                        linechart_data[testset_id][batch_number] = "Null"
                linechart_data[testset_id] = [{"x_axis": k, "y_axis": v} for k, v in
                                              sorted(linechart_data[testset_id].items(), key=lambda x: x[0])]

            DiskCacheUtil.set_value(cache_key, linechart_data, 60 * 60 * 4)

        return Response(status=status.HTTP_200_OK, data=ApiResult.success_dict(msg="执行成功", data=linechart_data))

    def process_case_pass_rate(self, rate):
        """处理通过率字符串，去掉百分号并转换为浮点数"""
        return float(rate[:-1]) if rate.endswith("%") else float(rate)
