import json
import traceback

from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from rest_framework import status

from mantis.settings import ApiResult
from mantis.settings import logger as log

from measurement.common.personal_capacity_common.personal_capacity_constants import MainSkillEnum, PersCapaBaseTypeEnum, \
    PersCapaAllTimeCycleEnum
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaTypeEnum
from measurement.service.personal_capacity_srv.personal_capacity_srv import PersCapaSrv


class PersCapaTypeAPI(ViewSet):
    def list(self, request):
        res_data = {}
        req_data = request.data
        opt_user = request.query_params.get("opt_user")
        log.info(">>>> req_data = {}".format(req_data))
        log.info(">>>> opt_user = {}".format(opt_user))

        for pct_enum in list(PersCapaTypeEnum):
            res_data[pct_enum.name] = pct_enum.value

        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(msg="执行成功", data=res_data))


class PersCapaForCheckAPI(ViewSet):
    def list(self, request):
        res_data = {}
        req_data = request.data
        opt_user = request.query_params.get("opt_user")
        time_cycle = request.query_params.get("time_cycle")
        log.info(">>>> req_data = {}".format(req_data))
        log.info(">>>> opt_user = {}".format(opt_user))
        log.info(">>>> time_cycle = {}".format(time_cycle))
        try:
            if not opt_user:
                raise ValueError("opt_user is required")
            # if not time_cycle:
            #     raise ValueError("time_cycle is required")
            srv = PersCapaSrv(opt_user, time_cycle)
            res_data = srv.get_person_capacity_for_check()
        except Exception as e:
            traceback.print_exc()
            return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            data={"msg": str(e)})
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(msg="执行成功", data=res_data))


class PersCapaForOutputAPI(ViewSet):
    def list(self, request):
        res_data = {}
        req_data = request.data
        opt_user = request.query_params.get("opt_user")
        time_cycle = request.query_params.get("time_cycle")
        # log.info(">>>> req_data = {}".format(req_data))
        # log.info(">>>> opt_user = {}".format(opt_user))
        # log.info(">>>> time_cycle = {}".format(time_cycle))
        try:
            if not opt_user:
                raise ValueError("opt_user is required")
            # 需求变更为近10月数，不再需要传入时间周期的枚举。zt@2025-02-17
            # if not time_cycle:
            #     raise ValueError("time_cycle is required")
            # srv = PersCapaSrv(opt_user, time_cycle)
            # res_data = srv.get_person_capacity(PersCapaBaseTypeEnum.OUTPUT_INDEX)
            if not time_cycle:
                time_cycle = PersCapaAllTimeCycleEnum.THIS_MONTH
            srv = PersCapaSrv(opt_user, time_cycle)
            res_data = srv.get_person_capacity_for_output()

            res_status = status.HTTP_200_OK
            res_data = ApiResult.success_dict(msg="执行成功", data=res_data)
        except Exception as e:
            traceback.print_exc()
            res_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            res_data = ApiResult.failed_dict(msg="执行失败", data=str(e))
        return Response(status=res_status, data=res_data)


class PersCapaForQualityAPI(ViewSet):
    def list(self, request):
        res_data = {}
        req_data = request.data
        opt_user = request.query_params.get("opt_user")
        time_cycle = request.query_params.get("time_cycle")
        # log.info(">>>> req_data = {}".format(req_data))
        # log.info(">>>> opt_user = {}".format(opt_user))
        # log.info(">>>> time_cycle = {}".format(time_cycle))
        try:
            if not opt_user:
                raise ValueError("opt_user is required")
            # 需求变更为近10月数，不再需要传入时间周期的枚举。zt@2025-02-17
            # if not time_cycle:
            #     raise ValueError("time_cycle is required")
            # srv = PersCapaSrv(opt_user, time_cycle)
            # res_data = srv.get_person_capacity(PersCapaBaseTypeEnum.QUALITY_INDEX)
            srv = PersCapaSrv(opt_user, time_cycle)
            res_data = srv.get_person_capacity_for_quality()
        except Exception as e:
            traceback.print_exc()
            return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            data={"msg": str(e)})
        return Response(status=status.HTTP_200_OK,
                        data=ApiResult.success_dict(msg="执行成功", data=res_data))
