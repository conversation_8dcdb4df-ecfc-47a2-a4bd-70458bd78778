import datetime
import json

from django.db import connection, transaction
from measurement.dao.measurement_dao import MeasurementDao, TestsetStatusEnum
from mantis.settings import TAPD, logger, QAP
from measurement.model.models import TestFlowRunRecordTestSetResult, TestFlowRunRecordTestset
from measurement.service.measurement_utils import send_request


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class TestFlowRunRecordTestSetResultSer:

    # 处理执行异常的执行结果，补0
    def handle_failed_execute_id_result_info(self, ready_to_handle_execute_id, ready_to_handle_execute_id_result_map, 
                                              execute_id_t_id_map, execute_id_testset_detail_map, 
                                              exclude_execute_id_list, execute_id_app_run_detail_map=None):
        """
        处理执行失败的测试用例结果信息

        参数:
            ready_to_handle_execute_id: 待处理的执行ID列表
            ready_to_handle_execute_id_result_map: 执行ID到状态的映射
            execute_id_t_id_map: 执行ID到t_id的映射
            execute_id_testset_detail_map: 执行ID到测试集详情的映射
            exclude_execute_id_list: 需要排除的执行ID列表

        返回:
            无
        """
        # 1. 准备基础数据
        curr_time = datetime.datetime.now()
        default_values = {
            'interface_pass_rate': '0.00%',
            'case_pass_rate': '0.00%',
            'update_user': TAPD["sync_user"],
            'update_time': curr_time,
            'create_user': TAPD["sync_user"],
            'create_time': curr_time,
            'stamp': 0
        }

        # 2. 过滤需要处理的执行ID
        need_to_handle_execute_ids = set(ready_to_handle_execute_id) - set(exclude_execute_id_list)

        # 3. 批量处理失败的执行ID
        for execute_id in need_to_handle_execute_ids:
            if ready_to_handle_execute_id_result_map.get(execute_id) != TestsetStatusEnum.FAILURE.status:
                continue

            t_id = execute_id_t_id_map.get(execute_id)
            if not t_id:
                logger.warning(f"执行ID {execute_id} 没有对应的t_id")
                continue

            # 处理测试集详情
            testset_detail_json = execute_id_testset_detail_map.get(execute_id)
            try:
                testset_detail = json.loads(testset_detail_json) if testset_detail_json else {}
            except json.JSONDecodeError:
                logger.error(f"执行ID {execute_id} 的测试集详情JSON解析失败: {testset_detail_json}")
                testset_detail = {}

            # 获取应用运行详情中的应用列表，用于条件判断
            allowed_apps = set()
            if execute_id_app_run_detail_map:
                app_run_detail_json = execute_id_app_run_detail_map.get(execute_id)
                if app_run_detail_json:
                    try:
                        app_run_detail = json.loads(app_run_detail_json) if app_run_detail_json else {}
                        app_list = app_run_detail.get('appList', [])
                        allowed_apps = set(app_list) if isinstance(app_list, list) else set()
                    except json.JSONDecodeError:
                        logger.error(f"执行ID {execute_id} 的应用运行详情JSON解析失败: {app_run_detail_json}")
                        allowed_apps = set()

            # 更新或创建测试结果记录
            for app_name, script_branch in testset_detail.items():
                # 自动扩展机制增加条件：只有在allowed_apps中存在的应用才执行自动扩展
                if execute_id_app_run_detail_map and allowed_apps and app_name not in allowed_apps:
                    logger.info(f"应用 {app_name} 不在允许的应用列表中，跳过自动扩展机制")
                    continue

                branch = 'master' if script_branch == 'lastArchived' else script_branch
                TestFlowRunRecordTestSetResult.objects.update_or_create(
                    t_id=t_id,
                    app_name=app_name,
                    script_branch=branch,
                    defaults=default_values
                )

    def get_not_running_execute_id_list(self, execute_id_list):
        url = '{}qa-info/external/testSet/getTestSetStatusList'.format(QAP["url"])
        params = {'executeIds': execute_id_list}
        testset_status_list = send_request(url, 'post', data=params)
        execute_id_list = []
        execute_id_status = []
        for item in testset_status_list:
            # todo 根据后面的接口返回值的改动调整，目前只处理end状态
            if item.get('status') == 'end':
                execute_id_list.append(item.get('executeId'))
                execute_id_status.append({"execute_id": item.get('executeId'),
                                          "status": TestsetStatusEnum.SUCCESS.status})
        return execute_id_list, execute_id_status

    def create_flow_run_record_testSet_result(self):
        """
        创建或更新测试集执行结果记录

        返回:
            datetime: 当前时间，用于记录操作时间戳
        """
        curr_time = datetime.datetime.now()

        # 1. 获取执行ID数据
        self._fetch_execute_id_data()
        if not self.execute_id_list:
            logger.info("未获取到需要处理的执行ID列表")
            return curr_time

        # 2. 获取非运行状态的执行ID
        self._get_non_running_execute_ids()

        # 3. 获取测试集报告数据
        testset_result_list = self._fetch_testset_reports()
        if not testset_result_list:
            logger.info("未获取到测试集报告数据")
            return curr_time

        # 4. 处理测试集结果
        self._process_testset_results(testset_result_list, curr_time)

        # 5. 处理失败状态的执行记录
        self._handle_failed_executions(curr_time)

        return curr_time

    def _fetch_execute_id_data(self):
        """获取执行ID相关数据"""
        (
            self.execute_id_list,
            self.execute_id_t_id_map,
            self.result_id_map,
            self.result_status_map,
            self.execute_id_testset_detail_map
        ) = MeasurementDao.get_execute_id_list()
        
        # 获取应用运行详情映射
        self.execute_id_app_run_detail_map = MeasurementDao.get_execute_id_app_run_detail_map()

    def _get_non_running_execute_ids(self):
        """获取非运行状态的执行ID"""
        (
            self.not_running_execute_id_list,
            self.execute_id_status_list
        ) = self.get_not_running_execute_id_list(self.execute_id_list)

    def _fetch_testset_reports(self):
        """获取测试集报告数据"""
        url = f'{QAP["url"]}qa-info/external/testSet/getTestSetReports'
        params = {'executeIds': self.not_running_execute_id_list}
        return send_request(url, 'post', data=params)

    def _process_testset_results(self, testset_result_list, curr_time):
        """处理测试集结果数据"""
        create_user = TAPD["sync_user"]
        ins_list = []

        with transaction.atomic():
            # 处理已有结果的记录
            self._update_existing_results(testset_result_list, create_user, curr_time)

            # 创建新记录
            self._create_new_results(testset_result_list, create_user, curr_time, ins_list)

            # 批量创建新记录
            if ins_list:
                TestFlowRunRecordTestSetResult.objects.bulk_create(ins_list)
                logger.info(f'同步测试集执行结果：{len(self.execute_id_list)} {len(ins_list)}')

            # 更新测试集状态
            self._update_testset_statuses()

    def _update_existing_results(self, testset_result_list, create_user, curr_time):
        """更新已有测试结果记录"""
        for item in testset_result_list:
            execute_id = item.get('executeId')
            t_id = self.execute_id_t_id_map.get(execute_id)

            # 用业务主键更新测试集结果信息 20250716 by fwm
            TestFlowRunRecordTestSetResult.objects.filter(
                t_id=t_id,
                app_name=item.get('appName'),
                script_branch=item.get('version')
            ).update(
                version_mode=item.get('versionMode'),
                interface_pass_rate=item.get('scriptPassPercent'),
                case_pass_rate=item.get('casePassPercent'),
                end_time=item.get('updateTime'),
                duration=item.get('duration'),
                merge_latest_status=item.get('mergeLatestStatus'),
                result_url=item.get('reportUrl'),
                update_user=create_user,
                update_time=curr_time
            )

    def _create_new_results(self, testset_result_list, create_user, curr_time, ins_list):
        """创建新的测试结果记录"""
        for item in testset_result_list:
            execute_id = item.get('executeId')
            t_id = self.execute_id_t_id_map.get(execute_id)
            result_id_list = self.result_id_map.get(t_id, [])

            if not result_id_list:
                obj = TestFlowRunRecordTestSetResult(
                    t_id=t_id,
                    app_name=item.get('appName'),
                    script_branch=item.get('version'),
                    version_mode=item.get('versionMode'),
                    interface_pass_rate=item.get('scriptPassPercent'),
                    case_pass_rate=item.get('casePassPercent'),
                    start_time=item.get('createTime'),
                    end_time=item.get('updateTime'),
                    duration=item.get('duration'),
                    merge_latest_status=item.get('mergeLatestStatus'),
                    result_url=item.get('reportUrl'),
                    create_time=curr_time,
                    create_user=create_user,
                    stamp=0
                )
                ins_list.append(obj)

    def _update_testset_statuses(self):
        """更新测试集状态"""
        for item in self.execute_id_status_list:
            TestFlowRunRecordTestset.objects.update_or_create(
                execute_id=item.get("execute_id"),
                defaults={
                    'testset_status': item.get("status"),
                    'update_time': datetime.datetime.now(),
                    'update_user': 'howbuyscm'
                }
            )

    def _handle_failed_executions(self, curr_time):
        """处理失败状态的执行记录"""
        self.handle_failed_execute_id_result_info(
            self.execute_id_list,
            self.result_status_map,
            self.execute_id_t_id_map,
            self.execute_id_testset_detail_map,
            self.not_running_execute_id_list,
            self.execute_id_app_run_detail_map
        )