import datetime

from mantis.settings import logger as log
from measurement.common.personal_capacity_common.personal_capacity_constants import MainSkillEnum
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaTypeEnum
from measurement.dao.personal_capacity_dao import \
    get_person_data_list_for_project_every_day_bug, get_team_data_list_for_project_every_day_bug
from measurement.service.personal_capacity_srv.personal_capacity_srv import <PERSON><PERSON><PERSON><PERSON><PERSON>
from measurement.utils.diskcache_utils import Disk<PERSON>acheUtil
from measurement.utils.personal_capacity_util.personal_capacity_util import get_cache_key_with_enum
from measurement.utils.personal_capacity_util.personal_capacity_util import get_start_end_time


class ProjectEveryDayBugNumHandler(ParentHandler):
    type_enum = PersCapaTypeEnum.PROJECT_EVERY_DAY_BUG_NUM
    skill_list = [
        MainSkillEnum.SERVER,
        MainSkillEnum.H5,
        MainSkillEnum.APP,
        MainSkillEnum.AI,
        MainSkillEnum.DEVOPS,
    ]

    def _get_team_data_list(self) -> list:
        # 使用全新的方式计算指标的起始时间。
        start_time, end_time = get_start_end_time(self.time_cycle_enum)
        if start_time == end_time:
            return []
        sql_st = datetime.datetime.now()
        team_data_list = get_team_data_list_for_project_every_day_bug(self.cn_name, start_time, end_time)
        sql_et = datetime.datetime.now()
        sql_cost_time = round((sql_et - sql_st).total_seconds(), 3)
        log.info(">>>> 参与项目日均检出bug数SQL耗时：{}s".format(sql_cost_time))
        return team_data_list

    def get_pers_capa_dict(self) -> dict:
        if not self.cn_name:
            raise ValueError(">>>> 用户「{}」没有中文名。".format(self.user_name))
        if not self.p_team_id:
            raise ValueError(">>>> 用户「{}」没有大团队信息。".format(self.user_name))
        if not self.team_id:
            raise ValueError(">>>> 用户「{}」没有小团队信息".format(self.user_name))

        pers_capa_dict = None
        if not self.is_skill:
            # 先手动添加缓存，后面改造成统一缓存。
            # 缓存获取
            cache_key = get_cache_key_with_enum(self.type_enum, self.user_name, self.time_cycle)
            cache_dict = DiskCacheUtil.get_value(cache_key)
            if cache_dict:
                pers_capa_dict = cache_dict
            else:
                # 从DB获取数据
                data_list = self._get_team_data_list()
                pers_capa_dict = self._computer_pers_capa_dict(data_list)
                # 填充缓存
                cache_dict = pers_capa_dict
                DiskCacheUtil.set_value(cache_key, cache_dict, 60 * 60 * 4)

        return pers_capa_dict

    def _get_person_data_only(self) -> list:
        start_time, end_time = get_start_end_time(self.time_cycle_enum)

        sql_st = datetime.datetime.now()
        if start_time == end_time:
            return []
        team_data_list = get_person_data_list_for_project_every_day_bug(self.cn_name, start_time, end_time)
        sql_et = datetime.datetime.now()
        sql_cost_time = round((sql_et - sql_st).total_seconds(), 3)
        log.info(">>>> 参与项目日均检出bug数SQL耗时：{}s".format(sql_cost_time))
        return team_data_list
