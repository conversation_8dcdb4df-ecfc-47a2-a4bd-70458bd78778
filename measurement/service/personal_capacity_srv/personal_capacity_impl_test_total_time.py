import datetime

from mantis.settings import logger as log
from measurement.common.personal_capacity_common.personal_capacity_constants import MainSkillEnum
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaTypeEnum
from measurement.dao.personal_capacity_dao import \
    get_person_data_list_for_test_total_time_with_biz, \
    get_team_data_list_for_test_total_time_with_biz
from measurement.service.personal_capacity_srv.personal_capacity_srv import Pa<PERSON><PERSON>andler
from measurement.utils.personal_capacity_util.personal_capacity_util import get_start_end_time


class TestTotalTimeHandler(ParentHandler):

    type_enum = PersCapaTypeEnum.TEST_TOTAL_TIME
    skill_list = [
        MainSkillEnum.TEST,
    ]

    def _get_team_data_list(self) -> list:
        # 使用全新的方式计算指标的起始时间。zt@2024-12-20
        start_time, end_time = get_start_end_time(self.time_cycle_enum)

        # 使用业务表过滤任务类型。zt@2024-12-13
        # 拆分研发和测试的总工时。zt@2024-12-27
        sql_st = datetime.datetime.now()
        team_data_list = get_team_data_list_for_test_total_time_with_biz(self.cn_name, start_time, end_time)
        sql_et = datetime.datetime.now()
        sql_cost_time = round((sql_et - sql_st).total_seconds(), 3)
        log.info(">>>> 测试总填写工时SQL耗时：{}s".format(sql_cost_time))
        return team_data_list

    def _get_person_data_only(self) -> list:
        start_time, end_time = get_start_end_time(self.time_cycle_enum)

        sql_st = datetime.datetime.now()
        team_data_list = get_person_data_list_for_test_total_time_with_biz(self.cn_name, start_time, end_time)
        sql_et = datetime.datetime.now()
        sql_cost_time = round((sql_et - sql_st).total_seconds(), 3)
        log.info(">>>> 测试总填写工时SQL耗时：{}s".format(sql_cost_time))
        return team_data_list
