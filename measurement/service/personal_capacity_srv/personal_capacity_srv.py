import traceback
from typing import Dict, Type
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaTypeEnum, \
    PersCapaBaseTypeEnum
from measurement.service.personal_capacity_srv.personal_capacity_impl import Pa<PERSON><PERSON>andler
from measurement.service.personal_capacity_srv.personal_capacity_impl_auto_test_assertion_num import \
    AutoTestAssertionNumHandler
from measurement.service.personal_capacity_srv.personal_capacity_impl_auto_test_case_num import \
    AutoTestCaseNumHandler
from measurement.service.personal_capacity_srv.personal_capacity_impl_bug_kloc import BugKLOCHandler
from measurement.service.personal_capacity_srv.personal_capacity_impl_cost_time import CostTimeHandler
from measurement.service.personal_capacity_srv.personal_capacity_impl_dev_test_delivery_time_percent import \
    ProjectDevTestDeliveryTimePercentHandler
from measurement.service.personal_capacity_srv.personal_capacity_impl_dev_total_time import DevTotalTimeHandler
from measurement.service.personal_capacity_srv.personal_capacity_impl_project_every_day_bug_num import \
    ProjectEveryDayBugNumHandler
from mantis.settings import logger as log
from measurement.service.personal_capacity_srv.personal_capacity_impl_month_bug_num import MonthBugNumHandler
from measurement.service.personal_capacity_srv.personal_capacity_impl_project_quality_inferior_num import \
    ProjectQualityInferiorNumHandler
from measurement.service.personal_capacity_srv.personal_capacity_impl_project_quality_inferior_percent import \
    ProjectQualityInferiorPercentHandler
from measurement.service.personal_capacity_srv.personal_capacity_impl_test_total_time import TestTotalTimeHandler
from measurement.service.personal_capacity_srv.personal_capacity_impl_dev_delivery_time import DevDeliveryTimeHandler
from measurement.service.personal_capacity_srv.personal_capacity_impl_test_delivery_time import TestDeliveryTimeHandler
from measurement.service.personal_capacity_srv.personal_capacity_impl_total_time import TotalTimeHandler
from measurement.service.personal_capacity_srv.personal_capacity_impl_test_time import TestTimeHandler
from measurement.service.personal_capacity_srv.personal_capacity_impl_code_line import CodeLineHandler
from measurement.service.personal_capacity_srv.personal_capacity_impl_bug_resolve_num import BugResolveNumHandler
from measurement.service.personal_capacity_srv.personal_capacity_impl_bug_resolve_time import BugResolveTimeHandler
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaAllTimeCycleEnum
from measurement.utils.personal_capacity_util.personal_capacity_util import get_title_for_check, parse_percent_value, \
    parse_bug_num_every_work_day
from measurement.utils.personal_capacity_util.personal_capacity_util import get_title_for_output_and_quality


class BaseHandler:
    def __init__(self, opt_user, time_cycle):
        self.opt_user = opt_user
        self.time_cycle = time_cycle

    def get_pers_capa_dict(self) -> dict:
        # 这里应该是各个Handler特有的实现
        pass


# 创建一个字典，将PersCapaTypeEnum映射到对应的Handler类
handler_mapping: Dict[PersCapaTypeEnum, Type[BaseHandler]] = {
    PersCapaTypeEnum.DEV_TOTAL_TIME: DevTotalTimeHandler,
    PersCapaTypeEnum.TEST_TOTAL_TIME: TestTotalTimeHandler,
    PersCapaTypeEnum.DEV_DELIVERY_TIME: DevDeliveryTimeHandler,
    PersCapaTypeEnum.TEST_DELIVERY_TIME: TestDeliveryTimeHandler,
    PersCapaTypeEnum.CODE_LINE: CodeLineHandler,
    PersCapaTypeEnum.BUG_RESOLVE_NUM: BugResolveNumHandler,
    PersCapaTypeEnum.BUG_RESOLVE_TIME: BugResolveTimeHandler,
    PersCapaTypeEnum.BUG_KLOC: BugKLOCHandler,
    PersCapaTypeEnum.AUTO_TEST_CASE_NUM: AutoTestCaseNumHandler,
    PersCapaTypeEnum.AUTO_TEST_ASSERTION_NUM: AutoTestAssertionNumHandler,
    PersCapaTypeEnum.MONTH_BUG_NUM: MonthBugNumHandler,
    PersCapaTypeEnum.PROJECT_EVERY_DAY_BUG_NUM: ProjectEveryDayBugNumHandler,
    PersCapaTypeEnum.PROJECT_QUALITY_INFERIOR_NUM: ProjectQualityInferiorNumHandler,
    PersCapaTypeEnum.PROJECT_QUALITY_INFERIOR_PERCENT: ProjectQualityInferiorPercentHandler,
    PersCapaTypeEnum.DEV_TEST_DELIVERY_TIME_PERCENT: ProjectDevTestDeliveryTimePercentHandler,
}


class PersCapaSrv:
    def __init__(self, opt_user, time_cycle):
        self.opt_user = opt_user
        self.time_cycle = time_cycle

    def get_person_capacity(self, pers_capa_type: PersCapaBaseTypeEnum):
        res_list = []
        pct_enum_map = {
            PersCapaBaseTypeEnum.CHECK_INDEX: [
                PersCapaTypeEnum.DEV_TOTAL_TIME,
                PersCapaTypeEnum.TEST_TOTAL_TIME,
                PersCapaTypeEnum.DEV_DELIVERY_TIME,
                PersCapaTypeEnum.TEST_DELIVERY_TIME,
                PersCapaTypeEnum.CODE_LINE,
                PersCapaTypeEnum.BUG_RESOLVE_TIME,
                PersCapaTypeEnum.AUTO_TEST_CASE_NUM,
                PersCapaTypeEnum.BUG_RESOLVE_NUM,
                PersCapaTypeEnum.BUG_KLOC
            ],
            PersCapaBaseTypeEnum.OUTPUT_INDEX: [
                PersCapaTypeEnum.DEV_TOTAL_TIME,
                PersCapaTypeEnum.TEST_TOTAL_TIME,
                PersCapaTypeEnum.DEV_DELIVERY_TIME,
                PersCapaTypeEnum.TEST_DELIVERY_TIME,
                PersCapaTypeEnum.CODE_LINE,
                PersCapaTypeEnum.BUG_RESOLVE_TIME,
                PersCapaTypeEnum.AUTO_TEST_CASE_NUM
            ],
            PersCapaBaseTypeEnum.QUALITY_INDEX: [
                PersCapaTypeEnum.BUG_RESOLVE_NUM,
                PersCapaTypeEnum.BUG_KLOC
            ]
        }
        pct_enum_list = pct_enum_map.get(pers_capa_type, [])
        for pct_enum in pct_enum_list:
            pct_dict = self.get_person_capacity_dict(pct_enum)
            if pct_dict:
                res_list.append(pct_dict)

        return res_list

    def get_person_capacity_dict(self, pct_enum: PersCapaTypeEnum) -> dict:
        # pct_handler = None
        # if pct_enum == PersCapaTypeEnum.DEV_TOTAL_TIME:
        #     pct_handler = DevTotalTimeHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.TEST_TOTAL_TIME:
        #     pct_handler = TestTotalTimeHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.DEV_DELIVERY_TIME:
        #     pct_handler = DevDeliveryTimeHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.TEST_DELIVERY_TIME:
        #     pct_handler = TestDeliveryTimeHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.CODE_LINE:
        #     pct_handler = CodeLineHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.BUG_RESOLVE_NUM:
        #     pct_handler = BugResolveNumHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.BUG_RESOLVE_TIME:
        #     pct_handler = BugResolveTimeHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.BUG_KLOC:
        #     pct_handler = BugKLOCHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.AUTO_TEST_CASE_NUM:
        #     pct_handler = AutoTestCaseNumHandler(self.opt_user, self.time_cycle)
        # else:
        #     pct_handler = ParentHandler(self.opt_user, self.time_cycle)
        #
        # pct_dict = None
        # if pct_handler:
        #     pct_dict = pct_handler.get_pers_capa_dict()
        # return pct_dict
        # 使用字典映射来获取对应的处理器类，并实例化它
        handler_class = handler_mapping.get(pct_enum, ParentHandler)
        pct_handler = handler_class(self.opt_user, self.time_cycle)

        # 调用处理器的方法来获取个人能力字典
        return pct_handler.get_pers_capa_dict()

    def get_person_capacity_only(self, pct_enum: PersCapaTypeEnum) -> dict:
        """考核季指标--拆分个人。zt@2024-12-30"""
        # pct_handler = None
        # if pct_enum == PersCapaTypeEnum.DEV_TOTAL_TIME:
        #     pct_handler = DevTotalTimeHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.TEST_TOTAL_TIME:
        #     pct_handler = TestTotalTimeHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.DEV_DELIVERY_TIME:
        #     pct_handler = DevDeliveryTimeHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.TEST_DELIVERY_TIME:
        #     pct_handler = TestDeliveryTimeHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.CODE_LINE:
        #     pct_handler = CodeLineHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.BUG_RESOLVE_NUM:
        #     pct_handler = BugResolveNumHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.BUG_RESOLVE_TIME:
        #     pct_handler = BugResolveTimeHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.BUG_KLOC:
        #     pct_handler = BugKLOCHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.AUTO_TEST_CASE_NUM:
        #     pct_handler = AutoTestCaseNumHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.MONTH_BUG_NUM:
        #     pct_handler = MonthBugNumHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.PROJECT_EVERY_DAY_BUG_NUM:
        #     pct_handler = ProjectEveryDayBugNumHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.PROJECT_QUALITY_INFERIOR_NUM:
        #     pct_handler = ProjectQualityInferiorNumHandler(self.opt_user, self.time_cycle)
        # elif pct_enum == PersCapaTypeEnum.PROJECT_QUALITY_INFERIOR_PERCENT:
        #     pct_handler = ProjectQualityInferiorPercentHandler(self.opt_user, self.time_cycle)
        # else:
        #     pct_handler = ParentHandler(self.opt_user, self.time_cycle)

        handler_class = handler_mapping.get(pct_enum, ParentHandler)
        pct_handler = handler_class(self.opt_user, self.time_cycle)
        pct_dict = pct_handler.get_person_capacity_only()
        return pct_dict

    def get_person_capacity_for_check(self):
        # ret_dict = {}
        # output
        # output_enum_list = [
        #     PersCapaTypeEnum.DEV_TOTAL_TIME,
        #     PersCapaTypeEnum.TEST_DELIVERY_TIME,
        #     PersCapaTypeEnum.DEV_DELIVERY_TIME,
        #     PersCapaTypeEnum.TEST_DELIVERY_TIME,
        #     PersCapaTypeEnum.CODE_LINE,
        #     PersCapaTypeEnum.BUG_RESOLVE_TIME,
        #     PersCapaTypeEnum.AUTO_TEST_CASE_NUM,
        #     PersCapaTypeEnum.TOTAL_TIME_TO_IDEAL_TIME_RATE,
        #     PersCapaTypeEnum.DELIVERY_TIME_TO_TOTAL_TIME_RATE,
        # ]
        #
        # # quality
        # quality_enum_list = [
        #     PersCapaTypeEnum.BUG_RESOLVE_NUM,
        #     PersCapaTypeEnum.BUG_KLOC,
        #     PersCapaTypeEnum.MONTH_BUG_NUM,
        #     PersCapaTypeEnum.PROJECT_QUALITY_INFERIOR_NUM,
        #     PersCapaTypeEnum.PROJECT_QUALITY_INFERIOR_PERCENT,
        #     # PersCapaTypeEnum.PROJECT_EVERY_DAY_BUG_NUM,
        # ]
        #
        # # all
        # pct_enum_list = output_enum_list + quality_enum_list
        # 定义输出和质量指标的枚举集合
        output_enum_set = {
            PersCapaTypeEnum.DEV_TOTAL_TIME,
            PersCapaTypeEnum.TEST_TOTAL_TIME,
            PersCapaTypeEnum.DEV_DELIVERY_TIME,
            PersCapaTypeEnum.TEST_DELIVERY_TIME,
            PersCapaTypeEnum.CODE_LINE,
            PersCapaTypeEnum.BUG_RESOLVE_TIME,
            PersCapaTypeEnum.AUTO_TEST_CASE_NUM,
            PersCapaTypeEnum.AUTO_TEST_ASSERTION_NUM,
            PersCapaTypeEnum.TOTAL_TIME_TO_IDEAL_TIME_RATE,
            PersCapaTypeEnum.DELIVERY_TIME_TO_TOTAL_TIME_RATE,
            PersCapaTypeEnum.DEV_TEST_DELIVERY_TIME_PERCENT,
        }

        quality_enum_set = {
            PersCapaTypeEnum.BUG_RESOLVE_NUM,
            PersCapaTypeEnum.BUG_KLOC,
            PersCapaTypeEnum.MONTH_BUG_NUM,
            PersCapaTypeEnum.PROJECT_QUALITY_INFERIOR_NUM,
            PersCapaTypeEnum.PROJECT_QUALITY_INFERIOR_PERCENT,
            # PersCapaTypeEnum.PROJECT_EVERY_DAY_BUG_NUM,
        }
        # 合并所有指标的枚举集合
        pct_enum_set = output_enum_set.union(quality_enum_set)

        time_enum_list = [
            PersCapaAllTimeCycleEnum.THIS_QUARTER,
            PersCapaAllTimeCycleEnum.THIS_YEAR,
            PersCapaAllTimeCycleEnum.BEFORE_FIRST_QUARTER,
            PersCapaAllTimeCycleEnum.BEFORE_SECOND_QUARTER,
            PersCapaAllTimeCycleEnum.BEFORE_THIRD_QUARTER,
            PersCapaAllTimeCycleEnum.BEFORE_FOURTH_QUARTER,
        ]

        res_list = []
        for time_enum in time_enum_list:
            time_cycle = time_enum.time_cycle_code
            title, ideal_working_hours, month_diff, work_days_diff = get_title_for_check(time_enum)

            # data_dict = {
            #     "time_cycle": time_cycle,
            #     "title": title
            # }
            # self.time_cycle = time_cycle
            # for pct_enum in pct_enum_list:
            #     type_code = pct_enum.type_code
            #     pct_dict = self.get_person_capacity_only(pct_enum)
            #     # user_value = None
            #     if pct_dict:
            #         user_value = pct_dict.get("user_value")
            #         if not user_value:
            #             user_value = 0.0
            #         data_dict[type_code] = user_value
            #
            # # 所有数据取完后，根据已获取的数据计算新的指标。
            # data_dict = self.add_new_pers_capa(data_dict, ideal_working_hours, month_diff, work_days_diff)
            #
            # res_list.append(data_dict)
            data_dict = self.process_time_cycle(time_cycle, title, pct_enum_set, ideal_working_hours, month_diff,
                                                work_days_diff)
            res_list.append(data_dict)

        # output_header = self.get_person_capacity_dict_header_list(output_enum_list)
        # quality_header = self.get_person_capacity_dict_header_list(quality_enum_list)
        output_handler_list = [
            DevTotalTimeHandler(self.opt_user, self.time_cycle),
            TestTotalTimeHandler(self.opt_user, self.time_cycle),
            DevDeliveryTimeHandler(self.opt_user, self.time_cycle),
            TestDeliveryTimeHandler(self.opt_user, self.time_cycle),
            CodeLineHandler(self.opt_user, self.time_cycle),
            BugResolveTimeHandler(self.opt_user, self.time_cycle),
            AutoTestCaseNumHandler(self.opt_user, self.time_cycle),
            AutoTestAssertionNumHandler(self.opt_user, self.time_cycle),
            ProjectDevTestDeliveryTimePercentHandler(self.opt_user, self.time_cycle)
        ]
        output_no_handler_enum_list = [
            PersCapaTypeEnum.TOTAL_TIME_TO_IDEAL_TIME_RATE,
            PersCapaTypeEnum.DELIVERY_TIME_TO_TOTAL_TIME_RATE,
        ]
        output_handler_header_list = self.get_person_capacity_dict_header_list_with_handler(output_handler_list)
        output_no_handler_header_list = self.get_person_capacity_dict_header_list(output_no_handler_enum_list)
        output_header = output_handler_header_list + output_no_handler_header_list

        quality_handler_list = [
            BugResolveNumHandler(self.opt_user, self.time_cycle),
            BugKLOCHandler(self.opt_user, self.time_cycle),
            MonthBugNumHandler(self.opt_user, self.time_cycle),
            # ProjectEveryDayBugNumHandler(self.opt_user, self.time_cycle),
            ProjectQualityInferiorNumHandler(self.opt_user, self.time_cycle),
            ProjectQualityInferiorPercentHandler(self.opt_user, self.time_cycle),
        ]
        quality_no_handler_enum_list = [

        ]
        quality_handler_header_list = self.get_person_capacity_dict_header_list_with_handler(quality_handler_list)
        quality_no_handler_header_list = self.get_person_capacity_dict_header_list(quality_no_handler_enum_list)
        quality_header = quality_handler_header_list + quality_no_handler_header_list

        ret_dict = {
            "outputHeader": output_header,
            "qualityHeader": quality_header,
            "list": res_list,
        }

        return ret_dict

    def process_time_cycle(self, time_cycle, title, pct_enum_set, ideal_working_hours, month_diff, work_days_diff):
        data_dict = {
            "time_cycle": time_cycle,
            "title": title
        }
        self.time_cycle = time_cycle
        for pct_enum in pct_enum_set:
            pct_dict = self.get_person_capacity_only(pct_enum)
            user_value = 0.0
            if pct_dict:
                user_value = pct_dict.get("user_value")
            data_dict[pct_enum.type_code] = user_value

        # 计算新的指标
        return self.add_new_pers_capa(data_dict, ideal_working_hours, month_diff, work_days_diff)

    def process_time_cycle_for_output_and_quality(self, time_cycle, title, pct_enum_set, ideal_working_hours, work_days_diff):
        """调用子类的指标计算，专用于我的产能和我的质量。zt@2025-02-18"""
        data_dict = {
            "time_cycle": time_cycle,
            "title": title
        }
        self.time_cycle = time_cycle
        for pct_enum in pct_enum_set:
            pct_dict = self.get_person_capacity_dict(pct_enum)

            if pct_dict:
                pers_capa_dict = {
                    "type_name": pct_enum.type_code,
                    "user_value": pct_dict.get("user_value"),
                    "team_median": pct_dict.get("team_median"),
                    "team_mean": pct_dict.get("team_mean"),
                    "team_sum": pct_dict.get("team_sum"),
                    "team_percent": pct_dict.get("team_percent"),
                    "p_team_median": pct_dict.get("p_team_median"),
                    "p_team_mean": pct_dict.get("p_team_mean"),
                    "p_team_sum": pct_dict.get("p_team_sum"),
                    "p_team_percent": pct_dict.get("p_team_percent"),
                }
            else:
                pers_capa_dict = {
                    "type_name": pct_enum.type_code,
                    "user_value": 0.0,
                    "team_median": 0.0,
                    "team_mean": 0.0,
                    "team_sum": 0.0,
                    "team_percent": 0.0,
                    "p_team_median": 0.0,
                    "p_team_mean": 0.0,
                    "p_team_sum": 0.0,
                    "p_team_percent": 0.0,
                }
            data_dict[pct_enum.type_code] = pers_capa_dict

        # 计算新的指标
        return self.add_new_pers_capa_for_output_and_quality(data_dict, ideal_working_hours, work_days_diff)

    def add_new_pers_capa(self, data_dict, ideal_working_hours=None, month_diff=None, work_days_diff=None) -> dict:
        total_time = 0
        delivery_time = 0
        quality_inferior_percent = 0
        for k, v in data_dict.items():
            if k == PersCapaTypeEnum.DEV_TOTAL_TIME.type_code or k == PersCapaTypeEnum.TEST_TOTAL_TIME.type_code:
                if v:
                    total_time = v

            if k == PersCapaTypeEnum.DEV_DELIVERY_TIME.type_code or k == PersCapaTypeEnum.TEST_DELIVERY_TIME.type_code:
                if v:
                    delivery_time = v
            if k == PersCapaTypeEnum.PROJECT_QUALITY_INFERIOR_PERCENT.type_code:
                if v:
                    quality_inferior_percent = v
            if k not in ("time_cycle", "title"):

                if (k == PersCapaTypeEnum.DEV_TOTAL_TIME.type_code
                        or k == PersCapaTypeEnum.TEST_TOTAL_TIME.type_code
                        or k == PersCapaTypeEnum.DEV_DELIVERY_TIME.type_code
                        or k == PersCapaTypeEnum.TEST_DELIVERY_TIME.type_code
                        or k == PersCapaTypeEnum.MONTH_BUG_NUM.type_code
                        or k == PersCapaTypeEnum.CODE_LINE.type_code):
                    # 优化当季首月，月间隔为0的情况。zt@2025-04-09
                    if month_diff == 0:
                        v = 0.0
                    else:
                        v = round(v / month_diff, 3)
                    data_dict[k] = v
                elif k == PersCapaTypeEnum.PROJECT_EVERY_DAY_BUG_NUM.type_code:
                    try:
                        log.info(">>>> month_diff = {}, work_days_diff = {}, value = {}".format(month_diff, work_days_diff, v))
                        v = 0.0 if (work_days_diff == 0 or int(v) == 0) else round(v / work_days_diff, 3)
                        data_dict[k] = v
                        log.info(">>>>new value = {}".format(v))
                    except Exception as error:
                        log.error(">>>> error = {}".format(error))
                        traceback.print_exc()
                        pass
            elif k == PersCapaTypeEnum.BUG_RESOLVE_TIME.type_code:
                v = round(v, 3)
                data_dict[k] = v
        if quality_inferior_percent == 0:
            data_dict[PersCapaTypeEnum.PROJECT_QUALITY_INFERIOR_PERCENT.type_code] = "0%"
        else:
            data_dict[PersCapaTypeEnum.PROJECT_QUALITY_INFERIOR_PERCENT.type_code] = str(round(quality_inferior_percent * 100, 2)) + "%"
        if not total_time or total_time == 0:
            data_dict[PersCapaTypeEnum.DELIVERY_TIME_TO_TOTAL_TIME_RATE.type_code] = "0%"
            data_dict[PersCapaTypeEnum.TOTAL_TIME_TO_IDEAL_TIME_RATE.type_code] = "0%"
        else:
            data_dict[PersCapaTypeEnum.DELIVERY_TIME_TO_TOTAL_TIME_RATE.type_code] = str(
                round(delivery_time * 100 / total_time, 1)) + "%"
            data_dict[PersCapaTypeEnum.TOTAL_TIME_TO_IDEAL_TIME_RATE.type_code] = str(
                round(total_time * 100 / ideal_working_hours, 1)) + "%"
        return data_dict

    def add_new_pers_capa_for_output_and_quality(self, data_dict, ideal_working_hours=None, work_days_diff=None) -> dict:
        self_time_cycle = self.time_cycle
        log.info(">>> self_time_cycle = {}".format(self_time_cycle))

        ret_dict = {}
        # 这4个要用于后续计算
        dev_total_time_dict = None
        test_total_time_dict = None
        dev_delivery_time_dict = None
        test_delivery_time_dict = None

        for type_code, pers_capa_dict in data_dict.items():
            if type_code not in ("time_cycle", "title"):
                if not pers_capa_dict:
                    raise ValueError("pers_capa_dict is None")
                # 指标拆分（指标的计算和处理差异性很多，并且后续计算还要用到其它指标，合并不是一个好的设计）
                user_value = pers_capa_dict.get("user_value")
                team_median = pers_capa_dict.get("team_median")
                team_mean = pers_capa_dict.get("team_mean")
                if type_code == PersCapaTypeEnum.DEV_TOTAL_TIME.type_code:
                    dev_total_time_dict = pers_capa_dict
                elif type_code == PersCapaTypeEnum.TEST_TOTAL_TIME.type_code:
                    test_total_time_dict = pers_capa_dict
                elif type_code == PersCapaTypeEnum.DEV_DELIVERY_TIME.type_code:
                    dev_delivery_time_dict = pers_capa_dict
                elif type_code == PersCapaTypeEnum.TEST_DELIVERY_TIME.type_code:
                    test_delivery_time_dict  = pers_capa_dict
                elif type_code == PersCapaTypeEnum.PROJECT_QUALITY_INFERIOR_PERCENT.type_code:
                    user_value = parse_percent_value(user_value, 2)
                    team_median = parse_percent_value(team_median, 2)
                    team_mean = parse_percent_value(team_mean, 2)
                elif (type_code == PersCapaTypeEnum.MONTH_BUG_NUM.type_code
                      or type_code == PersCapaTypeEnum.CODE_LINE.type_code
                      or type_code == PersCapaTypeEnum.BUG_RESOLVE_TIME.type_code):
                    user_value = round(user_value, 3)
                    team_median = round(team_median, 3)
                    team_mean = round(team_mean, 3)
                elif type_code == PersCapaTypeEnum.PROJECT_EVERY_DAY_BUG_NUM.type_code:
                    user_value = parse_bug_num_every_work_day(user_value, work_days_diff, 3)
                    team_median = parse_bug_num_every_work_day(team_median, work_days_diff, 3)
                    team_mean = parse_bug_num_every_work_day(team_mean, work_days_diff, 3)
                else:
                    pass # 其它指标不额外处理。

                # 统一格式化处理
                ret_dict[type_code] = "{}／{}／{}".format(user_value, team_median, team_mean)
            else:
                # 单一格式不需要处理的kv
                ret_dict[type_code] = pers_capa_dict

        # 没错，循环完成后，还需要额外处理。（拆分的问题之一）
        dev_total_time_user_value = 0
        dev_total_time_team_median = 0
        dev_total_time_team_mean = 0
        dev_delivery_time_user_value = 0
        dev_delivery_time_team_median = 0
        dev_delivery_time_team_mean = 0

        test_total_time_user_value = 0
        test_total_time_team_median = 0
        test_total_time_team_mean = 0
        test_delivery_time_user_value = 0
        test_delivery_time_team_median = 0
        test_delivery_time_team_mean = 0

        # 埋个坑：先看开发有没有数据，如果没有就按测试处理。（不好一次改造完）
        if dev_total_time_dict:
            dev_total_time_user_value = dev_total_time_dict.get("user_value")
            dev_total_time_team_median = dev_total_time_dict.get("team_median")
            dev_total_time_team_mean = dev_total_time_dict.get("team_mean")
        if dev_delivery_time_dict:
            dev_delivery_time_user_value = dev_delivery_time_dict.get("user_value")
            dev_delivery_time_team_median = dev_delivery_time_dict.get("team_median")
            dev_delivery_time_team_mean = dev_delivery_time_dict.get("team_mean")
        if test_total_time_dict:
            test_total_time_user_value = test_total_time_dict.get("user_value")
            test_total_time_team_median = test_total_time_dict.get("team_median")
            test_total_time_team_mean = test_total_time_dict.get("team_mean")
        if test_delivery_time_dict:
            test_delivery_time_user_value = test_delivery_time_dict.get("user_value")
            test_delivery_time_team_median = test_delivery_time_dict.get("team_median")
            test_delivery_time_team_mean = test_delivery_time_dict.get("team_mean")
        # d2t：delivery_time_to_total
        # t2i：total_time_to_ideal
        if dev_total_time_user_value:
            d2t_user_val_time_rate = round(dev_delivery_time_user_value * 100 / dev_total_time_user_value, 1)
            d2t_team_median_time_rate = round(dev_delivery_time_team_median * 100 / dev_total_time_team_median, 1)
            d2t_team_mean_time_rate = round(dev_delivery_time_team_mean * 100 / dev_total_time_team_mean, 1)

            t2i_user_val_time_rate = round(dev_total_time_user_value * 100 / ideal_working_hours, 1)
            t2i_team_median_time_rate = round(dev_total_time_team_median * 100 / ideal_working_hours, 1)
            t2i_team_mean_time_rate = round(dev_total_time_team_mean * 100 / ideal_working_hours, 1)
        elif test_total_time_user_value:
            d2t_user_val_time_rate = round(test_delivery_time_user_value * 100 / test_total_time_user_value, 1)
            d2t_team_median_time_rate = round(test_delivery_time_team_median * 100 / test_total_time_team_median, 1)
            d2t_team_mean_time_rate = round(test_delivery_time_team_mean * 100 / test_total_time_team_mean, 1)

            t2i_user_val_time_rate = round(test_total_time_user_value * 100 / ideal_working_hours, 1)
            t2i_team_median_time_rate = round(test_total_time_team_median * 100 / ideal_working_hours, 1)
            t2i_team_mean_time_rate = round(test_total_time_team_mean * 100 / ideal_working_hours, 1)
        else:
            d2t_user_val_time_rate = "0%"
            d2t_team_median_time_rate = "0%"
            d2t_team_mean_time_rate = "0%"

            t2i_user_val_time_rate = "0%"
            t2i_team_median_time_rate = "0%"
            t2i_team_mean_time_rate = "0%"

        ret_dict[PersCapaTypeEnum.DELIVERY_TIME_TO_TOTAL_TIME_RATE.type_code] = "{}/{}/{}".format(
            d2t_user_val_time_rate,
            d2t_team_median_time_rate,
            d2t_team_mean_time_rate)
        ret_dict[PersCapaTypeEnum.TOTAL_TIME_TO_IDEAL_TIME_RATE.type_code] = "{}/{}/{}".format(
            t2i_user_val_time_rate,
            t2i_team_median_time_rate,
            t2i_team_mean_time_rate)

        return ret_dict

    def get_person_capacity_dict_header_list(self, pct_enum_list) -> list:
        header_list = []
        if pct_enum_list:
            for pct_enum in pct_enum_list:
                key = pct_enum.type_code
                text = pct_enum.type_name
                header_dict = {
                    "key": key,
                    "text": text,
                }
                header_list.append(header_dict)

        return header_list

    def get_person_capacity_dict_header_list_with_handler(self, handler_obj_list) -> list:
        header_list = []
        if handler_obj_list:
            for handler_obj in handler_obj_list:
                # 利用handler处理技能匹配
                if handler_obj and handler_obj.is_skill:
                    pct_enum = handler_obj.type_enum
                    if pct_enum:
                        key = pct_enum.type_code
                        text = pct_enum.type_name
                        header_dict = {
                            "key": key,
                            "text": text,
                        }
                        header_list.append(header_dict)
        return header_list

    def get_person_capacity_for_output(self):
        """我的产能近10个月的数据。zt@2025-02-17"""
        # 定义「我的产能」（包含开发、测试人员）需要的数据数据项枚举。
        output_enum_set = {
            PersCapaTypeEnum.CODE_LINE,
            PersCapaTypeEnum.DEV_TOTAL_TIME,
            PersCapaTypeEnum.DEV_DELIVERY_TIME,

            PersCapaTypeEnum.TEST_TOTAL_TIME,
            PersCapaTypeEnum.TEST_DELIVERY_TIME,
            PersCapaTypeEnum.AUTO_TEST_CASE_NUM,
            PersCapaTypeEnum.AUTO_TEST_ASSERTION_NUM,

            PersCapaTypeEnum.TOTAL_TIME_TO_IDEAL_TIME_RATE,
            PersCapaTypeEnum.DELIVERY_TIME_TO_TOTAL_TIME_RATE,
        }
        # 定义近10个月的时间枚举（修改成4个月_for张老板。zt@2025-02-18）
        time_enum_list = [
            PersCapaAllTimeCycleEnum.THIS_MONTH,
            PersCapaAllTimeCycleEnum.BEFORE_FIRST_MONTH,
            PersCapaAllTimeCycleEnum.BEFORE_SECOND_MONTH,
            PersCapaAllTimeCycleEnum.BEFORE_THIRD_MONTH,
            # PersCapaAllTimeCycleEnum.BEFORE_FOURTH_MONTH,
            # PersCapaAllTimeCycleEnum.BEFORE_FIFTH_MONTH,
            # PersCapaAllTimeCycleEnum.BEFORE_SIXTH_MONTH,
            # PersCapaAllTimeCycleEnum.BEFORE_SEVENTH_MONTH,
            # PersCapaAllTimeCycleEnum.BEFORE_EIGHTH_MONTH,
            # PersCapaAllTimeCycleEnum.BEFORE_NINTH_MONTH,
        ]
        # 循环计划N个时间周期的数据
        res_list = []
        output_header = []
        quality_header = []
        for time_enum in time_enum_list:
            time_cycle = time_enum.time_cycle_code
            # 标题和时间片的计算
            title, ideal_working_hours, month_diff, work_days_diff = get_title_for_output_and_quality(time_enum)
            # 每个时间片中的多个指标
            data_dict = self.process_time_cycle_for_output_and_quality(time_cycle,
                                                                       title,
                                                                       output_enum_set,
                                                                       ideal_working_hours,
                                                                       work_days_diff)
            res_list.append(data_dict)
        # 计算header
        output_header = self.get_output_header_list()
        # 汇总返回，和「考核季」保持一致。
        ret_dict = {
            "outputHeader": output_header,
            "qualityHeader": quality_header,
            "list": res_list,
        }

        return ret_dict

    def get_output_header_list(self):
        """获取我的产能的header列表，一种需要通过handler计算，另一种直接输出。zt@2025-02-17"""
        output_handler_list = [
            # 产能-研发
            CodeLineHandler(self.opt_user, self.time_cycle),
            DevTotalTimeHandler(self.opt_user, self.time_cycle),
            DevDeliveryTimeHandler(self.opt_user, self.time_cycle),
            # 产能-测试
            TestTotalTimeHandler(self.opt_user, self.time_cycle),
            TestDeliveryTimeHandler(self.opt_user, self.time_cycle),
            AutoTestCaseNumHandler(self.opt_user, self.time_cycle),
            AutoTestAssertionNumHandler(self.opt_user, self.time_cycle),
            # 质量-研发
            # BugResolveTimeHandler(self.opt_user, self.time_cycle),
            # BugResolveNumHandler(self.opt_user, self.time_cycle),
            # 质量-测试
            #   - 产线缺陷数（待实现）
            # 考核季才需要的额外指标
            #   - BugKLOCHandler(self.opt_user, self.time_cycle),
            #   - ProjectQualityInferiorNumHandler(self.opt_user, self.time_cycle),
            #   - ProjectQualityInferiorPercentHandler(self.opt_user, self.time_cycle),
            #   - ProjectDevTestDeliveryTimePercentHandler(self.opt_user, self.time_cycle),
        ]
        output_no_handler_enum_list = [
            # 产能-研发&测试
            PersCapaTypeEnum.TOTAL_TIME_TO_IDEAL_TIME_RATE,
            PersCapaTypeEnum.DELIVERY_TIME_TO_TOTAL_TIME_RATE,
        ]
        output_handler_header_list = self.get_person_capacity_dict_header_list_with_handler(output_handler_list)
        output_no_handler_header_list = self.get_person_capacity_dict_header_list(output_no_handler_enum_list)
        output_header_list = output_handler_header_list + output_no_handler_header_list
        return  output_header_list

    def get_person_capacity_for_quality(self):
        """我的质量近10个月的数据。zt@2025-02-18"""
        # 定义「我的产能」（包含开发、测试人员）需要的数据数据项枚举。
        quality_enum_set = {
            PersCapaTypeEnum.BUG_RESOLVE_TIME,
            PersCapaTypeEnum.BUG_RESOLVE_NUM,
        }
        # 定义近10个月的时间枚举
        time_enum_list = [
            PersCapaAllTimeCycleEnum.THIS_MONTH,
            PersCapaAllTimeCycleEnum.BEFORE_FIRST_MONTH,
            PersCapaAllTimeCycleEnum.BEFORE_SECOND_MONTH,
            PersCapaAllTimeCycleEnum.BEFORE_THIRD_MONTH,
            # PersCapaAllTimeCycleEnum.BEFORE_FOURTH_MONTH,
            # PersCapaAllTimeCycleEnum.BEFORE_FIFTH_MONTH,
            # PersCapaAllTimeCycleEnum.BEFORE_SIXTH_MONTH,
            # PersCapaAllTimeCycleEnum.BEFORE_SEVENTH_MONTH,
            # PersCapaAllTimeCycleEnum.BEFORE_EIGHTH_MONTH,
            # PersCapaAllTimeCycleEnum.BEFORE_NINTH_MONTH,
        ]
        # 循环计划N个时间周期的数据
        res_list = []
        output_header = []
        quality_header = []
        for time_enum in time_enum_list:
            time_cycle = time_enum.time_cycle_code
            # 标题和时间片的计算
            title, ideal_working_hours, month_diff, work_days_diff = get_title_for_output_and_quality(time_enum)
            # 每个时间片中的多个指标
            data_dict = self.process_time_cycle_for_output_and_quality(time_cycle,
                                                                       title,
                                                                       quality_enum_set,
                                                                       ideal_working_hours,
                                                                       work_days_diff)
            res_list.append(data_dict)
        # 计算header
        quality_header = self.get_quality_header_list()
        # 汇总返回，和「考核季」保持一致。
        ret_dict = {
            "outputHeader": output_header,
            "qualityHeader": quality_header,
            "list": res_list,
        }

        return ret_dict

    def get_quality_header_list(self):
        """获取我的质量的header列表，一种需要通过handler计算，另一种直接输出。zt@2025-02-18"""
        quality_handler_list = [
            # 产能-研发
            # CodeLineHandler(self.opt_user, self.time_cycle),
            # DevTotalTimeHandler(self.opt_user, self.time_cycle),
            # DevDeliveryTimeHandler(self.opt_user, self.time_cycle),
            # 产能-测试
            # TestTotalTimeHandler(self.opt_user, self.time_cycle),
            # TestDeliveryTimeHandler(self.opt_user, self.time_cycle),
            # AutoTestCaseNumHandler(self.opt_user, self.time_cycle),
            # 质量-研发
            BugResolveTimeHandler(self.opt_user, self.time_cycle),
            BugResolveNumHandler(self.opt_user, self.time_cycle),
            # 质量-测试
            #   - 产线缺陷数（待实现）
            # 考核季才需要的额外指标
            #   - BugKLOCHandler(self.opt_user, self.time_cycle),
            #   - ProjectQualityInferiorNumHandler(self.opt_user, self.time_cycle),
            #   - ProjectQualityInferiorPercentHandler(self.opt_user, self.time_cycle),
            #   - ProjectDevTestDeliveryTimePercentHandler(self.opt_user, self.time_cycle),
        ]
        quality_no_handler_enum_list = [

        ]
        quality_handler_header_list = self.get_person_capacity_dict_header_list_with_handler(quality_handler_list)
        quality_no_handler_header_list = self.get_person_capacity_dict_header_list(quality_no_handler_enum_list)
        quality_header = quality_handler_header_list + quality_no_handler_header_list
        return  quality_header
