from measurement.common.personal_capacity_common.personal_capacity_constants import MainSkillEnum
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaTypeEnum
from measurement.dao.personal_capacity_dao import get_team_data_list_for_test_delivery_time_with_biz
from measurement.service.personal_capacity_srv.personal_capacity_srv import <PERSON><PERSON><PERSON><PERSON><PERSON>
from measurement.utils.personal_capacity_util.personal_capacity_util import get_start_end_time


class TestTimeHandler(ParentHandler):

    type_enum = PersCapaTypeEnum.TEST_DELIVERY_TIME
    skill_list = [
        MainSkillEnum.TEST,
    ]

    def _get_team_data_list(self) -> list:
        # 使用全新的方式计算指标的起始时间。zt@2024-12-23
        start_time, end_time = get_start_end_time(self.time_cycle_enum)

        # 使用业务表过滤任务类型。zt@2024-12-11
        team_data_list = get_team_data_list_for_test_delivery_time_with_biz(self.cn_name, start_time, end_time)
        return team_data_list
