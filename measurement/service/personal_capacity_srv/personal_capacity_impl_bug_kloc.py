import datetime

from mantis.settings import logger as log
from measurement.common.personal_capacity_common.personal_capacity_constants import MainSkillEnum, \
    PersCapaAllTimeCycleEnum
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaTypeEnum
from measurement.dao.personal_capacity_dao import get_team_data_list_for_bug_kloc, \
    get_person_data_list_for_bug_kloc
from measurement.service.personal_capacity_srv.personal_capacity_srv import <PERSON><PERSON><PERSON>and<PERSON>
from measurement.utils.diskcache_utils import DiskCacheUtil
from measurement.utils.personal_capacity_util.personal_capacity_util import get_cache_key_with_bug_kloc, \
    get_start_end_time


class BugKLOCHandler(ParentHandler):
    type_enum = PersCapaTypeEnum.BUG_KLOC
    skill_list = [
        MainSkillEnum.SERVER,
        MainSkillEnum.H5,
        MainSkillEnum.APP,
        MainSkillEnum.AI,
        MainSkillEnum.DEVOPS,
    ]

    def _get_team_data_list(self) -> list:
        start_time_str, end_time_str = self.get_time_cycle()
        sql_st = datetime.datetime.now()
        team_data_list = get_team_data_list_for_bug_kloc(end_time_str)
        sql_et = datetime.datetime.now()
        sql_cost_time = round((sql_et - sql_st).total_seconds(), 3)
        log.info(">>>> 研发千行bug率SQL耗时：{}s".format(sql_cost_time))
        return team_data_list

    def get_time_cycle(self):
        if self.time_cycle_enum not in (PersCapaAllTimeCycleEnum.LAST_WEEK, PersCapaAllTimeCycleEnum.LAST_MONTH, PersCapaAllTimeCycleEnum.LAST_QUARTER, PersCapaAllTimeCycleEnum.LAST_YEAR):
            start_time, end_time = get_start_end_time(self.time_cycle_enum)
        else:
            # start_time 为当前月的1月1号
            now = datetime.datetime.now()
            # 将日期设置为当前月份的第一天
            end_time = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            # 计算上一年同一天的日期
            start_time = end_time.replace(year=end_time.year - 1)
        if start_time == end_time:
            return None, None
        # 将日期格式化为字符串
        start_time_str = start_time.strftime("%Y-%m-%d")
        end_time_str = end_time.strftime("%Y-%m-%d")
        return start_time_str, end_time_str

    def get_pers_capa_dict(self) -> dict:
        if not self.cn_name:
            raise ValueError(">>>> 用户「{}」没有中文名。".format(self.user_name))
        if not self.p_team_id:
            raise ValueError(">>>> 用户「{}」没有大团队信息。".format(self.user_name))
        if not self.team_id:
            raise ValueError(">>>> 用户「{}」没有小团队信息".format(self.user_name))

        pers_capa_dict = None
        if self.is_skill:
            # 先手动添加缓存，后面改造成统一缓存。
            # 缓存获取
            cache_key = get_cache_key_with_bug_kloc(self.type_enum)
            data_list = DiskCacheUtil.get_value(cache_key)
            if not data_list:
                data_list = self._get_team_data_list()
                DiskCacheUtil.set_value(cache_key, data_list, 60 * 60 * 24)

            pers_capa_dict = self._computer_pers_capa_dict(data_list)

        return pers_capa_dict

    def _get_person_data_only(self) -> list:
        start_time_str, end_time_str = self.get_time_cycle()
        person_data_list = []
        if start_time_str and end_time_str:
            sql_st = datetime.datetime.now()
            person_data_list = get_person_data_list_for_bug_kloc(self.cn_name, start_time_str, end_time_str)
            sql_et = datetime.datetime.now()
            sql_cost_time = round((sql_et - sql_st).total_seconds(), 3)
            log.info(">>>> 研发千行bug率SQL耗时：{}s".format(sql_cost_time))
        return person_data_list
