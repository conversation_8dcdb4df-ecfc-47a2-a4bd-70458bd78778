import datetime

from mantis.settings import logger as log
from measurement.common.personal_capacity_common.personal_capacity_constants import MainSkillEnum
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaTypeEnum
from measurement.dao.personal_capacity_dao import \
    get_person_data_list_for_dev_test_delivery_time_percent
from measurement.service.personal_capacity_srv.personal_capacity_srv import <PERSON><PERSON><PERSON><PERSON><PERSON>
from measurement.utils.personal_capacity_util.personal_capacity_util import get_start_end_time


class ProjectDevTestDeliveryTimePercentHandler(ParentHandler):

    type_enum = PersCapaTypeEnum.DEV_TEST_DELIVERY_TIME_PERCENT
    skill_list = [
        MainSkillEnum.TEST,
    ]

    def _get_team_data_list(self) -> list:

        # todo 未实现
        raise Exception('ProjectDevTestDeliveryTimePercentHandler._get_team_data_list方法未实现')

    def _get_person_data_only(self) -> list:
        start_time, end_time = get_start_end_time(self.time_cycle_enum)

        sql_st = datetime.datetime.now()
        team_data_list = get_person_data_list_for_dev_test_delivery_time_percent(self.cn_name, start_time, end_time)
        sql_et = datetime.datetime.now()
        sql_cost_time = round((sql_et - sql_st).total_seconds(), 3)
        log.info(">>>> 研发测试交付时长比SQL耗时：{}s".format(sql_cost_time))
        return team_data_list
