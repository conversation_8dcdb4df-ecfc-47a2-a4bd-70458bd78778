import datetime

from mantis.settings import logger as log
from measurement.common.personal_capacity_common.personal_capacity_constants import MainSkillEnum
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaTypeEnum
from measurement.dao.personal_capacity_dao import get_team_data_list_for_bug_resolve_time, \
    get_person_data_list_for_bug_resolve_time
from measurement.service.personal_capacity_srv.personal_capacity_srv import <PERSON><PERSON><PERSON><PERSON><PERSON>
from measurement.utils.personal_capacity_util.personal_capacity_util import get_start_end_time


class BugResolveTimeHandler(ParentHandler):

    type_enum = PersCapaTypeEnum.BUG_RESOLVE_TIME
    skill_list = [
        MainSkillEnum.SERVER,
        MainSkillEnum.H5,
        MainSkillEnum.APP,
        MainSkillEnum.AI,
        MainSkillEnum.DEVOPS,
    ]

    def _get_team_data_list(self) -> list:
        # 使用全新的方式计算指标的起始时间。zt@2024-12-23
        start_time, end_time = get_start_end_time(self.time_cycle_enum)
        sql_st = datetime.datetime.now()
        team_data_list = get_team_data_list_for_bug_resolve_time(self.cn_name, start_time, end_time)
        sql_et = datetime.datetime.now()
        sql_cost_time = round((sql_et - sql_st).total_seconds(), 3)
        log.info(">>>> 研发缺陷修复耗时SQL耗时：{}s".format(sql_cost_time))
        return team_data_list

    def _get_person_data_only(self) -> list:
        start_time, end_time = get_start_end_time(self.time_cycle_enum)

        sql_st = datetime.datetime.now()
        team_data_list = get_person_data_list_for_bug_resolve_time(self.cn_name, start_time, end_time)
        sql_et = datetime.datetime.now()
        sql_cost_time = round((sql_et - sql_st).total_seconds(), 3)
        log.info(">>>> 研发缺陷修复耗时SQL耗时：{}s".format(sql_cost_time))
        return team_data_list
