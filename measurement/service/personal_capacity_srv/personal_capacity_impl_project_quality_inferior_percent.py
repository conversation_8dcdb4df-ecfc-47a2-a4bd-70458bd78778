from mantis.settings import logger as log
from measurement.common.personal_capacity_common.personal_capacity_constants import MainSkillEnum
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaTypeEnum
from measurement.service.personal_capacity_srv.personal_capacity_impl_project_quality_inferior_num import \
    ProjectQualityInferiorNumHandler
from measurement.service.personal_capacity_srv.personal_capacity_srv import <PERSON><PERSON><PERSON><PERSON><PERSON>
from measurement.utils.diskcache_utils import Disk<PERSON>acheUtil
from measurement.utils.personal_capacity_util.personal_capacity_util import get_cache_key_with_enum


class ProjectQualityInferiorPercentHandler(ParentHandler):
    type_enum = PersCapaTypeEnum.PROJECT_QUALITY_INFERIOR_PERCENT
    skill_list = [
        MainSkillEnum.SERVER,
        MainSkillEnum.H5,
        MainSkillEnum.APP,
        MainSkillEnum.AI,
        MainSkillEnum.DEVOPS,
    ]

    def _get_team_data_list(self) -> list:
        # 调用查询质量差项目数据
        pct_handler = ProjectQualityInferiorNumHandler(self.user_name, self.time_cycle)
        team_data_list = pct_handler._get_team_data_list()
        return team_data_list

    def get_pers_capa_dict(self) -> dict:
        if not self.cn_name:
            raise ValueError(">>>> 用户「{}」没有中文名。".format(self.user_name))
        if not self.p_team_id:
            raise ValueError(">>>> 用户「{}」没有大团队信息。".format(self.user_name))
        if not self.team_id:
            raise ValueError(">>>> 用户「{}」没有小团队信息".format(self.user_name))

        pers_capa_dict = None
        if self.is_skill:
            # 先手动添加缓存，后面改造成统一缓存。
            # 缓存获取
            cache_key = get_cache_key_with_enum(self.type_enum, self.user_name, self.time_cycle)
            cache_dict = DiskCacheUtil.get_value(cache_key)
            if cache_dict:
                pers_capa_dict = cache_dict
            else:
                # 从DB获取数据
                data_list = self._get_team_data_list()
                pers_capa_dict = self._computer_pers_capa_dict(data_list)
                # 填充缓存
                cache_dict = pers_capa_dict
                DiskCacheUtil.set_value(cache_key, cache_dict, 60 * 60 * 4)

        return pers_capa_dict

    def _get_person_data_only(self) -> list:
        pct_handler = ProjectQualityInferiorNumHandler(self.user_name, self.time_cycle)
        person_data_list = pct_handler._get_person_data_only()
        for person_data in person_data_list:
            user_value = person_data.get('user_value')
            project_total = person_data.get('project_total')
            if user_value and project_total and user_value > 0 and project_total > 0:
                person_data['user_value'] = round(user_value / project_total, 4)
            else:
                person_data['user_value'] = 0
            person_data.pop('project_total')
            person_data.pop('inferior_project')
            log.info(">>>> 参与项目质量差占比，质量差项目数：{}，项目总数：{}，占比：{}".format(user_value, project_total, person_data.get('user_value')))
        return person_data_list
