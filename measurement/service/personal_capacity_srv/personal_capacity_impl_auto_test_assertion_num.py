import datetime

from mantis.settings import logger as log
from measurement.common.personal_capacity_common.personal_capacity_constants import MainSkillEnum
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaTypeEnum
from measurement.dao.personal_capacity_dao import get_team_data_list_for_auto_test_assertion
from measurement.dao.personal_capacity_dao import get_person_data_list_for_auto_test_assertion
from measurement.service.personal_capacity_srv.personal_capacity_impl import <PERSON><PERSON><PERSON><PERSON><PERSON>
from measurement.utils.personal_capacity_util.personal_capacity_util import get_start_end_time


class AutoTestAssertionNumHandler(ParentHandler):
    type_enum = PersCapaTypeEnum.AUTO_TEST_ASSERTION_NUM
    skill_list = [
        MainSkillEnum.TEST,
    ]

    def _get_team_data_list(self) -> list:
        start_time, end_time = get_start_end_time(self.time_cycle_enum)

        if start_time == end_time:
            return []
        sql_st = datetime.datetime.now()
        time_cycle_code = self.time_cycle_enum.time_cycle_code
        team_data_list = get_team_data_list_for_auto_test_assertion(self.cn_name, time_cycle_code, start_time, end_time)
        sql_et = datetime.datetime.now()
        sql_cost_time = round((sql_et - sql_st).total_seconds(), 3)
        log.info(">>>> 测试自动化「验证集」数SQL耗时：{}s".format(sql_cost_time))
        return team_data_list


    def _get_person_data_only(self) -> list:
        start_time, end_time = get_start_end_time(self.time_cycle_enum)

        sql_st = datetime.datetime.now()

        time_cycle_code = self.time_cycle_enum.time_cycle_code
        team_data_list = get_person_data_list_for_auto_test_assertion(self.cn_name, time_cycle_code, start_time, end_time)
        sql_et = datetime.datetime.now()
        sql_cost_time = round((sql_et - sql_st).total_seconds(), 3)
        log.info(">>>> 测试自动化「验证集」数SQL耗时：{}s".format(sql_cost_time))
        return team_data_list
