import traceback

import numpy

from mantis.settings import logger as log

from measurement.common.personal_capacity_common.personal_capacity_constants import MainSkillEnum
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaTypeEnum
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaAllTimeCycleEnum
from measurement.utils.personal_capacity_util.personal_capacity_util import get_cache_key_with_enum
from measurement.utils.personal_capacity_util.personal_capacity_util import get_cache_key_only
from measurement.utils.diskcache_utils import DiskCacheUtil
from measurement.model.models import TeamMgtUserInfo
from measurement.dao.personal_capacity_dao import get_team_data_list_for_other
from measurement.dao.personal_capacity_dao import get_person_data_list_for_other


class ParentHandler:
    type_enum = PersCapaTypeEnum.OTHER
    skill_list = [MainSkillEnum.OTHER]

    def __init__(self, user_name: str, time_cycle: str):
        self.user_name = user_name
        self.time_cycle = time_cycle

        self.cn_name = None
        self.p_team_id = 0
        self.p_team_name = None
        self.team_id = 0
        self.team_name = None
        self.main_skill = None
        self.is_skill = True
        # 获取用户信息
        self._get_team_user()
        # 主技能匹配
        self._check_skill()
        # 时间片枚举
        self._init_time_cycle_enum()

    def _get_team_user(self):
        user = TeamMgtUserInfo.objects.filter(bind_is_active=True, user_name=self.user_name).first()
        if not user:
            raise ValueError(">>>> 没有查到启用的用户！user_name = {}".format(self.user_name))

        self.cn_name = user.cn_name
        self.p_team_id = user.p_team_id
        self.p_team_name = user.p_team_name
        self.team_id = user.team_id
        self.team_name = user.team_name
        self.main_skill = user.main_skill

    def _check_skill(self):
        if not self.main_skill:
            raise ValueError(">>>> 用户「{}」没有配置主技能「{}」。".format(self.user_name, self.main_skill))

        owner_skill_enum = None
        for skill_enum in list(MainSkillEnum):
            if skill_enum.skill_name == self.main_skill:
                owner_skill_enum = skill_enum
                break

        if not owner_skill_enum:
            raise ValueError(">>>> 无法解析的主技能「{}」。".format(self.main_skill))
        # 填充主技能
        self.is_skill = owner_skill_enum in self.skill_list

    def _init_time_cycle_enum(self):
        if not self.time_cycle:
            raise ValueError(">>>> 没有指定时间段。")

        time_cycle_enum = None
        for time_cycle_enum in list(PersCapaAllTimeCycleEnum):
            if time_cycle_enum.time_cycle_code == self.time_cycle:
                time_cycle_enum = time_cycle_enum
                break

        if not time_cycle_enum:
            raise ValueError(">>>> 无法解析的时间段「{}」。".format(self.time_cycle))
        # 填充时间段枚举
        self.time_cycle_enum = time_cycle_enum

    def get_pers_capa_dict(self) -> dict:
        if not self.cn_name:
            raise ValueError(">>>> 用户「{}」没有中文名。".format(self.user_name))
        if not self.p_team_id:
            raise ValueError(">>>> 用户「{}」没有大团队信息。".format(self.user_name))
        if not self.team_id:
            raise ValueError(">>>> 用户「{}」没有小团队信息".format(self.user_name))

        pers_capa_dict = None
        if self.is_skill:
            # 先手动添加缓存，后面改造成统一缓存。
            # 缓存获取
            cache_key = get_cache_key_with_enum(self.type_enum, self.user_name, self.time_cycle)
            cache_dict = DiskCacheUtil.get_value(cache_key)
            if cache_dict:
                pers_capa_dict = cache_dict
            else:
                # 从DB获取数据
                data_list = self._get_team_data_list()
                pers_capa_dict = self._computer_pers_capa_dict(data_list)
                # 填充缓存
                cache_dict = pers_capa_dict
                DiskCacheUtil.set_value(cache_key, cache_dict, 60 * 60 * 4)

        return pers_capa_dict

    def _get_team_data_list(self) -> list:
        team_data_list = get_team_data_list_for_other(self.user_name)
        return team_data_list

    def _computer_pers_capa_dict(self, team_data_list) -> dict:
        pers_capa_dict = None
        if team_data_list:
            user_value = None
            team_list = []
            p_team_list = []
            try:
                for item in team_data_list:
                    item_value = item.get("user_value")
                    if item_value:
                        item_float = float(item_value)
                        if item.get("user_name") == self.user_name:
                            user_value = item_float
                            team_list.append(user_value)
                            p_team_list.append(user_value)
                            continue
                        if item.get("team_id") == self.team_id:
                            team_list.append(item_float)
                        if item.get("p_team_id") == self.p_team_id:
                            p_team_list.append(item_float)
                # 小团队数据
                if team_list:
                    team_median = round(numpy.median(team_list), 2)
                    team_mean = round(numpy.mean(team_list), 2)
                    team_sum = round(numpy.sum(team_list), 2)
                else:
                    team_median = 0.0
                    team_mean = 0.0
                    team_sum = 0.0

                # 大团队数据
                if p_team_list:
                    p_team_median = round(float(numpy.median(p_team_list)), 2)
                    p_team_mean = round(float(numpy.mean(p_team_list)), 2)
                    p_team_sum = round(float(numpy.sum(p_team_list)), 2)
                else:
                    p_team_median = 0.0
                    p_team_mean = 0.0
                    p_team_sum = 0.0

                # 新增指标百分比，用来做饼图
                if not user_value:
                    user_value = 0.0
                    team_percent = 0.0
                    p_team_percent = 0.0
                else:
                    if team_sum:
                        team_percent = round(user_value / team_sum * 100, 2)
                    else:
                        team_percent = 0.0

                    if p_team_median:
                        p_team_percent = round(user_value / p_team_sum * 100, 2)
                    else:
                        p_team_percent = 0.0

                pers_capa_dict = {
                    "type_name": self.type_enum.type_code,
                    "user_value": user_value,
                    "team_median": team_median,
                    "team_mean": team_mean,
                    "team_sum": team_sum,
                    "team_percent": team_percent,
                    "p_team_median": p_team_median,
                    "p_team_mean": p_team_mean,
                    "p_team_sum": p_team_sum,
                    "p_team_percent": p_team_percent,
                }
            except Exception as e:
                log.error(">>>> 用户「{}」团队指标「{}」计算失败。team_data_list = {}".format(
                    self.cn_name,
                    self.type_enum.type_name,
                    team_data_list)
                )
                traceback.print_exc()
                raise e
        else:
            pers_capa_dict = {
                "type_name": self.type_enum.type_code,
                "user_value": 0.0,
                "team_median": 0.0,
                "team_mean": 0.0,
                "team_sum": 0.0,
                "team_percent": 0.0,
                "p_team_median": 0.0,
                "p_team_mean": 0.0,
                "p_team_sum": 0.0,
                "p_team_percent": 0.0,
            }

        return pers_capa_dict

    def get_person_capacity_only(self) -> dict:
        if not self.cn_name:
            raise ValueError(">>>> 用户「{}」没有中文名。".format(self.user_name))
        if not self.p_team_id:
            raise ValueError(">>>> 用户「{}」没有大团队信息。".format(self.user_name))
        if not self.team_id:
            raise ValueError(">>>> 用户「{}」没有小团队信息".format(self.user_name))

        pers_capa_dict = None
        if self.is_skill:
            # 先手动添加缓存，后面改造成统一缓存。
            # 缓存获取
            cache_key = get_cache_key_only(self.type_enum, self.user_name, self.time_cycle)
            cache_dict = DiskCacheUtil.get_value(cache_key)
            if cache_dict:
                pers_capa_dict = cache_dict
            else:
                # 从DB获取数据
                data_list = self._get_person_data_only()
                pers_capa_dict = self._computer_person_capacity_only(data_list)
                # 填充缓存
                cache_dict = pers_capa_dict
                DiskCacheUtil.set_value(cache_key, cache_dict, 60 * 60 * 4)

        return pers_capa_dict

    def _get_person_data_only(self) -> list:
        person_data_list = get_person_data_list_for_other(self.user_name)
        return person_data_list

    def _computer_person_capacity_only(self, team_data_list) -> dict:
        pers_capa_dict = None
        if team_data_list:
            user_value = None
            team_list = []
            p_team_list = []
            try:
                for item in team_data_list:
                    item_value = item.get("user_value")
                    if item_value:
                        item_float = float(item_value)
                        if item.get("user_name") == self.user_name:
                            user_value = item_float
                            team_list.append(user_value)
                            p_team_list.append(user_value)
                            continue
                        if item.get("team_id") == self.team_id:
                            team_list.append(item_float)
                        if item.get("p_team_id") == self.p_team_id:
                            p_team_list.append(item_float)
                # 小团队数据
                if team_list:
                    team_median = round(numpy.median(team_list), 2)
                    team_mean = round(numpy.mean(team_list), 2)
                    team_sum = round(numpy.sum(team_list), 2)
                else:
                    team_median = 0.0
                    team_mean = 0.0
                    team_sum = 0.0

                # 大团队数据
                if p_team_list:
                    p_team_median = round(float(numpy.median(p_team_list)), 2)
                    p_team_mean = round(float(numpy.mean(p_team_list)), 2)
                    p_team_sum = round(float(numpy.sum(p_team_list)), 2)
                else:
                    p_team_median = 0.0
                    p_team_mean = 0.0
                    p_team_sum = 0.0

                # 新增指标百分比，用来做饼图
                if not user_value:
                    user_value = 0.0
                    team_percent = 0.0
                    p_team_percent = 0.0
                else:
                    if team_sum:
                        team_percent = round(user_value / team_sum * 100, 2)
                    else:
                        team_percent = 0.0

                    if p_team_median:
                        p_team_percent = round(user_value / p_team_sum * 100, 2)
                    else:
                        p_team_percent = 0.0

                pers_capa_dict = {
                    "type_name": self.type_enum.type_code,
                    "user_value": user_value,
                    "team_median": team_median,
                    "team_mean": team_mean,
                    "team_sum": team_sum,
                    "team_percent": team_percent,
                    "p_team_median": p_team_median,
                    "p_team_mean": p_team_mean,
                    "p_team_sum": p_team_sum,
                    "p_team_percent": p_team_percent,
                }
            except Exception as e:
                log.error(">>>> 用户「{}」团队指标「{}」计算失败。team_data_list = {}".format(
                    self.cn_name,
                    self.type_enum.type_name,
                    team_data_list)
                )
                traceback.print_exc()
                raise e
        else:
            pers_capa_dict = {
                "type_name": self.type_enum.type_code,
                "user_value": 0.0,
                "team_median": 0.0,
                "team_mean": 0.0,
                "team_sum": 0.0,
                "team_percent": 0.0,
                "p_team_median": 0.0,
                "p_team_mean": 0.0,
                "p_team_sum": 0.0,
                "p_team_percent": 0.0,
            }

        return pers_capa_dict
