import datetime

from mantis.settings import logger as log
from measurement.common.personal_capacity_common.personal_capacity_constants import MainSkillEnum
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaTypeEnum
from measurement.dao.personal_capacity_dao import \
    get_person_data_list_for_project_quality_inferior, get_team_data_list_for_project_quality_inferior
from measurement.service.personal_capacity_srv.personal_capacity_srv import <PERSON><PERSON><PERSON><PERSON><PERSON>
from measurement.utils.diskcache_utils import Disk<PERSON>acheUtil
from measurement.utils.personal_capacity_util.personal_capacity_util import get_cache_key_with_enum
from measurement.utils.personal_capacity_util.personal_capacity_util import get_start_end_time


class ProjectQualityInferiorNumHandler(ParentHandler):
    type_enum = PersCapaTypeEnum.PROJECT_QUALITY_INFERIOR_NUM
    skill_list = [
        MainSkillEnum.SERVER,
        MainSkillEnum.H5,
        MainSkillEnum.APP,
        MainSkillEnum.AI,
        MainSkillEnum.DEVOPS,
    ]

    def _get_team_data_list(self) -> list:
        # 使用全新的方式计算指标的起始时间。
        start_time, end_time = get_start_end_time(self.time_cycle_enum)
        if start_time == end_time:
            return []
        # todo 查询出项目质量值，需要进一步判断质量优差值
        sql_st = datetime.datetime.now()
        team_data_list = get_team_data_list_for_project_quality_inferior(self.cn_name, start_time, end_time)
        sql_et = datetime.datetime.now()
        sql_cost_time = round((sql_et - sql_st).total_seconds(), 3)
        log.info(">>>> 参与项目质量查询SQL耗时：{}s".format(sql_cost_time))
        return team_data_list

    def get_pers_capa_dict(self) -> dict:
        if not self.cn_name:
            raise ValueError(">>>> 用户「{}」没有中文名。".format(self.user_name))
        if not self.p_team_id:
            raise ValueError(">>>> 用户「{}」没有大团队信息。".format(self.user_name))
        if not self.team_id:
            raise ValueError(">>>> 用户「{}」没有小团队信息".format(self.user_name))

        pers_capa_dict = None
        if self.is_skill:
            # 先手动添加缓存，后面改造成统一缓存。
            # 缓存获取
            cache_key = get_cache_key_with_enum(self.type_enum, self.user_name, self.time_cycle)
            cache_dict = DiskCacheUtil.get_value(cache_key)
            if cache_dict:
                pers_capa_dict = cache_dict
            else:
                # 从DB获取数据
                data_list = self._get_team_data_list()
                pers_capa_dict = self._computer_pers_capa_dict(data_list)
                # 填充缓存
                cache_dict = pers_capa_dict
                DiskCacheUtil.set_value(cache_key, cache_dict, 60 * 60 * 4)

        return pers_capa_dict

    def _get_person_data_only(self) -> list:
        start_time, end_time = get_start_end_time(self.time_cycle_enum)
        if start_time == end_time:
            return []
        sql_st = datetime.datetime.now()
        team_data_list = get_person_data_list_for_project_quality_inferior(self.cn_name, start_time, end_time)
        sql_et = datetime.datetime.now()
        sql_cost_time = round((sql_et - sql_st).total_seconds(), 3)
        log.info(">>>> 参与项目质量查询SQL耗时：{}s".format(sql_cost_time))
        # 处理查询数据
        counter = 0
        inferior_data = []
        person_data_list = []
        person_data = None
        if team_data_list:
            person_data = team_data_list[0]
            for quality_data in team_data_list:
                project_id = quality_data.get('id')
                project_name = quality_data.get('name')
                user_value = quality_data.get('user_value')
                log.info(">>>> 参与项目质量，项目ID：{}，项目名：{}，质量：{}".format(project_id, project_name, user_value))
                if user_value and user_value > 5:
                    counter += 1
                    inferior_data.append({"project_id": project_id, "project_name": project_name})
            person_data['user_value'] = counter
            person_data['inferior_project'] = inferior_data
            person_data['project_total'] = len(team_data_list)
            person_data.pop('id')
            person_data.pop('name')

        log.info(">>>> 参与项目质量，项目总数：{}，质量差项目数：{}".format(len(team_data_list), counter))
        if person_data:
            person_data_list.append(person_data)
        log.info(">>>> 参与项目质量结果：{}".format(person_data_list))
        return person_data_list

