from django.db import connection
import datetime
import json
from enum import Enum

from mantis.settings import logger as log, logger
from measurement.dao.measurement_dao import MeasurementDao, TestsetStatusEnum, TestsetVersionModeEnum
from measurement.dao.app_auto_test_check_dao import AppAutoTestCheckDao
from measurement.model.models import TestFlowAppDeployInfo, TestFlowRunRecordTestset, TestFlowRunRecordTestSetResult, \
    DevopsCheckRecordInfo


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class CheckContentEnum(Enum):
    AUTO = ('auto_test_report', '自动化测试报告')
    MANUAL = ('manual_test_report', '人工测试报告')

    def __init__(self, content, content_desc):
        self.content = content
        self.content_desc = content_desc


class AppAutoTestCheckParam(object):
    business_name = ""
    biz_flow_name = ""
    suite_code = ""
    pass_rate_threshold = 0
    app_name = ""
    br_name = ""
    iteration_id = ""
    biz_code = ""

    def __init__(self, business_name, biz_flow_name, suite_code, pass_rate_threshold, app_name, br_name, iteration_id,
                 biz_code, create_time):
        self.business_name = business_name
        self.biz_flow_name = biz_flow_name
        self.suite_code = suite_code
        self.pass_rate_threshold = pass_rate_threshold
        self.app_name = app_name
        self.br_name = br_name
        self.iteration_id = iteration_id
        self.biz_code = biz_code
        self.create_time = create_time


class AppAutoTestCheckService:

    def __create_devops_check_record(self, app_auto_test_check_param: AppAutoTestCheckParam, run_batch_number):
        try:
            obj = DevopsCheckRecordInfo.objects.filter(iteration_id=app_auto_test_check_param.iteration_id,
                                                       app_name=app_auto_test_check_param.app_name,
                                                       check_content=CheckContentEnum.AUTO.content,
                                                       business_name=app_auto_test_check_param.business_name)
            if obj:
                obj.update(run_batch_number=run_batch_number, update_time=datetime.datetime.now(),
                           update_user='be-script')
            else:
                DevopsCheckRecordInfo.objects.create(iteration_id=app_auto_test_check_param.iteration_id,
                                                     app_name=app_auto_test_check_param.app_name,
                                                     check_content=CheckContentEnum.AUTO.content,
                                                     business_name=app_auto_test_check_param.business_name,
                                                     run_batch_number=run_batch_number,
                                                     create_time=datetime.datetime.now(),
                                                     create_user='be-script')
        except Exception as e:
            logger.error("写入数据库失败，原因为：{}".format(str(e)))
            raise Exception("写入数据库失败，原因为：{}".format(str(e)))

    def app_auto_test_check(self, app_auto_test_check_param: AppAutoTestCheckParam) -> tuple[bool, str]:
        """检查应用自动化测试结果
        
        Args:
            app_auto_test_check_param: 自动化测试检查参数
            
        Returns:
            tuple: (是否通过, 消息)
        """
        # 获取最新批次号
        batch_no_list = self._get_last_batch_no(app_auto_test_check_param)

        # 如果没有找到批次号，返回未找到结果的消息
        if not batch_no_list:
            return self._handle_no_batch_found(app_auto_test_check_param)

        # 处理找到批次号的情况
        run_batch_number = batch_no_list[0]['run_batch_number']
        return self._process_batch_results(app_auto_test_check_param, run_batch_number)

    def _get_last_batch_no(self, param: AppAutoTestCheckParam) -> list:
        """获取最新批次号"""
        return MeasurementDao.get_last_batch_no(
            param.app_name, param.br_name, param.create_time,
            param.biz_code, param.biz_flow_name, param.suite_code
        )

    def _handle_no_batch_found(self, param: AppAutoTestCheckParam) -> tuple[bool, str]:
        """处理未找到批次号的情况"""
        last_run_time = MeasurementDao.get_last_run_time(
            param.app_name, param.br_name, param.biz_code,
            param.biz_flow_name, param.suite_code
        )
        message = f'''未找到应用{param.app_name}的版本{param.br_name}的最新自动化测试结果，
                  请执行自动化测试后重试！(最后制品时间是: {param.create_time}，
                  最近一次执行时间是：{last_run_time})'''
        return False, message

    def _process_batch_results(self, param: AppAutoTestCheckParam, run_batch_number: str) -> tuple[bool, str]:
        """处理批次结果
        
        Args:
            param: 自动化测试检查参数
            run_batch_number: 运行批次号
            
        Returns:
            tuple: (是否通过, 消息)
        """
        # 创建检查记录
        self.__create_devops_check_record(param, run_batch_number)

        # 获取测试集信息列表
        testset_info_list = TestFlowRunRecordTestset.objects.filter(run_batch_number=run_batch_number)

        # 检查测试状态
        status_check_result = self._check_test_status(testset_info_list, run_batch_number)
        if status_check_result:
            return False, status_check_result

        # 获取并检查接口通过率
        interface_pass_rates, completeness_error = self._get_interface_pass_rates(testset_info_list, run_batch_number)

        # 如果有完整性错误，直接返回
        if completeness_error:
            return False, completeness_error

        # 如果没有接口通过率数据，返回未找到结果
        if not interface_pass_rates:
            return False, f'暂未查到批次号：{run_batch_number}的自动化测试结果，请稍后重试！'

        # 检查通过率是否达标
        is_pass = self._check_pass_rates(interface_pass_rates, param.pass_rate_threshold)

        # 返回结果消息
        return self._format_result_message(is_pass, interface_pass_rates, param.pass_rate_threshold, run_batch_number)

    def _check_test_status(self, testset_info_list: list, run_batch_number: str) -> str:
        """检查测试状态，返回错误信息（如果有）"""
        for testset_info in testset_info_list:
            # 检查运行状态
            if testset_info.testset_status == TestsetStatusEnum.RUNNING.status:
                return f'批次号： {run_batch_number} 的自动化测试正在运行中，请稍后重试'

            # 检查失败状态
            if testset_info.testset_status == TestsetStatusEnum.FAILURE.status:
                return f'批次号： {run_batch_number} 的自动化测试执行失败了，请联系测试同学排查！'

            # 检查版本类型
            if testset_info.testset_version_type == TestsetVersionModeEnum.LOCAL.status:
                return f'最近一次自动化运行的是LOCAL案例，不能作为上线依据，请重新选择REMOTE案例执行自动化！批次号： {run_batch_number}'

        # 没有错误
        return ''

    def _get_interface_pass_rates(self, testset_info_list: list, run_batch_number: str) -> tuple[list, str]:
        """获取接口通过率列表和完整性检查结果
        
        Returns:
            tuple: (接口通过率列表, 错误消息(如果有))
        """
        interface_pass_rates = []
        merge_msg = ''
        detailed_info = []  # 用于收集详细的检测信息

        for testset_info in testset_info_list:
            t_id = testset_info.id
            testset_detail = testset_info.testset_detail
            # keys_number = len(testset_detail)
            # 从 testset_app_run_detail 中获取 appList 的长度
            try:
                testset_app_run_detail = testset_info.testset_app_run_detail
                if testset_app_run_detail:
                    app_run_data = json.loads(testset_app_run_detail)
                    app_list = app_run_data.get('appList', [])
                    keys_number = len(app_list)
                else:
                    keys_number = len(testset_detail) if testset_detail else 0
            except (json.JSONDecodeError, AttributeError, TypeError):
                # 如果解析失败，回退到原来的逻辑
                keys_number = len(testset_detail) if testset_detail else 0

            # 适合强校验部署与测试案例应用一致的情况
            # testset_result_list = list(TestFlowRunRecordTestSetResult.objects.filter(t_id=t_id,
            # app_name=app_auto_test_check_param.app_name).values_list('interface_pass_rate', flat=True))

            # 获取测试结果, 适配部署的应用与案例的应用不一致的情况，比如，用cgi案例测中台order-center-remote应用
            # 使用DAO层获取测试结果
            test_results = AppAutoTestCheckDao.get_test_results_by_testset_id(t_id)
            
            # 收集当前测试集的详细信息
            testset_info_detail = {
                'testset_id': t_id,
                'testset_detail_count': keys_number,
                'actual_result_count': len(test_results),
                'test_results': []
            }
            
            # 检查案例分支是否回合了最新的master
            for pass_rate, merge_status, app_name, script_branch, test_set_id in test_results:
                interface_pass_rates.append(pass_rate)
                
                # 收集测试结果详细信息
                result_detail = {
                    'app_name': app_name,
                    'script_branch': script_branch,
                    'pass_rate': pass_rate,
                    'merge_status': merge_status,
                    'test_set_id': test_set_id
                }
                testset_info_detail['test_results'].append(result_detail)
                
                if not merge_status:
                    merge_msg += f'测试集id：{test_set_id}，应用：{app_name}，案例分支：{script_branch} 没有回合最新的master；'
            
            detailed_info.append(testset_info_detail)
            
            # 预留代码，上线后可能要短暂开启，功能稳定后关闭 20250610 by fwm
            # 打开拦截
            # merge_msg = ''
            if merge_msg:
                return [], merge_msg

            # 检查结果完整性 - 提供详细信息
            if keys_number != len(test_results):
                # 获取当前t_id对应的testset_id（从test_results的第一条记录中获取）
                current_testset_id = test_results[0][4] if test_results else t_id  # test_results[0][4]是testset_id
                missing_info = f'批次号：{run_batch_number}，测试集ID：{current_testset_id}，期望结果数：{keys_number}，实际结果数：{len(test_results)}'
                testset_detail_str = f'测试集详情：{testset_detail}' if testset_detail else '测试集详情：无'
                actual_apps = [result['app_name'] for result in testset_info_detail['test_results']]
                actual_apps_str = f'实际测试应用：{actual_apps}' if actual_apps else '实际测试应用：无'
                
                error_msg = f'自动化测试结果不完整！{missing_info}；{testset_detail_str}；{actual_apps_str}'
                return [], error_msg

        # 检查每个通过率是否有值 - 提供详细信息
        empty_rate_details = []
        for i, rate in enumerate(interface_pass_rates):
            if not rate:
                # 找到对应的测试结果详情
                result_index = 0
                for testset_detail in detailed_info:
                    for result in testset_detail['test_results']:
                        if result_index == i:
                            empty_rate_details.append(f"应用：{result['app_name']}，分支：{result['script_branch']}，测试集ID：{result['test_set_id']}")
                            break
                        result_index += 1
                    if result_index > i:
                        break
        
        if empty_rate_details:
            error_msg = f'批次号：{run_batch_number} 的自动化测试结果不完整，以下测试结果通过率为空：{"；".join(empty_rate_details)}'
            return [], error_msg

        return interface_pass_rates, ''

    def _check_pass_rates(self, pass_rates: list, threshold: float) -> bool:
        """检查通过率是否达标"""
        for rate in pass_rates:
            if float(rate.split('%')[0]) < float(threshold):
                return False
        return True

    def _format_result_message(self, is_pass: bool, pass_rates: list, threshold: float, batch_number: str) -> tuple[
        bool, str]:
        """格式化结果消息"""
        if is_pass:
            return True, f'自动化测试接口通过率为: {pass_rates}'
        else:
            return False, f'自动化测试接口通过率为: {",".join(pass_rates)}， 部分结果低于阈值： {threshold}, 执行批次号：{batch_number}'


class AppAutoTestStatusService:

    def app_auto_test_status(self, iteration_id, app_name, branch_name, create_time, biz_code, biz_flow_name,
                             suite_code):
        # batch_no_list = MeasurementDao.get_last_batch_no(app_name, branch_name, create_time, biz_code, biz_flow_name,
        #                                                  suite_code)
        # if batch_no_list:
        #     run_batch_number = batch_no_list[0]['run_batch_number']
        #     testset_info_list = TestFlowRunRecordTestset.objects.filter(run_batch_number=run_batch_number)
        #     interface_pass_rate_list = []
        #     for testset_info in testset_info_list:
        #         t_id = testset_info.id
        #         # 适配部署的应用与案例的应用不一致的情况，比如，用cgi案例测中台order-center-remote应用
        #         interface_pass_rate_list.extend(
        #             list(TestFlowRunRecordTestSetResult.objects.filter(t_id=t_id).values_list(
        #                 'interface_pass_rate', flat=True)))
        #     if len(interface_pass_rate_list) == len(testset_info_list):
        #         test_set_status = TestsetStatusEnum.SUCCESS.status
        #     else:
        #         test_set_status = TestsetStatusEnum.RUNNING.status
        # else:
        # todo 此接口使用场景：发布计划查询自动化是否执行完成，若完成，查询自动化通过率，未完成，继续等待
        test_set_status = TestsetStatusEnum.RUNNING.status

        return test_set_status
