import datetime
import logging

from mantis.settings import QAP, TAPD
from measurement.model.models import AutoTestStatisticsRecordInfo
from measurement.model.models import AutoTestCaseRecordInfo
from measurement.service.measurement_utils import send_request
from measurement.service.measurement_utils import send_request_new
from measurement.dao.measurement_dao import get_batch_by_time
from measurement.dao.measurement_dao import get_person_data_list_by_team_name_str
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaAllTimeCycleEnum
from measurement.utils.personal_capacity_util.personal_capacity_util import get_start_end_time


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class AutoTestStatisticsRecordSer:

    def create_or_update_auto_test_record(self, start_time):
        url = '{}qa-info/scriptCase/getCaseAddStatistics'.format(QAP["url"])
        params = {'datetime': start_time}
        auto_test_list = send_request(url, 'get', params=params)
        create_user = TAPD["sync_user"]
        curr_time = datetime.datetime.now()
        upd_list = []
        ins_list = []
        union_list = list()
        if auto_test_list:
            for item in auto_test_list:
                res = AutoTestStatisticsRecordInfo.objects.filter(user_name=item.get('creator'),
                                                                  opt_time=item.get('createDate'))
                opt = AutoTestStatisticsRecordInfo()
                opt.create_user = create_user
                opt.create_time = curr_time
                opt.user_name = item.get('creator')
                opt.opt_time = item.get('createDate')
                opt.script_number = item.get('countInterface')
                opt.case_number = item.get('countCase')
                opt.validation_set_number = item.get('countIncludeValidationSet')
                opt.validation_set_case_number = item.get('countIncludeValidationSetCase')
                opt.stamp = 0
                if res:
                    opt.id = res[0].id
                    upd_list.append(opt)
                else:
                    union_key = opt.user_name + opt.opt_time
                    if union_key not in union_list:
                        union_list.append(union_key)
                        opt.update_user = create_user
                        opt.update_time = curr_time
                        ins_list.append(opt)
        if ins_list:
            AutoTestStatisticsRecordInfo.objects.bulk_create(ins_list)
        if upd_list:
            AutoTestStatisticsRecordInfo.objects.bulk_update(upd_list,
                                                             ['script_number', 'case_number', 'validation_set_number',
                                                              'validation_set_case_number'])
        return curr_time


class AutoTestCaseRecordSrv:
    """从qa平台同步测试集和验证集数量。zt@2025-04-23"""

    team_name_str = "'测试-所有人'"
    qa_req_url = '{}qa-info/caseRecord/countDetail'.format(QAP["url"])

    def __get_batch_by_time(self, curr_time):
        time_batch = None
        batch_number = None
        db_data_list = get_batch_by_time(curr_time)
        if db_data_list:
            db_data_one = db_data_list[0]
            time_batch = db_data_one.get('time_batch')
            batch_number = db_data_one.get('batch_number')

        return time_batch, batch_number

    def __sync_record_with_time_enum_ret_map(self, curr_time, time_batch, batch_number, cn_name_map, time_enum_ret_map):
        ins_db_list = []
        opt_user = TAPD["sync_user"]
        opt_time = curr_time
        for time_enum, ret_map in time_enum_ret_map.items():
            time_cycle_code = time_enum.time_cycle_code
            start_time = ret_map.get('start_time')
            end_time = ret_map.get('end_time')
            ret_list = ret_map.get('ret_list')
            for obj in ret_list:
                creator = obj.get('creator')
                case_count = obj.get('caseCount')
                assertion_count = obj.get('assertionCount')

                user_name = None
                cn_name = None
                main_skill = None
                team_id = None
                team_name = None
                p_team_id = None
                p_team_name = None
                if creator:
                    person_data = cn_name_map.get(creator)
                    if person_data:
                        user_name = person_data.get('user_name')
                        cn_name = person_data.get('cn_name')
                        main_skill = person_data.get('main_skill')
                        team_id = person_data.get('team_id')
                        team_name = person_data.get('team_name')
                        p_team_id = person_data.get('p_team_id')
                        p_team_name = person_data.get('p_team_name')

                auto_test_case_record_info = AutoTestCaseRecordInfo()
                auto_test_case_record_info.create_user = opt_user
                auto_test_case_record_info.create_time = opt_time
                auto_test_case_record_info.update_user = opt_user
                auto_test_case_record_info.update_time = opt_time
                auto_test_case_record_info.stamp = 0
                auto_test_case_record_info.time_batch = time_batch
                auto_test_case_record_info.batch_number = batch_number
                auto_test_case_record_info.time_cycle_enum = time_cycle_code
                auto_test_case_record_info.req_start_time = start_time
                auto_test_case_record_info.req_end_time = end_time
                auto_test_case_record_info.user_name = user_name
                auto_test_case_record_info.cn_name = cn_name
                auto_test_case_record_info.main_skill = main_skill
                auto_test_case_record_info.team_id = team_id
                auto_test_case_record_info.team_name = team_name
                auto_test_case_record_info.p_team_id = p_team_id
                auto_test_case_record_info.p_team_name = p_team_name
                auto_test_case_record_info.ret_creator = creator
                auto_test_case_record_info.ret_case_count = case_count
                auto_test_case_record_info.ret_assertion_count = assertion_count

                ins_db_list.append(auto_test_case_record_info)

        if ins_db_list:
            AutoTestCaseRecordInfo.objects.bulk_create(ins_db_list)


    def __sync_record_with_time_enum_list(self, curr_time, time_batch, batch_number,
                                          cn_name_list, cn_name_map,
                                          time_enum_list):
        time_enum_ret_map = {}
        for time_enum in time_enum_list:
            start_time, end_time = get_start_end_time(time_enum, curr_time)
            start_time_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
            end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S")

            post_map = {
                'startTime': start_time_str,
                'endTime': end_time_str,
                'creator': cn_name_list,
            }
            ret_list = send_request_new(self.qa_req_url, 'post', data=post_map)
            ret_map = {
                'time_enum': time_enum,
                'curr_time': curr_time,
                'time_batch': time_batch,
                'batch_number': batch_number,
                'cn_name_map': cn_name_map,
                'start_time': start_time,
                'end_time': end_time,
                'ret_list': ret_list,
            }
            time_enum_ret_map[time_enum] = ret_map

        self.__sync_record_with_time_enum_ret_map(curr_time, time_batch, batch_number, cn_name_map, time_enum_ret_map)


    def create_or_update_auto_test_case_record(self, start_time):

        curr_time = datetime.datetime.now()

        time_batch, batch_number = self.__get_batch_by_time(curr_time)
        if not time_batch or not batch_number:
            raise Exception("获取批次号失败")

        person_data_list = get_person_data_list_by_team_name_str(self.team_name_str)
        cn_name_list = []
        cn_name_map = {}
        for person_data in person_data_list:
            # logging.error("person_data: {}".format(person_data))
            if person_data and person_data.get('cn_name'):
                cn_name = person_data.get('cn_name')
                cn_name_list.append(cn_name)
                cn_name_map[cn_name] = person_data

        if not cn_name_list:
            raise Exception("获取人员信息失败")

        time_enum_list = [
            # check
            PersCapaAllTimeCycleEnum.THIS_QUARTER,
            PersCapaAllTimeCycleEnum.THIS_YEAR,
            PersCapaAllTimeCycleEnum.BEFORE_FIRST_QUARTER,
            PersCapaAllTimeCycleEnum.BEFORE_SECOND_QUARTER,
            PersCapaAllTimeCycleEnum.BEFORE_THIRD_QUARTER,
            PersCapaAllTimeCycleEnum.BEFORE_FOURTH_QUARTER,
            # output
            PersCapaAllTimeCycleEnum.THIS_MONTH,
            PersCapaAllTimeCycleEnum.BEFORE_FIRST_MONTH,
            PersCapaAllTimeCycleEnum.BEFORE_SECOND_MONTH,
            PersCapaAllTimeCycleEnum.BEFORE_THIRD_MONTH,
        ]

        self.__sync_record_with_time_enum_list(curr_time, time_batch, batch_number, cn_name_list, cn_name_map, time_enum_list)
        return curr_time
