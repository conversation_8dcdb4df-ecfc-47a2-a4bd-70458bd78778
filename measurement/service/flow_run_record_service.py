import datetime

from mantis.settings import SPIDER, TAPD
from measurement.model.models import TestFlowScheduleInfo, TestFlowRunRecord
from measurement.service.measurement_utils import send_request, cron_to_time


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class TestFlowRunRecordSer:
    def create_or_update_flow_run_record(self, start_time):
        url = '{}external_interaction/mantis_mgt/biz_flow_run/get_test_flow_run_record/'.format(SPIDER["url"])
        params = {'start_time': start_time}
        flow_list = send_request(url, 'get', params=params)
        create_user = TAPD["sync_user"]
        curr_time = datetime.datetime.now()
        upd_list = []
        ins_list = []
        for item in flow_list:
            res = TestFlowRunRecord.objects.filter(run_batch_number=item.get('batch_no'))
            opt = TestFlowRunRecord()
            opt.create_user = create_user
            opt.create_time = curr_time
            opt.biz_code = item.get('biz_code')
            opt.biz_iter_branch = item.get('biz_iter_branch')
            opt.biz_flow_name = item.get('biz_flow_name')
            opt.suite_code = item.get('suite_code')
            opt.run_time = item.get('run_time')
            opt.exec_action_type = item.get('exec_action_type')
            opt.stamp = 0
            if res:
                opt.id = res[0].id
                upd_list.append(opt)
            else:
                opt.update_user = create_user
                opt.update_time = curr_time
                opt.run_batch_number = item.get('batch_no')
                ins_list.append(opt)
        if ins_list:
            TestFlowRunRecord.objects.bulk_create(ins_list)
        if upd_list:
            TestFlowRunRecord.objects.bulk_update(upd_list, ['biz_code', 'biz_iter_branch', 'biz_flow_name', 'suite_code', 'run_time', 'exec_action_type', 'stamp'])
        return curr_time
