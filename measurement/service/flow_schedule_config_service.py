import datetime

from mantis.settings import SPIDER, TAPD
from measurement.model.models import TestFlowScheduleInfo
from measurement.service.measurement_utils import send_request, cron_to_time


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class TestFlowScheduleInfoSer:
    def create_or_update_flow_schedule_info(self):
        url = '{}external_interaction/mantis_mgt/get_all_test_flow_list/'.format(SPIDER["url"])
        flow_list = send_request(url, 'get')
        create_user = TAPD["sync_user"]
        create_time = datetime.datetime.now()
        upd_list = []
        ins_list = []
        for item in flow_list:
            res = TestFlowScheduleInfo.objects.filter(biz_code=item.get('biz_code'), biz_iter_branch=item.get('biz_iter_branch'), biz_flow_name=item.get('biz_flow_name'), suite_code=item.get('suite_code'))
            opt = TestFlowScheduleInfo()
            opt.create_user = create_user
            opt.create_time = create_time
            opt.update_user = create_user
            opt.update_time = create_time
            opt.is_active = item.get('is_active')
            opt.execute_time = cron_to_time(item.get('cron'))
            opt.schedule_creator = item.get('create_user')
            opt.schedule_updater = item.get('update_user')
            if res:
                opt.id = res[0].id
                upd_list.append(opt)
            else:
                opt.biz_code = item.get('biz_code')
                opt.biz_iter_branch = item.get('biz_iter_branch')
                opt.biz_flow_name = item.get('biz_flow_name')
                opt.suite_code = item.get('suite_code')
                ins_list.append(opt)
        if ins_list:
            TestFlowScheduleInfo.objects.bulk_create(ins_list)
        if upd_list:
            TestFlowScheduleInfo.objects.bulk_update(upd_list, ['is_active', 'execute_time', 'schedule_creator', 'schedule_updater'])
