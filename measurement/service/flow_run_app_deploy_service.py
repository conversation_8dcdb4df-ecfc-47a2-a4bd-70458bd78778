import datetime
import json

from django.db import connection

from mantis.settings import SPIDER, TAPD, logger
from measurement.dao.measurement_dao import MeasurementDao
from measurement.model.models import TestFlowRunRecordTestset, TestFlowAppDeployInfo
from measurement.service.measurement_utils import send_request


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class TestFlowAppDeploySer:
    def create_flow_app_deploy_info(self):
        batch_no_and_suite_list = MeasurementDao.get_batch_no_and_suite_list()
        logger.info('需要同步编排线部署信息的批次号与环境信息：{}'.format(batch_no_and_suite_list))
        url = '{}external_interaction/mantis_mgt/biz_flow_app_deploy_info/get_test_flow_app_deploy_info/'.format(
            SPIDER["url"])
        data = {'batch_no_and_suite_list': batch_no_and_suite_list}
        app_deploy_info_list = send_request(url, 'post', data=json.dumps(data))
        create_user = TAPD["sync_user"]
        curr_time = datetime.datetime.now()
        ins_list = []
        exist_app_deploy_info_batch_no_list = []
        if app_deploy_info_list:
            for item in app_deploy_info_list:
                if item.get('app_name'):
                    obj = TestFlowAppDeployInfo()
                    obj.run_batch_number = item.get('run_batch_number')
                    obj.app_name = item.get('app_name')
                    obj.app_deploy_branch = item.get('app_deploy_branch')
                    obj.create_user = create_user
                    obj.create_time = curr_time
                    obj.stamp = 0

                    ins_list.append(obj)

                    if item.get('run_batch_number') not in exist_app_deploy_info_batch_no_list:
                        exist_app_deploy_info_batch_no_list.append(item.get('run_batch_number'))

        if ins_list:
            TestFlowAppDeployInfo.objects.bulk_create(ins_list)
        logger.info('同步编排线部署信息成功的批次号：{}'.format(exist_app_deploy_info_batch_no_list))

        return curr_time
