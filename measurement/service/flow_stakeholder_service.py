import datetime
import json
from enum import Enum

from django.db import connection
from measurement.dao.measurement_dao import MeasurementDao
from mantis.settings import SPIDER, TAPD, logger
from measurement.model.models import DevelopsStakeholderInfo, DevopsAppTeamInfo
from measurement.service.measurement_utils import send_request


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class StakeholderEnum(Enum):
    CREATE_BRANCH = ('create_branch', '分支创建者')
    ARCHIVE_BRANCH = ('archive_branch', '分支归档者')
    CODE_BUILD = ('code_build', '代码编译者')
    DEPLOY_TEST = ('deploy_test', '测试环境发布者')
    PROD_APPLY = ('prod_apply', '产线申请者')
    PROD_PUBLISH = ('prod_publish', '产线发布者')
    CREATE_FLOW = ('create_flow', '编排线创建者')
    UPDATE_FLOW = ('update_flow', '编排线更新者')
    CREATE_AUTOMATION = ('create_automation', '自动化创建者')
    UPDATE_AUTOMATION = ('update_automation', '自动化更新者')
    MANUAL_EXECUTE_AUTOMATION = ('manual_execute_automation', '自动化手动执行者')
    APP_MANAGE = ('app_manage', '应用负责人')

    def __init__(self, status_name, status_name_desc):
        self.status_name = status_name
        self.status_name_desc = status_name_desc


class TestFlowStakeholderSer:

    def create_or_update_flow_stakeholder(self, start_time):
        logger.info('开始同步干系人信息开始啦{}'.format(start_time))
        create_user = TAPD["sync_user"]
        curr_time = datetime.datetime.now()
        if start_time:
            start_time_str = str(start_time)
        batch_no_list, batch_no_map = MeasurementDao.get_batch_no_list_by_start_time(start_time_str)
        if not batch_no_list:
            return

        url = '{}external_interaction/mantis_mgt/stakeholder/get_flow_stakeholder_api/'.format(SPIDER["url"])
        params = {'batch_no_list': batch_no_list, 'start_time': start_time_str}
        headers = {'Content-Type': 'application/json'}
        stakeholder_result = send_request(url, 'post', data=params, headers=headers)
        if not stakeholder_result:
            return
        # 先处理特殊类型干系人CREATE_BRANCH、ARCHIVE_BRANCH、CREATE_FLOW、UPDATE_FLOW、CREATE_AUTOMATION、UPDATE_AUTOMATION
        biz_stakeholder = stakeholder_result.get('biz_stakeholder')
        app_stakeholder = stakeholder_result.get('app_stakeholder')
        # manage_stakeholder = stakeholder_result.get('manage_stakeholder')
        ins_list = []
        try:
            self.__update_biz_stakeholder(biz_stakeholder, create_user, curr_time, ins_list)
            self.__update_app_stakeholder(app_stakeholder, create_user, curr_time, ins_list)
            # self.__update_manage_stakeholder(create_user, curr_time, manage_stakeholder, batch_no_map)
            if ins_list:
                DevelopsStakeholderInfo.objects.bulk_create(ins_list)
        except Exception as e:
            logger.error(e)
            raise Exception(e)
        return curr_time

    def __update_manage_stakeholder(self, create_user, curr_time, manage_stakeholder):
        if manage_stakeholder:
            for manage_stakeholder_item in manage_stakeholder:
                app_name = manage_stakeholder_item.get('app_name')
                opt_type = manage_stakeholder_item.get('opt_type')
                DevelopsStakeholderInfo.objects.update_or_create(opt_type=opt_type, app_name=app_name,
                                                                 defaults={'create_user': create_user,
                                                                           'create_time': curr_time,
                                                                           'update_user': create_user,
                                                                           'update_time': curr_time})

    def __update_app_stakeholder(self, app_stakeholder, create_user, curr_time, ins_list):
        if app_stakeholder:
            for app_stakeholder_item in app_stakeholder:
                app_name = app_stakeholder_item.get('app_name')
                branch_name = app_stakeholder_item.get('br_name')
                opt_type = app_stakeholder_item.get('opt_type')
                opt_user = app_stakeholder_item.get('opt_user')
                is_update = False
                if StakeholderEnum.CREATE_BRANCH.status_name == opt_type:
                    is_update = True
                elif StakeholderEnum.ARCHIVE_BRANCH.status_name == opt_type:
                    is_update = True
                else:
                    obj = DevelopsStakeholderInfo.objects.filter(app_name=app_name, branch_name=branch_name,
                                                                 opt_type=opt_type,
                                                                 opt_user=opt_user)
                    if not obj:
                        stakeholder = DevelopsStakeholderInfo()
                        stakeholder.opt_type = opt_type
                        stakeholder.app_name = app_name
                        stakeholder.branch_name = branch_name
                        stakeholder.create_user = create_user
                        stakeholder.create_time = curr_time
                        stakeholder.opt_user = opt_user
                        ins_list.append(stakeholder)
                if is_update:
                    DevelopsStakeholderInfo.objects.update_or_create(opt_type=opt_type, app_name=app_name,
                                                                     branch_name=branch_name,
                                                                     defaults={'opt_user': opt_user,
                                                                               'create_user': create_user,
                                                                               'create_time': curr_time,
                                                                               'update_user': create_user,
                                                                               'update_time': curr_time})

    def __update_biz_stakeholder(self, biz_stakeholder, create_user, curr_time, ins_list):
        if biz_stakeholder:
            for biz_stakeholder_item in biz_stakeholder:
                biz_flow_name = biz_stakeholder_item.get('biz_flow_name')
                opt_type = biz_stakeholder_item.get('opt_type')
                opt_user = biz_stakeholder_item.get('opt_user')
                is_update = False
                if StakeholderEnum.CREATE_FLOW.status_name == opt_type:
                    is_update = True
                elif StakeholderEnum.UPDATE_FLOW.status_name == opt_type:
                    is_update = True
                elif StakeholderEnum.CREATE_AUTOMATION.status_name == opt_type:
                    is_update = True
                elif StakeholderEnum.UPDATE_AUTOMATION.status_name == opt_type:
                    is_update = True
                else:
                    obj = DevelopsStakeholderInfo.objects.filter(biz_flow_name=biz_flow_name, opt_type=opt_type,
                                                                 opt_user=opt_user)
                    if not obj:
                        stakeholder = DevelopsStakeholderInfo()
                        stakeholder.opt_type = opt_type
                        stakeholder.biz_flow_name = biz_flow_name
                        stakeholder.create_user = create_user
                        stakeholder.create_time = curr_time
                        stakeholder.opt_user = opt_user
                        ins_list.append(stakeholder)
                if is_update:
                    DevelopsStakeholderInfo.objects.update_or_create(opt_type=opt_type, biz_flow_name=biz_flow_name,
                                                                     defaults={'opt_user': opt_user,
                                                                               'create_user': create_user,
                                                                               'create_time': curr_time,
                                                                               'update_user': create_user,
                                                                               'update_time': curr_time})


class DevopsAppTeamSer:

    def sync_app_team_info(self):
        logger.info('')
        url = '{}external_interaction/mantis_mgt/app_team/get_app_team_info/'.format(SPIDER["url"])
        app_team_list = send_request(url, 'get')
        if not app_team_list:
            return
        create_user = TAPD["sync_user"]
        curr_time = datetime.datetime.now()
        ins_list = []
        for item in app_team_list:
            app_name = item.get('app_name')
            team_owner = item.get('team_owner')
            obj = DevopsAppTeamInfo.objects.filter(app_name=app_name, team_owner=team_owner)
            if not obj:
                stakeholder = DevopsAppTeamInfo()
                stakeholder.app_name = item.get('app_name')
                stakeholder.team_owner = item.get('team_owner')
                stakeholder.create_user = create_user
                stakeholder.create_time = curr_time
                ins_list.append(stakeholder)
        if ins_list:
            DevopsAppTeamInfo.objects.bulk_create(ins_list)
