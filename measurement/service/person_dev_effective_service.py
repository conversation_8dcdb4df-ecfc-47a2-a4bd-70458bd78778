import datetime
import enum
from enum import Enum

from mantis.settings import logger
from measurement.dao.person_quality_dashborad_dao import get_dev_effective_data, get_team_avg, get_team_median, \
    get_user_cn_name
from measurement.utils.diskcache_utils import DiskCacheUtil


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]

@enum.unique
class TimeCycleEnum(Enum):
    LAST_WEEK = ('last_week', '最近一周')
    LAST_MONTH = ('last_month', '近一月')
    LAST_QUARTER = ('last_quarter', '近季度')
    LAST_YEAR = ('last_year', '近一年')

    def __init__(self, module_name, module_desc):
        self.module_name = module_name
        self.module_desc = module_desc


class PersonDevEffectiveSer:

    def get_start_time(self, time_cycle):
        time_pattern = '%Y-%m-%d'
        if TimeCycleEnum.LAST_MONTH.module_name == time_cycle:
            return (datetime.datetime.now() - datetime.timedelta(days=30)).strftime(time_pattern)
        elif TimeCycleEnum.LAST_QUARTER.module_name == time_cycle:
            return (datetime.datetime.now() - datetime.timedelta(days=90)).strftime(time_pattern)
        elif TimeCycleEnum.LAST_YEAR.module_name == time_cycle:
            return (datetime.datetime.now() - datetime.timedelta(days=365)).strftime(time_pattern)
        elif TimeCycleEnum.LAST_WEEK.module_name == time_cycle:
            return (datetime.datetime.now() - datetime.timedelta(days=7)).strftime(time_pattern)
        else:
            raise Exception('不支持的时间格式')

    def get_cache_key(self, time_cycle, operator):
        cache_key = 'dev_effective_{}_{}'.format(time_cycle, operator)
        return cache_key


    def get_person_dev_effective(self, operator, time_cycle):
        operator = get_user_cn_name(operator)
        logger.info("operator:{},time_cycle:{}".format(operator, time_cycle))
        cache_key = self.get_cache_key(time_cycle, operator)
        # 先按多次查询实现，后面优化成单次查询出整个小团队的数据。zt@2024-10-21
        cache_dist = DiskCacheUtil.get_value(cache_key)
        if (not cache_dist
                or not cache_dist.get("owner")
                or not cache_dist.get("team_median")
                or not cache_dist.get("team_average")):
            start_time = self.get_start_time(time_cycle)
            exhausted = get_dev_effective_data(operator, start_time)
            team_avg = get_team_avg(operator, start_time)
            team_median = get_team_median(operator, start_time)
            cache_dist = {
                "type_name": "dev_effort",
                "owner": exhausted,
                "team_median": team_median,
                "team_average": team_avg,
            }
            DiskCacheUtil.set_value(cache_key, cache_dist, 60 * 60 * 4)
        data_list = [cache_dist]

        return data_list



