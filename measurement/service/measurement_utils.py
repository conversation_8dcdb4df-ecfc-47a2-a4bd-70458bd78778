import json
import time
import requests
from datetime import datetime
from croniter import croniter

from mantis.settings import logger


def send_request(url, method="GET", data=None, headers=None, params=None, retry=3):
    try:
        logger.info("send_request-请求地址：{}".format(url))
        if method.upper() == "GET":
            response = requests.get(url, params=params, headers=headers)
        elif method.upper() == "POST":
            if not headers:
                headers = {'Content-Type': 'application/json'}
            response = requests.post(url, data=json.dumps(data), headers=headers)
        else:
            raise ValueError("Invalid request method: {}".format(method))
        # 检查返回状态码
        if response.status_code == 200:
            # 返回响应内容
            text = response.text
            json_text = json.loads(text)
            if json_text.get('status') == 'success' or json_text.get('code') == 200:
                return json_text.get("data")
            else:
                logger.error('send_request error response: {}'.format(response.text))
                raise Exception('请求发生异常：{}'.format(response.text) + "请求地址：" + url)
        else:
            logger.error('send_request error response: {}'.format(response.text))
            if retry > 0:
                time.sleep(10)
                retry = retry - 1
                send_request(url, method, data, headers, params, retry)
    except Exception as e:
        logger.error("{}:请求发生异常：", url, e)
        raise Exception('请求发生异常：{}'.format(e) + "请求地址：" + url)

def send_request_new(url, method="GET", data=None, headers=None, params=None, retry=3):
    """pa平台两个组统一接口返回格式。zt@2025-04-23"""
    try:
        logger.info("send_request-请求地址：{}".format(url))
        if method.upper() == "GET":
            response = requests.get(url, params=params, headers=headers)
        elif method.upper() == "POST":
            if not headers:
                headers = {'Content-Type': 'application/json'}
            response = requests.post(url, data=json.dumps(data), headers=headers)
        else:
            raise ValueError("Invalid request method: {}".format(method))
        # 检查返回状态码
        if response.status_code == 200:
            # 返回响应内容
            res_txt = response.text
            res_json = json.loads(res_txt)
            if res_json.get('code') == '0000':
                return res_json.get("data")
            else:
                logger.error('send_request error response: {}'.format(res_txt))
                raise Exception('请求发生异常：{}'.format(res_txt) + "请求地址：" + url)
        else:
            logger.error('send_request error response: {}'.format(response.text))
            if retry > 0:
                time.sleep(10)
                retry = retry - 1
                send_request(url, method, data, headers, params, retry)
    except Exception as e:
        logger.error("{}:请求发生异常：", url, e)
        raise Exception('请求发生异常：{}'.format(e) + "请求地址：" + url)

def cron_to_time(cron_expression):
    # 获取当前时间
    now = datetime.now()

    # 创建cron迭代器，并设置其起始时间为当前时间
    cron = croniter(cron_expression, now)

    # 获取下一次执行的时间
    next_time = cron.get_next(datetime)

    # 将时间转换为字符串格式
    time_str = next_time.strftime("%H:%M:%S")

    return time_str
