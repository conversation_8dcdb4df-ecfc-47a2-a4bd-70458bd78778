import datetime
import enum
import json

from django.db import connection
from measurement.dao.measurement_dao import MeasurementDao
from mantis.settings import SPIDER, TAPD, logger
from measurement.model.models import TestFlowScheduleInfo, TestFlowRunRecord, TestFlowRunRecordTestset
from measurement.service.measurement_utils import send_request, cron_to_time





def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class TestFlowRunRecordTestsetSer:
    def create_flow_run_record_testset(self):
        batch_no_list = MeasurementDao.get_batch_no_list()
        url = '{}external_interaction/mantis_mgt/biz_flow_run_testset/get_test_flow_run_record_testset/'.format(
            SPIDER["url"])
        params = {'batch_no_list': batch_no_list}
        testset_run_list = send_request(url, 'get', params=params)
        create_user = TAPD["sync_user"]
        curr_time = datetime.datetime.now()
        ins_list = []
        upd_list = []
        exist_testset_batch_no_list = []

        run_execute_id_list = [item.get("execute_id") for item in testset_run_list if item.get("execute_id")]
        existing_records = TestFlowRunRecordTestset.objects.filter(execute_id__in=run_execute_id_list)

        existing_records_dict = {record.execute_id: record for record in existing_records}

        if testset_run_list:
            for item in testset_run_list:
                if item.get('testset_id'):
                    obj = TestFlowRunRecordTestset()
                    obj.run_batch_number = item.get('run_batch_number')
                    obj.testset_id = int(item.get('testset_id'))
                    obj.exec_order = int(item.get('exec_order'))
                    obj.execute_id = item.get("execute_id")
                    obj.testset_detail = json.loads(item.get('testset_detail')) if item.get('testset_detail') else None
                    obj.testset_version_type = item.get('testset_version_type')
                    obj.testset_status = item.get('testset_run_status')
                    obj.testset_app_run_detail = item.get('testset_app_run_detail')
                    obj.create_user = create_user
                    obj.create_time = curr_time
                    obj.update_user = create_user
                    obj.update_time = curr_time
                    obj.stamp = 0

                    res = existing_records_dict.get(item.get("execute_id"))

                    if res:
                        obj.id = res.id
                        upd_list.append(obj)
                    else:
                        ins_list.append(obj)

                    if item.get('run_batch_number') not in exist_testset_batch_no_list:
                        exist_testset_batch_no_list.append(item.get('run_batch_number'))

        if ins_list:
            TestFlowRunRecordTestset.objects.bulk_create(ins_list)
        if upd_list:
            TestFlowRunRecordTestset.objects.bulk_update(upd_list, ['testset_status','testset_app_run_detail', 'update_user', 'update_time'])
        logger.info('同步测试集执行记录成功的批次号：{}'.format(exist_testset_batch_no_list))
        logger.info(
            '同步测试集执行记录失败的批次号：{}'.format(list(set(batch_no_list) - set(exist_testset_batch_no_list))))
        return curr_time
