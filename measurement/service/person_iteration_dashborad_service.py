from django.db import connection

from mantis.settings import logger
from measurement.service.person_dev_effective_service import PersonDevEffectiveSer


class PersonIterationDashboardSer(PersonDevEffectiveSer):

    def get_cache_key(self, time_cycle, operator):
        cache_key = 'person_iteration_dashboard_{}_{}'.format(time_cycle, operator)
        return cache_key


class PersonIterationLinechartSer(PersonDevEffectiveSer):

    def get_linechart_cache_key(self, time_cycle, app_name, branch_name):
        cache_key = 'person_iteration_linechart_{}_{}_{}'.format(time_cycle, app_name, branch_name)
        return cache_key

