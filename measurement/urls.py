from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from measurement.api import measurement_api
from measurement.api.personal_capacity_api import personal_capacity_api

router = DefaultRouter()

router.register(r'create_flow_schedule_info', measurement_api.TestFlowScheduleInfoView,
                basename="create_flow_schedule_info/")
router.register(r'create_flow_run_record', measurement_api.TestFlowRunRecordView, basename="create_flow_run_record/")
router.register(r'create_auto_test_record', measurement_api.AutoTestStatisticsRecordView, basename="create_auto_test_record/")
router.register(r'sync_auto_test_case_record', measurement_api.AutoTestCaseRecordView,
                basename="sync_auto_test_case_record/")
router.register(r'create_flow_run_record_testset', measurement_api.TestFlowRunRecordTestsetView,
                basename="create_flow_run_record_testset/")
router.register(r'create_flow_app_deploy_info', measurement_api.TestFlowAppDeployView,
                basename="create_flow_app_deploy_info/")
router.register(r'create_biz_stakeholder', measurement_api.TestFlowStakeholderView, basename="create_biz_stakeholder/")
router.register(r'upd_user_team_info', measurement_api.UserTeamInfoView, basename="upd_user_team_info/")
router.register(r'create_testset_result', measurement_api.TestFlowRunRecordTestSetResultView,
                basename="create_testset_result/")
router.register(r'get_person_quality_dashboard', measurement_api.PersonQualityDashboardView,
                basename="get_person_quality_dashboard/")
router.register(r'get_every_auto_test_result', measurement_api.EveryDayAutoTestResultView,
                basename="get_every_auto_test_result/")
router.register(r'get_pers_capa_type', personal_capacity_api.PersCapaTypeAPI, basename="get_pers_capa_type/")
router.register(r'get_pers_capa_for_check', personal_capacity_api.PersCapaForCheckAPI,
                basename="get_pers_capa_for_check/")
router.register(r'get_pers_capa_for_output', personal_capacity_api.PersCapaForOutputAPI,
                basename="get_pers_capa_for_output/")
router.register(r'get_pers_capa_for_quality', personal_capacity_api.PersCapaForQualityAPI,
                basename="get_pers_capa_for_quality/")
router.register(r'get_person_dev_effective', measurement_api.PersonDevEffectiveView,
                basename="get_person_dev_effective/")
router.register(r'get_biz_flow_run_result', measurement_api.BizFlowRunResultView,
                basename="get_biz_flow_run_result/")
router.register(r'create_app_team_owner', measurement_api.DevopsAppTeamView, basename="create_app_team_owner/")
router.register(r'get_app_auto_test_check_result', measurement_api.CheckAppAutoTestResultView,
                basename="get_app_auto_test_check_result/")
router.register(r'get_app_auto_test_status', measurement_api.CheckAppAutoTestStatusView,
                basename="get_app_auto_test_status/")
router.register(r'get_person_iteration_dashboard', measurement_api.PersonIterationDashboardView,
                basename="get_person_iteration_dashboard/")
router.register(r'get_person_iteration_Linechart', measurement_api.PersonIterationLineChartView,
                basename="get_person_iteration_Linechart/")

urlpatterns = [
    path("", include(router.urls))
]
