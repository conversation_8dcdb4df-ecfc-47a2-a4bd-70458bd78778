import functools
import diskcache
import os
import sys
import inspect

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from mantis.settings import DISK_CACHE
from mantis.settings import logger as log


class DiskCacheUtil:
    # 实例化缓存对象，指定缓存目录
    cache = diskcache.Cache(DISK_CACHE['cache_dir'], eviction_policy='least-recently-stored', cull_limit=60 * 60 * 24)

    @classmethod
    def set_value(cls, key, value, expire):
        cls.cache.set(key, value, expire)

    @classmethod
    def get_value(cls, key):
        value = cls.cache.get(key)
        return value


def disk_cache(expire_seconds=60*60*4):
    def cache_decorator(func):
        @functools.wraps(func)
        def wrapper_func(*args, **kwargs):
            cache_key = __get_cache_key(args, kwargs)
            log.info(">>>> cache_key: {}".format(cache_key))
            log.info(">>>> expire_seconds: {}".format(expire_seconds))
            return func(*args, **kwargs)
        return wrapper_func
    return cache_decorator


def __get_cache_key(*args, **kwargs):
    cache_key = "disk_cache"
    for arg in args:
        cache_key = cache_key + "_" + str(arg)

    for k, v in kwargs.items():
        cache_key = cache_key + "_" + str(k) + "=" + v

    return cache_key


class SimpleCache:
    # cache_path = 'C:\\Users\\<USER>\\temp\\'
    cache_path = DISK_CACHE['cache_dir']
    eviction_policy = 'least-recently-stored'
    cull_limit = 60 * 60 * 24

    cache_key_prefix = "disk_cache"
    cache_expire_seconds = 60 * 60 * 4

    cache = diskcache.Cache(directory=cache_path, eviction_policy=eviction_policy, cull_limit=cull_limit)

    def __init__(self, cache_path=None, eviction_policy=None, cull_limit=None, expire_seconds=None):
        if cache_path:
            self.cache_path = cache_path
        if eviction_policy:
            self.eviction_policy = eviction_policy
        if cull_limit:
            self.cull_limit = cull_limit
        if expire_seconds:
            self.expire_seconds = expire_seconds
        else:
            self.expire_seconds = SimpleCache.cache_expire_seconds

        self.cache = diskcache.Cache(directory=self.cache_path, eviction_policy=self.eviction_policy,
                                     cull_limit=self.cull_limit)

    def __call__(self, func):
        @functools.wraps(func)
        def wrapper_func(*args, **kwargs):
            params = args
            if params:
                first_param = params[0]
                if inspect.isclass(first_param):
                    log.info(">>>> first_param: {}".format(first_param))
                    replace_first_param = first_param.__class__.__name__
                    params = [replace_first_param].extend(params[1:])
            cache_key = self._get_cache_key(func.__name__, *params, **kwargs)
            # log.info(">>>> cache_key: {}".format(cache_key))
            # log.info(">>>> expire_seconds: {}".format(self.expire_seconds))

            is_except = False
            try:
                key_prefix = self.cache_key_prefix + "_" + func.__name__
                if cache_key == key_prefix:
                    err_msg = "无参函数：{}，禁用缓存！".format(func.__name__)
                    raise Exception(err_msg)
            except Exception as e:
                is_except = True
                log.warning("Warning: {}".format(e))
            finally:
                pass

            try:
                if not is_except:
                    cache_obj = self.cache.get(cache_key)
                    if not cache_obj:
                        log.info(">>>> 没有查到缓存数据，cache_key：{}".format(cache_key))
                        cache_obj = func(*args, **kwargs)
                        if cache_obj:
                            self.cache.set(cache_key, cache_obj, self.expire_seconds)
                        else:
                            log.warning(">>>> 无数据返回，不需要缓存。")
                    else:
                        log.info(">>>> 直接从缓存：{}，获取到数据：{}".format(cache_key, cache_obj))
                else:
                    cache_obj = func(*args, **kwargs)
            except Exception as e:
                log.error("Exception: {}".format(e))
                raise e
            finally:
                pass

            return cache_obj

        return wrapper_func

    def _get_cache_key(self, func_name, *args, **kwargs):
        cache_key = self.cache_key_prefix + "_" + func_name
        for arg in args:
            cache_key = cache_key + "_" + str(arg)

        for k, v in kwargs.items():
            cache_key = cache_key + "_" + str(k) + "=" + str(v)

        return cache_key


@SimpleCache(expire_seconds=60*5)
def myfunc():
    print(">>>>myfunc()")
    return ">>>>myfunc()"


@SimpleCache(expire_seconds=60*5)
def myfunc1(param):
    print(">>>>myfunc1()")
    return "myfunc1({})".format(param)


@SimpleCache(expire_seconds=60*5)
def myfunc2(param1, param2, kwargs1=None):
    print(">>>>myfunc2()")
    return "myfunc2({}, {}, kwargs1={})".format(param1, param2, kwargs1)


class TestCache:
    def __init__(self, user_name=None):
        self.user_name = user_name

    @SimpleCache(expire_seconds=60*5)
    def myfunc(self, param1, param2, kwargs1=None):
        msg = ">>>>TestCache:myfunc() >> user_name = {}".format(self.user_name)
        log.info(msg)
        return msg


if __name__ == '__main__':
    print(">>>>main()")

    myfunc()

    myfunc1("zt")

    myfunc2(1, 2, kwargs1=3)

    test_cache = TestCache("恏")
    test_cache.myfunc(1, 2, kwargs1=3)
