import datetime
import logging

import chinese_calendar

from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaTypeEnum
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaAllTimeCycleEnum
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaCheckTimeCycleEnum
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaOutputTimeCycleEnum
from measurement.common.personal_capacity_common.personal_capacity_constants import PersCapaQualityTimeCycleEnum


def get_cache_key_with_enum(type_enum: PersCapaTypeEnum, time_cycle, operator):
    cache_key = 'dev_effic_{}_{}_{}'.format(type_enum, time_cycle, operator)
    return cache_key


def get_cache_key_only(type_enum: PersCapaTypeEnum, time_cycle, operator):
    cache_key = 'dev_effic_only_{}_{}_{}'.format(type_enum, time_cycle, operator)
    return cache_key


def get_cache_key_with_bug_kloc(type_enum: PersCapaTypeEnum):
    cache_key = 'dev_effic_{}'.format(type_enum)
    return cache_key


def get_start_end_time(time_cycle_enum: PersCapaAllTimeCycleEnum, current_time=None):
    if current_time is None:
        current_time = datetime.datetime.now()

    curr_date = current_time.date()
    curr_month = curr_date.month
    curr_year = curr_date.year
    curr_quarter = (curr_month - 1) // 3 + 1

    first_day_of_month = datetime.datetime(curr_year, curr_month, 1)
    first_day_of_quarter = datetime.datetime(curr_year, (curr_quarter - 1) * 3 + 1, 1)
    first_day_of_year = datetime.datetime(curr_year, 1, 1)

    last_year = curr_date.year - 1
    next_year = curr_date.year + 1

    if time_cycle_enum == PersCapaAllTimeCycleEnum.THIS_QUARTER:
        start_time = first_day_of_quarter
        end_time = first_day_of_month
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.THIS_YEAR:
        start_time = first_day_of_year
        end_time = first_day_of_quarter
        if end_time > first_day_of_month:
            end_time = first_day_of_month
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_FIRST_QUARTER:
        start_time, end_time = get_time_before_first_quarter(current_time)

    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_SECOND_QUARTER:
        start_time, end_time = get_time_before_second_quarter(current_time)

    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_THIRD_QUARTER:
        start_time, end_time = get_time_before_third_quarter(current_time)

    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_FOURTH_QUARTER:
        start_time, end_time = get_time_before_fourth_quarter(current_time)

    elif time_cycle_enum == PersCapaAllTimeCycleEnum.FIRST_HALF_OF_THE_YEAR:
        start_time = datetime.datetime(curr_date.year, 1, 1)
        end_time = datetime.datetime(curr_date.year, 7, 1)
        if end_time > first_day_of_month:
            end_time = first_day_of_month
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.SECOND_HALF_OF_THE_YEAR:
        start_time = datetime.datetime(curr_date.year, 7, 1)
        end_time = datetime.datetime(next_year, 1, 1)
        if end_time > first_day_of_month:
            end_time = first_day_of_month
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.LAST_WEEK:
        start_time = curr_date - datetime.timedelta(days=7)
        end_time = curr_date
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.LAST_MONTH:
        start_time = curr_date - datetime.timedelta(days=30)
        end_time = curr_date
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.LAST_QUARTER:
        start_time = curr_date - datetime.timedelta(days=90)
        end_time = curr_date
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.LAST_YEAR:
        start_time = curr_date - datetime.timedelta(days=365)
        end_time = curr_date
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.THIS_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, 0)
        start_time = title_start_date
        end_time = title_end_date + datetime.timedelta(days=1)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_FIRST_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -1)
        start_time = title_start_date
        end_time = title_end_date + datetime.timedelta(days=1)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_SECOND_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -2)
        start_time = title_start_date
        end_time = title_end_date + datetime.timedelta(days=1)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_THIRD_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -3)
        start_time = title_start_date
        end_time = title_end_date + datetime.timedelta(days=1)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_FOURTH_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -4)
        start_time = title_start_date
        end_time = title_end_date + datetime.timedelta(days=1)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_FIFTH_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -5)
        start_time = title_start_date
        end_time = title_end_date + datetime.timedelta(days=1)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_SIXTH_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -6)
        start_time = title_start_date
        end_time = title_end_date + datetime.timedelta(days=1)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_SEVENTH_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -7)
        start_time = title_start_date
        end_time = title_end_date + datetime.timedelta(days=1)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_EIGHTH_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -8)
        start_time = title_start_date
        end_time = title_end_date + datetime.timedelta(days=1)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_NINTH_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -9)
        start_time = title_start_date
        end_time = title_end_date + datetime.timedelta(days=1)
    else:
        raise Exception('不支持的时间格式')
    return start_time, end_time


def get_start_end_time_for_check(check_time_cycle_enum: PersCapaCheckTimeCycleEnum, current_time=None):
    if current_time is None:
        current_time = datetime.datetime.now()

    curr_date = current_time.date()
    curr_month = curr_date.month
    curr_year = curr_date.year
    curr_quarter = (curr_month - 1) // 3 + 1

    first_day_of_month = datetime.datetime(curr_year, curr_month, 1)
    first_day_of_quarter = datetime.datetime(curr_year, (curr_quarter - 1) * 3 + 1, 1)
    first_day_of_year = datetime.datetime(curr_year, 1, 1)

    last_year = curr_date.year - 1
    next_year = curr_date.year + 1

    if check_time_cycle_enum == PersCapaCheckTimeCycleEnum.THIS_QUARTER:
        start_time = first_day_of_quarter
        end_time = first_day_of_month
    elif check_time_cycle_enum == PersCapaCheckTimeCycleEnum.THIS_YEAR:
        start_time = first_day_of_year
        end_time = first_day_of_quarter
        if end_time > first_day_of_month:
            end_time = first_day_of_month
    elif check_time_cycle_enum == PersCapaCheckTimeCycleEnum.FIRST_QUARTER:
        start_time = datetime.datetime(curr_date.year, 1, 1)
        end_time = datetime.datetime(curr_date.year, 4, 1)
        if end_time > first_day_of_month:
            end_time = first_day_of_month
    elif check_time_cycle_enum == PersCapaCheckTimeCycleEnum.SECOND_QUARTER:
        start_time = datetime.datetime(curr_date.year, 4, 1)
        end_time = datetime.datetime(curr_date.year, 7, 1)
        if end_time > first_day_of_month:
            end_time = first_day_of_month
    elif check_time_cycle_enum == PersCapaCheckTimeCycleEnum.THIRD_QUARTER:
        start_time = datetime.datetime(curr_date.year, 7, 1)
        end_time = datetime.datetime(curr_date.year, 10, 1)
        if end_time > first_day_of_month:
            end_time = first_day_of_month
    elif check_time_cycle_enum == PersCapaCheckTimeCycleEnum.FOURTH_QUARTER:
        start_time = datetime.datetime(curr_date.year, 10, 1)
        end_time = datetime.datetime(next_year, 1, 1)
        if end_time > first_day_of_month:
            end_time = first_day_of_month
    elif check_time_cycle_enum == PersCapaCheckTimeCycleEnum.FIRST_HALF_OF_THE_YEAR:
        start_time = datetime.datetime(curr_date.year, 1, 1)
        end_time = datetime.datetime(curr_date.year, 7, 1)
        if end_time > first_day_of_month:
            end_time = first_day_of_month
    elif check_time_cycle_enum == PersCapaCheckTimeCycleEnum.SECOND_HALF_OF_THE_YEAR:
        start_time = datetime.datetime(curr_date.year, 7, 1)
        end_time = datetime.datetime(next_year, 1, 1)
        if end_time > first_day_of_month:
            end_time = first_day_of_month
    else:
        raise Exception('不支持的时间格式')

    return start_time, end_time


def get_start_end_time_for_output(output_time_cycle_enum: PersCapaOutputTimeCycleEnum, current_time=None):
    if current_time is None:
        current_time = datetime.datetime.now()

    curr_date = current_time.date()
    if output_time_cycle_enum == PersCapaOutputTimeCycleEnum.LAST_WEEK:
        start_time = curr_date - datetime.timedelta(days=7)
        end_time = curr_date
    elif output_time_cycle_enum == PersCapaOutputTimeCycleEnum.LAST_MONTH:
        start_time = curr_date - datetime.timedelta(days=30)
        end_time = curr_date
    elif output_time_cycle_enum == PersCapaOutputTimeCycleEnum.LAST_QUARTER:
        start_time = curr_date - datetime.timedelta(days=90)
        end_time = curr_date
    elif output_time_cycle_enum == PersCapaOutputTimeCycleEnum.LAST_YEAR:
        start_time = curr_date - datetime.timedelta(days=365)
        end_time = curr_date
    else:
        raise Exception('不支持的时间格式')
    return start_time, end_time


def get_start_end_time_for_quality(quality_time_cycle_enum: PersCapaQualityTimeCycleEnum, current_time=None):
    if current_time is None:
        current_time = datetime.datetime.now()

    current_date = current_time.date()
    if quality_time_cycle_enum == PersCapaQualityTimeCycleEnum.LAST_WEEK:
        start_time = current_date - datetime.timedelta(days=7)
        end_time = current_date
    elif quality_time_cycle_enum == PersCapaQualityTimeCycleEnum.LAST_MONTH:
        start_time = current_date - datetime.timedelta(days=30)
        end_time = current_date
    elif quality_time_cycle_enum == PersCapaQualityTimeCycleEnum.LAST_QUARTER:
        start_time = current_date - datetime.timedelta(days=90)
        end_time = current_date
    elif quality_time_cycle_enum == PersCapaQualityTimeCycleEnum.LAST_YEAR:
        start_time = current_date - datetime.timedelta(days=365)
        end_time = current_date
    else:
        raise Exception('不支持的时间格式')
    return start_time, end_time


def get_title_for_check(time_cycle_enum: PersCapaAllTimeCycleEnum, current_time=None):
    if current_time is None:
        current_time = datetime.datetime.now()

    curr_date = current_time.date()
    curr_month = curr_date.month
    curr_year = curr_date.year
    curr_quarter = (curr_month - 1) // 3 + 1

    first_day_of_month = datetime.datetime(curr_year, curr_month, 1)
    first_day_of_quarter = datetime.datetime(curr_year, (curr_quarter - 1) * 3 + 1, 1)
    first_day_of_year = datetime.datetime(curr_year, 1, 1)

    last_month = curr_month - 1
    next_month = curr_month + 1
    last_quarter = curr_quarter - 1
    next_quarter = curr_quarter + 1
    last_year = curr_date.year - 1
    next_year = curr_date.year + 1

    title = ""
    work_days_diff = 0
    if time_cycle_enum == PersCapaAllTimeCycleEnum.THIS_QUARTER:
        title_month = last_month
        title_start_date = first_day_of_quarter
        title_end_date = first_day_of_month - datetime.timedelta(days=1)
        if title_end_date < title_start_date:
            title = "当季首月【无完整月数据】"
        else:
            title_start_date_str = title_start_date.strftime("%m.%d")
            title_end_date_str = title_end_date.strftime("%m.%d")
            title = "当季截至{}月【{}~{}】".format(title_month, title_start_date_str, title_end_date_str)
        work_days_diff = len(chinese_calendar.get_workdays(first_day_of_quarter, title_end_date))
        ideal_working_hours = work_days_diff * 8
        month_diff = (title_end_date.month - first_day_of_quarter.month) + 1
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.THIS_YEAR:
        title_start_date = first_day_of_year
        title_end_date = first_day_of_quarter - datetime.timedelta(days=1)
        if title_end_date < title_start_date:
            title = "年初【无完整季数据】"
        else:
            title_quarter = last_quarter
            title_start_date_str = first_day_of_year.strftime("%m.%d")
            title_end_date_str = title_end_date.strftime("%m.%d")
            title = "当年截至Q{}结束【{}~{}】".format(title_quarter, title_start_date_str, title_end_date_str)

        work_days_diff = len(chinese_calendar.get_workdays(first_day_of_year, title_end_date))
        ideal_working_hours = work_days_diff * 8
        month_diff = (title_end_date.month - first_day_of_year.month) + 1
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_FIRST_QUARTER:
        title, ideal_working_hours, month_diff, work_days_diff = get_title_before_first_quarter(current_time)

    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_SECOND_QUARTER:

        # title, ideal_working_hours, month_diff = get_title_before_second_quarter(current_time)
        title, ideal_working_hours, month_diff, work_days_diff = get_title_before_second_quarter(current_time)

    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_THIRD_QUARTER:
        title, ideal_working_hours, month_diff, work_days_diff = get_title_before_third_quarter(current_time)

    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_FOURTH_QUARTER:
        title, ideal_working_hours, month_diff, work_days_diff = get_title_before_fourth_quarter(current_time)

    else:
        raise Exception('不支持的时间格式')

    return title, ideal_working_hours, month_diff, work_days_diff


def get_time_before_first_quarter(current_time=None):
    if current_time is None:
        current_time = datetime.datetime.now()

    curr_date = current_time.date()
    curr_month = curr_date.month
    curr_year = curr_date.year
    curr_quarter = (curr_month - 1) // 3 + 1

    first_day_of_quarter = datetime.datetime(curr_year, (curr_quarter - 1) * 3 + 1, 1)

    before_first_quarter_year = curr_year
    before_first_quarter_month = curr_month - 3
    if before_first_quarter_month <= 0:
        before_first_quarter_month = before_first_quarter_month + 12
        before_first_quarter_year = before_first_quarter_year - 1

    before_first_quarter_time = datetime.datetime(before_first_quarter_year, before_first_quarter_month, 1)
    before_first_quarter_date = before_first_quarter_time.date()

    computer_year = before_first_quarter_date.year
    computer_quarter = (before_first_quarter_date.month - 1) // 3 + 1

    start_time = datetime.datetime(computer_year, (computer_quarter - 1) * 3 + 1, 1)
    end_time = first_day_of_quarter

    return start_time, end_time


def get_time_before_second_quarter(current_time=None):
    if current_time is None:
        current_time = datetime.datetime.now()

    curr_date = current_time.date()
    curr_month = curr_date.month
    curr_year = curr_date.year
    curr_quarter = (curr_month - 1) // 3 + 1

    first_day_of_quarter = datetime.datetime(curr_year, (curr_quarter - 1) * 3 + 1, 1)

    # 三个月前的1号：
    before_first_quarter_year = curr_year
    before_first_quarter_month = curr_month - 3
    if before_first_quarter_month <= 0:
        before_first_quarter_month = before_first_quarter_month + 12
        before_first_quarter_year = before_first_quarter_year - 1
    before_first_quarter_time = datetime.datetime(before_first_quarter_year, before_first_quarter_month, 1)
    before_first_quarter_date = before_first_quarter_time.date()
    before_first_quarter = (before_first_quarter_date.month - 1) // 3 + 1
    first_day_of_before_first_quarter = datetime.datetime(before_first_quarter_date.year,
                                                          (before_first_quarter - 1) * 3 + 1,
                                                          1)
    # 六个月前的1号：
    before_second_quarter_year = curr_year
    before_second_quarter_month = curr_month - 6
    if before_second_quarter_month <= 0:
        before_second_quarter_month = before_second_quarter_month + 12
        before_second_quarter_year = before_second_quarter_year - 1
    before_second_quarter_time = datetime.datetime(before_second_quarter_year, before_second_quarter_month, 1)
    before_second_quarter_date = before_second_quarter_time.date()
    before_second_quarter = (before_second_quarter_date.month - 1) // 3 + 1
    first_day_of_before_second_quarter = datetime.datetime(before_second_quarter_date.year,
                                                           (before_second_quarter - 1) * 3 + 1,
                                                           1)

    start_time = first_day_of_before_second_quarter
    end_time = first_day_of_before_first_quarter

    return start_time, end_time


def get_time_before_third_quarter(current_time=None):
    if current_time is None:
        current_time = datetime.datetime.now()

    curr_date = current_time.date()
    curr_month = curr_date.month
    curr_year = curr_date.year
    curr_quarter = (curr_month - 1) // 3 + 1

    first_day_of_quarter = datetime.datetime(curr_year, (curr_quarter - 1) * 3 + 1, 1)

    # 六个月前的1号：
    before_second_quarter_year = curr_year
    before_second_quarter_month = curr_month - 6
    if before_second_quarter_month <= 0:
        before_second_quarter_month = before_second_quarter_month + 12
        before_second_quarter_year = before_second_quarter_year - 1
    before_second_quarter_time = datetime.datetime(before_second_quarter_year, before_second_quarter_month, 1)
    before_second_quarter_date = before_second_quarter_time.date()
    before_second_quarter = (before_second_quarter_date.month - 1) // 3 + 1
    first_day_of_before_second_quarter = datetime.datetime(before_second_quarter_date.year,
                                                           (before_second_quarter - 1) * 3 + 1,
                                                           1)
    # 九个月前的1号：
    before_third_quarter_year = curr_year
    before_third_quarter_month = curr_month - 9
    if before_third_quarter_month <= 0:
        before_third_quarter_month = before_third_quarter_month + 12
        before_third_quarter_year = before_third_quarter_year - 1
    before_third_quarter_time = datetime.datetime(before_third_quarter_year, before_third_quarter_month, 1)
    before_third_quarter_date = before_third_quarter_time.date()
    before_third_quarter = (before_third_quarter_date.month - 1) // 3 + 1
    first_day_of_before_third_quarter = datetime.datetime(before_third_quarter_date.year,
                                                          (before_third_quarter - 1) * 3 + 1,
                                                          1)

    start_time = first_day_of_before_third_quarter
    end_time = first_day_of_before_second_quarter

    return start_time, end_time


def get_time_before_fourth_quarter(current_time=None):
    if current_time is None:
        current_time = datetime.datetime.now()

    curr_date = current_time.date()
    curr_month = curr_date.month
    curr_year = curr_date.year
    curr_quarter = (curr_month - 1) // 3 + 1

    first_day_of_quarter = datetime.datetime(curr_year, (curr_quarter - 1) * 3 + 1, 1)

    # 九个月前的1号：
    before_third_quarter_year = curr_year
    before_third_quarter_month = curr_month - 9
    if before_third_quarter_month <= 0:
        before_third_quarter_month = before_third_quarter_month + 12
        before_third_quarter_year = before_third_quarter_year - 1
    before_third_quarter_time = datetime.datetime(before_third_quarter_year, before_third_quarter_month, 1)
    before_third_quarter_date = before_third_quarter_time.date()
    before_third_quarter = (before_third_quarter_date.month - 1) // 3 + 1
    first_day_of_before_third_quarter = datetime.datetime(before_third_quarter_date.year,
                                                          (before_third_quarter - 1) * 3 + 1,
                                                          1)
    # 十二个月前的1号：
    before_fourth_quarter_year = curr_year
    before_fourth_quarter_month = curr_month - 12
    if before_fourth_quarter_month <= 0:
        before_fourth_quarter_month = before_fourth_quarter_month + 12
        before_fourth_quarter_year = before_fourth_quarter_year - 1
    before_fourth_quarter_time = datetime.datetime(before_fourth_quarter_year, before_fourth_quarter_month, 1)
    before_fourth_quarter_date = before_fourth_quarter_time.date()
    before_fourth_quarter = (before_fourth_quarter_date.month - 1) // 3 + 1
    first_day_of_before_fourth_quarter = datetime.datetime(before_fourth_quarter_date.year,
                                                           (before_fourth_quarter - 1) * 3 + 1,
                                                           1)

    start_time = first_day_of_before_fourth_quarter
    end_time = first_day_of_before_third_quarter

    return start_time, end_time


def get_title_before_first_quarter(current_time=None):
    if current_time is None:
        current_time = datetime.datetime.now()

    curr_date = current_time.date()
    curr_month = curr_date.month
    curr_year = curr_date.year
    curr_quarter = (curr_month - 1) // 3 + 1

    first_day_of_quarter = datetime.datetime(curr_year, (curr_quarter - 1) * 3 + 1, 1)

    before_first_quarter_year = curr_year
    before_first_quarter_month = curr_month - 3
    if before_first_quarter_month <= 0:
        before_first_quarter_month = before_first_quarter_month + 12
        before_first_quarter_year = before_first_quarter_year - 1

    before_first_quarter_time = datetime.datetime(before_first_quarter_year, before_first_quarter_month, 1)
    before_first_quarter_date = before_first_quarter_time.date()

    title_year = before_first_quarter_date.year
    title_quarter = (before_first_quarter_date.month - 1) // 3 + 1

    # 上季第一天
    title_start_date = datetime.datetime(title_year, (title_quarter - 1) * 3 + 1, 1)
    title_start_date_str = title_start_date.strftime("%m.%d")
    # 本季第一天，再减一天
    title_end_date = first_day_of_quarter - datetime.timedelta(days=1)
    title_end_date_str = title_end_date.strftime("%m.%d")

    work_days_diff = len(chinese_calendar.get_workdays(title_start_date, title_end_date))
    ideal_working_hours = work_days_diff * 8
    month_diff = (title_end_date.month - title_start_date.month) + 1
    title = "{}Q{}【{}~{}】".format(title_year, title_quarter, title_start_date_str, title_end_date_str)

    return title, ideal_working_hours, month_diff, work_days_diff


def get_title_before_second_quarter(current_time=None):
    if current_time is None:
        current_time = datetime.datetime.now()

    curr_date = current_time.date()
    curr_month = curr_date.month
    curr_year = curr_date.year
    curr_quarter = (curr_month - 1) // 3 + 1

    first_day_of_quarter = datetime.datetime(curr_year, (curr_quarter - 1) * 3 + 1, 1)

    # 三个月前的1号：
    before_first_quarter_year = curr_year
    before_first_quarter_month = curr_month - 3
    if before_first_quarter_month <= 0:
        before_first_quarter_month = before_first_quarter_month + 12
        before_first_quarter_year = before_first_quarter_year - 1
    before_first_quarter_time = datetime.datetime(before_first_quarter_year, before_first_quarter_month, 1)
    before_first_quarter_date = before_first_quarter_time.date()
    before_first_quarter = (before_first_quarter_date.month - 1) // 3 + 1
    first_day_of_before_first_quarter = datetime.datetime(before_first_quarter_date.year,
                                                          (before_first_quarter - 1) * 3 + 1,
                                                          1)
    # 六个月前的1号：
    before_second_quarter_year = curr_year
    before_second_quarter_month = curr_month - 6
    if before_second_quarter_month <= 0:
        before_second_quarter_month = before_second_quarter_month + 12
        before_second_quarter_year = before_second_quarter_year - 1
    before_second_quarter_time = datetime.datetime(before_second_quarter_year, before_second_quarter_month, 1)
    before_second_quarter_date = before_second_quarter_time.date()
    before_second_quarter = (before_second_quarter_date.month - 1) // 3 + 1
    first_day_of_before_second_quarter = datetime.datetime(before_second_quarter_date.year,
                                                           (before_second_quarter - 1) * 3 + 1,
                                                           1)

    title_year = before_second_quarter_date.year
    title_quarter = before_second_quarter

    # 上上季第一天
    title_start_date = first_day_of_before_second_quarter
    title_start_date_str = title_start_date.strftime("%m.%d")
    # 上季第一天，再减一天
    title_end_date = first_day_of_before_first_quarter - datetime.timedelta(days=1)
    title_end_date_str = title_end_date.strftime("%m.%d")

    work_days_diff = len(chinese_calendar.get_workdays(title_start_date, title_end_date))
    ideal_working_hours = work_days_diff * 8
    month_diff = (title_end_date.month - title_start_date.month) + 1
    title = "{}Q{}【{}~{}】".format(title_year, title_quarter, title_start_date_str, title_end_date_str)

    return title, ideal_working_hours, month_diff, work_days_diff


def get_title_before_third_quarter(current_time=None):
    if current_time is None:
        current_time = datetime.datetime.now()

    curr_date = current_time.date()
    curr_month = curr_date.month
    curr_year = curr_date.year
    curr_quarter = (curr_month - 1) // 3 + 1

    first_day_of_quarter = datetime.datetime(curr_year, (curr_quarter - 1) * 3 + 1, 1)

    # 六个月前的1号：
    before_second_quarter_year = curr_year
    before_second_quarter_month = curr_month - 6
    if before_second_quarter_month <= 0:
        before_second_quarter_month = before_second_quarter_month + 12
        before_second_quarter_year = before_second_quarter_year - 1
    before_second_quarter_time = datetime.datetime(before_second_quarter_year, before_second_quarter_month, 1)
    before_second_quarter_date = before_second_quarter_time.date()
    before_second_quarter = (before_second_quarter_date.month - 1) // 3 + 1
    first_day_of_before_second_quarter = datetime.datetime(before_second_quarter_date.year,
                                                           (before_second_quarter - 1) * 3 + 1,
                                                           1)
    # 九个月前的1号：
    before_third_quarter_year = curr_year
    before_third_quarter_month = curr_month - 9
    if before_third_quarter_month <= 0:
        before_third_quarter_month = before_third_quarter_month + 12
        before_third_quarter_year = before_third_quarter_year - 1
    before_third_quarter_time = datetime.datetime(before_third_quarter_year, before_third_quarter_month, 1)
    before_third_quarter_date = before_third_quarter_time.date()
    before_third_quarter = (before_third_quarter_date.month - 1) // 3 + 1
    first_day_of_before_third_quarter = datetime.datetime(before_third_quarter_date.year,
                                                          (before_third_quarter - 1) * 3 + 1,
                                                          1)

    title_year = before_third_quarter_date.year
    title_quarter = before_third_quarter

    # 上上上季第一天
    title_start_date = first_day_of_before_third_quarter
    title_start_date_str = title_start_date.strftime("%m.%d")
    # 上上季第一天，再减一天
    title_end_date = first_day_of_before_second_quarter - datetime.timedelta(days=1)
    title_end_date_str = title_end_date.strftime("%m.%d")

    work_days_diff = len(chinese_calendar.get_workdays(title_start_date, title_end_date))
    ideal_working_hours = work_days_diff * 8
    month_diff = (title_end_date.month - title_start_date.month) + 1
    title = "{}Q{}【{}~{}】".format(title_year, title_quarter, title_start_date_str, title_end_date_str)

    return title, ideal_working_hours, month_diff, work_days_diff


def get_title_before_fourth_quarter(current_time=None):
    if current_time is None:
        current_time = datetime.datetime.now()

    curr_date = current_time.date()
    curr_month = curr_date.month
    curr_year = curr_date.year
    curr_quarter = (curr_month - 1) // 3 + 1

    first_day_of_quarter = datetime.datetime(curr_year, (curr_quarter - 1) * 3 + 1, 1)

    # 九个月前的1号：
    before_third_quarter_year = curr_year
    before_third_quarter_month = curr_month - 9
    if before_third_quarter_month <= 0:
        before_third_quarter_month = before_third_quarter_month + 12
        before_third_quarter_year = before_third_quarter_year - 1
    before_third_quarter_time = datetime.datetime(before_third_quarter_year, before_third_quarter_month, 1)
    before_third_quarter_date = before_third_quarter_time.date()
    before_third_quarter = (before_third_quarter_date.month - 1) // 3 + 1
    first_day_of_before_third_quarter = datetime.datetime(before_third_quarter_date.year,
                                                          (before_third_quarter - 1) * 3 + 1,
                                                          1)
    # 十二个月前的1号：
    before_fourth_quarter_year = curr_year
    before_fourth_quarter_month = curr_month - 12
    if before_fourth_quarter_month <= 0:
        before_fourth_quarter_month = before_fourth_quarter_month + 12
        before_fourth_quarter_year = before_fourth_quarter_year - 1
    before_fourth_quarter_time = datetime.datetime(before_fourth_quarter_year, before_fourth_quarter_month, 1)
    before_fourth_quarter_date = before_fourth_quarter_time.date()
    before_fourth_quarter = (before_fourth_quarter_date.month - 1) // 3 + 1
    first_day_of_before_fourth_quarter = datetime.datetime(before_fourth_quarter_date.year,
                                                           (before_fourth_quarter - 1) * 3 + 1,
                                                           1)

    title_year = before_fourth_quarter_date.year
    title_quarter = before_fourth_quarter
    # 上上上上季第一天
    title_start_date = first_day_of_before_fourth_quarter
    title_start_date_str = title_start_date.strftime("%m.%d")
    # 上上上季第一天，再减一天
    title_end_date = first_day_of_before_third_quarter - datetime.timedelta(days=1)
    title_end_date_str = title_end_date.strftime("%m.%d")

    work_days_diff = len(chinese_calendar.get_workdays(title_start_date, title_end_date))
    ideal_working_hours = work_days_diff * 8
    month_diff = (title_end_date.month - title_start_date.month) + 1
    title = "{}Q{}【{}~{}】".format(title_year, title_quarter, title_start_date_str, title_end_date_str)

    return title, ideal_working_hours, month_diff, work_days_diff


def get_title_for_output_and_quality(time_cycle_enum: PersCapaAllTimeCycleEnum, current_time=None):
    """获取产能和质量的标题（近10个月）。zt@2025-02-27"""
    if current_time is None:
        current_time = datetime.datetime.now()

    curr_date = current_time.date()
    curr_month = curr_date.month
    curr_year = curr_date.year

    title_start_date = None
    title_end_date = None
    title = None
    if time_cycle_enum == PersCapaAllTimeCycleEnum.THIS_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, 0)
        title_start_date_str = title_start_date.strftime("%m.%d")
        title_end_date_str = title_end_date.strftime("%m.%d")
        title = "当月【{}~{}】".format(title_start_date_str, title_end_date_str)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_FIRST_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -1)
        title_start_date_str = title_start_date.strftime("%m.%d")
        title_end_date_str = title_end_date.strftime("%m.%d")
        title = "上月【{}~{}】".format(title_start_date_str, title_end_date_str)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_SECOND_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -2)
        title_start_date_str = title_start_date.strftime("%m.%d")
        title_end_date_str = title_end_date.strftime("%m.%d")
        title = "上上月【{}~{}】".format(title_start_date_str, title_end_date_str)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_THIRD_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -3)
        title_start_date_str = title_start_date.strftime("%m.%d")
        title_end_date_str = title_end_date.strftime("%m.%d")
        title = "往前第3月【{}~{}】".format(title_start_date_str, title_end_date_str)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_FOURTH_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -4)
        title_start_date_str = title_start_date.strftime("%m.%d")
        title_end_date_str = title_end_date.strftime("%m.%d")
        title = "往前第4月【{}~{}】".format(title_start_date_str, title_end_date_str)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_FIFTH_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -5)
        title_start_date_str = title_start_date.strftime("%m.%d")
        title_end_date_str = title_end_date.strftime("%m.%d")
        title = "往前第5月【{}~{}】".format(title_start_date_str, title_end_date_str)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_SIXTH_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -6)
        title_start_date_str = title_start_date.strftime("%m.%d")
        title_end_date_str = title_end_date.strftime("%m.%d")
        title = "往前第6月【{}~{}】".format(title_start_date_str, title_end_date_str)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_SEVENTH_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -7)
        title_start_date_str = title_start_date.strftime("%m.%d")
        title_end_date_str = title_end_date.strftime("%m.%d")
        title = "往前第7月【{}~{}】".format(title_start_date_str, title_end_date_str)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_EIGHTH_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -8)
        title_start_date_str = title_start_date.strftime("%m.%d")
        title_end_date_str = title_end_date.strftime("%m.%d")
        title = "往前第8月【{}~{}】".format(title_start_date_str, title_end_date_str)
    elif time_cycle_enum == PersCapaAllTimeCycleEnum.BEFORE_NINTH_MONTH:
        title_month, title_start_date, title_end_date = get_month_title_for_output_and_quality(current_time, -9)
        title_start_date_str = title_start_date.strftime("%m.%d")
        title_end_date_str = title_end_date.strftime("%m.%d")
        title = "往前第9月【{}~{}】".format(title_start_date_str, title_end_date_str)

    work_days_diff = len(chinese_calendar.get_workdays(title_start_date, title_end_date))
    ideal_working_hours = work_days_diff * 8
    month_diff = 1

    return title, ideal_working_hours, month_diff, work_days_diff


def get_month_title_for_output_and_quality(current_time=None, month_num=0):
    """通过月份偏移量计划，直接返回月数和起始日期。zt@2025-02-17"""
    if current_time is None:
        current_time = datetime.datetime.now()

    curr_date = current_time.date()
    curr_month = curr_date.month
    curr_year = curr_date.year

    if month_num == 0:
        ret_month = curr_month
        first_day_of_month = datetime.datetime(curr_year, curr_month, 1)
        # 优化当月月末日期。zt@2025-08-01
        # end_day_of_month = current_time - datetime.timedelta(days=1)
        next_month = curr_month + 1
        first_day_of_next_month = datetime.datetime(curr_year, next_month, 1)
        end_day_of_month = first_day_of_next_month - datetime.timedelta(days=1)
    else:
        computer_year = curr_year + month_num // 12
        computer_month = curr_month + month_num % 12
        if computer_month > 12:
            computer_year = computer_year + 1
            computer_month = computer_month - 12

        if computer_month == 12:
            ret_year = computer_year
            ret_month = 12
            ret_next_year = computer_year + 1
            ret_next_month = 1
        else:
            ret_year = computer_year
            ret_month = computer_month
            ret_next_year = computer_year
            ret_next_month = ret_month + 1

        first_day_of_month = datetime.datetime(ret_year, ret_month, 1)
        end_day_of_month = datetime.datetime(ret_next_year, ret_next_month, 1) - datetime.timedelta(days=1)

    title_month = ret_month
    title_start_date = first_day_of_month
    title_end_date = end_day_of_month

    return  title_month, title_start_date, title_end_date

def parse_percent_value(percent_val=0, round_int=2) -> str:
    if not percent_val or 0 == percent_val:
        ret_val = "0%"
    else:
        ret_val = str(round(percent_val * 100, round_int)) + "%"
    return ret_val

def parse_bug_num_every_work_day(bug_num=0, work_days=0, round_int=3):
    if not bug_num or not work_days or bug_num == "0":
        ret_val = 0
    else:
        try:
            bug_num = int(bug_num)
            ret_val = round(bug_num / work_days, round_int)
        except Exception as value_error:
            logging.warning("转「int」失败，bug_num ={}, msg= {}".format(bug_num, value_error))
            ret_val = 0
    return ret_val