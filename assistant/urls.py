from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from assistant.service import knowledge, blueocean_trouble_shooter, table_data_fighter, \
    hbapidoc_fighter, table_data_fighter_spark, table_data_fighter_low, question_answer_view
from assistant.service.abandoned import coding_finder
from assistant.service.ai_ops import jks_trouble_shooter_view
from assistant.service.base import sparkgpt, sparkgpt35, general_gpt, hunyuan, optional_gpt

router = DefaultRouter()

router.register('chatgpt', hunyuan.HunyuanTurbo, basename="chatgpt")
router.register('chatgpt4', sparkgpt35.SparkGptMax32, basename="chatgpt4")
router.register('knowledge', knowledge.Knowledge, basename="knowledge")
router.register('hbapidoc_fighter', hbapidoc_fighter.HbapidocFighter, basename="hbapidoc_fighter")

router.register('coding_finder', coding_finder.InterfaceImplementFinder, basename="coding_finder")
router.register('blueocean_trouble_shooter', blueocean_trouble_shooter.BlueOceanTroubleShooter, basename="blueocean_trouble_shooter")

router.register('table_data_fighter', table_data_fighter.TableDataFighter, basename="table_data_fighter")

router.register('sparkgpt', sparkgpt35.SparkGptMax32, basename="sparkgpt")
router.register('sparkgpt35', sparkgpt35.SparkGpt35, basename="sparkgpt35")
router.register('sparkgptmax32', sparkgpt35.SparkGptMax32, basename="sparkgptmax32")
router.register('sparkgptpro128', sparkgpt35.SparkGptPro128, basename="sparkgptpro128")

router.register('table_data_fighter_spark', table_data_fighter_spark.TableDataFighterSpark, basename="table_data_fighter_spark")
router.register('table_data_fighter_low', table_data_fighter_low.TableDataFighterLow, basename="table_data_fighter_low")
router.register('jks_trouble_shooter_view', jks_trouble_shooter_view.JksTroubleShooter, basename="jks_trouble_shooter_view")

router.register('general_gpt', general_gpt.GeneralGpt, basename="general_gpt")
router.register('general_gpt_lite', general_gpt.GeneralGptLite, basename="general_gpt_lite")
router.register('optional_gpt', optional_gpt.OptionalGpt, basename="optional_gpt")

router.register('question_answer', question_answer_view.QuestionAnswer, basename="question_answer")

router.register('hunyuan_turbo', hunyuan.HunyuanTurbo, basename="hunyuan_turbo")
router.register('hunyuan_t1', hunyuan.HunyuanT1, basename="hunyuan_t1")
router.register('hunyuan_lite', hunyuan.HunyuanLite, basename="hunyuan_lite")
router.register('hunyuan_std256', hunyuan.HunyuanStd256, basename="hunyuan_std256")
router.register('deepseekv3', hunyuan.DeepseekV3, basename="deepseekv3")

urlpatterns = [
    path("", include(router.urls))
]
