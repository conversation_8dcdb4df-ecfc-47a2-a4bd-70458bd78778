from mantis.pool import instance_db_session
from sqlalchemy.sql import text
from utest.data.utest_report_suites_model import ReportSuitesModel


class ReportSuitesDao():
    __query_current_suites_sql = '''
        	select distinct c_m.test_class, c_m.module_name, app.team_alias from
        	(
        	select distinct replace(rs.test_class, ".", "/") as test_class, last.module_name
        	FROM
        		utest_report_suites rs,
        	(
        	SELECT
        		distinct ri.module_name, rb.id
        	FROM
        		utest_report_info ri, utest_report_behaviors rb
        	where ri.id = rb.unit_report_id and rb.failed = 0 and rb.broken = 0
        	and ri.create_time >= date_format( date_add(now(), interval -5 day), '%Y-%m-01')
        	) last
        	where rs.behaviors_id = last.id
        	) c_m, mantis.v_spider_java_apps app
        	where
        	c_m.module_name = app.module_name and
        	c_m.test_class not in
        	(
        	select distinct replace(rs.test_class, ".", "/") as test_class
        	FROM
        		utest_report_suites rs,
        	(
        	SELECT
        		ri.module_name, max(rb.id) id
        	FROM
        		utest_report_info ri, utest_report_behaviors rb
        	where ri.id = rb.unit_report_id and rb.failed = 0 and rb.broken = 0
        	and ri.create_time < date_format( date_add(now(), interval -5 day), '%Y-%m-01')
        	group by module_name
        	) last
        	where rs.behaviors_id = last.id
        	)
            '''

    def query_current_suites(self):
        db_session = instance_db_session()
        result = db_session.query(ReportSuitesModel).from_statement(
            text(self.__query_current_suites_sql)).all()
        db_session.close()
        return result


if __name__ == '__main__':
    dao = ReportSuitesDao()
    test = dao.query_current_suites()
