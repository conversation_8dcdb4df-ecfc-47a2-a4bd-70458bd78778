from django.db import models
from django.db.models.expressions import RawSQL


class AssistQuestionAnswerModel(models.Model):
    id = models.IntegerField(primary_key=True)
    q_key = models.CharField(max_length=200)
    q_content = models.BinaryField()
    q_question = models.BinaryField()
    q_url = models.CharField(max_length=500)
    q_answer = models.BinaryField()
    q_satisfied = models.CharField(max_length=100)
    update_date = models.DateTimeField()

    @classmethod
    def create_with_compressed_content(cls, q_key, q_content, q_question, q_url):
        compressed_content = RawSQL("COMPRESS(%s)", (q_content,))
        compressed_question = RawSQL("COMPRESS(%s)", (q_question,))
        return cls.objects.create(q_key=q_key, q_content=compressed_content, q_question=compressed_question,
                                  q_url=q_url)

    @classmethod
    def update_with_compressed_content(cls, q_key, q_answer):
        compressed_answer = RawSQL("COMPRESS(%s)", (q_answer,))
        update_date_now = RawSQL("now()", ())
        return cls.objects.filter(q_key=q_key).update(q_answer=compressed_answer, update_date=update_date_now)

    @classmethod
    def update_satisfied(cls, q_key, q_satisfied):
        update_date_now = RawSQL("now()", ())
        return cls.objects.filter(q_key=q_key).update(q_satisfied=q_satisfied, update_date=update_date_now)

    class Meta:
        db_table = 'assist_question_answer'
