from django.db import models, connection


class FileBlameModel(models.Model):
    lines = models.Char<PERSON>ield(max_length=2000)
    project_id = models.IntegerField()
    path = models.CharField(max_length=250)
    sequence = models.IntegerField()

    class Meta:
        db_table = 'spider.code_repo_gitlab_file_blames'

    @staticmethod
    def find_relative_classes_by_name(class_name):
        sql = '''
            select project_id, path, line3 as line from
            (
            -- ==========找到所有可能和查询的类发生关系的类定义==============================
            select project_id, path, group_concat(`lines` order by sequence) as line3
            from
            (
            select distinct fb.project_id, fb.path, fb.sequence, fb.`lines`
            from
                spider.code_repo_gitlab_file_blames fb
            inner join
            (
            select distinct fb.project_id, fb.path, fb.id
            from
                spider.code_repo_gitlab_file_blames fb
            inner join
                spider.code_repo_gitlab_file_line_detail ld
            on ld.fraction = '{class_name}'
            and fb.project_id = ld.project_id and fb.path = ld.path
            where fb.`lines` like '%{class_name}%'
            and fb.`lines` not like 'import%'
            and fb.effective = 'Y'
            and fb.path like '%.java'
            ) location
            on location.project_id = fb.project_id and location.path = fb.path
            and fb.id < location.id + 3
            and fb.id > location.id - 3
            and fb.effective = 'Y'
            ) loc_3
            group by project_id, path
            -- ==============================找到所有可能和查询的类发生关系的类定义==========
            ) loc_3_com
            where line3 like '%interface%' or line3 like '%class%'
        '''.format(class_name=class_name)
        cursor = connection.cursor()
        cursor.execute(sql)

        return cursor.fetchall()

    @staticmethod
    def find_lines(project_id, path):
        sql = '''
            SELECT
                fb.`lines`
            FROM
                spider.code_repo_gitlab_file_blames fb
            where fb.project_id = {project_id}  and fb.path = '{path}'
            and (fb.effective = 'Y' or (fb.effective = 'N' and fb.`lines` like 'import%'))
            and trim(fb.`lines`) <> ''
            order by fb.sequence
        '''.format(project_id=project_id, path=path)

        cursor = connection.cursor()
        cursor.execute(sql)

        return cursor.fetchall()

    @staticmethod
    def find_by_class_and_method(class_name, method_name):
        sql = '''
            select fb.id, fb.`lines`, fb.project_id, fb.path, fb.sequence
            from
                spider.code_repo_gitlab_file_blames fb
            inner join
            (

            select fb.project_id, fb.path, fb.sequence
            from
                spider.code_repo_gitlab_file_blames fb
            inner join
            (
            select fb.project_id, fb.path
            from
                spider.code_repo_gitlab_file_blames fb
            inner join
                spider.code_repo_gitlab_file_line_detail ld
            on ld.fraction = '{class_name}'
            and fb.project_id = ld.project_id and fb.path = ld.path
            where fb.`lines` like '%class%{class_name}%'
            ) correct
            on fb.project_id = correct.project_id and fb.path = correct.path
            and fb.`lines` like '%public%{method_name}(%'

            ) met
            on met.project_id = fb.project_id and met.path = fb.path and fb.sequence >= met.sequence
            where fb.effective = 'Y'
            order by sequence
            limit 200
        '''.format(class_name=class_name, method_name=method_name)
        cursor = connection.cursor()
        cursor.execute(sql)

        return cursor.fetchall()
