from django.db import models


class AssistGptContentModel(models.Model):
    level1 = models.CharField(max_length=25)
    level2 = models.CharField(max_length=25)
    level3 = models.CharField(max_length=25)
    level4 = models.CharField(max_length=25)
    level5 = models.CharField(max_length=25)
    content = models.CharField(max_length=4000)

    class Meta:
        db_table = 'assist_gpt_content'
