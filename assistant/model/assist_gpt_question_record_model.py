from django.db import models


class AssistGptQuestionRecordModel(models.Model):
    id = models.IntegerField(primary_key=True)
    question = models.CharField(max_length=65535)
    output_param = models.Char<PERSON>ield(max_length=16777215)
    input_param = models.CharField(max_length=16777215)
    from_client = models.CharField(max_length=100)
    result_state = models.IntegerField()
    duration = models.DecimalField(max_digits=18, decimal_places=3)

    class Meta:
        db_table = 'assist_gpt_question_record'
