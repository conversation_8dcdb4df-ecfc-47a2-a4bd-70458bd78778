from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from assistant.model.assist_gpt_content_model import AssistGptContentModel
# from assistant.service.base.chatgpt4 import Chatgpt4 as Chatgpt
# from assistant.service.base.sparkgpt35 import <PERSON>rkGpt35 as Chatgpt
from assistant.service.base.hunyuan import HunyuanT1 as Chatgpt


class Knowledge(ViewSet):
    authentication_classes = []

    def __find_all_relat_contents(self, level1, level2, level3, level4, level5):
        if len(level4.strip()) == 0:
            res = AssistGptContentModel.objects.filter(level1=level1, level2=level2, level3=level3)
        else:
            if len(level5.strip()) == 0:
                res = AssistGptContentModel.objects.filter(level1=level1, level2=level2, level3=level3, level4=level4)
            else:
                res = AssistGptContentModel.objects.filter(level1=level1, level2=level2, level3=level3, level4=level4,
                                                           level5=level5)

        contents = []
        for e in res:
            if e.content:
                contents.append(e.content)

        return contents

    @action(methods=['POST'], detail=False, url_path='ask')
    def ask(self, request):
        """
        ask 提问接口
        request.data["question"] 是一个String，用于传入问题
        """
        try:
            question = request.data["question"]
            from_client = Chatgpt.get_from_client(request)
            level1 = request.data["level1"]
            level2 = request.data["level2"]
            level3 = request.data["level3"]
            level4 = request.data["level4"]
            level5 = request.data["level5"]
            rounds = None
            if "rounds" in request.data:
                rounds = request.data["rounds"]

            return self.do_ask(level1, level2, level3, level4, level5, question, from_client, rounds)

        except Exception as err:
            return Response(status=status.HTTP_200_OK, data={"answer": "发生错误无法回答{}".format(err)})

    def do_ask(self, level1, level2, level3, level4, level5, question, from_client, rounds=None):
        chatgpt = Chatgpt()
        try:
            contents = self.__find_all_relat_contents(level1, level2, level3, level4, level5)
            answer = chatgpt.do_ask(contents, question, from_client, rounds)
            return Response(status=status.HTTP_200_OK, data={"answer": answer})
        except Exception as err:
            return Response(status=status.HTTP_200_OK, data={"answer": "发生错误无法回答{}".format(err)})
