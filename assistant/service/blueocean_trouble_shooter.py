import json
import re

import requests
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from assistant.service.base.hunyuan import HunyuanTurbo as Chatgpt
from mantis.settings import logger


class BlueOceanTroubleShooter(ViewSet):
    authentication_classes = []

    condition_for_base_info = """这是一个jenkins流水线的地址，host是什么，job名是什么，执行编号是什么。用以下格式回答：
{"is_jenkins":"True", "host_name":"", "job_name":"", "number":""}
如果不是jenkins流水线的地址，请回答
{"is_jenkins":"False"}
"""
    problem_url_template = "http://{}/jenkins/job/{}/{}/logText/progressiveHtml/api/json"
    condition_for_problem = """问题用以下格式回答：
1.
2.
3.

如果主要是ios问题，询问ios开发同学；如果主要是安卓问题，询问安卓开发同学；如果主要是Java问题，询问Java开发同学；如果是前端项目问题，询问H5开发同学；如果是部署问题，询问运维同学；其他问题，询问张怀天。
"""
    answer_for_wrong_question = "这显然不是一个正常的jenkins流水线地址！"
    jenkins_error_pattern = '</span><span class=".*?">Failed in'
    question_for_problem_template = '{}\n\n上述日志中发生错误最可能的问题是什么？用格式回答。并提示询问谁。如果没有发生错误就回答没有发现错误。'

    # jenkins_error_pattern = 'Stage ".*?" skipped due to earlier failure'

    @action(methods=['POST'], detail=False, url_path='ask')
    def ask(self, request):
        self.gpt = Chatgpt()
        from_client = Chatgpt.get_from_client(request)
        jenkins_url = request.data["jenkins_url"]

        param = self.__get_blueocean_log_url(jenkins_url, from_client)
        param = json.loads(param)

        if "is_jenkins" in param and param["is_jenkins"] == 'True' and "job_name" in param and "number" in param:
            problem_url = self.problem_url_template.format(param["host_name"], param["job_name"], param["number"])
            answer = self.__find_problem(problem_url, from_client)
        else:
            answer = self.answer_for_wrong_question

        return Response(status=status.HTTP_200_OK, data={"answer": answer})

    def __get_blueocean_log_url(self, problem_url, from_client):
        return self.gpt.do_ask([self.condition_for_base_info], problem_url, from_client)

    def __find_problem(self, log_text_url, from_client):
        res = "没有找到内容。"
        try:
            response = requests.get(log_text_url)
            if response.status_code == 200:
                content = response.text

                match = re.search(self.jenkins_error_pattern, content)
                if match:
                    end_index = match.start()
                else:
                    end_index = len(content)

                start_index = end_index - 8000 if end_index - 8000 > 0 else 0
                problem = self.question_for_problem_template.format(content[start_index:end_index])
                res = self.gpt.do_ask([self.condition_for_problem], problem, from_client)

        except Exception as err:
            logger.error(err)

        return res
