from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from bs4 import BeautifulSoup
from assistant.service.base.hunyuan import DeepseekV3 as Chatgpt


class TableDataFighter(ViewSet):
    authentication_classes = []

    gpt = Chatgpt()

    @action(methods=['POST'], detail=False, url_path='ask')
    def ask(self, request):
        from_client = Chatgpt.get_from_client(request)
        table_data = request.data["table_data"]
        question = "{}\n\n用示例的表格形式回答。".format(request.data["question"])

        condition = "这是输入表格：{}。\n\n回答用表格形式：<table><tr><td></td></tr></table>。".format(
            TableDataFighter.list_to_table(table_data))

        i = 3
        solved = False
        while i > 0:
            i = i - 1
            answer = self.gpt.do_ask([condition], question, from_client)
            if "<table>" in answer:
                solved = True
                break

        if solved:
            res = TableDataFighter.html_table_to_list(answer)
        else:
            res = table_data

        return Response(status=status.HTTP_200_OK, data={"answer": res})

    @staticmethod
    def list_to_table(data):
        table_html = "<table>"
        for row in data:
            table_html += "<tr>"
            for item in row:
                table_html += "<td>" + str(item) + "</td>"
            table_html += "</tr>"
        table_html += "</table>"
        return table_html

    @staticmethod
    def html_table_to_list(html_table):
        soup = BeautifulSoup(html_table, 'html.parser')
        # 找到表格元素
        table = soup.find('table')
        # 找到所有行元素
        rows = table.find_all('tr')
        # 遍历行元素并提取数据
        data = []
        for row in rows:
            cells = row.find_all('td')
            if cells:
                # 如果是数据行，将数据添加到列表中
                item = [cell.text for cell in cells]
                data.append(item)

        return data

