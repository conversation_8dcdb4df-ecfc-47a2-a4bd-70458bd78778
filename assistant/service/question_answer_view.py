from hashlib import md5

from django.http import HttpResponse
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from assistant.model.assist_question_answer_model import AssistQuestionAnswerModel
# from assistant.service.base.chatgpt4 import Chatgpt4 as Chatgpt
# from assistant.service.base.sparkgpt import SparkGpt
# from assistant.service.base.sparkgpt35 import SparkGpt35 as Chatgpt
#from assistant.service.base.sparkgpt35 import SparkGptPro128 as Chatgpt
from assistant.service.base.hunyuan import HunyuanTurbo as Chatgpt
from mantis.settings import logger


class QuestionAnswer(ViewSet):
    authentication_classes = []

    gpt = Chatgpt()

    @action(methods=['POST'], detail=False, url_path='question_store')
    def question_store(self, request):
        content = str(request.data["content"]) if "content" in request.data else ""
        question = str(request.data["question"])
        key_str = '%s%s' % (content, question)
        url = request.data["url"] if 'url' in request.data else None
        key = md5(key_str.encode('utf-8')).hexdigest()

        try:
            result = AssistQuestionAnswerModel.objects.extra(
                select={'q_content': "CONVERT(UNCOMPRESS(q_content), CHAR)"}).values('id', 'q_key', 'q_content',
                                                                                     'q_question').filter(q_key=key)
            if result and len(result) > 0:
                return Response(status=status.HTTP_200_OK, data={"result": True, "key": key})

            AssistQuestionAnswerModel.create_with_compressed_content(key, content, question, url)
            return Response(status=status.HTTP_200_OK, data={"result": True, "key": key})
        except Exception as err:
            logger.error("存入问题时报错了！", err)
            return Response(status=status.HTTP_200_OK, data={"result": False, "key": "骗骗你的！"})

    @action(methods=['GET'], detail=False, url_path='answer_satisfied')
    def answer_satisfied(self, request):
        key = request.query_params["key"]
        satisfied = request.query_params["satisfied"]
        AssistQuestionAnswerModel.update_satisfied(key, satisfied)
        return Response(status=status.HTTP_200_OK, data={"result": True})

    @action(methods=['GET'], detail=False, url_path='question_answer')
    def question_answer(self, request):
        from_client = Chatgpt.get_from_client(request)
        key = request.query_params["key"]
        content, question, answer = self.__question_query(key)

        if answer is None or answer == "":
            answer = self.gpt.do_ask([content], question, from_client)
            AssistQuestionAnswerModel.update_with_compressed_content(key, answer)

        # return Response(status=status.HTTP_200_OK, data={"answer": answer})

        html_content = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>问题解答</title>
    <style>
        .dialog-message {
            border: 2px solid red;
            text-align: center;
        }

        .dialog-message p {
            font-size: 32px;
            font-weight: bold;
        }
    </style>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
    <script>
        $(document).ready(function () {
            $('input[name="satisfaction"]').on('change', function() {
                var value = $(this).val();
                var url = "../answer_satisfied/?key=%s&satisfied=" + value;
                $.ajax({
                    url: url,
                    type: "GET",
                    success: function () {
                        // 弹出对话框
                        var dialog = $('<div class="dialog-message"><p>感谢评价</p></div>').dialog({
                            modal: true,
                            closeOnEscape: false,
                            draggable: false,
                            resizable: false,
                            dialogClass: "no-close",
                            open: function(event, ui) {
                                $(".ui-dialog-titlebar-close", ui.dialog | ui).hide();
                            }
                        });
                        setTimeout(function () {
                            dialog.dialog('close');
                        }, 3000);
                    }
                });
            });
        });
    </script>
</head>
<body>
<h3>
James here to tell you：
</h3>
<form>
    <label>
        <input type="radio" name="satisfaction" value="excellent">特别有用
    </label>
    <label>
        <input type="radio" name="satisfaction" value="good">有点指导性
    </label>
    <label>
        <input type="radio" name="satisfaction" value="nonsense">没什么用
    </label>
    <label>
        <input type="radio" name="satisfaction" value="bullshit">完全瞎说
    </label>
</form>
<h3>回答</h3>
<textarea name="answer" id="answer" cols="150" rows="20" readonly>%s</textarea>
<h3>原始问题</h3>
<textarea name="question" id="question" cols="150" rows="10" readonly>%s</textarea>
</body>
</html>
        """ % (key, answer, question)

        # 返回HTML响应
        return HttpResponse(content=html_content, content_type='text/html')

    def __question_query(self, key):
        result = AssistQuestionAnswerModel.objects.extra(select={'q_content': "CONVERT(UNCOMPRESS(q_content), CHAR)",
                                                                 'q_question': "CONVERT(UNCOMPRESS(q_question), CHAR)",
                                                                 'q_answer': "CONVERT(UNCOMPRESS(q_answer), CHAR)"}).values(
            'id', 'q_key', 'q_content', 'q_question', 'q_answer').filter(q_key=key)
        return result[0].get('q_content'), result[0].get('q_question'), result[0].get('q_answer')


"""
groovy中try catch用法
                    try{
                    } catch (Exception e) {
                        def logContent = currentBuild.rawBuild.getLog(200)
                        def params = [
                            "content": logContent, 
                            "question": "这是什么问题？"
                            ]
                        println params
                        def response = httpRequest(
                            contentType: 'APPLICATION_JSON',
                            httpMode: 'POST',
                            requestBody: JsonOutput.toJson(params),
                            url: 'http://【host】/mantis/assistant/question_answer/question_store/'
                        )
                        def json = readJSON text: response.content
                        def key = json.key
        
                        println "看不懂？移步这里：http://mantis-test.howbuy.pa/mantis/assistant/question_answer/question_answer/?key=$key"
                        
                        throw e
                    }
"""
