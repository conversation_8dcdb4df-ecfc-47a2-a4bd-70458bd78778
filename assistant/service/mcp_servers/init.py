# import os
# import threading
# import logging
# import importlib
# import pkgutil
#
# # 设置日志记录器
# logger = logging.getLogger(__name__)
#
#
# def start_mcp_server():
#     """
#     在后台线程中启动MCP服务器
#     """
#     try:
#         from assistant.service.base.mcp_service import mcp, create_starlette_app
#         import uvicorn
#
#         # 获取MCP服务器实例
#         mcp_server = mcp._mcp_server
#
#         # 创建Starlette应用
#         starlette_app = create_starlette_app(mcp_server, debug=True)
#
#         # 启动服务器
#         host = '0.0.0.0'
#         port = 8033
#         logger.info(f"Starting MCP server on {host}:{port}")
#         uvicorn.run(starlette_app, host=host, port=port)
#     except Exception as e:
#         logger.error(f"Failed to start MCP server: {str(e)}")
#
#
# def import_mcp_tools():
#     """
#     动态导入所有mcp工具模块
#     """
#     try:
#         # 导入assistant.service.mcp_servers包
#         from assistant.service import mcp_servers
#
#         # 遍历包中所有以mcp_开头的模块
#         for _, name, _ in pkgutil.iter_modules(mcp_servers.__path__):
#             if name.startswith('mcp_'):
#                 importlib.import_module(f'assistant.service.mcp_servers.{name}')
#                 logger.info(f"Loaded MCP tool module: {name}")
#     except Exception as e:
#         logger.error(f"Failed to import MCP tools: {str(e)}")
#
#
# # 在Django启动时自动启动MCP服务
# def start_mcp_server_thread():
#     """
#     创建并启动MCP服务器线程
#     """
#     # 首先导入所有MCP工具
#     import_mcp_tools()
#
#     # 然后启动MCP服务器
#     mcp_thread = threading.Thread(target=start_mcp_server, daemon=True)
#     mcp_thread.start()
#     logger.info("MCP server thread started")
#
#
# # 当Django应用准备就绪时启动MCP服务
# start_mcp_server_thread()