

import json
import httpx
from assistant.service.base.mcp_service import mcp


@mcp.tool()
async def get_apidoc(app_name: str, branch: str, api_name: str):
    """
    这个方法是根据应用名，分支号（有时候叫版本号）和接口名（关键字）获取接口定义的文档。这个接口文档也可以用于开发前端界面。

    参数:
    app_name: 应用名，例如 "howbuy-qa-info-remote"
    branch: 分支号，例如 "1.5.4"
    api_name: 接口名，例如 "get_agent_info"

    返回:
    Json格式的接口文档
    """
    # 构建API URL
    url = f"http://spider.howbuy.pa/spider/app_mgt/app_interface_api_param_for_mring?app_name={app_name}&branch_name={branch}"
    
    # 发送HTTP请求获取接口信息
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, timeout=30.0)
            data = response.json()
            
            # 检查请求是否成功
            if data.get("status") != "success":
                return {"error": f"获取接口信息失败: {data.get('msg', '未知错误')}"}
            
            # 遍历接口列表，查找匹配的接口
            for interface in data.get("data", []):
                # 检查interfacePath是否包含api_name
                if api_name in interface.get("interfacePath", ""):
                    return interface
            
            # 如果没有找到匹配的接口
            return {"error": f"未找到包含 {api_name} 的接口"}
            
        except Exception as e:
            return {"error": f"请求异常: {str(e)}"}

@mcp.tool()
async def get_app_deployed_branch(suite_code: str):
    """
    这个方法是根据环境名称获取应用部署版本（分支）的列表

    参数:
    suite_code: 环境名称，例如环境名是it50，it62，或bs-prod

    返回:
    Json格式的应用部署版本（分支）情况
    """
    # 构建API URL
    url = f"http://spider.howbuy.pa/spider/lib_repo_mgt/get_app_deployed_branch/?suite_code={suite_code}"

    # 发送HTTP请求获取接口信息
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, timeout=30.0)
            data = response.json()

            return data

        except Exception as e:
            return {"error": f"请求异常: {str(e)}"}