{"status": "success", "data": [{"appName": "otc-center-remote", "branchName": "7.3.3", "interfaceName": "spring内部error接口", "interfacePath": "/error", "interfaceMethod": "GET", "interfaceType": "http", "status": 1, "createUser": "pa", "createTime": "2025-04-10T17:50:21", "updateUser": null, "updateTime": null, "param_list": []}, {"appName": "otc-center-remote", "branchName": "7.3.3", "interfaceName": "PostDubboComHowbuyOtcCenterFacadeWebUpdatefundmancreditUpdatefundmancreditfacadeExecute", "interfacePath": "dubbo:com.howbuy.otc.center.facade.web.updatefundmancredit.UpdateFundManCreditFacade.execute()", "interfaceMethod": "POST", "interfaceType": "http", "status": 1, "createUser": "pa", "createTime": "2025-04-10T17:50:21", "updateUser": null, "updateTime": null, "param_list": [{"param_type": "response_param", "param_name": "description", "param_default_value": null, "param_desc": "<p>描述</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "request_param", "param_name": "operator", "param_default_value": null, "param_desc": "<p>操作员</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "request_param", "param_name": "productUnifyCreditStatus", "param_default_value": null, "param_desc": "<p>产品统一授信状态 0-否 1-是</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "request_param", "param_name": "creditStatus", "param_default_value": null, "param_desc": "<p>授信状态 0-未授信 1-已授信</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "request_param", "param_name": "coopMerchantId", "param_default_value": null, "param_desc": "<p>商户号</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "request_param", "param_name": "updateProductAutoCreditFlag", "param_default_value": null, "param_desc": "<p>更新管理人下所有产品自动授信标识</br>默认false-该管理人下已授信产品授信状态不变，后续新增产品将不自动授信，true-该管理人下所有产品及后续新增产品将自动授信</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "response_param", "param_name": "returnCode", "param_default_value": null, "param_desc": "<p>返回代码</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "request_param", "param_name": "ip<PERSON><PERSON><PERSON>", "param_default_value": null, "param_desc": "<p>IP地址</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "request_param", "param_name": "serviceCode", "param_default_value": null, "param_desc": "<p>服务代码 F310142</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "request_param", "param_name": "updateAllProductCreditFlag", "param_default_value": null, "param_desc": "<p>更新管理人下所有产品授信状态标识 默认false不更新，true-更新</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "request_param", "param_name": "creditAmount", "param_default_value": null, "param_desc": "<p>授信金额</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "request_param", "param_name": "fundManCode", "param_default_value": null, "param_desc": "<p>管理人代码</p>", "param_allowed_values": null, "param_size": null, "optional": 0}]}, {"appName": "otc-center-remote", "branchName": "7.3.3", "interfaceName": "PostDubboComHowbuyOtcCenterFacadeWebUpdatefundproductcreditUpdatefundproductcreditfacadeExecute", "interfacePath": "dubbo:com.howbuy.otc.center.facade.web.updatefundproductcredit.UpdateFundProductCreditFacade.execute()", "interfaceMethod": "POST", "interfaceType": "http", "status": 1, "createUser": "pa", "createTime": "2025-04-10T17:50:21", "updateUser": null, "updateTime": null, "param_list": [{"param_type": "request_param", "param_name": "operator", "param_default_value": null, "param_desc": "<p>操作员</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "request_param", "param_name": "creditStatus", "param_default_value": null, "param_desc": "<p>授信状态 0-未授信 1-已授信</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "request_param", "param_name": "coopMerchantId", "param_default_value": null, "param_desc": "<p>商户号</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "response_param", "param_name": "returnCode", "param_default_value": null, "param_desc": "<p>返回代码</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "request_param", "param_name": "ip<PERSON><PERSON><PERSON>", "param_default_value": null, "param_desc": "<p>IP地址</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "response_param", "param_name": "description", "param_default_value": null, "param_desc": "<p>描述</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "request_param", "param_name": "serviceCode", "param_default_value": null, "param_desc": "<p>服务代码 F310143</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "request_param", "param_name": "creditAmount", "param_default_value": null, "param_desc": "<p>授信金额</p>", "param_allowed_values": null, "param_size": null, "optional": 0}, {"param_type": "request_param", "param_name": "fundManCode", "param_default_value": null, "param_desc": "<p>管理人代码</p>", "param_allowed_values": null, "param_size": null, "optional": 0}]}], "msg": "查询应用otc-center-remote版本7.3.3的接口信息成功！"}