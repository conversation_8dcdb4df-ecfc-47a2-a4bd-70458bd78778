import re

from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from assistant.service.base.hunyuan import <PERSON><PERSON>uanTur<PERSON> as Chatgpt

from assistant.service.base.sparkgpt import SparkGpt


class TableDataFighterSpark(ViewSet):
    authentication_classes = []

    @action(methods=['POST'], detail=False, url_path='ask')
    def ask(self, request):
        self.gpt = SparkGpt()
        from_client = Chatgpt.get_from_client(request)
        table_data = request.data["table_data"]
        question = "\n\n{}\n\n用同样的表格形式回答。不要额外的语句来解释。".format(request.data["question"])

        question = "这是输入表格：{}。\n\n。".format(
            TableDataFighterSpark.list_to_table(table_data)) + question

        i = 3
        solved = False
        answer = ""
        while i > 0:
            i = i - 1
            answer = self.gpt.do_ask([], question, from_client)
            if "|" in answer:
                solved = True
                break

        if solved:
            res = TableDataFighterSpark.markdown_to_list(answer)
        else:
            res = table_data

        return Response(status=status.HTTP_200_OK, data={"answer": res})

    @staticmethod
    def list_to_table(data):
        table_md = "|"
        for item in data[0]:
            table_md += str(item) + "|"
        table_md += "\n|"
        for i in range(len(data[0])):
            table_md += "---|"
        table_md += "\n"
        for row in data[1:]:
            table_md += "|"
            for item in row:
                table_md += str(item) + "|"
            table_md += "\n"
        return table_md

    @staticmethod
    def markdown_to_list(markdown_str):
        """
        Convert markdown table to list of lists.
        """
        # Split markdown string into rows
        rows = markdown_str.strip().replace('||', '|').split('\n')

        # Strip row borders and split into cells
        row_data = [re.sub(' *\| *', '|', row[1:-1]).split('|') for row in rows]

        # Strip whitespace from cells
        row_data = [[cell.strip() for cell in row] for row in row_data]

        return row_data
