from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from assistant.service.base.hunyuan import HunyuanTurbo, HunyuanFC, DeepseekV3, HunyuanT1
from assistant.service.base.sparkgpt35 import SparkGptMax32


class OptionalGpt(ViewSet):
    authentication_classes = []

    @action(methods=['POST'], detail=False, url_path='ask')
    def ask(self, request):
        """
        ask 提问接口
        request.data["conditions"] 是一个String数组，用于传入样本
        request.data["question"] 是一个String，用于传入问题
        request.data["rounds"] 是一个String数组，用于传入多轮。奇数个是问题，偶数个是回答
        """
        if "conditions" in request.data:
            conditions = request.data["conditions"]
        else:
            conditions = None
        question = request.data["question"]
        model_name = request.data["model_name"]

        from_client = OptionalGpt.get_from_client(request)

        selected_model =self.__select_model(model_name)

        if "rounds" in request.data:
            rounds = request.data["rounds"]
            answer = selected_model.do_ask(conditions, question, from_client, rounds)
        else:
            answer = selected_model.do_ask(conditions, question, from_client)

        return Response(status=status.HTTP_200_OK, data={"answer": answer})

    @staticmethod
    def get_from_client(request):
        if 'HTTP_X_FORWARDED_FOR' in request.META:
            return request.META.get('HTTP_X_FORWARDED_FOR')
        else:
            return request.META.get('REMOTE_ADDR')

    def __select_model(self, model_name):
        model_mapping = {
            "HunyuanTurbo": HunyuanTurbo,
            "HunyuanFC": HunyuanFC,
            "DeepseekV3": HunyuanT1,
            "SparkGptMax32": HunyuanTurbo,
        }

        if model_name not in model_mapping:
            return HunyuanFC()
        else:
            return model_mapping[model_name]()


