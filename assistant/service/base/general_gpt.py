from assistant.service.base.hunyuan import Hu<PERSON>uanTurbo, DeepseekV3, HunyuanStd256, HunyuanStd, HunyuanFC
from assistant.service.base.sparkgpt import SparkGpt


class GeneralGpt(SparkGpt):
    gpt = HunyuanTurbo

    def do_ask(self, conditions, question, from_client="127.0.0.1", rounds=None):
        return self.gpt.do_ask(self.gpt, conditions, question, from_client, rounds)


class GeneralGptLite(SparkGpt):
    gpt = HunyuanFC

    def do_ask(self, conditions, question, from_client="127.0.0.1", rounds=None):
        return self.gpt.do_ask(self.gpt, conditions, question, from_client, rounds)
