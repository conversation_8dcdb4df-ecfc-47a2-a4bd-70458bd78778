from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from assistant.model.assist_gpt_question_record_model import AssistGptQuestionRecordModel
from assistant.service.base.base_gpt import BaseGpt
from assistant.tools import SparkApi
from mantis.settings import logger


class SparkGpt(ViewSet, BaseGpt):
    authentication_classes = []

    # 以下密钥信息从控制台获取
    appid = "f28aa02c"  # 填写控制台中获取的 APPID 信息
    api_secret = "NzY3OWY4ZDY0NTI0NjE2M2Q1MmY3N2Yz"  # 填写控制台中获取的 APISecret 信息
    api_key = "95637f9b8e5ed166ba3bf6cadce4b692"  # 填写控制台中获取的 APIKey 信息

    # 用于配置大模型版本，默认“general/generalv2”
    # domain = "general"  # v1.5版本
    domain = "generalv3"    # v2.0版本
    # 云端环境的服务地址
    # Spark_url = "ws://spark-api.xf-yun.com/v1.1/chat"  # v1.5环境的地址

    Spark_url = "ws://spark-api.xf-yun.com/v3.1/chat"  # v2.0环境的地址

    def do_ask(self, conditions, question, from_client="127.0.0.1", rounds=None):
        messages = []

        if conditions is not None:
            for condition in conditions:
                piece = {"role": "system", "content": condition}
                messages.append(piece)

        if rounds:
            i = 0
            for r in rounds:
                piece = {"role": "user" if i % 2 == 0 else "assistant", "content": r}
                messages.append(piece)
                i = i + 1

        piece = {"role": "user", "content": question}
        messages.append(piece)

        i = 0
        while i < 3:
            try:
                SparkApi.main(self.appid, self.api_key, self.api_secret, self.Spark_url, self.domain, messages)
                AssistGptQuestionRecordModel.objects.create(question=question, input_param=question, result_state=1,
                                                            output_param=SparkApi.answer, from_client=from_client)
                break
            except Exception as err:
                logger.error("do_ask error: {}".format(err))
                AssistGptQuestionRecordModel.objects.create(question=question, input_param=question, result_state=0,
                                                            output_param="James said sorry to you. {}".format(err),
                                                            from_client=from_client)
                i = i + 1

        answer = SparkApi.answer

        logger.info("do_ask conditions: {}".format(conditions))
        logger.info("do_ask question: {}".format(question))
        logger.info("do_ask answer: {}".format(answer))

        return answer

    @action(methods=['POST'], detail=False, url_path='ask')
    def ask(self, request):
        """
        ask 提问接口
        request.data["conditions"] 是一个String数组，用于传入样本
        request.data["question"] 是一个String，用于传入问题
        request.data["rounds"] 是一个String数组，用于传入多轮。奇数个是问题，偶数个是回答
        """
        if "conditions" in request.data:
            conditions = request.data["conditions"]
        else:
            conditions = None
        question = request.data["question"]

        from_client = SparkGpt.get_from_client(request)

        if "rounds" in request.data:
            rounds = request.data["rounds"]
            answer = self.do_ask(conditions, question, from_client, rounds)
        else:
            answer = self.do_ask(conditions, question, from_client)

        return Response(status=status.HTTP_200_OK, data={"answer": answer})

    @staticmethod
    def get_from_client(request):
        if 'HTTP_X_FORWARDED_FOR' in request.META:
            return request.META.get('HTTP_X_FORWARDED_FOR')
        else:
            return request.META.get('REMOTE_ADDR')


if __name__ == '__main__':
    gpt = SparkGpt()
    gpt.do_ask("", "1,3,6,10,15,下一个数字是几？")
