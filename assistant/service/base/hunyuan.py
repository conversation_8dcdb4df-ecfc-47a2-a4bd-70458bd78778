import json
import time
from assistant.model.assist_gpt_question_record_model import AssistGptQuestionRecordModel
from openai import OpenAI
from mantis.settings import logger
from assistant.service.base.sparkgpt import SparkGpt


class HunyuanTurbo(SparkGpt):
    client = OpenAI(
        # api_key="sk-9w8bg3pgkAUQzDX8T3BGw22m4pO7x7DG6OtZvszERltYCpGx",
        api_key="sk-A9eLmfPeLaQxJPHOQx7R6OP3evCq7CWQd4SRTpXWYUp86cjS",
        # os.environ.get("HUNYUAN_API_KEY"), # 混元 APIKey
        base_url="https://api.hunyuan.cloud.tencent.com/v1",  # 混元 endpoint
    )
    model = "hunyuan-turbos-20250604"

    def do_ask(self, conditions, question, from_client="127.0.0.1", rounds=None):
        messages = []

        if conditions is not None:
            condition_str = ""
            for condition in conditions:
                #把condition拼到condition_str上，前面加上"已知条件：\n"，后面加上2个换行
                condition_str = condition_str + "\n" + condition + "\n\n"

            if condition_str != "":
                piece = {"role": "system", "content": condition_str}
                messages.append(piece)

        if rounds:
            i = 0
            for r in rounds:
                piece = {"role": "user" if i % 2 == 0 else "assistant", "content": r}
                messages.append(piece)
                i = i + 1

        piece = {"role": "user", "content": question}
        messages.append(piece)

        completion = None
        i = 0
        while i < 3:
            #记录开始时间到start_time
            start_time = time.time()
            try:
                completion = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    extra_body={
                        "enable_enhancement": True,  # <- 自定义参数
                    },
                )

                AssistGptQuestionRecordModel.objects.create(question=question, input_param=messages, result_state=1,
                                                            output_param=completion.choices, from_client=from_client,
                                                            duration=round(time.time() - start_time, 3))
                answer = completion.choices[0].message.content
                break
            except Exception as err:
                logger.error("do_ask error: {}".format(err))
                AssistGptQuestionRecordModel.objects.create(question=question, input_param=messages, result_state=0,
                                                            output_param="James said sorry to you. {}".format(err),
                                                            from_client=from_client,
                                                            duration=round(time.time() - start_time, 3))
                answer = err.__str__()
                i = i + 1
                time.sleep(1)

        logger.info("do_ask conditions: {}".format(conditions))
        logger.info("do_ask question: {}".format(question))
        logger.info("do_ask answer: {}".format(answer))

        return answer

    def __get_answer_or_errormsg(self, response):
        try:
            answer = json.loads(response.text).get("choices")[0].get("message").get("content")
        except Exception as err:
            answer = "James said sorry to you. {}".format(
                json.loads(response.text).get("error").get("message").split('.')[0])

        return answer


class HunyuanT1(HunyuanTurbo):
    model = "hunyuan-t1-20250711"


class HunyuanLite(HunyuanTurbo):
    model = "hunyuan-lite"


class HunyuanStd256(HunyuanTurbo):
    model = "hunyuan-standard-256K"


class HunyuanStd(HunyuanTurbo):
    model = "hunyuan-standard"


class HunyuanFC(HunyuanTurbo):
    model = "hunyuan-functioncall"


class DeepseekV3(HunyuanTurbo):
    client = OpenAI(
        # api_key="sk-9w8bg3pgkAUQzDX8T3BGw22m4pO7x7DG6OtZvszERltYCpGx",
        api_key="sk-c4zH1jiwDf5taiE5CHO8WH39jws3ZK98FSORA2E8d3R1hvqW",
        # os.environ.get("HUNYUAN_API_KEY"), # 混元 APIKey
        base_url="https://api.lkeap.cloud.tencent.com/v1",  # 混元 endpoint
    )
    model = "deepseek-v3"
