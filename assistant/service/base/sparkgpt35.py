from assistant.service.base.sparkgpt import SparkGpt


class SparkGpt35(SparkGpt):
    """
    Spark MAX
    """
    domain = "generalv3.5"
    Spark_url = "wss://spark-api.xf-yun.com/v3.5/chat"


class SparkGptMax32(SparkGpt):
    """
    Spark MAX 32
    """
    domain = "generalv3.5"
    Spark_url = "wss://spark-api.xf-yun.com/chat/max-32k"
    appid = "5f36870e"  # 填写控制台中获取的 APPID 信息
    api_secret = "ZjMyYjAyZDc4ZmQzYjFhMWE3Y2NlYzU3"  # 填写控制台中获取的 APISecret 信息
    api_key = "9d372d68996528ee1079940b9a354cae"  # 填写控制台中获取的 APIKey 信息


class SparkGptPro128(SparkGpt):
    """
    Spark Pro 128
    """
    domain = "generalv3.5"
    Spark_url = "wss://spark-api.xf-yun.com/chat/pro-128k"
    appid = "5f36870e"  # 填写控制台中获取的 APPID 信息
    api_secret = "ZjMyYjAyZDc4ZmQzYjFhMWE3Y2NlYzU3"  # 填写控制台中获取的 APISecret 信息
    api_key = "9d372d68996528ee1079940b9a354cae"  # 填写控制台中获取的 APIKey 信息
