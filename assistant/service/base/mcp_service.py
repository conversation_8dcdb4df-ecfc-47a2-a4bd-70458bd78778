import sys
import os
from mcp.server.fastmcp import FastMCP
from dotenv import load_dotenv
import httpx
import json

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from bs4 import BeautifulSoup
from typing import Any
import httpx
from mcp.server.fastmcp import FastMCP
from starlette.applications import Starlette
from mcp.server.sse import SseServerTransport
from starlette.requests import Request
from starlette.routing import Mount, Route
from mcp.server import Server
import uvicorn
import threading
import logging
import importlib
import pkgutil

# 设置日志记录器
logger = logging.getLogger(__name__)

mcp = FastMCP("docs")

USER_AGENT = "docs-app/1.0"
SERPER_URL = "https://google.serper.dev/search"

docs_urls = {
    "langchain": "python.langchain.com/docs",
    "llama-index": "docs.llamaindex.ai/en/stable",
    "autogen": "microsoft.github.io/autogen/stable",
    "agno": "docs.agno.com",
    "openai-agents-sdk": "openai.github.io/openai-agents-python",
    "mcp-doc": "modelcontextprotocol.io",
    "camel-ai": "docs.camel-ai.org",
    "crew-ai": "docs.crewai.com"
}

async def search_web(query: str) -> dict | None:
    payload = json.dumps({"q": query, "num": 2})

    headers = {
        "X-API-KEY": os.getenv("SERPER_API_KEY"),
        "Content-Type": "application/json",
    }

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                SERPER_URL, headers=headers, data=payload, timeout=30.0
            )
            response.raise_for_status()
            return response.json()
        except httpx.TimeoutException:
            return {"organic": []}

async def fetch_url(url: str):
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, timeout=30.0)
            soup = BeautifulSoup(response.text, "html.parser")
            text = soup.get_text()
            return text
        except httpx.TimeoutException:
            return "Timeout error"


@mcp.tool()
async def get_apidoc(app_name: str, branch: str, api_name: str):
    """
    这个方法是根据应用名，分支号（有时候叫版本号）和接口名（关键字）获取接口定义的文档。这个接口文档也可以用于开发前端界面。

    参数:
    app_name: 应用名，例如 "howbuy-qa-info-remote"
    branch: 分支号，例如 "1.5.4"
    api_name: 接口名，例如 "get_agent_info"

    返回:
    Json格式的接口文档
    """
    # 构建API URL
    url = f"http://spider.howbuy.pa/spider/app_mgt/app_interface_api_param_for_mring?app_name={app_name}&branch_name={branch}"

    # 发送HTTP请求获取接口信息
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, timeout=30.0)
            data = response.json()

            # 检查请求是否成功
            if data.get("status") != "success":
                return {"error": f"获取接口信息失败: {data.get('msg', '未知错误')}"}

            # 遍历接口列表，查找匹配的接口
            for interface in data.get("data", []):
                # 检查interfacePath是否包含api_name
                if api_name in interface.get("interfacePath", ""):
                    return interface

            # 如果没有找到匹配的接口
            return {"error": f"未找到包含 {api_name} 的接口"}

        except Exception as e:
            return {"error": f"请求异常: {str(e)}"}

@mcp.tool()
async def get_app_deployed_branch(suite_code: str):
    """
    这个方法是根据环境名称获取应用部署版本（分支）的列表

    参数:
    suite_code: 环境名称，例如环境名是it50，it62，或bs-prod

    返回:
    Json格式的应用部署版本（分支）情况
    """
    # 构建API URL
    url = f"http://spider.howbuy.pa/spider/lib_repo_mgt/get_app_deployed_branch/?suite_code={suite_code}"

    # 发送HTTP请求获取接口信息
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, timeout=30.0)
            data = response.json()

            return data

        except Exception as e:
            return {"error": f"请求异常: {str(e)}"}


def create_starlette_app(mcp_server: Server, *, debug: bool = False) -> Starlette:
        """Create a Starlette application that can serve the provided mcp server with SSE."""
        sse = SseServerTransport("/messages/")

        async def handle_sse(request: Request) -> None:
            async with sse.connect_sse(
                    request.scope,
                    request.receive,
                    request._send,  # noqa: SLF001
            ) as (read_stream, write_stream):
                await mcp_server.run(
                    read_stream,
                    write_stream,
                    mcp_server.create_initialization_options(),
                )

        return Starlette(
            debug=debug,
            routes=[
                Route("/sse", endpoint=handle_sse),
                Mount("/messages/", app=sse.handle_post_message),
            ],
        )



def import_mcp_tools():
    """
    动态导入所有mcp工具模块
    """
    try:
        # 导入assistant.service.mcp_servers包
        from assistant.service import mcp_servers

        # 遍历包中所有以mcp_开头的模块
        for _, name, _ in pkgutil.iter_modules(mcp_servers.__path__):
            if name.startswith('mcp_'):
                importlib.import_module(f'assistant.service.mcp_servers.{name}')
                logger.info(f"Loaded MCP tool module: {name}")
    except Exception as e:
        logger.error(f"Failed to import MCP tools: {str(e)}")

def start_mcp_server():
    """
    在后台线程中启动MCP服务器
    """
    try:
        import uvicorn

        # 获取MCP服务器实例
        mcp_server = mcp._mcp_server

        # 创建Starlette应用
        starlette_app = create_starlette_app(mcp_server, debug=True)

        # 启动服务器
        host = '0.0.0.0'
        port = 8033
        logger.info(f"Starting MCP server on {host}:{port}")
        uvicorn.run(starlette_app, host=host, port=port)
    except Exception as e:
        logger.error(f"Failed to start MCP server: {str(e)}")


if __name__ == '__main__':

    # 首先导入所有MCP工具
    import_mcp_tools()

    start_mcp_server()