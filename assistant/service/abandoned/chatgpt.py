import json
import time

import requests
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from assistant.model.assist_gpt_question_record_model import AssistGptQuestionRecordModel
from assistant.service.base.base_gpt import BaseGpt
from mantis.settings import logger


class Chatgpt(ViewSet, BaseGpt):
    authentication_classes = []

    url = ["https://jamesai.openai.azure.com/openai/deployments/forstudy/chat/completions?api-version=2023-05-15",
           "https://jamesai4.openai.azure.com/openai/deployments/forstudy2/chat/completions?api-version=2023-05-15"
           ]

    hds = [
        {
            "Content-Type": "application/json",
            "api-key": "********************************"
        },
        {
            "Content-Type": "application/json",
            "api-key": "********************************"
        }

    ]

    def do_ask(self, conditions, question, from_client="127.0.0.1", rounds=None):
        messages = []

        for condition in conditions:
            piece = {"role": "system", "content": condition}
            messages.append(piece)

        if rounds:
            i = 0
            for r in rounds:
                piece = {"role": "user" if i % 2 == 0 else "assistant", "content": r}
                messages.append(piece)
                i = i + 1

        piece = {"role": "user", "content": question}
        messages.append(piece)

        data = {"messages": messages}

        i = 0
        response = None
        while i < 3:
            try:
                response = requests.post(url=self.url[i % 2], headers=self.hds[i % 2], data=json.dumps(data))
                AssistGptQuestionRecordModel.objects.create(question=question, input_param=data, result_state=1,
                                                            output_param=response.text, from_client=from_client)
                break
            except Exception as err:
                logger.error("do_ask error: {}".format(err))
                AssistGptQuestionRecordModel.objects.create(question=question, input_param=data, result_state=0,
                                                            output_param="James said sorry to you. {}".format(err),
                                                            from_client=from_client)
                i = i + 1
                time.sleep(1)

        if response is None:
            return "James said sorry to you."

        answer = self.__get_answer_or_errormsg(response)

        logger.info("do_ask conditions: {}".format(conditions))
        logger.info("do_ask question: {}".format(question))
        logger.info("do_ask answer: {}".format(answer))

        return answer

    def __get_answer_or_errormsg(self, response):
        try:
            answer = json.loads(response.text).get("choices")[0].get("message").get("content")
        except Exception as err:
            answer = "James said sorry to you. {}".format(
                json.loads(response.text).get("error").get("message").split('.')[0])

        return answer

    @action(methods=['POST'], detail=False, url_path='ask')
    def ask(self, request):
        """
        ask 提问接口
        request.data["conditions"] 是一个String数组，用于传入样本
        request.data["question"] 是一个String，用于传入问题
        request.data["rounds"] 是一个String数组，用于传入多轮。奇数个是问题，偶数个是回答
        """
        if "conditions" in request.data:
            conditions = request.data["conditions"]
        else:
            conditions = None
        question = request.data["question"]

        from_client = Chatgpt.get_from_client(request)

        if "rounds" in request.data:
            rounds = request.data["rounds"]
            answer = self.do_ask(conditions, question, from_client, rounds)
        else:
            answer = self.do_ask(conditions, question, from_client)

        return Response(status=status.HTTP_200_OK, data={"answer": answer})

    @staticmethod
    def get_from_client(request):
        if 'HTTP_X_FORWARDED_FOR' in request.META:
            return request.META.get('HTTP_X_FORWARDED_FOR')
        else:
            return request.META.get('REMOTE_ADDR')
