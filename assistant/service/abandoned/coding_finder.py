import re

from rest_framework import viewsets, response

from assistant.model.class_file_model import ClassFileModel
from assistant.model.file_blame_model import FileBlameModel
from assistant.service.base.hunyuan import HunyuanTurbo as Chatgpt
from mantis.settings import ApiResult


class InterfaceImplementFinder(viewsets.ViewSet):
    authentication_classes = []

    q4_get_entire_method = '把给定代码片段的第一个方法完整截取出来'
    q4_get_invoked_methods_of_sentence = '''将{method_name}方法中，所有用到了的方法全都列出来。一定要全部，完整。回答格式
    [{{"name":方法名(仅方法本身的名字), "variable": 方法所属变量名(如果没有就给None), "defined": 方法所属变量名是{method_name}方法内部申明的还是外部申明的？(回答：内部/外部) }}]
    '''
    q4_judge_is_the_implement_class = '''列出这段代码中实现的function。有没有实现代码？用这个格式回答：
[{"name", "", "is_implemented": True/False}]
    '''

    symbols = ["class", "interface"]

    def list(self, request):
        self.bot = Chatgpt()
        class_name = request.query_params.get("class_name")
        method_name = request.query_params.get("method_name")

        res = self.__query(class_name, method_name)

        return response.Response(data=ApiResult.success_dict(msg="找到代码片段", data=res))

    def __query(self, class_name, method_name):
        class_list = self.__find_all_relative_classes(class_name)

        code_implement = ""

        for class_file in class_list:
            answer, coding_content = self.__judge_is_the_implement_class(class_file.project_id, class_file.path,
                                                                         method_name)
            if answer:
                code_implement = coding_content
                return code_implement

        involved_methods = self.__find_involved_methods_of_sentence(code_implement, method_name)

        method_list = eval(involved_methods)

        '''res = 
        condition = "代码片段是：\n"
        for e in list:
            condition = condition + e[1]

        conditions = [condition]

        res = bot.do_ask(conditions, self.q4_get_entire_method)
        '''

        return code_implement

    def __judge_is_the_implement_class(self, project_id, path, method_name):
        lines = FileBlameModel.find_lines(project_id, path)
        coding_content = '''代码片段是:
        '''.format(project_id=project_id, path=path, method_name=method_name)

        for e in lines:
            coding_content = coding_content + '\n' + e[0]

        try:
            answer = self.bot.do_ask([coding_content], self.q4_judge_is_the_implement_class)
            method_list = eval(answer)
            for m in method_list:
                if m["name"] == method_name and m["is_implemented"]:
                    return True, coding_content

            return False, coding_content
        except Exception as err:
            return False, coding_content

    def __find_all_relative_classes(self, class_name):
        class_list = []
        line_list = FileBlameModel.find_relative_classes_by_name(class_name)

        pattern = '[^\w]+'  # 使用正则表达式分隔字符串

        for line in line_list:
            words = re.split(pattern, line[2])
            word = self.__get_class_name_from_words(words)
            if word:
                model = ClassFileModel()
                model.project_id = line[0]
                model.path = line[1]
                model.class_name = word
                class_list.append(model)

        return class_list

    def __get_class_name_from_words(self, words):
        find_symbol = False
        for word in words:
            if find_symbol:
                return word
            else:
                if word in self.symbols:
                    find_symbol = True

        return None

    def __find_involved_methods_of_sentence(self, sentence, method_name):
        res = self.bot.do_ask([sentence], self.q4_get_invoked_methods_of_sentence.format(method_name=method_name))

        return res
