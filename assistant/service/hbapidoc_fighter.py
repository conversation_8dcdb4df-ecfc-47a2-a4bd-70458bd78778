from rest_framework.decorators import action
from rest_framework.viewsets import ViewSet

from assistant.service.base.hunyuan import Hu<PERSON>uanTurbo as Chatgpt
from assistant.service.knowledge import Knowledge
from assistant.service.table_data_fighter import TableDataFighter


class H<PERSON>pidocFighter(ViewSet):
    authentication_classes = []

    question_template = "按好买标准，按给出的代码生成hbapidoc。\n{}"

    @action(methods=['POST'], detail=False, url_path='ask')
    def ask(self, request):
        self.gpt = Knowledge()
        from_client = Chatgpt.get_from_client(request)
        table_data = request.data["table_data"]
        api_type = "dubbo"
        if "api_type" in request.data:
            api_type = request.data["api_type"]
        question = self.question_template.format(TableDataFighter.list_to_table(table_data))

        return self.gpt.do_ask("it", "pasys", "devops", "apidoc", api_type, question, from_client)
