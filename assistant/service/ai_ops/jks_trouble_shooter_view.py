import json
import time
import uuid

import jenkins
from rest_framework.response import Response
from rest_framework import viewsets, status
from rest_framework.decorators import action
from xml.etree import ElementTree as ET

from assistant.model.assist_jks_pipeline_model import AssistJksPipelineModel
from mantis.settings import JENKINS_INFO, logger


class JksTroubleShooter(viewsets.ViewSet):
    authentication_classes = []

    job_name = 'trouble_shooter'
    id_field = 'INVOKING_ID'
    jenkins_url_template = "{}/blue/organizations/jenkins/{}/detail/{}/{}/pipeline"
    jks = None

    @action(methods=['POST'], detail=False, url_path='proc')
    def proc(self, request):
        name = request.data["name"]
        params = request.data["params"]

        try:
            self.jks = jenkins.<PERSON>(url=JENKINS_INFO["URL"], username=JENKINS_INFO["USER"],
                                       password=JENKINS_INFO["PASSWORD"])
            model = AssistJksPipelineModel.objects.filter(name=name)[0]

            job_config = self.jks.get_job_config(self.job_name)
            root = ET.fromstring(job_config)

            self._init_pipeline(root, model.pipeline_script)
            self._init_parameters(root, model.param_keys)

            new_job_config = ET.tostring(root, encoding='unicode')
            self.jks.reconfig_job(self.job_name, new_job_config)

            parameters = json.loads(params)
            unique_id = self._add_invoking_id(parameters)
            queue_id = self.jks.build_job(self.job_name, parameters=parameters)

            job_id = self._match_current_id(unique_id)

            return Response(status=status.HTTP_200_OK, data={"job_id": job_id, "unique_id": unique_id,
                                                             "url": self.jenkins_url_template.format(
                                                                 JENKINS_INFO["URL"], self.job_name, self.job_name,
                                                                 job_id)})
        except Exception as err:
            logger.error(err)
            raise err

    def _match_current_id(self, unique_id):
        while True:
            time.sleep(2)
            job_info = self.jks.get_job_info(self.job_name)
            # 获取 job 的最近10次构建信息
            builds = job_info['builds'][:5]
            try:
                for build in builds:
                    info = self.jks.get_build_info(self.job_name, build["number"])
                    if "actions" in info:
                        for action in info["actions"]:
                            if "parameters" in action:
                                for params in action["parameters"]:
                                    if params["name"] == self.id_field and params["value"] == unique_id:
                                        return info["id"]
            except Exception as err:
                logger.error(err)
                return None

    def _add_invoking_id(self, ref_params):
        unique_id = uuid.uuid4()
        if ref_params and isinstance(ref_params, dict):
            ref_params[self.id_field] = str(unique_id)
        return str(unique_id)

    def _init_pipeline(self, ref_root, pipeline_script):
        for script_tag in ref_root.iter('script'):
            script_tag.text = pipeline_script

    def _init_parameters(self, ref_root, param_keys):

        params = ref_root.find('.//hudson.model.ParametersDefinitionProperty')
        if params is not None:
            ref_root.find('.//properties').remove(params)

        # 在这个配置XML中，添加新的字符串参数
        parameters = ET.SubElement(ref_root.find('.//properties'), 'hudson.model.ParametersDefinitionProperty')
        parameters = ET.SubElement(parameters, 'parameterDefinitions')

        for str in json.loads(param_keys):
            str_param = ET.SubElement(parameters, 'hudson.model.StringParameterDefinition')
            ET.SubElement(str_param, 'name').text = str

        str_param = ET.SubElement(parameters, 'hudson.model.StringParameterDefinition')
        ET.SubElement(str_param, 'name').text = self.id_field
